import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../response_data_models/b2b/b2bAppCustomer/b2b_app_customer_page/b2b_app_customer_list.dart';
import 'customer_user_logic.dart';
import 'customer_user_state.dart';
import '../../../resource/color_resource.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomerUserPage extends StatelessWidget {
  const CustomerUserPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(CustomerUserLogic());
    final state = logic.state;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text('门店用户'),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
                colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight),
          ),
        ),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: logic.addUser,
            child: Text(
              '添加',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索框
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: logic.searchUsers,
              decoration: InputDecoration(
                hintText: '手机号/用户名',
                hintStyle: TextStyle(color: ColorResource.GRAY_EDIT_COLOR),
                prefixIcon:
                    Icon(Icons.search, color: ColorResource.GRAY_EDIT_COLOR),
                filled: true,
                fillColor: const Color(0xFFF8F8F8),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(80),
                  borderSide: BorderSide.none,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),

          // 用户列表
          Expanded(
            child: RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Obx(() {
                if (state.userList.isEmpty) {
                  return const Center(
                    child: Text(
                      '暂无用户数据',
                      style: TextStyle(color: Color(0xFF999999), fontSize: 16),
                    ),
                  );
                }
                return NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification scrollInfo) {
                    var metrics = scrollInfo.metrics;
                    if (metrics.maxScrollExtent > 0 &&
                        metrics.pixels == metrics.maxScrollExtent) {
                      // 滚动到底部，加载更多
                      logic.loadMore();
                      return true;
                    }
                    return false;
                  },
                  child: ListView.builder(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.all(16),
                    itemCount: state.userList.length + 1,
                    itemBuilder: (context, index) {
                      if (index == state.userList.length) {
                        return _buildLoadMoreIndicator(state);
                      }
                      final user = state.userList[index];
                      return _buildUserCard(user, logic);
                    },
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(B2bAppCustomerList user, CustomerUserLogic logic) {
    return Container(
      // height: 300.h,
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start, // 添加对齐方式
          children: [
            // 头像
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.person,
                color: ColorResource.GRAY_TITLE_COLOR,
                size: 24,
              ),
            ),

            const SizedBox(width: 12),

            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Wrap(
                    crossAxisAlignment: WrapCrossAlignment.center,
                    children: [
                      Text(
                        user.memberName ?? '',
                        style: TextStyle(
                          fontSize: 16,
                          height: 1.25,
                          fontWeight: FontWeight.w600,
                          color: ColorResource.BLACK_NORMAL_TITLE_COLOR,
                        ),
                      ),
                      if (user.status == 0) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: ColorResource.GRAY_TITLE_COLOR,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '停用',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ],
                      if (user.status == 1) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: ColorResource.RED_COMMON_COLOR,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '启用',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                      ]
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.phone_android,
                        color: ColorResource.RED_COMMON_COLOR,
                        size: 24.w,
                      ),
                      SizedBox(width: 6.w),
                      Text(
                        user.memberPhone ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorResource.BLACK_NORMAL_TITLE_COLOR,
                        ),
                      )
                    ],
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.home_work_outlined,
                        color: ColorResource.RED_COMMON_COLOR,
                        size: 24.w,
                      ),
                      SizedBox(width: 6.w),
                      Expanded(
                        child: Text(
                          user.branchName ?? '',
                          style: TextStyle(
                              fontSize: 14,
                              height: 1.25,
                              color: ColorResource.BLACK_NORMAL_TITLE_COLOR),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '最后登录：${user.lastLoginTime ?? ''}',
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorResource.GRAY_COMMON_COLOR,
                    ),
                  ),
                ],
              ),
            ),

            // 操作按钮
            SizedBox(
              width: 100.w, // 为操作按钮区域设置固定宽度
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                GestureDetector(
                  onTap: () => logic.editUser(user),
                  child: Container(
                      width: 80.w, // 增加宽度以容纳图标和文字
                      height: 70.w,
                      alignment: Alignment.center,
                      // padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      child: Row(
                    mainAxisSize: MainAxisSize.min, // 使用最小尺寸
                    children: [
                      Icon(
                        Icons.edit_note,
                        color: ColorResource.RED_COMMON_COLOR,
                        size: 20.w, // 稍微减小图标尺寸
                      ),
                      SizedBox(width: 4.w), // 添加间距
                      Text(
                        '编辑',
                        style: TextStyle(
                            color: ColorResource.RED_COMMON_COLOR,
                            fontSize: 12,
                            fontWeight: FontWeight.w600),
                      ),
                    ],
                  )),
                ),
                // 增加按钮间距
                // Container(
                //   width: 60.w,
                //   height: 60.w,
                // ),
                if (user.status == 1)
                  GestureDetector(
                    onTap: () => logic.showDeactivateConfirm(user),
                    child: Container(
                      width: 80.w, // 增加宽度
                      height: 70.w,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 10.w), // 减少左边距
                      child: const Text(
                        '  停用',
                        style: TextStyle(
                          color: Color(0xFFFF4444),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                if (user.status == 0)
                  GestureDetector(
                    onTap: () => logic.showNoDeactivateConfirm(user),
                    child: Container(
                      width: 80.w, // 增加宽度
                      height: 70.w,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 10.w), // 减少左边距
                      child: Text(
                        '  启用',
                        style: TextStyle(
                          color: ColorResource.PAY_QRCODE_PAGE_BACKGROUND_COLOR,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            ), // 添加缺失的SizedBox闭合括号
          ],
        ),
      ),
    );
  }

  // 加载更多指示器
  static Widget _buildLoadMoreIndicator(CustomerUserState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Obx(() {
          if (state.isLoadingMore.value) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 8),
                const Text(
                  '加载中...',
                  style: TextStyle(color: Color(0xFF999999), fontSize: 14),
                ),
              ],
            );
          } else if (!state.hasMore.value) {
            return const Text(
              '没有更多数据了',
              style: TextStyle(color: Color(0xFF999999), fontSize: 14),
            );
          } else {
            return const SizedBox.shrink();
          }
        }),
      ),
    );
  }
}
