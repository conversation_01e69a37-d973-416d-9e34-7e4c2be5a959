import 'dart:math';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:fuduoduo/store/state.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_pickers/time_picker/model/date_mode.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_pickers/time_picker/model/suffix.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'logic.dart';
import 'package:fuduoduo/widget/ImageLoad.dart';
import '../../../resource/color_resource.dart';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:fuduoduo/utils/color_utils.dart';
import '../../../utils/open_third_url_utils.dart';

class B2bCustomerSearchPage extends StatefulWidget {
  const B2bCustomerSearchPage({super.key});

  @override
  State<B2bCustomerSearchPage> createState() => B2bCustomerSearchPageState();
}

class B2bCustomerSearchPageState extends State<B2bCustomerSearchPage> {
  final logic = Get.put(B2bCustomerSearchPageLogic());
  final state = Get.find<B2bCustomerSearchPageLogic>().state;
  double clsBoxWidth = 180.w; // 分类宽度
  double typeItemHeight = 80.w; //类型高度

  // 下拉刷新控制器
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  // 下拉刷新
  void _onRefresh() async {
    await logic.onRefresh();
    if (state.isRefreshing) {
      _refreshController.refreshFailed();
    } else {
      _refreshController.refreshCompleted();
    }
  }

  // 上拉加载更多
  void _onLoading() async {
    logic.loadMore();
    // 等待一小段时间让数据加载完成
    await Future.delayed(Duration(milliseconds: 500));
    if (state.hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _AppBar(context),
        body: GetBuilder<B2bCustomerSearchPageLogic>(
            builder: (_) => Column(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.all(10.w),
                      height: 72.h,
                      decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular((40.0))),
                      child: TextField(
                        maxLines: 1,
                        minLines: 1,
                        textAlign: TextAlign.center,
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.search,
                        controller:
                            TextEditingController(text: state.searchText),
                        onChanged: (value) {
                          if (value == '' && state.searchText != '') {
                            logic.confirmSearch(value);
                          }
                        },
                        onSubmitted: (value) {
                          logic.confirmSearch(value);
                        },
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            isCollapsed: true,
                            hintText: '请输入搜索关键字',
                            hintStyle: TextStyle(fontSize: 28.sp)),
                      ),
                    ),
                    Expanded(
                        child: Row(
                      children: [Expanded(child: _ContentBox(context))],
                    ))
                  ],
                )));
  }

  void showSignIn(context, index) async {
    print("state.customerList[index]['longitude'] ${state.CustomerSearch[index]['longitude']}");

    // 安全获取经纬度
    String longitudeStr = state.CustomerSearch[index]['longitude']?.toString() ?? '0.0';
    String latitudeStr = state.CustomerSearch[index]['latitude']?.toString() ?? '0.0';

    double longitude = double.tryParse(longitudeStr) ?? 0.0;
    double latitude = double.tryParse(latitudeStr) ?? 0.0;

    if (longitude == 0.0 && latitude == 0.0) {
      MyCommonUtils.showToast('门店经纬度信息无效');
      return;
    }
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              return false;
            },
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(30.w))),
              child: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setDiaLogState) {
                    return SingleChildScrollView(
                      child: Container(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius:
                                BorderRadius.all(Radius.circular(30.w)),
                              ),
                              padding: EdgeInsets.fromLTRB(30.w, 30.w, 30.w, 0.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    '导航（打开地图）',
                                    style: TextStyle(
                                        fontSize: 28.w,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Stack(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(top: 10.w),
                                        height: 300.w,
                                        alignment: Alignment.center,
                                        color: Colors.grey,
                                        child: AmapView(
                                          mapType: MapType.Standard,
                                          showZoomControl: false,
                                          rotateGestureEnabled: false,
                                          zoomLevel: 18,
                                          showCompass: false,
                                          showScaleControl: false,
                                          centerCoordinate: LatLng(
                                              latitude,
                                              longitude),
                                          maskDelay:
                                          const Duration(milliseconds: 500),
                                          onMapCreated: (controller) async {
                                            logic.controller = controller;
                                            logic.mapAddMarker(
                                                latitudeStr,
                                                longitudeStr);
                                            if (mounted) setState(() {});
                                          },
                                          // onMapClicked: (latLng) async {
                                          // _latLng = latLng;
                                          // _mapAddMarker(latLng);
                                          // }
                                        ),
                                      ),
                                      Positioned(
                                        child: Container(
                                          alignment: Alignment.centerLeft,
                                          width: 1.sw,
                                          padding: EdgeInsets.symmetric(
                                              vertical: 14.w, horizontal: 8.w),
                                          color: ColorUtil.fromHex('#66000000'),
                                          child: Text(
                                            "${state.CustomerSearch[index]['branchAddr'] ?? '暂无地址'}",
                                            overflow: TextOverflow.fade,
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 20.w),
                                          ),
                                        ),
                                        bottom: 0,
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              height: 1.w,
                              width: 1.sw,
                              margin: EdgeInsets.only(top: 40.w),
                              color:
                              ColorResource.LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                            ),
                            Container(
                              child: Row(
                                children: [
                                  Expanded(
                                    child: InkWell(
                                      child: Container(
                                        child: Text('取消'),
                                        alignment: Alignment.center,
                                        padding:
                                        EdgeInsets.symmetric(vertical: 12.w),
                                      ),
                                      onTap: () {
                                        Get.back();
                                      },
                                    ),
                                  ),
                                  Container(
                                    height: 96.w,
                                    width: 1.w,
                                    color: ColorResource
                                        .LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                                  ),
                                  Expanded(
                                    child: InkWell(
                                      child: Container(
                                          alignment: Alignment.center,
                                          padding:
                                          EdgeInsets.symmetric(vertical: 12.w),
                                          child: Text('导航')),
                                      onTap: () {
                                        OpenThirdUrlUtils.showNavigationList(
                                            latitudeStr,
                                            longitudeStr,
                                            state.CustomerSearch[index]['branchName']?.toString() ?? '');
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }),
            ),
          );
        });
  }

  // 内容
  _ContentBox(BuildContext context) {
    return Column(
      children: [
        Expanded(
            child: Container(
          padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
          color: Colors.white,
          child: SmartRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            header: WaterDropHeader(
              complete: Text('刷新完成', style: TextStyle(fontSize: 24.w)),
              failed: Text('刷新失败', style: TextStyle(fontSize: 24.w)),
            ),
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget body;
                if (mode == LoadStatus.idle) {
                  body = Text("上拉加载更多", style: TextStyle(fontSize: 24.w));
                } else if (mode == LoadStatus.loading) {
                  body = Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 30.w,
                        height: 30.w,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 10.w),
                      Text("加载中...", style: TextStyle(fontSize: 24.w)),
                    ],
                  );
                } else if (mode == LoadStatus.failed) {
                  body = Text("加载失败，点击重试", style: TextStyle(fontSize: 24.w));
                } else if (mode == LoadStatus.canLoading) {
                  body = Text("松手加载更多", style: TextStyle(fontSize: 24.w));
                } else {
                  body = Text("没有更多数据了", style: TextStyle(fontSize: 24.w));
                }
                return Container(
                  height: 100.w,
                  child: Center(child: body),
                );
              },
            ),
            child: ListView.builder(
              itemCount: state.CustomerSearch.length,
              itemBuilder: (context, index) {
              dynamic item = state.CustomerSearch[index];
              return InkWell(
                onTap: () {
                  //跳转工作台页面
                  Get.toNamed(PageName.B2bCustomerInfoPage, parameters: {
                    "consumerNo": item['consumerNo'] ?? "",
                    "consumerName": item['consumerName'] ?? "",
                    "consumerAddress": item['shippingAddress'] ?? "",
                    "branchAddr": item['branchAddr'] ?? "",
                    "branchId": item['branchId'] ?? "",
                    "branchNo": item['branchNo'] ?? "",
                    "branchName": item['branchName'] ?? "",
                  });
                },
                child: Container(
                  padding: EdgeInsets.fromLTRB(0, 20.w, 0, 10.w),
                  decoration: BoxDecoration(
                      border: Border(
                    bottom: BorderSide(
                        width: 1.0.w,
                        color: Color.fromARGB(255, 240, 237, 237)),
                  )),
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ImageLoad.loadWidget(item["branchImages"] ?? item["imgUrls"] ?? "",
                              size: 120),
                          SizedBox(
                            width: 20.w,
                          ),
                          // Container(
                          //   width: 120.w,
                          //   height: 120.w,
                          //   margin: EdgeInsets.fromLTRB(0, 0, 20.w, 0),
                          //   decoration: BoxDecoration(
                          //       borderRadius: BorderRadius.circular(20.w),
                          //       image: DecorationImage(
                          //         image: NetworkImage(
                          //             "${Apis.imgCdnUrl}${item['imgUrls']}"),
                          //         fit: BoxFit.cover,
                          //       )),
                          // ),

                          Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${item['branchName']}",
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(fontSize: 28.w),
                                  ),
                                  Row(
                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        '距上次拜访:',
                                        style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 24.w),
                                      ),
                                      SizedBox(
                                        width: 20.w,
                                      ),
                                      // Text("${item['lastVisitDay'] ?? 0}天")
                                      Expanded(
                                        child: Text("${item['lastVisitDay'] > -1 ? '${item['lastVisitDay']}天' : '从未拜访'}",style: TextStyle(
                                          fontSize: 23.w,
                                        )),
                                      )
                                    ],
                                  ),
                                  Row(
                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        '距上次订货:',
                                        style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 24.w),
                                      ),
                                      SizedBox(
                                        width: 20.w,
                                      ),
                                      // Text("${item['lastActivityDay'] ?? 0}天")
                                      Expanded(
                                        child: Text("${item['lastActivityDay'] > -1 ? '${item['lastActivityDay']}天' : '从未下单'}",style: TextStyle(
                                          fontSize: 23.w,
                                        ),),
                                      )
                                    ],
                                  )
                                ],
                              )
                          ),



                          Column(
                                children: [
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  InkWell(
                                    onTap: () {
                                      // 获取客户详细信息
                                      OpenThirdUrlUtils.callPhone(item['contactPhone']);
                                    },
                                    child: Container(
                                      height: 69.w,
                                      width: 69.w,
                                      child: Icon(
                                        Icons.phone_rounded,
                                        size: 36.w,
                                        color: Colors.green,
                                      ),
                                    ),
                                    // child: Icon(
                                    //   Icons.phone_rounded,
                                    //   size: 36.w,
                                    //   color: Colors.green,
                                    // ),
                                    // child: Container(
                                    //   alignment: Alignment.center,
                                    //   margin: EdgeInsets.only(right: 24.w),
                                    //   height: 40.w,
                                    //   width: 40.w,
                                    //   decoration: BoxDecoration(
                                    //       color: ColorResource
                                    //           .WHITE_COMMON_COLOR,
                                    //       gradient: LinearGradient(
                                    //           colors: ColorResource
                                    //               .LINEAR_GRADIENT_COMMON_COLOR,
                                    //           begin: Alignment.centerLeft,
                                    //           end: Alignment.centerRight),
                                    //       borderRadius: BorderRadius.all(
                                    //           Radius.circular(24.w))),
                                    //   child: Icon(
                                    //     Icons.phone_rounded,
                                    //     size: 30.w,
                                    //     color:
                                    //     ColorResource.WHITE_COMMON_COLOR,
                                    //   ),
                                    // ),
                                  ),
                                  // SizedBox(
                                  //   height: 30.h,
                                  // ),
                                  InkWell(
                                    onTap: () {
                                      // 获取客户详细信息
                                      showSignIn(context, index);
                                    },
                                    child: Container(
                                        height: 69.w,
                                        width: 69.w,
                                        child:  Icon(
                                          Icons.near_me,
                                          size: 36.w,
                                          color: Colors.deepOrange,
                                        )
                                    )
                                    // child: Icon(
                                    //   Icons.near_me,
                                    //   size: 36.w,
                                    //   color: Colors.deepOrange,
                                    // )
                                    // child: Container(
                                    //   alignment: Alignment.center,
                                    //   margin: EdgeInsets.only(right: 24.w),
                                    //   height: 40.w,
                                    //   width: 40.w,
                                    //   decoration: BoxDecoration(
                                    //       color: ColorResource
                                    //           .WHITE_COMMON_COLOR,
                                    //       gradient: LinearGradient(
                                    //           colors: ColorResource
                                    //               .LINEAR_GRADIENT_COMMON_COLOR,
                                    //           begin: Alignment.centerLeft,
                                    //           end: Alignment.centerRight),
                                    //       borderRadius: BorderRadius.all(
                                    //           Radius.circular(24.w))),
                                    //   child: Icon(
                                    //     Icons.near_me,
                                    //     size: 30.w,
                                    //     color:
                                    //     ColorResource.WHITE_COMMON_COLOR,
                                    //   ),
                                    // ),
                                  ),
                                ],
                              )


                          // Expanded(
                          //     child: Column(
                          //   crossAxisAlignment: CrossAxisAlignment.start,
                          //   children: [
                          //
                          //
                          //
                          //
                          //     // Column(
                          //     //   children: [
                          //     //     InkWell(
                          //     //       onTap: () {
                          //     //         // 获取客户详细信息
                          //     //         // OpenThirdUrlUtils.callPhone(item['contactPhone']);
                          //     //       },
                          //     //       child: Container(
                          //     //         alignment: Alignment.center,
                          //     //         margin: EdgeInsets.only(right: 24.w),
                          //     //         height: 40.w,
                          //     //         width: 40.w,
                          //     //         decoration: BoxDecoration(
                          //     //             color: ColorResource
                          //     //                 .WHITE_COMMON_COLOR,
                          //     //             gradient: LinearGradient(
                          //     //                 colors: ColorResource
                          //     //                     .LINEAR_GRADIENT_COMMON_COLOR,
                          //     //                 begin: Alignment.centerLeft,
                          //     //                 end: Alignment.centerRight),
                          //     //             borderRadius: BorderRadius.all(
                          //     //                 Radius.circular(24.w))),
                          //     //         child: Icon(
                          //     //           Icons.phone_rounded,
                          //     //           size: 30.w,
                          //     //           color:
                          //     //           ColorResource.WHITE_COMMON_COLOR,
                          //     //         ),
                          //     //       ),
                          //     //     ),
                          //     //     SizedBox(
                          //     //       height: 10.h,
                          //     //     ),
                          //     //     InkWell(
                          //     //       onTap: () {
                          //     //         // 获取客户详细信息
                          //     //         // showSignIn(context, index);
                          //     //       },
                          //     //       child: Container(
                          //     //         alignment: Alignment.center,
                          //     //         margin: EdgeInsets.only(right: 24.w),
                          //     //         height: 40.w,
                          //     //         width: 40.w,
                          //     //         decoration: BoxDecoration(
                          //     //             color: ColorResource
                          //     //                 .WHITE_COMMON_COLOR,
                          //     //             gradient: LinearGradient(
                          //     //                 colors: ColorResource
                          //     //                     .LINEAR_GRADIENT_COMMON_COLOR,
                          //     //                 begin: Alignment.centerLeft,
                          //     //                 end: Alignment.centerRight),
                          //     //             borderRadius: BorderRadius.all(
                          //     //                 Radius.circular(24.w))),
                          //     //         child: Icon(
                          //     //           Icons.near_me,
                          //     //           size: 30.w,
                          //     //           color:
                          //     //           ColorResource.WHITE_COMMON_COLOR,
                          //     //         ),
                          //     //       ),
                          //     //     ),
                          //     //   ],
                          //     // )
                          //   ],
                          // ))
                        ],
                      ),
                      SizedBox(
                        height: 15.w,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 36.w,
                            color: Colors.red[400],
                          ),
                          Expanded(
                              child: Container(
                                  child: Text(
                            "${item['branchAddr'] ?? '暂无地址'}",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                color: Colors.grey[600], fontSize: 26.w),
                          )))
                        ],
                      )
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        ))
      ],
    );
  }

  // 头部标题
  _AppBar(BuildContext context) {
    return AppBar(
        foregroundColor: Colors.white,
        title: const Text("搜索客户"),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR, begin: Alignment.centerLeft, end: Alignment.centerRight),
          ),
        ));
  }
}
