import 'dart:math';

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/api/config.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/pages/b2b/customer_info/logic.dart';
import 'package:fuduoduo/response_data_models/b2b/b2bAppCoupon/b2b_app_available_coupons/b2b_app_available_coupons.dart';
import 'package:fuduoduo/response_data_models/b2b/b2bAppCoupon/b2b_app_branch_coupon_list/b2b_app_branch_coupon_list.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/widget/MyDialog.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'coupon_state.dart';

/// 优惠券页面逻辑控制器
class CouponLogic extends GetxController with GetTickerProviderStateMixin {
  final CouponState state = CouponState();
  final infoPageState = Get.find<B2bCustomerInfoPageLogic>().state;

  late TabController tabController;

  @override
  void onInit() {
    super.onInit();
    // 初始化TabController
    tabController = TabController(
      length: 3,
      vsync: this,
      initialIndex: 0,
    );

    // 监听标签页切换
    tabController.addListener(() {
      if (!tabController.indexIsChanging) {
        state.currentTabIndex.value = tabController.index;
      }
    });

    // 初始化数据
    // state.initMockData();
    // 获取可领取优惠券
    getAvailableCoupons((list) {
      state.availableCoupons.value = list;
    });
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  /// 可领取优惠券
  void getAvailableCoupons(void Function(List<CouponModel> list) callback) {
    SmartDialog.showLoading(msg: "加载中...");
    MyDio.post(
      Apis.b2bAppAvailableCoupons,
      queryParameters: {
        "branchId": infoPageState.branchId,
        "receiveType": "5", // 业务员发券 默认传5
        'activityStatus': '0',
      },
      successCallBack: (value) {
        SmartDialog.dismiss();
        var res = B2bAppAvailableCoupons.fromJson(commonModalFromJson(value));
        List<CouponModel> data = res.data!.list!
            .map(
              (e) => CouponModel(
                sysCode: int.parse(e.sysCode ?? '0'),
                couponId: int.parse(e.couponId ?? '0'),
                couponTemplateId: e.couponTemplateId ?? '',
                couponName: e.couponName ?? '',
                state: e.state ?? '1', // 可领取
                expirationDateStart: e.expirationDateStart ?? '',
                expirationDateEnd: e.expirationDateEnd ?? '',
                spuScope: int.parse((e.spuScope ?? '0').toString()), // 全场通用
                discountType:
                    int.parse((e.discountType ?? '0').toString()), // 折扣券
                discountAmt: double.parse((e.discountAmt ?? '0').toString()),
                discountPercent:
                    double.parse((e.discountPercent ?? '0').toString()),
                triggerAmt: double.parse((e.triggerAmt ?? '0').toString()),
                tabsType: 'available',
              ),
            )
            .toList();
        callback(data);
      },
      failCallBack: (error) {
        SmartDialog.dismiss();
        MyCommonUtils.showToast("获取可领取优惠券失败，请重试");
      },
    );
  }

  /// 未使用优惠券、已使用优惠券
  /// 优惠券状态state 1-未使用 2-已使用 3-已过期
  void getCoupons({
    String state = '1',
    String type = 'unused',
    void Function(List<CouponModel> list)? callback,
  }) {
    SmartDialog.showLoading(msg: "加载中...");
    MyDio.post(
      Apis.b2bAppBranchCouponList,
      queryParameters: {
        "branchId": infoPageState.branchId,
        "state": state,
      },
      successCallBack: (value) {
        SmartDialog.dismiss();
        var res = B2bAppBranchCouponList.fromJson(commonModalFromJson(value));
        List<CouponModel> data = (res.data?.list ?? []).map((e) {
          return CouponModel(
            sysCode: int.parse(e.sysCode ?? '0'),
            couponId: int.parse(e.couponId ?? '0'),
            couponTemplateId: e.couponTemplateId ?? '',
            couponName: e.couponName ?? '',
            state: state,
            expirationDateStart: e.expirationDateStart ?? '',
            expirationDateEnd: e.expirationDateEnd ?? '',
            spuScope: int.parse((e.spuScope ?? '0').toString()), // 全场通用
            discountType: int.parse((e.discountType ?? '0').toString()), // 折扣券
            discountAmt: double.parse((e.discountAmt ?? '0').toString()),
            discountPercent:
                double.parse((e.discountPercent ?? '0').toString()),
            triggerAmt: double.parse((e.triggerAmt ?? '0').toString()),
            tabsType: type,
          );
        }).toList();
        callback?.call(data);
      },
      failCallBack: (error) {
        SmartDialog.dismiss();
        MyCommonUtils.showToast("获取优惠券列表失败，请重试");
      },
    );
  }

  /// 切换标签页
  void switchTab(int index) {
    switch (index) {
      case 0:
        getAvailableCoupons((list) {
          state.availableCoupons.value = list;
        });
        break;
      case 1:
        getCoupons(
          state: '1',
          type: 'unused',
          callback: (list) {
            state.unusedCoupons.value = list;
          },
        );
        break;
      case 2:
        getCoupons(
          state: '2',
          type: 'used',
          callback: (list) {
            state.usedCoupons.value = list;
          },
        );
        break;
      default:
        MyCommonUtils.showToast("没有对应tabs");
    }
  }

  /// 领取优惠券
  void claimCoupon(String couponTemplateId) {
    try {
      print('领取优惠券: $couponTemplateId');
      MyDio.post(
        Apis.b2bAppSendCoupons,
        queryParameters: {
          "couponTemplateId": couponTemplateId,
          "branchIds": [infoPageState.branchId],
        },
        successCallBack: (value) {
          var res = ResponseStruct.fromJson(value);
          var data = (res.data as List<dynamic>?)
              ?.map((e) => SendCouponsData.fromJson(e as Map<String, dynamic>))
              .toList();
          if (data?[0].code == 0) {
            // print('领取优惠券=======');
            MyCommonUtils.showToast('优惠券领取成功');

            getAvailableCoupons((list) {
              state.availableCoupons.value = list;
            });
          } else {
            MyCommonUtils.showToast(data?[0].msg ?? '领取失败，请稍后重试');
          }
        },
        failCallBack: (error) {
          MyCommonUtils.showToast('网络请求失败，请稍后重试');
        },
      );
    } catch (e) {
      MyCommonUtils.showToast('领取失败，请稍后重试');
    }
  }

  /// 复制优惠券码
  void copyCouponCode(String couponCode) {
    // 这里应该调用剪贴板复制功能
    MyCommonUtils.showToast("复制成功,优惠券码已复制到剪贴板");
  }

  /// 刷新数据
  Future<void> refreshData(String type) async {
    state.isRefreshing.value = true;
    if (type == 'available') {}
    switch (type) {
      case 'available':
        getAvailableCoupons((list) {
          state.availableCoupons.value = list;
        });
        break;
      case 'unused':
        getCoupons(
          state: '1',
          type: 'unused',
          callback: (list) {
            state.unusedCoupons.value = list;
          },
        );
        break;
      case 'used':
        getCoupons(
          state: '2',
          type: 'used',
          callback: (list) {
            state.usedCoupons.value = list;
          },
        );
        break;
      default:
        MyCommonUtils.showToast("没有对应类型");
        break;
    }
    state.isRefreshing.value = false;
    MyCommonUtils.showToast("刷新成功,数据已更新");
  }

  /// 获取优惠券颜色
  Color getCouponColor(String state) {
    switch (state) {
      case '0': // 可领取
        return Colors.orange;
      case '1': // 未使用
        return Colors.orange;
      case '2': // 已使用
        return Colors.grey;
      case '3': // 已过期
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 获取按钮文本
  String getButtonText(String type) {
    switch (type) {
      case 'available': // 可领取
        return '立即领取';
      case 'unused': // 已领取
        return '已领取';
      case 'used': // 已使用
        return '已使用';
      case '3': // 已过期
        return '已过期';
      default:
        return '';
    }
  }

  /// 获取按钮颜色
  Color getButtonColor(String type) {
    switch (type) {
      case 'available': // 可领取
        return Colors.orange;
      case 'unused': // 未使用
        return Colors.grey;
      case 'used': // 已使用
        return Colors.grey;
      case '3': // 已过期
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 是否可以点击按钮
  bool isButtonEnabled(String type) {
    return type.contains('available');
  }

  /// 切换使用规则展开状态
  void toggleRuleExpansion(String couponTemplateId) {
    final currentState = state.expandedRules[couponTemplateId] ?? false;
    state.expandedRules[couponTemplateId] = !currentState;
  }

  /// 获取使用规则展开状态
  bool isRuleExpanded(String couponId) {
    return state.expandedRules[couponId] ?? false;
  }

  /// 获取使用规则文本
  List<String> getCouponRules() {
    return [
      '全场通用、海尔、美的、格力、奥克斯、志高门店',
      '使用后满30天内可用',
      '现购后满30天内可用',
    ];
  }

  String getExpirationText(String expirationDateEnd) {
    DateTime now = DateTime.now();
    var time = DateTime.parse(expirationDateEnd);
    Duration difference = time.difference(now);

    if (difference.inDays > 14) {
      // 如果大于两周，显示日期
      return expirationDateEnd;
    } else if (difference.inDays > 7) {
      // 如果大于一周但小于两周，显示"下周X"
      List<String> weekdays = ['一', '二', '三', '四', '五', '六', '日'];
      return '下周${weekdays[time.weekday - 1]}';
    } else {
      // 如果小于等于一周，显示"本周X"
      List<String> weekdays = ['一', '二', '三', '四', '五', '六', '日'];
      return '本周${weekdays[time.weekday - 1]}';
    }
  }

  String twoDigits(int n) {
    if (n >= 10) return '$n';
    return '0$n';
  }

  bool isExpiringThisWeek(String expirationDateEnd) {
    DateTime now = DateTime.now();
    var time = DateTime.parse(expirationDateEnd);
    Duration difference = time.difference(now);
    return difference.inDays <= 7;
  }
}
