/// 价格组列表数据模型
class PriceGroupBean {
  int? total;
  List<PriceGroupItem>? rows;
  int? code;
  String? msg;

  PriceGroupBean({this.total, this.rows, this.code, this.msg});

  PriceGroupBean.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    if (json['rows'] != null) {
      rows = <PriceGroupItem>[];
      json['rows'].forEach((v) {
        rows!.add(PriceGroupItem.fromJson(v));
      });
    }
    code = json['code'];
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total'] = total;
    if (rows != null) {
      data['rows'] = rows!.map((v) => v.toJson()).toList();
    }
    data['code'] = code;
    data['msg'] = msg;
    return data;
  }
}

class PriceGroupItem {
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  int? dictCode;
  int? dictSort;
  String? dictLabel;
  String? dictValue;
  String? dictType;
  String? cssClass;
  String? listClass;
  String? isDefault;
  String? status;
  String? remark;
  bool? defaultFlag;

  PriceGroupItem({
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.dictCode,
    this.dictSort,
    this.dictLabel,
    this.dictValue,
    this.dictType,
    this.cssClass,
    this.listClass,
    this.isDefault,
    this.status,
    this.remark,
    this.defaultFlag,
  });

  PriceGroupItem.fromJson(Map<String, dynamic> json) {
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    dictCode = json['dictCode'];
    dictSort = json['dictSort'];
    dictLabel = json['dictLabel'];
    dictValue = json['dictValue'];
    dictType = json['dictType'];
    cssClass = json['cssClass'];
    listClass = json['listClass'];
    isDefault = json['isDefault'];
    status = json['status'];
    remark = json['remark'];
    defaultFlag = json['default'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['updateBy'] = updateBy;
    data['updateTime'] = updateTime;
    data['dictCode'] = dictCode;
    data['dictSort'] = dictSort;
    data['dictLabel'] = dictLabel;
    data['dictValue'] = dictValue;
    data['dictType'] = dictType;
    data['cssClass'] = cssClass;
    data['listClass'] = listClass;
    data['isDefault'] = isDefault;
    data['status'] = status;
    data['remark'] = remark;
    data['default'] = defaultFlag;
    return data;
  }
}
