import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/item_list.dart';
import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/supplier_group_list.dart';
import 'store_carts_logic.dart';
import 'store_carts_state.dart';
import '../../../resource/color_resource.dart';

class StoreCartsPage extends StatelessWidget {
  const StoreCartsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(StoreCartsLogic());
    final state = logic.state;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
                colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight),
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          '购物车',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        ),
      ),
      body: Column(
        children: [
          // Tab栏
          // Container(
          //   color: Colors.white,
          //   child: Row(
          //     children: [
          //       Expanded(
          //         child: Obx(() => _buildTabItem(
          //             '购物车', 0, state.currentTabIndex.value == 0, logic)),
          //       ),
          //       // Expanded(
          //       //   child: Obx(() => _buildTabItem(
          //       //       '常购', 1, state.currentTabIndex.value == 1, logic)),
          //       // ),
          //       // Expanded(
          //       //   child: Obx(() => _buildTabItemWithBadge(
          //       //       '最近搜索', 2, state.currentTabIndex.value == 2, logic, 5)),
          //       // ),
          //     ],
          //   ),
          // ),
          // 内容区域
          Expanded(
            child: Obx(() {
              switch (state.currentTabIndex.value) {
                case 0:
                  return _buildCartTab(state, logic);
                case 1:
                  return _buildFrequentTab(state, logic);
                case 2:
                  return _buildRecentSearchTab(state, logic);
                default:
                  return Container();
              }
            }),
          ),
        ],
      ),
    );
  }

  // Tab项构建
  Widget _buildTabItem(
      String title, int index, bool isSelected, StoreCartsLogic logic) {
    return GestureDetector(
      onTap: () => logic.switchTab(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? const Color(0xFF4A90E2) : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color:
                isSelected ? const Color(0xFF4A90E2) : const Color(0xFF666666),
          ),
        ),
      ),
    );
  }

  // 带徽章的Tab项
  Widget _buildTabItemWithBadge(String title, int index, bool isSelected,
      StoreCartsLogic logic, int badgeCount) {
    return GestureDetector(
      onTap: () => logic.switchTab(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? const Color(0xFF4A90E2) : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected
                    ? const Color(0xFF4A90E2)
                    : const Color(0xFF666666),
              ),
            ),
            if (badgeCount > 0)
              Positioned(
                right: 20,
                top: 0,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    badgeCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 购物车Tab内容
  Widget _buildCartTab(StoreCartsState state, StoreCartsLogic logic) {
    return Column(
      children: [
        _buildDeliveryTabs(state, logic),
        Expanded(
          child: RefreshIndicator(
            onRefresh: logic.onRefresh,
            child: Obx(() {
              final cartData = state.cartData.value;
              if (cartData.supplierGroupList == null) {
                return const Center(
                  child: Text(
                    '暂无购物车数据',
                    style: TextStyle(color: Color(0xFF999999), fontSize: 16),
                  ),
                );
              }
              return NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  var metrics = scrollInfo.metrics;
                  if (metrics.maxScrollExtent > 0 &&
                      metrics.pixels == metrics.maxScrollExtent) {
                    // 滚动到底部，加载更多
                    logic.loadMore();
                    return true;
                  }
                  return false;
                },
                child: ListView.builder(
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: (cartData.supplierGroupList ?? []).length + 1,
                  itemBuilder: (context, index) {
                    if (index == (cartData.supplierGroupList ?? []).length) {
                      // 底部加载更多指示器
                      return _buildLoadMoreIndicator(state);
                    } else {
                      // 供应商分组
                      final supplierGroup =
                          (cartData.supplierGroupList ?? [])[index];
                      return _buildSupplierGroup(supplierGroup);
                    }
                  },
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  // 购物车头部信息
  Widget _buildCartHeader(StoreCartsState state, StoreCartsLogic logic) {
    return Column(
      children: [
        // 门店信息头部
        Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // 本地进货和全国批包邮Tab切换
              _buildDeliveryTabs(state, logic),
            ],
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  // 配送方式Tab切换
  Widget _buildDeliveryTabs(StoreCartsState state, StoreCartsLogic logic) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Obx(() => Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => logic.switchDeliveryTab(0),
                  child: Container(
                    height: 32,
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: state.deliveryTabIndex.value == 0
                          ? ColorResource.RED_COMMON_COLOR
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (state.deliveryTabIndex.value == 0)
                            Container(
                              width: 6,
                              height: 6,
                              margin: const EdgeInsets.only(right: 4),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Text(
                            '本地进货',
                            style: TextStyle(
                              color: state.deliveryTabIndex.value == 0
                                  ? Colors.white
                                  : const Color(0xFF666666),
                              fontSize: 14,
                              fontWeight: state.deliveryTabIndex.value == 0
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => logic.switchDeliveryTab(1),
                  child: Container(
                    height: 32,
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: state.deliveryTabIndex.value == 1
                          ? ColorResource.RED_COMMON_COLOR
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (state.deliveryTabIndex.value == 1)
                            Container(
                              width: 6,
                              height: 6,
                              margin: const EdgeInsets.only(right: 4),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                          Text(
                            '全国批包邮',
                            style: TextStyle(
                              color: state.deliveryTabIndex.value == 1
                                  ? Colors.white
                                  : const Color(0xFF666666),
                              fontSize: 14,
                              fontWeight: state.deliveryTabIndex.value == 1
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )),
    );
  }

  // 供应商分组
  Widget _buildSupplierGroup(SupplierGroupList supplierGroup) {
    return Column(
      children: [
        // 供应商头部
        Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                supplierGroup.supplierName ?? '',
                style: const TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 16,
                    fontWeight: FontWeight.w500),
              ),
              // GestureDetector(
              //   onTap: () {
              //     // 进入店铺逻辑
              //   },
              //   child: Row(
              //     children: [
              //       const Text(
              //         '进入店铺',
              //         style: TextStyle(color: Color(0xFFFF6B35), fontSize: 14),
              //       ),
              //       const SizedBox(width: 4),
              //       const Icon(Icons.arrow_forward_ios, color: Color(0xFFFF6B35), size: 12),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
        // 商品列表
        Container(
          color: Colors.white,
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: supplierGroup.itemList!.length,
            separatorBuilder: (context, index) =>
                const Divider(height: 1, color: Color(0xFFF0F0F0)),
            itemBuilder: (context, index) {
              final item = supplierGroup.itemList![index];
              return _buildCartItem(item);
            },
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  // 加载更多指示器
  static Widget _buildLoadMoreIndicator(StoreCartsState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Obx(() {
          if (state.isLoadingMore.value) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 8),
                const Text(
                  '加载中...',
                  style: TextStyle(color: Color(0xFF999999), fontSize: 14),
                ),
              ],
            );
          } else if (!state.hasMore.value) {
            return const Text(
              '没有更多数据了',
              style: TextStyle(color: Color(0xFF999999), fontSize: 14),
            );
          } else {
            return const SizedBox.shrink();
          }
        }),
      ),
    );
  }

  // 购物车商品项
  Widget _buildCartItem(ItemList item) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 商品图片
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(4),
            ),
            child: item.skuThumb != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      item.skuThumb ?? '',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.image,
                          color: Color(0xFFCCCCCC),
                          size: 24,
                        );
                      },
                    ),
                  )
                : const Icon(
                    Icons.image,
                    color: Color(0xFFCCCCCC),
                    size: 24,
                  ),
          ),
          const SizedBox(width: 12),
          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.spuName ?? '',
                  style: const TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      child: Row(
                        children: [
                          Text(
                            '¥${(item.markPrice ?? 0) % 1 == 0 ? (item.markPrice ?? 0).toInt().toString() : (item.markPrice ?? 0).toStringAsFixed(2)}',
                            style: const TextStyle(
                              color: Color(0xFFFF6B35),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            ' / ${item.unitName}',
                            style: const TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),

                    Container(
                      child:  Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            '加购数量: ',
                            style: TextStyle(
                              color: Color(0xFF666666),
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            item.productNum.toString(),
                            style: const TextStyle(
                              color: Color(0xFFFF6B35), // 橙色数字
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 4),
                if (item.addTime != null)
                  Text(
                    '加购时间：${item.addTime}',
                    style: const TextStyle(
                      color: Color(0xFF999999),
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),
          // // 数量显示 - 与价格对齐
          // Column(
          //   crossAxisAlignment: CrossAxisAlignment.end,
          //   children: [
          //     // 与价格保持对齐的间距
          //     const SizedBox(height: 8),
          //
          //   ],
          // )
        ],
      ),
    );
  }

  // 常购Tab内容
  Widget _buildFrequentTab(StoreCartsState state, StoreCartsLogic logic) {
    return RefreshIndicator(
      onRefresh: logic.onRefresh,
      child: const Center(
        child: Text(
          '暂无常购商品',
          style: TextStyle(color: Color(0xFF999999), fontSize: 16),
        ),
      ),
    );
  }

  // 最近搜索Tab内容
  Widget _buildRecentSearchTab(StoreCartsState state, StoreCartsLogic logic) {
    return RefreshIndicator(
      onRefresh: logic.onRefresh,
      child: Column(
        children: [
          // 搜索时间范围Tab
          Container(
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: Obx(() => _buildSearchTabItem(
                      '最近3天', 0, state.recentSearchTabIndex.value == 0, logic)),
                ),
                Expanded(
                  child: Obx(() => _buildSearchTabItem(
                      '最近一周', 1, state.recentSearchTabIndex.value == 1, logic)),
                ),
                Expanded(
                  child: Obx(() => _buildSearchTabItem('最近一个月', 2,
                      state.recentSearchTabIndex.value == 2, logic)),
                ),
              ],
            ),
          ),
          // 搜索列表头部
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                const Text(
                  '搜索关键词',
                  style: TextStyle(color: Color(0xFF666666), fontSize: 14),
                ),
                const Spacer(),
                Row(
                  children: [
                    const Text(
                      '搜索频次',
                      style: TextStyle(color: Color(0xFFFF6B35), fontSize: 14),
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.keyboard_arrow_up,
                        color: Color(0xFFFF6B35), size: 16),
                  ],
                ),
                const SizedBox(width: 40),
                const Text(
                  '最近搜索时间',
                  style: TextStyle(color: Color(0xFF666666), fontSize: 14),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.keyboard_arrow_up,
                    color: Color(0xFF666666), size: 16),
              ],
            ),
          ),
          // 搜索记录列表
          Expanded(
            child: Container(
              color: Colors.white,
              child: Obx(() => ListView.separated(
                    itemCount: state.recentSearches.length,
                    separatorBuilder: (context, index) =>
                        const Divider(height: 1, color: Color(0xFFF0F0F0)),
                    itemBuilder: (context, index) {
                      final item = state.recentSearches[index];
                      return _buildSearchItem(item);
                    },
                  )),
            ),
          ),
        ],
      ),
    );
  }

  // 搜索Tab项
  Widget _buildSearchTabItem(
      String title, int index, bool isSelected, StoreCartsLogic logic) {
    return GestureDetector(
      onTap: () => logic.switchRecentSearchTab(index),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? const Color(0xFFFF6B35) : Colors.transparent,
              width: 2,
            ),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color:
                isSelected ? const Color(0xFFFF6B35) : const Color(0xFF666666),
          ),
        ),
      ),
    );
  }

  // 搜索记录项
  Widget _buildSearchItem(SearchItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item.keyword,
              style: const TextStyle(
                color: Color(0xFF333333),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              item.resultCount.toString(),
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFF333333),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              item.searchTime,
              textAlign: TextAlign.right,
              style: const TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
