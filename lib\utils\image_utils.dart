import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../widget/MyDialog.dart';

import '../common/config.dart';

typedef void OnSuccess(imageFile);

class ImageUtils {
  static final ImagePicker _picker = ImagePicker();

  // 检查相机和存储权限
  static Future<void> _checkPermissions(BuildContext context, VoidCallback onSuccess) async {
    List<Permission> permissions = [];

    // 添加相机权限
    permissions.add(Permission.camera);

    // 根据平台和版本添加存储权限
    if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      if (androidInfo.version.sdkInt >= 33) {
        permissions.add(Permission.photos);
      } else {
        permissions.add(Permission.storage);
      }
    } else {
      permissions.add(Permission.photos);
    }

    // 检查权限状态
    bool allGranted = true;
    for (Permission permission in permissions) {
      PermissionStatus status = await permission.status;
      if (!status.isGranted) {
        allGranted = false;
        break;
      }
    }

    if (allGranted) {
      // 所有权限都已授予
      onSuccess();
      return;
    }

    // 请求权限
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    // 检查请求结果
    bool hasPermission = true;
    bool shouldShowDialog = false;

    for (Permission permission in permissions) {
      PermissionStatus status = statuses[permission] ?? PermissionStatus.denied;
      if (!status.isGranted) {
        hasPermission = false;
        if (status == PermissionStatus.permanentlyDenied) {
          shouldShowDialog = true;
        }
      }
    }

    if (hasPermission) {
      // 权限获取成功
      onSuccess();
    } else if (shouldShowDialog) {
      // 权限被永久拒绝，显示设置对话框
      _showPermissionDialog(context);
    } else {
      // 权限被拒绝，显示提示
      MyCommonUtils.showToast('需要相机和存储权限才能使用此功能');
    }
  }

  // 显示权限设置对话框
  static void _showPermissionDialog(BuildContext context) {
    MyDialog.showDialog(() {
      // 跳转到设置页面
      openAppSettings();
    }, content: "需要相机和存储权限才能使用此功能，请前往设置开启权限", sureText: "去设置");
  }

  //获取图片文件
  static void getImageFile(BuildContext context, OnSuccess onSuccess,
      {bool isShowDialog = true,
      int? type = 0,
      List<AssetEntity>? selectImages,
      int? maxLength = 1}) {
    print('------------$isShowDialog');

    // 先检查权限
    _checkPermissions(context, () {
      // 权限通过，继续原有逻辑
      isShowDialog
          ? showModalBottomSheet(
              context: context,
              builder: (BuildContext context) {
                //构建弹框中的内容
                return _buildBottomSheetWidget(context,
                    type: type,
                    onSuccess: onSuccess,
                    images: selectImages,
                    maxLength: maxLength);
              })
          : getImageFromCamera(context, onSuccess,
              type: type, images: selectImages);
    });
  }

  static Widget _buildBottomSheetWidget(BuildContext context,
      {required OnSuccess onSuccess,
      int? type = 0,
      int? maxLength,
      List<AssetEntity>? images}) {
    return SizedBox(
      height: 160,
      child: Column(
        children: [
          _buildItem(context, "拍照", onTap: () {
            // MyCommonUtils.showToast("拍照");
            getImageFromCamera(context, onSuccess, type: type, images: images);
          }),
          //分割线
          const Divider(),
          _buildItem(context, "从相册中选择", onTap: () {
            // MyCommonUtils.showToast("从相册中选择");
            getImageFromAlbum(context, onSuccess,
                type: type, maxLength: maxLength, images: images);
          }),
          Container(
            color: Colors.grey[300],
            height: 8,
          ),
          //取消按钮
          InkWell(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              child: const Text("取消"),
              height: 54,
              alignment: Alignment.center,
            ),
          )
        ],
      ),
    );
  }

  static Widget _buildItem(BuildContext context, String title,
      {required Function onTap}) {
    return InkWell(
      //点击回调
      onTap: () {
        //关闭弹框
        Navigator.of(context).pop();
        //外部回调
        onTap();
      },
      child: SizedBox(
        height: 40,
        //左右排开的线性布局
        child: Row(
          //所有的子Widget 水平方向居中
          mainAxisAlignment: MainAxisAlignment.center,
          //所有的子Widget 竖直方向居中
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [Text(title)],
        ),
      ),
    );
  }

  //从相机获取图片
  static Future getImageFromCamera(BuildContext context, OnSuccess onSuccess,
      {int? type = 0, List<AssetEntity>? images}) async {
    if (images != null) {
      if (AppConfig.isWeb) {
        var pickerImages;
        if (type == 1) {
          pickerImages = await _picker.pickVideo(source: ImageSource.camera);
        } else {
          pickerImages = await _picker.pickImage(
              source: ImageSource.gallery, imageQuality: 70);
        }
        if (pickerImages != null) {
          onSuccess(pickerImages);
        } else {
          //图片为空
          print('没有照片可以选择');
        }
      } else {
        CameraPickerConfig config;
        if (type == 1) {
          config = const CameraPickerConfig(
            onlyEnableRecording: true,
            enableRecording: true,
            enableTapRecording: true,
            resolutionPreset: ResolutionPreset.medium,
            maximumRecordingDuration: Duration(seconds: 60),
          );
        } else {
          config = const CameraPickerConfig(onlyEnableRecording: false);
        }
        final AssetEntity? entity =
            await CameraPicker.pickFromCamera(context, pickerConfig: config);
        if (entity == null) return;
        if (images != null) {
          List<AssetEntity>? temp = [];
          temp.addAll(images);
          temp.add(entity);
          onSuccess(temp);
        } else {
          File? imgFile = await entity.file;
          if (imgFile == null) return;
          // onSuccess(imgFile);
          File? file = (await compressFile(imgFile)) as File?;
          onSuccess(file!);
        }
      }
    } else {
      final XFile? xFile = await _picker.pickImage(source: ImageSource.camera);
      if (xFile != null) {
        // 使用选择的图片
        print('拍摄的照片路径：${xFile.path}');
        File? unCompressFile = File(xFile.path);
        var imageFile = (await compressFile(unCompressFile));
        // File? imageFile = (await compressFile(unCompressFile)) as File?;
        onSuccess(imageFile);
      } else {
        print('没有拍摄照片。');
      }
    }

    // if (AppConfig.isWeb) {
    //   var pickerImages;
    //   if (type == 1) {
    //     pickerImages = await _picker.pickVideo(source: ImageSource.camera);
    //   } else {
    //     pickerImages = await _picker.pickImage(source: ImageSource.gallery, imageQuality: 70);
    //   }
    //   if (pickerImages != null) {
    //     onSuccess(pickerImages);
    //   } else {
    //     //图片为空
    //     print('没有照片可以选择');
    //   }
    // } else {
    //   CameraPickerConfig config;
    //   if (type == 1) {
    //     config = const CameraPickerConfig(
    //       onlyEnableRecording: true,
    //       enableRecording: true,
    //       enableTapRecording: true,
    //       resolutionPreset: ResolutionPreset.medium,
    //       maximumRecordingDuration: Duration(seconds: 60),
    //     );
    //   } else {
    //     config = const CameraPickerConfig(onlyEnableRecording: false);
    //   }
    //   final AssetEntity? entity = await CameraPicker.pickFromCamera(context, pickerConfig: config);
    //   if (entity == null) return;
    //   if (images != null) {
    //     List<AssetEntity>? temp = [];
    //     temp.addAll(images);
    //     temp.add(entity);
    //     onSuccess(temp);
    //   } else {
    //     File? imgFile = await entity.file;
    //     if (imgFile == null) return;
    //     // onSuccess(imgFile);
    //     File? file = await compressFile(imgFile);
    //     onSuccess(file!);
    //   }
    // }
  }

  //从相册获取图片
  static Future getImageFromAlbum(BuildContext context, OnSuccess onSuccess,
      {int? type = 0, List<AssetEntity>? images, int? maxLength = 1}) async {
    // final XFile? xFile = await _picker.pickImage(source: ImageSource.gallery);
    //
    // if (xFile != null) {
    //   // 使用选择的图片
    //   print('选择的图片路径：${xFile.path}');
    //   File? unCompressFile = File(xFile.path);
    //   File? imageFile = await compressFile(unCompressFile);
    //   onSuccess(imageFile);
    // } else {
    //   print('没有选择图片。');
    // }

    if (AppConfig.isWeb) {
      var pickerImages;
      if (type == 1) {
        pickerImages = await _picker.pickVideo(source: ImageSource.gallery);
      } else {
        pickerImages = await _picker.pickImage(
            source: ImageSource.gallery, imageQuality: 70);
      }
      if (pickerImages != null) {
        onSuccess(pickerImages);
      } else {
        //图片为空
        print('没有照片可以选择');
      }
    } else {
      AssetPickerConfig config;
      if (type == 1) {
        config =
            AssetPickerConfig(maxAssets: 1, requestType: RequestType.video);
      } else {
        if (images != null) {
          config = AssetPickerConfig(
              maxAssets: maxLength ?? 1,
              requestType: RequestType.image,
              selectedAssets: images);
        } else {
          config =
              AssetPickerConfig(maxAssets: 1, requestType: RequestType.image);
        }
      }
      final List<AssetEntity>? entityList =
          await AssetPicker.pickAssets(context, pickerConfig: config);
      if (entityList != null) {
        if (images != null) {
          onSuccess(entityList);
        } else {
          File? imgFile = await entityList[0].file;
          if (imgFile != null) {
            if (type == 1) {
              onSuccess(imgFile);
            } else {
              var file = (await compressFile(imgFile));
              onSuccess(file!);
            }
          }
        }
      }
    }
  }

  //压缩照片
  static Future<Object?> compressFile(File file) async {
    if (file.lengthSync() < 200 * 1024) {
      return file;
    }
    var quality = 100;
    if (file.lengthSync() > 4 * 1024 * 1024) {
      quality = 50;
    } else if (file.lengthSync() > 2 * 1024 * 1024) {
      quality = 60;
    } else if (file.lengthSync() > 1 * 1024 * 1024) {
      quality = 70;
    } else if (file.lengthSync() > 0.5 * 1024 * 1024) {
      quality = 80;
    } else if (file.lengthSync() > 0.25 * 1024 * 1024) {
      quality = 90;
    }
    String suffix = file.path.split('.').last;
    var dir = await getTemporaryDirectory();
    var targetPath = dir.absolute.path +
        "/" +
        DateTime.now().millisecondsSinceEpoch.toString() +
        "." +
        suffix;
    CompressFormat format = CompressFormat.jpeg;
    if (suffix == 'png') {
      format = CompressFormat.png;
    } else if (suffix == "heic") {
      format = CompressFormat.heic;
    } else if (suffix == "webp") {
      format = CompressFormat.webp;
    }
    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      format: format,
      quality: quality,
    );
    return result;
  }
}
