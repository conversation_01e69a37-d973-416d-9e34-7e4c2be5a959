import 'package:collection/collection.dart';

import 'available_coupons_list.dart';

class AvailableCouponsData {
  List<AvailableCouponsList>? list;
  int? total;
  dynamic rest;

  AvailableCouponsData({this.list, this.total, this.rest});

  factory AvailableCouponsData.fromJson(Map<String, dynamic> json) {
    return AvailableCouponsData(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => AvailableCouponsList.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int?,
      rest: json['rest'] as dynamic,
    );
  }

  Map<String, dynamic> toJson() => {
        'list': list?.map((e) => e.toJson()).toList(),
        'total': total,
        'rest': rest,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AvailableCouponsData) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => list.hashCode ^ total.hashCode ^ rest.hashCode;
}
