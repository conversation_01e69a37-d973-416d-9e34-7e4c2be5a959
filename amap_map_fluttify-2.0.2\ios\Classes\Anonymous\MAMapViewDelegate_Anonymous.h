//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import <Foundation/Foundation.h>
#import <MAMapKit/MAMapKit.h>

@protocol FlutterPluginRegistrar;

NS_ASSUME_NONNULL_BEGIN

@interface MAMapViewDelegate_Anonymous : NSObject<MAMapViewDelegate>

- (instancetype) initWithFlutterPluginRegistrar: (NSObject <FlutterPluginRegistrar> *) registrar;

@property(nonatomic) NSObject<FlutterPluginRegistrar>* registrar;

@end

NS_ASSUME_NONNULL_END

