import 'dart:math';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/domain/branch_info_bean.dart';
import 'package:fuduoduo/domain/monthly_order_stats_bean.dart';
import 'package:fuduoduo/domain/sign_in_request_bean.dart';
import 'package:fuduoduo/domain/sign_out_request_bean.dart';
import 'package:fuduoduo/domain/sign_in_out_flag_bean.dart';

import 'package:get/get.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../domain/bind_customer_cloud_bean.dart';
import '../../../route/index.dart';
import '../../../utils/EventBusUtil.dart';
import '../../../utils/print_utils.dart';
import '../../../utils/set_info.dart';
import 'state.dart';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:fuduoduo/utils/storage.dart';

class B2bCustomerInfoPageLogic extends GetxController {
  var eventData;
  final B2bCustomerInfoPageState state = B2bCustomerInfoPageState();
  final publicLogic = Get.find<Public>();
  final publicState = Get.find<Public>().state;
  @override
  void onReady() {
    dynamic userInfo = publicState.userInfo ?? {};
    dynamic userSettings = userInfo['userSettings'] ?? {};
    double sales_scope = 500.0;
    try {
      sales_scope =
          (num.parse(userSettings['sales_scope'] ?? '500')).toDouble();
    } catch (e) {}

    bool store_address_update = userSettings['store_address_update'] == '1';
    state.effectiveMaxDistance = sales_scope;
    state.isAuthorityEdit = store_address_update;
    getCartsInfo();

    if (state.fromPage == PageName.PlaceOrderPage) {
      state.isNeedVisit = false;
      SpUtil.putBool('PlaceOrder', true);

      Map<String, String> parameters = {};
      parameters['branchNo'] = SpUtil.getString('branchNo')!;
      parameters['consumerNo'] = state.consumerNo;
      parameters['consumerName'] = state.consumerName;
      parameters['consumerAddress'] = state.consumerAddress;
      parameters['fromPage'] = state.fromPage;
      Get.toNamed(PageName.VehicleOrderPage, parameters: parameters);
    }

    super.onReady();
  }

  @override
  void onInit() {
    eventData = EventBusUtil.getInstance()!.eventBus.on().listen((event) {
      if (event["eventType"] == EventBusUtil.EVENT_UPDATE_CART) {
        getCartsInfo();
      }
    });

    if (Get.parameters["branchNo"] != null) {
      state.branchNo = Get.parameters["branchNo"]!;
    }

    if (Get.parameters["branchId"] != null) {
      state.branchId = Get.parameters["branchId"]!;
    }

    if (Get.parameters["branchName"] != null) {
      state.branchName = Get.parameters["branchName"]!;
    }

    if (Get.parameters["branchAddr"] != null) {
      state.branchAddr = Get.parameters["branchAddr"]!;
    }

    if (Get.parameters["consumerNo"] != null) {
      state.consumerNo = Get.parameters["consumerNo"]!;
    }

    if (Get.parameters["consumerName"] != null) {
      state.consumerName = Get.parameters["consumerName"]!;
    }

    if (Get.parameters["consumerAddress"] != null) {
      state.consumerAddress = Get.parameters["consumerAddress"]!;
    }

    if (Get.parameters["fromPage"] != null) {
      state.fromPage = Get.parameters["fromPage"]!;
    }

    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    eventData.cancel();
  }

  // 更新用户地理位置
  updateUserAddress(obj) {
    state.userLocation = obj;
  }

  // 获取是否需要签到，签退
  signInOrOutFlag(showSignIn, context) {
    // 检查必要参数
    if (state.branchId.isEmpty) {
      print("branchId 为空，无法获取签到签退状态");
      return;
    }

    dynamic userInfo = SpUtil.getObject('userInfo');
    String colonelId = '';

    // 获取用户信息中的业务员ID
    if (userInfo['colonelId'] != null) {
      colonelId = userInfo['colonelId'].toString();
    }

    print("SpUtil.getString('colonelId') ${colonelId}");

    SmartDialog.showLoading(msg: "正在加载...");
    Map<String, dynamic>? queryParameters = {};
    queryParameters['colonelId'] = colonelId ?? '';
    queryParameters['consumerNo'] = state.branchId; // 使用 branchId 作为 consumerNo

    MyDio.get(Apis.b2bSignInOrOutFlag, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      try {
        print("完整的API响应: $value");
        print("响应类型: ${value.runtimeType}");
        print("value['code']  ${value['code']} ${value['data']}");

        // 先使用原始数据处理，避免序列化错误
        if (value['code'] == 200 || value['code'] == 0 || value['code'] == '200' || value['code'] == '0') {
          final flagData = value['data'];
          print("flagData: $flagData, 类型: ${flagData.runtimeType}");

          if (flagData != null) {
            // 处理不同的数据格式
            if (flagData is String) {
              // 如果 data 是字符串，直接使用
              state.status = flagData;
            } else if (flagData is Map) {
              // 如果 data 是 Map，解析其中的字段
              if (flagData['signInFlag'] == '1' && flagData['signOutFlag'] == '0') {
                state.status = 'signIn';
              } else if (flagData['signInFlag'] == '1' && flagData['signOutFlag'] == '1') {
                state.status = 'signOut';
              } else {
                state.status = '';
              }

              // 保存访问日志ID等信息
              if (flagData['visitLogId'] != null) {
                state.visitLogId = flagData['visitLogId'].toString();
              }
            } else {
              // 其他情况，设为空
              state.status = '';
            }
          } else {
            state.status = '';
          }

          if (state.status == 'signIn' && showSignIn != false) {
            dynamic userInfo = publicState.userInfo ?? {};
            print("userInfo 类型: ${userInfo.runtimeType}");
            print("userInfo 内容: $userInfo");

            // 获取用户组织设置，兼容不同的数据结构
            dynamic userOrgSettings;
            if (userInfo is Map) {
              userOrgSettings = userInfo['userOrgSettings'];
            } else {
              // 如果是 B2bUserData 对象，尝试从 SpUtil 获取完整的用户信息
              Map? storedUserInfo = SpUtil.getObject('userInfo');
              userOrgSettings = storedUserInfo?['userOrgSettings'];
              print("从存储获取 userOrgSettings: $userOrgSettings");
            }

            if (userOrgSettings != null &&
                userOrgSettings['is_visit_signIn'] != null) {
              //0是不需要签到 1是需要
              if (userOrgSettings['is_visit_signIn'] == '0') {
                state.isNeedVisit = false;
              } else {
                state.isNeedVisit = true;
              }
            } else {
              state.isNeedVisit = true;
            }

            if (userOrgSettings != null &&
                userOrgSettings['is_visit_photograph'] != null) {
              //0是不需要拍照 1是需要
              if (userOrgSettings['is_visit_photograph'] == '0') {
                state.isNeedPhoto = false;
              } else {
                state.isNeedPhoto = true;
              }
            } else {
              state.isNeedPhoto = true;
            }
            //判断是否需要签到
            if (state.isNeedVisit) {
              // 获取客户的签到状态
              showSignIn(context, 'in');
            }
          }
          // getkefuInfo();
          update();
        } else {
          MyCommonUtils.showToast(value['msg']?.toString() ?? "获取签到状态失败");
        }
      } catch (e) {
        print("解析签到状态失败: $e");
        MyCommonUtils.showToast("数据解析失败");
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
      MyCommonUtils.showToast("网络请求失败");
    });
  }

  // 获取门店详细信息（新接口）
  void getBranchInfo(showSignIn, context) {
    // if (state.branchId.isEmpty) {
    //   // 如果没有 branchId，回退到旧的接口
    //   getConsumerByNo(showSignIn, context);
    //   return;
    // }

    SmartDialog.showLoading(msg: "加载中...");
    String apiUrl = "${Apis.getBranchInfo}${state.branchId}";
    Map<String, dynamic>? queryParameters = {};
    queryParameters['id'] = state.branchId;

    MyDio.get(apiUrl, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      try {
        final response = BranchInfoBean.fromJson(value);
        if (response.code == 200 || response.code == 0) {
          final branchData = response.data;
          if (branchData != null) {
            state.branchInfo = branchData.toJson();

            // 更新基本信息
            if (branchData.branchName != null) {
              state.branchName = branchData.branchName!;
              state.consumerName = branchData.branchName!; // 兼容旧版本
            }
            if (branchData.branchAddr != null) {
              state.branchAddr = branchData.branchAddr!;
              state.consumerAddress = branchData.branchAddr!; // 兼容旧版本
            }
            if (branchData.branchNo != null) {
              state.branchNo = branchData.branchNo!;
              state.consumerNo = branchData.branchNo!; // 兼容旧版本
            }

            // 为了兼容现有的视图代码，将新数据映射到 consumerInfo
            state.consumerInfo = {
              'consumerName': branchData.branchName ?? '',
              'contactMan': branchData.contactName ?? '',
              'telephoneNum': branchData.contactPhone ?? '',
              'shippingAddress': branchData.branchAddr ?? '',
              'branchId': branchData.branchId ?? '',
              'dictValue': branchData.dictValue ?? '',
              'branchNo': branchData.branchNo ?? '',
              'branchName': branchData.branchName ?? '',
              'branchAddr': branchData.branchAddr ?? '',
              'contactName': branchData.contactName ?? '',
              'contactPhone': branchData.contactPhone ?? '',
              'colonelId': branchData.colonelId ?? '',
              'channelId': branchData.channelId ?? 0,
              'channelName': branchData.channelName ?? '',
              'areaId': branchData.areaId ?? '',
              'areaName': branchData.areaName ?? '',
              'groupId': branchData.groupId ?? '',
              'groupName': branchData.groupName ?? '',
              'status': branchData.status ?? 0,
              'auditState': branchData.auditState ?? 0,
              'salePriceCode': branchData.salePriceCode ?? 0,
              'salePriceName': branchData.salePriceName ?? '',
              'hdfkSupport': branchData.hdfkSupport ?? 0,
              'hdfkMaxAmt': branchData.hdfkMaxAmt ?? 0,
              'provinceName': branchData.provinceName ?? '',
              'cityName': branchData.cityName ?? '',
              'districtName': branchData.districtName ?? '',
              'provinceCode': branchData.provinceCode ?? '',
              'cityCode': branchData.cityCode ?? '',
              'districtCode': branchData.districtCode ?? '',
              'streetCode': branchData.streetCode ?? '',
              'cityRegionName': branchData.cityRegionName ?? '',
              'lifecycleStage': branchData.lifecycleStage ?? 0,
              'lifecycleStageName': branchData.lifecycleStageName ?? '',
              'nextBranchLifecycleMessage': branchData.nextBranchLifecycleMessage ?? '',
              'isPayOnline': branchData.isPayOnline ?? 0,
              'memo': branchData.memo ?? '',
              'longitude': branchData.longitude ?? 0.0,
              'latitude': branchData.latitude ?? 0.0,
              'thisMonthOrderCount': branchData.thisMonthOrderCount ?? 0,
              'thisMonthOrderAmount': branchData.thisMonthOrderAmount ?? 0.0,
              'lastVisitDay': branchData.lastVisitDays ?? 0,
              'lastActivityDay': branchData.lastOrderDays ?? 0,
              'lastVisitDays': branchData.lastVisitDays ?? 0,
              'lastOrderDays': branchData.lastOrderDays ?? 0,
              'branchUserCount': branchData.branchUserCount ?? 0,
              'imgUrls': branchData.branchImages ?? '',
              'createTime': branchData.createTime ?? '',
              'updateTime': branchData.updateTime ?? '',
            };

            state.userAcountNum = branchData.branchUserCount!;

            // 缓存用户票据类型
            SpUtil.putString('invoiceType', '0');

            // printLong("branchInfo=>${state.branchInfo}");
            // printLong("consumerInfo=>${state.consumerInfo}");

            signInOrOutFlag(showSignIn, context);

            // 获取门店信息成功后，获取订单统计数据
            getMonthlyOrderStats();

            update();
          } else {
            MyCommonUtils.showToast("门店信息为空");
          }
        } else {
          MyCommonUtils.showToast(response.msg ?? "获取门店信息失败");
        }
      } catch (e) {
        print("解析门店详细信息失败: $e");
        MyCommonUtils.showToast("数据解析失败");
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
      MyCommonUtils.showToast("网络请求失败");
    });
  }

  // 获取月度订单统计数据
  void getMonthlyOrderStats() {
    if (state.branchId.isEmpty) {
      print("branchId 为空，无法获取订单统计数据");
      return;
    }

    Map<String, dynamic>? queryParameters = {};
    queryParameters['branchId'] = state.branchId;

    MyDio.get(Apis.getMonthlyOrderStats, queryParameters: queryParameters,
        successCallBack: (value) {
      try {
        final response = MonthlyOrderStatsBean.fromJson(value);
        if (response.code == 200 || response.code == 0) {
          final statsData = response.data;
          if (statsData != null) {
            state.monthlyOrderStats = statsData.toJson();

            // 更新 consumerInfo 中的订单统计数据以保持兼容性
            state.consumerInfo['thisMonthOrderCount'] = statsData.currentMonthOrders?.count ?? 0;
            state.consumerInfo['thisMonthOrderAmount'] = statsData.currentMonthOrders?.totalAmount ?? 0.0;
            state.consumerInfo['lastMonthOrderCount'] = statsData.previousMonthOrders?.count ?? 0;
            state.consumerInfo['lastMonthOrderAmount'] = statsData.previousMonthOrders?.totalAmount ?? 0.0;

            printLong("monthlyOrderStats=>${state.monthlyOrderStats}");
            update();
          } else {
            print("订单统计数据为空");
          }
        } else {
          print("获取订单统计数据失败: ${response.msg}");
        }
      } catch (e) {
        print("解析订单统计数据失败: $e");
      }
    }, failCallBack: (value) {
      print("获取订单统计数据网络请求失败");
    });
  }

  // 获取客户信息（旧接口，保持兼容性）
  void getConsumerByNo(showSignIn, context) {
    SmartDialog.showLoading(msg: "加载中...");
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = state.consumerNo;
    MyDio.get(Apis.consumerByNo, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic item = response.data ?? {};
        state.consumerInfo = item;

        // 缓存用户票据类型(1.不开票，2.增值税普通发票，3.增值税专用发票，4.其它票据，5.农产品收购发票)
        if (item.containsKey('invoiceType')) {
          SpUtil.putString('invoiceType', item['invoiceType']);
        } else {
          SpUtil.putString('invoiceType', '0');
        }

        printLong("consumerInfo=>${state.consumerInfo}");
        if (state.consumerInfo["routeCode"] == null ||
            state.consumerInfo["routeCode"] == null) {
          Get.toNamed(PageName.StoreInfoPage, arguments: {
            "type": "edit",
            "from": "customerList",
            "customerInfo": state.consumerInfo
          })?.then((result) {
            if (result != null && result == true) {
              getBranchInfo(false, false);
            }
          });
        }
        signInOrOutFlag(showSignIn, context);
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
    });
  }

  getkefuInfo() {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = state.consumerNo;
    MyDio.get(Apis.consumerByNo, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic item = response.data ?? {};
        state.consumerInfo = item;
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  // 签到/ 签退
  void sign(dynamic obj, context, String signType) {
    Location? userLocation;
    if (state.userLocation is Location) userLocation = state.userLocation;
    LatLng? _latLng = userLocation?.latLng;
    String _signInImgUrls = obj['_signInImgUrls'] ?? ''; // 签到门头照片
    bool _isEffectiveDistance = obj['_isEffectiveDistance']; // 是否在有效期距离内
    bool _isEditAddress = obj['_isEditAddress']; // 是否确认覆盖
    bool _isConsumerLatLonNull = obj['_isConsumerLatLonNull']; //门店地址是否为空
    print(obj);
    print(userLocation);

    if (!obj['_isEffectiveDistance'] &&
        !state.isAuthorityEdit &&
        !_isConsumerLatLonNull) {
      MyCommonUtils.showToast("没有达到${signType == 'in' ? '签到' : '签退'}条件");
      return;
    }
    String signInLatitude = '';
    String signInLongitude = '';
    String signInType = "1"; // 正常签到
    String? signInAddress = userLocation?.address;
    if (_latLng != null) {
      signInLatitude = _latLng.latitude.toString();
      signInLongitude = _latLng.longitude.toString();
    }
    // 获取用户信息中的业务员ID
    String colonelId = '';
    dynamic userInfo = SpUtil.getObject('userInfo');
    if (userInfo['colonelId'] != null) {
      colonelId = userInfo['colonelId'].toString();
    }

    String _image = _signInImgUrls;
    // print('imgUrl.value ${imgUrl.value}');
    if (_signInImgUrls.isNotEmpty && !_signInImgUrls.contains("http")) {
      _image = '';
      String prefix = '';
      if (!Apis.baseUrl.contains("seller.annto")) {  // 默认是 生产环境的biz04地址 如果不是生产环境  就用biz01的地址
        prefix = "https://annto-cos-biz01-1317294607.cos.ap-guangzhou.myqcloud.com";
      } else {
        prefix = Apis.imgCdnUrl;
      }
      _signInImgUrls = prefix + _signInImgUrls;
    }
    // print('imgUrl.value ${_image}');
    // return;
    // _signInImgUrls = _image;

    if (signType == 'in') {
      // 签到
      if (_signInImgUrls == '' && state.isNeedPhoto) {
        MyCommonUtils.showToast("请上传门头照");
        return;
      }
      // 确认覆盖门店地址
      if (_isEditAddress) {
        signInType = '2';
      } else {
        // 门店位置为空
        if (_isConsumerLatLonNull) {
          signInType = '3';
        } else {
          // 不在有效距离内
          if (_isEffectiveDistance) {
            signInType = '4';
          }
        }
      }



      // 创建签到请求对象
      final signInRequest = SignInRequestBean(
        colonelId: colonelId,
        latitude: signInLatitude,
        longitude: signInLongitude,
        consumerNo: state.branchId,
        signInAddress: signInAddress ?? '',
        signInImgUrls: _signInImgUrls,
        signInLatitude: signInLatitude,
        signInLongitude: signInLongitude,
        signInType: signInType,
        signInDistance: obj['_distance']?.toString() ?? '0',
        branchNo: state.branchNo,
      );

      MyDio.post(Apis.b2bSignIn, model: signInRequest.toJson(),
          successCallBack: (value) {
        try {
          final response = SignInResponseBean.fromJson(value);
          print('response.code ${response.code} ${response.code == '200'}');
          if (response.code == 200 || response.code == '200' || response.code == 0 ) {
            MyCommonUtils.showToast('签到成功!');
            signInOrOutFlag(false, false);
            Navigator.pop(context);
          } else {
            MyCommonUtils.showToast(response.msg ?? '签到失败');
          }
        } catch (e) {
          print("签到响应解析失败: $e");
          MyCommonUtils.showToast('签到失败');
        }
      }, failCallBack: (value) {
        MyCommonUtils.showToast('网络请求失败');
      });
    } else if (signType == 'out') {
      // 签退
      final signOutRequest = SignOutRequestBean(
        colonelId: colonelId,
        latitude: signInLatitude,
        longitude: signInLongitude,
        consumerNo: state.branchId,
        signOutAddress: signInAddress ?? '',
        signOutLatitude: signInLatitude,
        signOutLongitude: signInLongitude,
        signOutDistance: obj['_distance']?.toString() ?? '0',
        remark: obj['remark'] ?? "签退备注",
        branchNo: state.branchNo,
      );

      MyDio.put(Apis.b2bSignOut, queryParameters: signOutRequest.toJson(),
          successCallBack: (value) {
        try {
          final response = SignOutResponseBean.fromJson(value);
          if (response.code == 200 || response.code == '200' || response.code == 0 ) {
            state.status = "";
            MyCommonUtils.showToast('签退成功!');
            Navigator.pop(context);
            Get.back();
          } else {
            MyCommonUtils.showToast(response.msg ?? '签退失败');
          }
        } catch (e) {
          print("签退响应解析失败: $e");
          MyCommonUtils.showToast('签退失败');
        }
      }, failCallBack: (value) {
        MyCommonUtils.showToast('网络请求失败');
      });
    }
  }

  // 放弃拜访
  void waive() {
    // 获取用户信息中的业务员ID
    String colonelId = '';
    dynamic userInfo = SpUtil.getObject('userInfo');
    if (userInfo['colonelId'] != null) {
      colonelId = userInfo['colonelId'].toString();
    }

    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = state.branchId;
    queryParameters['colonelId'] = colonelId;
    MyDio.get("${Apis.b2bWaive}",
        queryParameters: queryParameters, successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  toCommonScanPage() async {
    var status = await Permission.camera.request();
    if (status.isGranted) {
      Get.toNamed(PageName.CommonScanPage)?.then((value) => {
            if (value != null && value.toString().isNotEmpty)
              {bindCustomerToCloud(value.toString())}
            else
              {MyCommonUtils.showToast("请扫描有效二维码或者条码")}
          });
    } else if (status.isDenied) {
      openAppSettings();
    }
  }

  //云商绑定
  bindCustomerToCloud(String scanResult) {
    Map<String, dynamic> queryParameters = {
      "consumerNo": state.consumerNo,
      "code": scanResult
    };
    MyDio.post(Apis.bindCustomerToCloud, queryParameters: queryParameters,
        successCallBack: (value) async {
      var response = BindCustomerCloudBean.fromJson(value);
      if (response.data?.code == 200) {
        MyCommonUtils.showToast("云商绑定成功！");
        getkefuInfo();
      } else if (response.data?.code == 401) {
        MyCommonUtils.showToast('登录失效，请重新登录');

        /// 删除存储的令牌。
        /// 
        /// 此操作会清除安全存储中的令牌信息。       
        await SecureStorage.token().delete?.call();

        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.data!.msg!);
      }
    }, failCallBack: (value) {
      MyCommonUtils.showToast("网络连接失败");
    });
  }

  //获取代课下单列表地址
  getPlaceOrderListUrl() {
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getPlaceOrderListUrl + state.consumerNo,
        queryParameters: queryParameters, successCallBack: (value) async {
      if (value["code"] == 200) {
        String sign = value["data"]["sign"];
        String from = value["data"]["from"];
        num shopId = value["data"]["shopId"];
        num supplierShopId = value["data"]["supplierShopId"];

        String duoduoShopName = value["data"]["duoduoShopName"];
        RegExp plusPattern = RegExp('[+]');
        bool hasPlus = plusPattern.hasMatch(duoduoShopName);
        List tempList = [];
        String duoduoShopNameNew = "";
        if (hasPlus) {
          tempList = duoduoShopName.split("");
          tempList.removeWhere((element) => element == "+");
          tempList.insert(duoduoShopName.indexOf("+"), "%2B");
          tempList.forEach((element) {
            duoduoShopNameNew += element;
          });
        }
        print("duoduoShopName=>${duoduoShopName}");
        print("duoduoShopNameNew=>${duoduoShopNameNew}");

        String requestTime = value["data"]["requestTime"];
        String salesmanCode = value["data"]["salesmanCode"];
        String salesmanName = value["data"]["salesmanName"];
        num salesmanRole = value["data"]["salesmanRole"];

        // "https://dev-shop.tongfuyouxuan.com/api/tfshop/merchant/fdd/forclientOrderEntry/formDuoDuo"
        String listUrl = SetInfo.instance.tmpWebviewUrl +
            "api/tfshop/merchant/fdd/forclientOrderEntry/formDuoDuo" +
            "?sign=${sign}&from=${from}&shopId=${shopId}&supplierShopId=${supplierShopId}&duoduoShopName=${duoduoShopNameNew.isEmpty ? duoduoShopName : duoduoShopNameNew}&requestTime=${requestTime}&salesmanCode=${salesmanCode}&salesmanName=${salesmanName}&salesmanRole=${salesmanRole}";
        print("listUrl=>${listUrl}");
        Get.toNamed(PageName.CommonWebViewPage,
            arguments: {'title': '代客下单列表', 'url': listUrl});
      } else if (value["code"] == 401) {
        MyCommonUtils.showToast('登录失效，请重新登录');

        /// 删除存储的令牌。
        /// 
        /// 此操作会清除安全存储中的令牌信息。       
        await SecureStorage.token().delete?.call();

        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        if (value["msg"] != null) {
          MyCommonUtils.showToast(value["msg"]);
        }
      }
    }, failCallBack: (value) {
      // MyCommonUtils.showToast("网络连接失败");
    });
  }

  double calculateDistance(LatLng start, LatLng end) {
    const double earthRadius = 6371.0; // Radius of the Earth in kilometers
    // Convert coordinates to radians
    final double lat1 = start.latitude * (pi / 180.0);
    final double lon1 = start.longitude * (pi / 180.0);
    final double lat2 = end.latitude * (pi / 180.0);
    final double lon2 = end.longitude * (pi / 180.0);
    // Calculate the differences between the coordinates
    final double dLat = lat2 - lat1;
    final double dLon = lon2 - lon1;
    // Apply the Haversine formula
    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1) * cos(lat2) * sin(dLon / 2) * sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final double distance = earthRadius * c;
    return distance * 1000; // Distance in kilometers, add "*1000" to get meters
  }

  // 获取购物车信息
  void getCartsInfo() {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = state.consumerNo;
    MyDio.get(Apis.getCartInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        state.cartData = response.data?['transList'] ?? [];
        print("1=>cartData=>${state.cartData}");
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }
}
