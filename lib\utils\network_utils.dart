import 'package:get/get.dart';
import 'network_manager.dart';
import 'common_utils.dart';

/// 网络工具类
class NetworkUtils {
  /// 检查网络连接并显示Toast提示
  static Future<bool> checkNetworkWithToast() async {
    final networkManager = NetworkManager.instance;
    final isConnected = await networkManager.checkConnection();
    
    if (!isConnected) {
      MyCommonUtils.showToast('当前网络不可用，请检查网络连接');
    }
    
    return isConnected;
  }
  
  /// 在网络请求前检查网络状态
  static bool checkNetworkBeforeRequest() {
    final networkManager = NetworkManager.instance;
    final isConnected = networkManager.isConnected.value;
    
    if (!isConnected) {
      MyCommonUtils.showToast('网络连接不可用，请检查网络设置');
      return false;
    }
    
    return true;
  }
  
  /// 获取网络状态描述
  static String getNetworkStatusText() {
    final networkManager = NetworkManager.instance;
    return networkManager.getConnectionTypeText();
  }
  
  /// 是否有网络连接
  static bool get isConnected {
    return NetworkManager.instance.isConnected.value;
  }
}
