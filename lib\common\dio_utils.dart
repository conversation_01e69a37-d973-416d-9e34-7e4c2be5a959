/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月11日08:28:27
 * 修改时间：2023年09月11日08:28:26
 */

import 'dart:async';
import 'dart:io';
import 'package:fuduoduo/domain/common_result_bean.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dio_manager.dart';
import 'package:flustars/flustars.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'apis.dart';
import 'package:fuduoduo/utils/storage.dart';
// import 'package:fuduoduo/common/config.dart';
import 'package:fuduoduo/utils/storage_common.dart';
import '../utils/network_utils.dart';

// 下载进度
typedef OnProgress = void Function(int count, int total);
// 下载成功
typedef OnResult = void Function();
// 请求失败
typedef OnFailure = void Function();

class MyDio {
  ///获取版本名
  static Future _getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  static void get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    required Function successCallBack,
    Function? failCallBack,
    bool? showErrMsg,
  }) {
    // 检查网络连接状态
    if (!NetworkUtils.checkNetworkBeforeRequest()) {
      failCallBack?.call(null);
      return;
    }

    _getAppVersion().then((version) async {
      var token = SpUtil.getString('access_token', defValue: '');
      var branchNo = SpUtil.getString('branchNo');
      var tenantId = SpUtil.getString('tenant_id');
      var userType = await SecureStorageCommon.save('userType').get();
      if (queryParameters != null) {
        // 如果没有指定库存默认缓存中的库存编号
        if (!queryParameters.containsKey('branchNo')) {
          queryParameters['branchNo'] = branchNo ?? '';
        }
      }
      var options = Options(
        receiveTimeout: Duration(milliseconds: 150000),
        responseType: ResponseType.json,
        validateStatus: (status) {
          // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
          return true;
        },
        headers: {
          'Accept': 'application/json,*/*',
          'Content-Type': 'application/json',
          'Authorization': token,
          'requestSource': 'APP',
          'X-Access-Token': token,
          'os': Platform.isAndroid ? 'ANDROID' : 'IOS',
          'system': userType == 'b2bSystem' &&
                  path != '/miniapi/app-order/getApplications'
              ? 'b2bmart'
              : '',
          'version': version,
          // 'applicationCode': AppConfig.applicationCode,
          'tenantId': tenantId ?? '',
        },
      );

      // print('dio.options    ${dio.options.baseUrl} ${path}');

      // 如果是saas 接口 api 前缀要替换
      if (path.contains('api-saas-auth/')) {
        if (dio.options.baseUrl.contains('ver')) {
          dio.options.baseUrl = Apis.SaasBaseUrlVer;
        } else if (dio.options.baseUrl.contains('seller') ||
            dio.options.baseUrl.contains('saasapi.annto')) {
          dio.options.baseUrl = Apis.SaasBaseUrl;
          // } else if  (dio.options.baseUrl.contains('uat')) {
        } else {
          dio.options.baseUrl = Apis.SaasBaseUrlUat;
        }
      } else {
        dio.options.baseUrl = Apis.baseUrl;
      }

      dio
          .get(
        path,
        // data: JsonUtil.encodeObj({}),
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      )
          .then((value) async {
        if (value.statusCode == 200 || value.statusCode == 201) {
          CommonResultBean result = CommonResultBean.fromJson(value.data);
          if (result.code == 0 || result.code == 200 || result.code == '0') {
            successCallBack(value.data);
          } else if (result.code == 401) {
            MyCommonUtils.showToast(result.msg ?? '');
            // MyCommonUtils.showToast('登录失效，请重新登录');

            /// 删除存储的令牌。
            /// 此操作会清除安全存储中的令牌信息。
            await SecureStorage.token().delete?.call();
            SpUtil.remove("access_token");
            SpUtil.remove("branchName");
            SpUtil.remove("branchNo");
            Get.offAllNamed(PageName.LOGIN);
          } else {
            if (showErrMsg ?? true) {
              MyCommonUtils.showToast(result.msg ?? '请求失败');
            }
            failCallBack?.call(result);
          }
        } else {
          failCallBack?.call(null);
          MyCommonUtils.showToast("网络请求失败");
        }
      }, onError: (err) {
        failCallBack?.call(null);
        if (err is DioException) {
          switch (err.type) {
            case DioExceptionType.connectionTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.sendTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.receiveTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.badCertificate:
              break;
            case DioExceptionType.badResponse:
              break;
            case DioExceptionType.cancel:
              break;
            case DioExceptionType.connectionError:
              break;
            case DioExceptionType.unknown:
              break;
          }
        }
      });
    });
    return;
  }

  static void post(
    String path, {
    Map<String, dynamic>? queryParameters,
    dynamic model,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    required Function successCallBack,
    Function? failCallBack,
    bool? showErrMsg,
  }) {
    // 检查网络连接状态
    if (!NetworkUtils.checkNetworkBeforeRequest()) {
      failCallBack?.call(null);
      return;
    }

    _getAppVersion().then((version) async {
      var token = SpUtil.getString('access_token');
      var branchNo = SpUtil.getString('branchNo');
      var tenantId = SpUtil.getString('tenant_id');
      var userType = await SecureStorageCommon.save('userType').get();
      if (queryParameters != null) {
        // 如果没有指定库存默认缓存中的库存编号
        if (!queryParameters.containsKey('branchNo')) {
          queryParameters['branchNo'] = branchNo ?? '';
        }
      }
      var options = Options(
        receiveTimeout: Duration(milliseconds: 150000),
        responseType: ResponseType.json,
        validateStatus: (status) {
          // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
          return true;
        },
        headers: {
          'Accept': 'application/json,*/*',
          'Content-Type': 'application/json',
          'Authorization': token,
          'requestSource': 'APP',
          'X-Access-Token': token,
          'os': Platform.isAndroid ? 'ANDROID' : 'IOS',
          'system': userType == 'b2bSystem' ? 'b2bmart' : '',
          'version': version,
          // 'applicationCode': AppConfig.applicationCode,
          'tenantId': tenantId ?? '',
        },
      );

      // 如果是saas 接口 api 前缀要替换
      if (path.contains('api-saas-auth/')) {
        if (dio.options.baseUrl.contains('ver')) {
          dio.options.baseUrl = Apis.SaasBaseUrlVer;
        } else if (dio.options.baseUrl.contains('seller') ||
            dio.options.baseUrl.contains('saasapi.annto')) {
          dio.options.baseUrl = Apis.SaasBaseUrl;
          // } else if  (dio.options.baseUrl.contains('uat')) {
        } else {
          dio.options.baseUrl = Apis.SaasBaseUrlUat;
        }
      } else {
        dio.options.baseUrl = Apis.baseUrl;
      }

      dio
          .post(
        path,
        // queryParameters: queryParameters,
        queryParameters: {},
        data: model == null
            ? JsonUtil.encodeObj(queryParameters)
            : JsonUtil.encodeObj(model),
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      )
          .then((value) async {
        if (value.statusCode == 200 || value.statusCode == 201) {
          CommonResultBean result = CommonResultBean.fromJson(value.data);
          if (result.code == 200 || result.code == '0' || result.code == 0) {
            successCallBack(value.data);
          } else if (result.code == 401) {
            MyCommonUtils.showToast(result.msg ?? '');
            // MyCommonUtils.showToast('登录失效，请重新登录');

            /// 删除存储的令牌。
            /// 此操作会清除安全存储中的令牌信息。
            await SecureStorage.token().delete?.call();

            SpUtil.remove("access_token");
            SpUtil.remove("branchName");
            SpUtil.remove("branchNo");
            Get.offAllNamed(PageName.LOGIN);
          } else if (value.data != null &&
              result.msg == null &&
              failCallBack != null) {
            failCallBack(value.data);
          } else {
            if (showErrMsg ?? true) {
              MyCommonUtils.showToast(result.msg ?? '请求失败');
            }
            failCallBack?.call(result);
          }
        } else {
          MyCommonUtils.showToast("网络请求失败");
          failCallBack?.call(value);
        }
      }, onError: (err) {
        failCallBack?.call(null);
        if (err is DioException) {
          switch (err.type) {
            case DioExceptionType.connectionTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.sendTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.receiveTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.badCertificate:
              break;
            case DioExceptionType.badResponse:
              break;
            case DioExceptionType.cancel:
              break;
            case DioExceptionType.connectionError:
              break;
            case DioExceptionType.unknown:
              break;
          }
        }
      });
    });
    return;
  }

  static void delete(
    String path, {
    Map<String, dynamic>? queryParameters,
    dynamic? model,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    required Function successCallBack,
    Function? failCallBack,
    bool? showErrMsg,
  }) {
    _getAppVersion().then((version) async {
      var token = SpUtil.getString('access_token');
      var branchNo = SpUtil.getString('branchNo');
      var tenantId = SpUtil.getString('tenant_id');
      var userType = await SecureStorageCommon.save('userType').get();
      if (queryParameters != null) {
        // 如果没有指定库存默认缓存中的库存编号
        if (!queryParameters.containsKey('branchNo')) {
          queryParameters['branchNo'] = branchNo ?? '';
        }
      }
      var options = Options(
        receiveTimeout: Duration(milliseconds: 150000),
        responseType: ResponseType.json,
        validateStatus: (status) {
          // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
          return true;
        },
        headers: {
          'Accept': 'application/json,*/*',
          'Content-Type': 'application/json',
          'Authorization': userType != 'b2bSystem' ? token : '',
          'requestSource': 'APP',
          'os': Platform.isAndroid ? 'ANDROID' : 'IOS',
          'system': userType == 'b2bSystem' ? 'b2bmart' : '',
          'X-Access-Token': userType == 'b2bSystem' ? token : '',
          'version': version,
          'tenantId': tenantId ?? '',
        },
      );

      // 如果是saas 接口 api 前缀要替换
      if (path.contains('api-saas-auth/')) {
        if (dio.options.baseUrl.contains('ver')) {
          dio.options.baseUrl = Apis.SaasBaseUrlVer;
        } else if (dio.options.baseUrl.contains('seller') ||
            dio.options.baseUrl.contains('saasapi.annto')) {
          dio.options.baseUrl = Apis.SaasBaseUrl;
          // } else if  (dio.options.baseUrl.contains('uat')) {
        } else {
          dio.options.baseUrl = Apis.SaasBaseUrlUat;
        }
      } else {
        dio.options.baseUrl = Apis.baseUrl;
      }

      dio
          .delete(
        path,
        // queryParameters: queryParameters,
        queryParameters: {},
        data: model == null
            ? JsonUtil.encodeObj(queryParameters)
            : JsonUtil.encodeObj(model),
        options: options,
        cancelToken: cancelToken,
      )
          .then((value) async {
        if (value.statusCode == 200 || value.statusCode == 201) {
          CommonResultBean result = CommonResultBean.fromJson(value.data);
          if (result.code == 200 || result.code == '0' || result.code == 0) {
            successCallBack(value.data);
          } else if (result.code == 401) {
            MyCommonUtils.showToast(result.msg ?? '');
            // MyCommonUtils.showToast('登录失效，请重新登录');

            /// 删除存储的令牌。
            /// 此操作会清除安全存储中的令牌信息。
            await SecureStorage.token().delete?.call();

            SpUtil.remove("access_token");
            SpUtil.remove("branchName");
            SpUtil.remove("branchNo");
            Get.offAllNamed(PageName.LOGIN);
          } else if (value.data != null &&
              result.msg == null &&
              failCallBack != null) {
            failCallBack(value.data);
          } else {
            if (showErrMsg ?? true) {
              MyCommonUtils.showToast(result.msg ?? '请求失败');
            }
            failCallBack?.call(result);
          }
        } else {
          MyCommonUtils.showToast("网络请求失败");
          failCallBack?.call(value);
        }
      }, onError: (err) {
        failCallBack?.call(null);
        if (err is DioException) {
          switch (err.type) {
            case DioExceptionType.connectionTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.sendTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.receiveTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.badCertificate:
              break;
            case DioExceptionType.badResponse:
              break;
            case DioExceptionType.cancel:
              break;
            case DioExceptionType.connectionError:
              break;
            case DioExceptionType.unknown:
              break;
          }
        }
      });
    });
    return;
  }

  static void put(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
    required Function successCallBack,
    Function? failCallBack,
  }) {
    _getAppVersion().then((version) async {
      var token = SpUtil.getString('access_token');
      var branchNo = SpUtil.getString('branchNo');
      var tenantId = SpUtil.getString('tenant_id');
      var userType = await SecureStorageCommon.save('userType').get();
      if (queryParameters != null) {
        // 如果没有指定库存默认缓存中的库存编号
        if (!queryParameters.containsKey('branchNo')) {
          queryParameters['branchNo'] = branchNo ?? '';
        }
      }
      var options = Options(
          receiveTimeout: Duration(milliseconds: 150000),
          responseType: ResponseType.json,
          validateStatus: (status) {
            // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
            return true;
          },
          headers: {
            'Accept': 'application/json,*/*',
            'Content-Type': 'application/json',
            'Authorization': token,
            'requestSource': 'APP',
            'X-Access-Token': token,
            'os': Platform.isAndroid ? 'ANDROID' : 'IOS',
            'system': userType == 'b2bSystem' ? 'b2bmart' : '',
            'version': version,
            'tenantId': tenantId ?? '',
          });

      dio
          .put(
        path,
        // queryParameters: queryParameters,
        queryParameters: {},
        data: JsonUtil.encodeObj(queryParameters),
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      )
          .then((value) async {
        if (value.statusCode == 200 || value.statusCode == 201) {
          CommonResultBean result = CommonResultBean.fromJson(value.data);
          if (result.code == 200 || result.code == '0' || result.code == 0) {
            successCallBack(value.data);
          } else if (result.code == 401) {
            MyCommonUtils.showToast(result.msg ?? '');
            // MyCommonUtils.showToast('登录失效，请重新登录');
            /// 删除存储的令牌。
            /// 此操作会清除安全存储中的令牌信息。
            await SecureStorage.token().delete?.call();

            SpUtil.remove("access_token");
            SpUtil.remove("branchName");
            SpUtil.remove("branchNo");
            Get.offAllNamed(PageName.LOGIN);
          } else {
            MyCommonUtils.showToast(result.msg ?? '请求失败');
            failCallBack?.call(value);
          }
        } else {
          MyCommonUtils.showToast("网络请求失败");
          failCallBack?.call(value);
        }
      }, onError: (err) {
        failCallBack?.call(null);
        if (err is DioException) {
          switch (err.type) {
            case DioExceptionType.connectionTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.sendTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.receiveTimeout:
              MyCommonUtils.showToast("网络连接超时");
              break;
            case DioExceptionType.badCertificate:
              break;
            case DioExceptionType.badResponse:
              break;
            case DioExceptionType.cancel:
              break;
            case DioExceptionType.connectionError:
              break;
            case DioExceptionType.unknown:
              break;
          }
        }
      });
    });
    return;
  }

  final httpHeaders = {
    'Accept': 'application/json, text/plain, */*',
    'Authorization': '666',
    'Origin': 'http://localhost:8080',
    'Referer': 'http://localhost:8080/',
    'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36',
    'contentType': 'application/json;charset=UTF-8',
  };

  static void doDownload(
    String url,
    String savePath, {
    CancelToken? cancelToken,
    OnProgress? progress,
    OnResult? result,
    OnFailure? failure,
  }) {
    _getAppVersion().then((version) {
      var options = Options(
        receiveTimeout: Duration(milliseconds: 150000),
        responseType: ResponseType.json,
        validateStatus: (status) {
          // 不使用http状态码判断状态，使用AdapterInterceptor来处理（适用于标准REST风格）
          return true;
        },
        headers: {
          'Accept': 'application/json,*/*',
          'Content-Type': 'application/json',
          'os': Platform.isAndroid ? 'ANDROID' : 'IOS',
          'version': version,
        },
      );

      dio.download(
        url,
        savePath,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: (int count, int total) {
          if (total != -1) {
            if (cancelToken?.isCancelled ?? false) {
              return;
            }
            double downloadRatio = (count / total);
            if (downloadRatio == 1) {
              result?.call();
            } else {
              progress?.call(count, total);
            }
          } else {
            MyCommonUtils.showToast("无法获取文件大小, 下载失败!");
            failure?.call();
          }
        },
      ).onError((error, stackTrace) {
        print('下载失败 onError ${error.toString()}');
        MyCommonUtils.showToast('下载失败, 请稍后重试!');
        failure?.call();
        throw Exception(error.toString());
      });
    });
  }
}

class ResponseStruct {
  num code;
  String? msg;
  dynamic data;

  ResponseStruct({
    required this.code,
    required this.msg,
    this.data,
  });

  factory ResponseStruct.fromJson(Map<String, dynamic> json) {
    return ResponseStruct(
      code: json['code'],
      msg: json['msg'],
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    data['data'] = data;

    return data;
  }
}
