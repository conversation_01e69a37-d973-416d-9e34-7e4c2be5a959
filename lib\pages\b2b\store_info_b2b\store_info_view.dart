import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flustars/flustars.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/common/apis.dart';
// import 'package:fuduoduo/pages/homePages/store_info/store_info_logic.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/widget/ImageLoad.dart';
import 'package:get/get.dart';

import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../resource/string_resource.dart';

import '../../../route/index.dart';
import '../../../utils/common_utils.dart';
import 'package:sp_util/sp_util.dart';
import 'store_info_logic.dart';

class B2bStoreInfoView extends StatefulWidget {
  const B2bStoreInfoView({super.key});

  @override
  State<B2bStoreInfoView> createState() => _StoreInfoViewState();
}

class _StoreInfoViewState extends State<B2bStoreInfoView> {
  final logic = Get.put(StoreInfoLogic());
  final state = Get.find<StoreInfoLogic>().state;

  DateTime? lastPopTime;

  // 创建一个 FocusNode
  FocusNode _focusNode = FocusNode();
  bool isFocus = false;
  bool _isShowAccount = false;
  String? _isB2b = SpUtil.getString('is_unified_b2b');

  // 校验相关状态
  Map<String, String?> _validationErrors = {}; // 存储每个字段的错误信息
  Map<String, FocusNode> _focusNodes = {}; // 存储每个字段的 FocusNode

  // 用于获取详细地址输入框的位置
  GlobalKey _addressFieldKey = GlobalKey();
  double _addressFieldHeight = 150.0; // 详细地址输入框的高度

  // 货到付款相关状态
  bool _isCashOnDelivery = false; // 是否货到付款
  bool _isB2bOnlinePayment = false; // 是否B2B在线收款

  @override
  void initState() {
    super.initState();
    // 监听焦点变化
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        setState(() {
          isFocus = false;
        });
      } else {
        setState(() {
          isFocus = false;
        });

      }
    });
    logic.requestPermission();
  }

  @override
  void dispose() {
    // 释放所有 FocusNode 资源
    _focusNode.dispose();
    _focusNodes.values.forEach((focusNode) {
      focusNode.dispose();
    });
    // 释放货到付款相关控制器
    logic.maxDebtController.dispose();
    super.dispose();
  }

  // 校验方法
  String? _validateField(String title, String value) {
    switch (title) {
      case '手机号':
        // 手机号不是必填，但如果有值就需要校验格式
        if (value.isNotEmpty && !RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
          return '请输入正确的手机号格式';
        }
        break;
      case '详细地址':
        if (value.isEmpty) {
          return '请输入详细地址';
        }
        if (value.length < 5) {
          return '地址信息过于简单，请输入详细地址';
        }
        break;

      case '账号密码':
        if (value.isEmpty) {
          return '请输入密码';
        }
        if (value.length < 8 || value.length > 16) {
          return '必须包含字母、数字和特殊字符，长度在8到16个字符之间';
        }
        // 检查是否包含至少一个字母
        if (!RegExp(r'[a-zA-Z]').hasMatch(value)) {
          return '必须包含字母、数字和特殊字符，长度在8到16个字符之间';
        }
        // 检查是否包含至少一个数字
        if (!RegExp(r'[0-9]').hasMatch(value)) {
          return '必须包含字母、数字和特殊字符，长度在8到16个字符之间';
        }
        // 检查是否包含至少一个特殊字符
        if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(value)) {
          return '必须包含字母、数字和特殊字符，长度在8到16个字符之间';
        }
        break;
      case '门店名称':
        if (value.isEmpty) {
          return '请输入门店名称';
        }
        if (value.length < 2) {
          return '门店名称至少需要2个字符';
        }
        if (value.length > 24) {
          return '门店名称不能超过24个字符';
        }
        break;
      case '联系人':
        if (value.isEmpty) {
          return '请输入联系人姓名';
        }
        if (value.length < 2) {
          return '联系人姓名至少需要2个字符';
        }
        if (value.length > 20) {
          return '联系人姓名不能超过20个字符';
        }
        break;
      case '最大可欠款金额':
        if (value.isEmpty) {
          return '请输入最大可欠款金额';
        }
        // 检查是否为有效数字
        double? amount = double.tryParse(value);
        if (amount == null) {
          return '请输入有效的数字';
        }
        if (amount < 0) {
          return '金额不能为负数';
        }
        if (amount > 999999999) {
          return '金额不能超过999,999,999';
        }
        break;
      default:
        if (value.isEmpty) {
          return '此字段不能为空';
        }
        break;
    }
    return null;
  }

  // 处理失焦事件
  void _handleFocusChange(String title, String value, bool isRequired) {

    String? error = _validateField(title, value);

    // print('$title  ttttt $error');

    // 当手机号正确且不为空时显示同步创建账号，为空时隐藏
    if (title == '手机号') {
      if (error == null && value.isNotEmpty) {
        state.isShowAccount = true;
        setState(() {
          _isShowAccount = true;
        });
      } else {
        state.isShowAccount = false;
        setState(() {
          _isShowAccount = false;
        });
      }
    }

    // 非必填字段的特殊处理：手机号有值时需要校验
    if (!isRequired && title != '手机号') return; // 除了手机号，其他非必填字段不校验

    // 手机号特殊处理：即使不是必填，有值时也要校验并显示错误
    if (title == '手机号' && !isRequired) {
      setState(() {
        _validationErrors[title] = error;
      });
      return;
    }

    setState(() {
      _validationErrors[title] = error;
    });
  }

  // 动态计算地址搜索框的位置
  double _getAddressDropdownTop() {
    // print('_addressFieldKey.currentContext ${_addressFieldKey.currentContext}');
    if (_addressFieldKey.currentContext != null) {
      final RenderBox renderBox = _addressFieldKey.currentContext!.findRenderObject() as RenderBox;
      final position = renderBox.localToGlobal(Offset.zero);
      final height = renderBox.size.height;
      return position.dy + height + 110; // 在详细地址输入框下方20像素
    }
    return 1158.h; // 默认值，如果无法获取位置
  }

  // 获取输入字段的最大长度限制
  int _getMaxLength(String title) {
    switch (title) {
      case '门店名称':
        return 20; // 门店名称最大20个字符
      case '联系人':
        return 20; // 联系人姓名最大20个字符
      case '手机号':
        return 11; // 手机号最大11位
      case '最大可欠款金额':
        return 10; // 金额字段最大10位
      case '详细地址':
        return 128; // 详细地址最大128个字符
      case '备注':
        return 128; // 备注最大128个字符
      default:
        return 80; // 其他字段默认80个字符
    }
  }

  // 创建单选框组件
  Widget _buildRadioOption(String title, bool value, Function(bool) onChanged, {bool isRequired = true}) {
    return Container(
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      margin: EdgeInsets.only(bottom: 16.h),
      height: 95.h,
      color: ColorResource.WHITE_COMMON_COLOR,
      child: Row(
        children: [
          Container(
            margin: EdgeInsets.only(right: 12.w),
            width: 210.w,
            child: Text.rich(TextSpan(children: [
              if (isRequired)
                TextSpan(
                  text: '* ',
                  style: TextStyle(
                    color: ColorResource.RED_COMMON_COLOR,
                    fontSize: 30.sp,
                  ),
                ),
              if (!isRequired)
                TextSpan(
                  text: '  ',
                  style: TextStyle(
                    color: ColorResource.RED_COMMON_COLOR,
                    fontSize: 30.sp,
                  ),
                ),
              TextSpan(
                text: title,
                style: TextStyle(
                  color: ColorResource.BLACK_COMMON_COLOR,
                  fontSize: 30.sp,
                ),
              ),
            ])),
          ),
          Expanded(
            child: Row(
              children: [
                InkWell(
                  onTap: () {
                    // 只有在非编辑模式或者编辑模式开启时才允许点击
                    if (logic.arguments == null || logic.isEditMode) {
                      onChanged(true);
                    }
                  },
                  borderRadius: BorderRadius.circular(8.w),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 36.w, // 增大图标尺寸
                          height: 36.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: value ? ColorResource.RED_COMMON_COLOR : Colors.grey,
                              width: 2,
                            ),
                            color: value ? ColorResource.RED_COMMON_COLOR : Colors.transparent,
                          ),
                          child: value
                              ? Icon(
                                  Icons.check,
                                  size: 14.w, // 相应增大check图标
                                  color: Colors.white,
                                )
                              : null,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          '是',
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: ColorResource.BLACK_COMMON_COLOR,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 40.w),
                InkWell(
                  onTap: () {
                    // 只有在非编辑模式或者编辑模式开启时才允许点击
                    if (logic.arguments == null || logic.isEditMode) {
                      onChanged(false);
                    }
                  },
                  borderRadius: BorderRadius.circular(8.w),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 36.w, // 增大图标尺寸
                          height: 36.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: !value ? ColorResource.RED_COMMON_COLOR : Colors.grey,
                              width: 2,
                            ),
                            color: !value ? ColorResource.RED_COMMON_COLOR : Colors.transparent,
                          ),
                          child: !value
                              ? Icon(
                                  Icons.check,
                                  size: 14.w, // 相应增大check图标
                                  color: Colors.white,
                                )
                              : null,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          '否',
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: ColorResource.BLACK_COMMON_COLOR,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreInfoLogic>(
      builder: (logic) {
        return Scaffold(
            backgroundColor: ColorResource.WHITE_COMMON_COLOR,
            appBar: AppBar(
                title: Text(
                  // 当有参数传入时，显示"客户详细信息"，否则显示原来的逻辑
                  logic.arguments != null ? "客户详细信息"
                      : logic.type == "edit" ? "门店信息"
                      : logic.type == "bind" ? "绑定"
                      : "拓店",
                  style: TextStyle(
                      color: ColorResource.WHITE_COMMON_COLOR, fontSize: 36.sp),
                ),
                elevation: 0,
                centerTitle: true,
                // 当有参数传入时，显示编辑按钮
                actions: logic.arguments != null ? [
                  TextButton(
                    onPressed: () {
                      logic.toggleEditMode();
                    },
                    child: Text(
                      logic.isEditMode ? "" : "编辑",
                      style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 32.sp,
                      ),
                    ),
                  ),
                ] : null,
                flexibleSpace: Container(
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                            colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                            begin: Alignment.centerLeft,
                        end: Alignment.centerRight))),
            leading: IconButton(
                onPressed: () {
                  if (logic.from == "customerList" && !state.fillFlag) {
                    Get.until((route) {
                      return route.settings.name?.contains(PageName.CustomerListPage) == true;
                    });
                  } else {
                    Get.back();
                  }
                },
                icon: Icon(Icons.arrow_back_ios_rounded,
                    color: ColorResource.WHITE_COMMON_COLOR))),
        body: _bodyBuilder(context));
      },
    );
  }

  _bodyBuilder(context) {
    return Column(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Expanded(child: Container(child: SingleChildScrollView(child: _contentVie()) )),
      // 只有在非编辑模式或者编辑模式开启时才显示底部按钮
      if (logic.arguments == null || logic.isEditMode)
        Align(alignment: Alignment.bottomCenter, child: _bottomView())
    ]);
    // return Stack(children: [
    //   _contentVie(),
    //   Align(alignment: Alignment.bottomCenter, child: _bottomView())
    // ]);
  }

  _contentVie() {


    return Stack(
      children: [
        Column(
          children: [
            SizedBox(height: 40.w),
            Obx(() => _headImgView()),
            SizedBox(height: 20.w),
            Text('请上传真实的门头照片',
                style: TextStyle(
                    fontSize: 24.sp, color: ColorResource.GRAY_COMMON_COLOR)),
            SizedBox(height: 40.w),
            _cellView('门店名称', '请输入门店名称', logic.infoEditCtr),
            _cellView('联系人', '请输入联系人', logic.nameEditCtr,),
            _cellView('手机号', '请输入手机号', logic.phoneEditCtr,isRequired: false),



            _isShowAccount && !logic.isHasInfo ? _b2bUser() : Offstage(
              offstage: true,  // 设置为 true，会将 widget 从布局树中移除
              child: Text(""),
            ),

            _isShowAccount && !logic.isHasInfo && state.isSyncB2b == 1 ?
            _cellView('账号密码', '请输入密码', logic.pwdEditCtr)
              : Offstage(
                offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                child: Text(""),
              ),

            // state.isShowAccount ?

            _cellView('所在地区', '请选择省、市、区、街道', Height: 108, Line: 2, logic.areaEditCtr, assets: 'arrow_right',
                onClick: () {
                  logic.buildHorizontalCascader(context);
                }
            ),
            _cellView('详细地址', '请输入门店详细地址', Height: 150, logic.addressEditCtr, key: _addressFieldKey),
            _cellView('渠道类型', '请选择渠道类型', logic.channelTypeEditCtr, assets: 'arrow_right',
                onClick: () {
                  logic.selectChannelType(context);
                }),
            _cellView('全国分组', '请选择分组', logic.groupEditCtr, assets: 'arrow_right',
                onClick: () {
                  logic.selectGroup(context);
                }),

            _cellView('城市区域', '请选择城市区域', logic.areaCityEditCtr, assets: 'arrow_right',
                onClick: () {
                  logic.selectArea(context);
                }),

            _cellView('价格组', '请选择价格组', logic.dictEditCtr, assets: 'arrow_right',
                onClick: () {
                  logic.selectDict(context);
                }),
                

            // _cellView('等级价格', '请选择等级价格', logic.priceLevelEditCtr, isRequired: state.level_price_required, assets: 'arrow_right',
            //     onClick: () {
            //       logic.selectpriceLevel(context);
            //     }),

            // _cellView('定位', '请选择当前地址', logic.pointEditCtr,
            //     isRequired: false, assets: 'positioning_red', onClick: () {
            //   logic.openMap(context);
            // }),


            // 货到付款单选框
            _buildRadioOption('货到付款', state.CashOnDelivery ?? _isCashOnDelivery, (value) {
              setState(() {
                _isCashOnDelivery = value;
                state.CashOnDelivery = value;
                // 如果选择"否"，清空最大可欠款金额和B2B在线收款状态
                if (!value) {
                  logic.maxDebtController.clear();
                  _isB2bOnlinePayment = false; // 重置B2B在线收款状态
                }
              });
            }),

            // 当选择"是"时显示最大可欠款金额输入框和B2B在线收款选项
            if (state.CashOnDelivery ?? _isCashOnDelivery) ...[
              _cellView('最大可欠款金额', '请输入最大可欠款金额', logic.maxDebtController, isRequired: false,),

              // B2B在线收款单选框
              _buildRadioOption('B2B在线收款', state.B2bOnlinePayment ?? _isB2bOnlinePayment, (value) {
                setState(() {
                  _isB2bOnlinePayment = value;
                  state.B2bOnlinePayment = value;
                });
              }, isRequired: false),
            ],
            _cellView('备注', '请输入备注信息', Height: 150, logic.desEditCtr, isRequired: false),
            // _b2bBox()
            SizedBox(height: 40.w),
          ],
        ),
        Visibility(
          visible: isFocus,
          child: Positioned(
            top: _getAddressDropdownTop(),
            left: 30.w,
            child: Container(
              height: 380.h,
              width: 660.w,
              decoration: BoxDecoration(
                color: Colors.white, // 背景色为白色
                borderRadius: BorderRadius.circular(8.0), // 圆角
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2), // 阴影颜色
                    blurRadius: 10.0, // 阴影模糊半径
                    offset: Offset(0, 4), // 阴影偏移
                  ),
                ],
              ),
              child: Center(child: _listView()),
            ),
          )
        )

      ],
    );
    // return Column(children:);
  }

  _b2bUser() {

    // print('_isB2b_isB2b_isB2b $_isB2b');
    return Container(
        padding: EdgeInsets.only(left: 50.w, right: 30.w),
        margin: EdgeInsets.only(bottom: 36.h, top: 10.h),
        child: Visibility(
          visible: _isShowAccount,
          child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                    '同步创建账号',
                    style: TextStyle(
                        color: ColorResource.BLACK_COMMON_COLOR,
                        fontSize: 30.sp)
                ),
                SizedBox(
                  width: 10.w,
                ),
                TDSwitch(
                    isOn: state.isAddUserAccount,
                    type: TDSwitchType.icon,
                    size: TDSwitchSize.small,
                    thumbContentOnColor: ColorResource.ORANGE_COMMON_COLOR,
                    trackOnColor: ColorResource.ORANGE_COMMON_COLOR,
                    enable: !state.isEdit,
                    onChanged: (bool value) {
                      // print('cccc $value');
                      state.isSyncB2b = value ? 1 : 0;
                      state.isAddUserAccount = value;
                      logic.updateView();
                      return false;
                    }
                ),
              ]
          ),
        )

    );
  }

  _b2bBox() {

    // print('_isB2b_isB2b_isB2b $_isB2b');
    return Container(
      padding: EdgeInsets.only(left: 50.w, right: 30.w),
      margin: EdgeInsets.only(bottom: 36.h, top: 10.h),
      child: Visibility(
        visible: _isB2b != '' && _isB2b == '1',
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  '是否同步B2b门店',
                  style: TextStyle(
                      color: ColorResource.BLACK_COMMON_COLOR,
                      fontSize: 30.sp)
              ),
              TDSwitch(
                  isOn: state.isSyncB2b == 1 ? true : false,
                  type: TDSwitchType.icon,
                  size: TDSwitchSize.small,
                  thumbContentOnColor: ColorResource.ORANGE_COMMON_COLOR,
                  trackOnColor: ColorResource.ORANGE_COMMON_COLOR,
                  enable: !state.isEdit,
                  onChanged: (bool value) {
                    state.isSyncB2b = value ? 1 : 0;
                    return false;
                  }
              ),
            ]
        ),
      )

    );
  }

  // 地图
  _listView() {
    return GetBuilder<StoreInfoLogic>(
        id: StringResource.B2B_STORE_INFO_ADDRESS_LIST,
        builder: (controller) {
          return ListView.separated(
              itemBuilder: (context, index) {
                return GestureDetector(
                    onTap: () {
                      logic.setMapInView(state.pois[index]);
                      setState(() {
                        isFocus = false;
                      });
                    },
                    child: _listCell(index));
              },
              separatorBuilder: (BuildContext context, int index) =>
              const Divider(height: 0.5, color: Colors.transparent),
              itemCount: state.pois.length);
        }
    );

  }

  _listCell(int index) {
    var poi = state.pois[index];
    return Container(
        color: Colors.transparent,
        padding: EdgeInsets.only(top: 20.h, left: 20.w, right: 20.w),
        child:
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 600.w,
                  child: Text(poi.title ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 14, color: ColorResource.ORANGE_COMMON_COLOR)),
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                        width: 600.w,
                        child: Text("${poi.address}",
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey)))
                  ]
                )
              ]),
          // Offstage(
          //     offstage: index != _index,
          //     child: const Icon(Icons.done, color: Color(0xffabb3be), size: 16))
        ]));
  }


  // 地图

  _headImgView() {
    return Container(
        decoration: BoxDecoration(
          boxShadow: [
            //阴影效果
            BoxShadow(
              offset: Offset(0, 0.w), //阴影在X轴和Y轴上的偏移
              color: Color.fromARGB(255, 220, 217, 217), //阴影颜色
              blurRadius: 3.0, //阴影程度
              spreadRadius: 0, //阴影扩散的程度 取值可以正数,也可以是负数
            ),
          ],
          borderRadius: BorderRadius.circular(12.w),
          color: Colors.white,
        ),
        height: 200.w,
        width: 200.w,
        child: Stack(children: [
          ImageLoad.loadWidget(logic.imgUrl.value, size: 200),
          Align(
              alignment: Alignment.bottomRight,
              child: GestureDetector(
                  onTap: () {
                    // print('logic.isEditMode ${logic.isEditMode}');
                    if (logic.arguments == null || logic.isEditMode) {
                      logic.openImagePicker();
                    }

                  },
                  child: Image.asset('assets/images/camera_red.png',
                      width: 68.w, height: 68.w)))
        ]));
  }

  _cellView(String title, String hintText,
      TextEditingController textEditingController,
      {String? assets, bool isRequired = true, double Height = 85, int Line = 1, Function? onClick, Key? key}) {

    // 获取或创建该字段的 FocusNode
    if (!_focusNodes.containsKey(title)) {
      _focusNodes[title] = FocusNode();
      _focusNodes[title]!.addListener(() {
        if (!_focusNodes[title]!.hasFocus) {
          // 失焦时进行校验
          _handleFocusChange(title, textEditingController.text, isRequired);
        }
      });
    }

    // 检查是否有校验错误
    bool hasError = _validationErrors[title] != null;



    return GestureDetector(
        onTap: () {
          // 只有在非编辑模式或者没有参数时才允许点击
          if (onClick != null && (logic.arguments == null || logic.isEditMode)) {
            onClick();
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                key: key, // 添加 key 支持
                padding: EdgeInsets.only(left: 30.w, right: 30.w),
                margin: EdgeInsets.only(bottom: hasError ? 8.h : 16.h),

                color: ColorResource.WHITE_COMMON_COLOR,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start ,
                  children: [
                    Container(
                        margin: EdgeInsets.only(bottom: 12.w),
                        // width: 150.w,
                        child: Text.rich(TextSpan(children: [
                          if (isRequired)
                            TextSpan(
                                text: '* ',
                                style: TextStyle(
                                    color: ColorResource.RED_COMMON_COLOR,
                                    fontSize: 30.sp)),

                          if (!isRequired)
                            TextSpan(
                                text: '   ',
                                style: TextStyle(
                                    color: ColorResource.RED_COMMON_COLOR,
                                    fontSize: 30.sp)),

                          TextSpan(
                              text: title,
                              style: TextStyle(
                                  color: ColorResource.BLACK_COMMON_COLOR,
                                  fontSize: 30.sp)),
                        ]))),
                    Container(
                      height: Height.h,
                      // child: Expanded(
                          child: TextField(
                            style: TextStyle(
                                color: ColorResource.BLACK_COMMON_COLOR,
                                fontSize: 28.sp),
                            maxLines: Height != 85 ? (Line > 1 ? Line * 2 : 6) : 2,
                            minLines: Height != 85 ? (Line > 1 ? Line * 2 : 6) : 1,
                            keyboardType: title == '最大可欠款金额'
                                ? TextInputType.numberWithOptions(decimal: true)
                                : title == '手机号'
                                ? TextInputType.number
                                : TextInputType.multiline,
                            controller: textEditingController,
                            onChanged: (value) {
                              // 清除当前字段的错误信息
                              if (_validationErrors[title] != null) {
                                setState(() {
                                  _validationErrors[title] = null;
                                });
                              }
                              // print('_validationErrors[title]  $title ${_validationErrors[title]} ==  $value');

                              if (title == '详细地址') {
                                if (value.isEmpty) {
                                  setState(() {
                                    isFocus = false;
                                  });
                                } else {
                                  logic.getMapList(value);
                                  setState(() {
                                    isFocus = true;
                                  });
                                }
                              }
                            },
                            focusNode: title == '详细地址' ? _focusNode : _focusNodes[title], // 绑定对应的 FocusNode
                            enabled: assets == null && (logic.arguments == null || logic.isEditMode),
                            decoration: InputDecoration(
                                filled: true,  // 启用背景填充
                                fillColor: ColorResource.GRAY_F7F8FA_COLOR,
                                contentPadding: const EdgeInsets.only(left: 15, bottom: 10, top: 5, right: 15),
                                border: hasError ? OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.red, width: 1),
                                  borderRadius: BorderRadius.circular(4),
                                ) : InputBorder.none,
                                enabledBorder: hasError ? OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.red, width: 1),
                                  borderRadius: BorderRadius.circular(4),
                                ) : InputBorder.none,
                                focusedBorder: hasError ? OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.red, width: 2),
                                  borderRadius: BorderRadius.circular(4),
                                ) : InputBorder.none,
                                hintText: hintText,
                                hintStyle: TextStyle(
                                    color: ColorResource.GRAY_COMMON_COLOR,
                                    fontSize: 28.sp)),
                            inputFormatters: <TextInputFormatter>[
                              LengthLimitingTextInputFormatter(_getMaxLength(title)),//限制长度
                              if (title == '最大可欠款金额')
                                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')), // 只允许数字和小数点
                              if (title == '手机号')
                                FilteringTextInputFormatter.digitsOnly, // 只允许数字
                              if (title == '账号密码')
                                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?`~]')), // 只允许ASCII字符，禁止中文
                            ],
                          )
                      // ),
                    ),
                  ],
                ),
                // Row(
                //   crossAxisAlignment: CrossAxisAlignment.center,
                //   children: [
                //
                // ]),
                Positioned(
                  top: 30,
                  bottom: 0,
                  right: 6,
                  child: Align(
                    alignment: Alignment.center,
                    child: assets != null
                        ? Image.asset('assets/images/$assets.png',
                        width: 32.w, height: 32.w)
                        : SizedBox(width: 32.w),
                  )// Distance from the left
                ),
                Visibility(
                  visible: title == '详细地址', // Set to true to show the widget, false to hide it
                  child: Positioned(
                      top: 0,
                      bottom: 6,
                      right: 6,
                      child: Align(
                        alignment: Alignment.bottomRight,
                        child: InkWell(
                          onTap: () {
                            logic.openMap(context);
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset('assets/images/positioning_red.png', width: 24.w, height: 24.w),
                              Text(' 定位地址', style: TextStyle(fontSize: 12, color: ColorResource.ORANGE_COMMON_COLOR),)
                            ],
                          )
                        )
                      )// Distance from the left
                  ),
                ),


              ],
            ),
            ),

            if (!hasError && title == '账号密码')
              // 密码规则提示
              Container(
                padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 8.h),
                child: Text(
                  '必须包含字母、数字和特殊字符，长度在8到16个字符之间',
                  style: TextStyle(
                    color: hasError ? Colors.red : Colors.grey,
                    fontSize: 24.sp,
                  ),
                ),
              ),
            // 错误提示显示
            if (hasError)
              Container(
                padding: EdgeInsets.only( right: 30.w, bottom: 8.h),
                child: Row(
                  children: [
                    SizedBox(
                      width: 30.w,
                    ),
                    Text(
                      _validationErrors[title]!,
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 24.sp,
                      ),
                    )
                  ],
                ) ,
              ),
          ],
        ));
  }

  _bottomView() {
    return GestureDetector(
        onTap: () {
          // 防止重复点击
          if (lastPopTime == null || DateTime.now().difference(lastPopTime!) > Duration(seconds: 2)) {
            lastPopTime = DateTime.now();
            logic.postSaveConsumer();
          } else {
            lastPopTime = DateTime.now();
            MyCommonUtils.showToast("请勿重复点击！");
          }
        },
        child: Container(
            width: 1.sw,
            color: ColorResource.WHITE_COMMON_COLOR,
            padding: EdgeInsets.fromLTRB(
                32.w, 10.h, 32.w, 10.h + ScreenUtil().bottomBarHeight),
            child: Container(
                alignment: Alignment.center,
                height: 80.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100.r),
                    gradient: LinearGradient(
                        colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight)),
                child: Text(
                    // logic.isEdit ? '修改' : '提交',
                    logic.type == "edit" ? "保存"
                        : logic.type == "bind" ? "绑定"
                        : "提交",
                    style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w600)))));
  }

}


