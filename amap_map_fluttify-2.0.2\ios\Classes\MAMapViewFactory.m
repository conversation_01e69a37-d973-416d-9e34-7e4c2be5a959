//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "MAMapViewFactory.h"
#import "AmapMapFluttifyPlugin.h"
#import <objc/runtime.h>
#import "FluttifyMessageCodec.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation MAMapViewFactory {
}

- (instancetype)initWithRegistrar:(NSObject <FlutterPluginRegistrar> *)registrar {
  self = [super init];
  if (self) {
    _registrar = registrar;
  }

  return self;
}

- (NSObject<FlutterMessageCodec>*)createArgsCodec {
  return [FlutterStandardMessageCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]];
}

- (NSObject <FlutterPlatformView> *)createWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id _Nullable)args {
  return [[MAMapViewPlatformView alloc] initWithViewId:viewId frame: frame registrar:_registrar arguments: args];
}

@end

@implementation MAMapViewPlatformView {
  int64_t _viewId;
  CGRect _frame;
  NSDictionary<NSString *, Handler>* _handlerMap;
  MAMapView* _view;
  id _args;
}

- (instancetype)initWithViewId:(int64_t)viewId frame:(CGRect)frame registrar:(NSObject <FlutterPluginRegistrar> *)registrar arguments:(id _Nullable)args {
  self = [super init];
  if (self) {
    _viewId = viewId;
    _registrar = registrar;
    _frame = frame;
    _args = args;
  }

  return self;
}

- (UIView *)view {
  __weak __typeof(self)weakSelf = self;
  if (_view == nil) {
    NSDictionary<NSString*, id>* params = (NSDictionary<NSString*, id>*) _args;

    _view = [[MAMapView alloc] initWithFrame:_frame];

    ////////////////////////////////初始化UiKitView////////////////////////////////////////
    NSNumber* mapType = (NSNumber*) params[@"mapType"];
    NSNumber* showZoomControl = (NSNumber*) params[@"showZoomControl"];
    NSNumber* showCompass = (NSNumber*) params[@"showCompass"];
    NSNumber* showScaleControl = (NSNumber*) params[@"showScaleControl"];
    NSNumber* zoomGesturesEnabled = (NSNumber*) params[@"zoomGesturesEnabled"];
    NSNumber* scrollGesturesEnabled = (NSNumber*) params[@"scrollGesturesEnabled"];
    NSNumber* rotateGestureEnabled = (NSNumber*) params[@"rotateGestureEnabled"];
    NSNumber* tiltGestureEnabled = (NSNumber*) params[@"tiltGestureEnabled"];
    NSNumber* zoomLevel = (NSNumber*) params[@"zoomLevel"];
    NSNumber* tilt = (NSNumber*) params[@"tilt"];
    NSNumber* bearing = (NSNumber*) params[@"bearing"];
    NSNumber* centerCoordinateLatitude = (NSNumber*) params[@"centerCoordinateLatitude"];
    NSNumber* centerCoordinateLongitude = (NSNumber*) params[@"centerCoordinateLongitude"];

    if (mapType != nil && (NSNull*)
        mapType != [NSNull null]) _view.mapType = [mapType intValue];
    if (showCompass != nil && (NSNull*) showCompass != [NSNull null])
        _view.showsCompass = [showCompass boolValue];
    if (showScaleControl != nil && (NSNull*) showScaleControl != [NSNull null])
        _view.showsScale = [showScaleControl boolValue];
    if (zoomGesturesEnabled != nil && (NSNull*) zoomGesturesEnabled != [NSNull null])
        _view.zoomEnabled = [zoomGesturesEnabled boolValue];
    if (scrollGesturesEnabled != nil && (NSNull*) scrollGesturesEnabled != [NSNull null])
        _view.scrollEnabled = [scrollGesturesEnabled boolValue];
    if (rotateGestureEnabled != nil && (NSNull*) rotateGestureEnabled != [NSNull null])
        _view.rotateEnabled = [rotateGestureEnabled boolValue];
    if (tiltGestureEnabled != nil && (NSNull*) tiltGestureEnabled != [NSNull null])
        _view.rotateCameraEnabled = [tiltGestureEnabled boolValue];
    if (zoomLevel != nil && (NSNull*) zoomLevel != [NSNull null])
        _view.zoomLevel = [zoomLevel doubleValue];
    if (tilt != nil && (NSNull*) tilt != [NSNull null])
        _view.cameraDegree = [tilt doubleValue];
    if (bearing != nil && (NSNull*) bearing != [NSNull null])
        _view.rotationDegree = [bearing doubleValue];
    if ((centerCoordinateLatitude != nil && (NSNull*) centerCoordinateLatitude != [NSNull null])
        && (centerCoordinateLongitude != nil && (NSNull*) centerCoordinateLongitude != [NSNull null]))
        _view.centerCoordinate = CLLocationCoordinate2DMake([centerCoordinateLatitude doubleValue], [centerCoordinateLongitude doubleValue]);
    ////////////////////////////////初始化UiKitView////////////////////////////////////////

    // 这里用一个magic number调整一下id
    // 同时存放viewId和refId的对象, 供后续viewId转refId使用
    HEAP[[NSString stringWithFormat:@"%@", @(2147483647 - _viewId)]] = _view;
    HEAP[[NSString stringWithFormat:@"%@:%@", @"MAMapView", @(_view.hash)]] = _view;
  }
  return _view;
}

@end
