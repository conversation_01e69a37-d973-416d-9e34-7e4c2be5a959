import 'package:collection/collection.dart';

import 'supplier_group_list.dart';

class ChartPageData {
  int? total;
  List<SupplierGroupList>? supplierGroupList;
  String? payPlatform;

  ChartPageData({this.total, this.supplierGroupList, this.payPlatform});

  factory ChartPageData.fromJson(Map<String, dynamic> json) => ChartPageData(
        total: json['total'] as int?,
        supplierGroupList: (json['supplierGroupList'] as List<dynamic>?)
            ?.map((e) => SupplierGroupList.fromJson(e as Map<String, dynamic>))
            .toList(),
        payPlatform: json['payPlatform'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'total': total,
        'supplierGroupList': supplierGroupList?.map((e) => e.toJson()).toList(),
        'payPlatform': payPlatform,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! ChartPageData) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      total.hashCode ^ supplierGroupList.hashCode ^ payPlatform.hashCode;
}
