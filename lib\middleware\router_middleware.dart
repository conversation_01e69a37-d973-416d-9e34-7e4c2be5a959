import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:network_logger/network_logger.dart';
import 'package:flutter/foundation.dart';
import '../widget/network_status_widget.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  GetPage<dynamic>? onPageCalled(GetPage<dynamic>? page) {
    // 如果需要添加 FloatingActionButton，可以修改 page
    return GetPage(
      name: page?.name ?? '/',
      page: () {
        return ScaffoldWithFAB(
          child: NetworkStatusWidget(
            child: page?.page() ?? Container(),
          ),
        );
      },
    );

    // 如果不需要 继续跳转
    // return super.onPageCalled(page);
  }
}

class ScaffoldWithFAB extends StatelessWidget {
  final Widget child;

  ScaffoldWithFAB({required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      floatingActionButton:  kDebugMode ? Padding(
        padding: EdgeInsets.only(bottom: 80, right: 30),
        child: FloatingActionButton(
          onPressed: () {
            NetworkLoggerScreen.open(context);
          },
          child: Icon(Icons.cloud),
          backgroundColor: Colors.blue,
        ),
      ) : null,
    );
  }
}