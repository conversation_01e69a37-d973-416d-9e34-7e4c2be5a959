/*
 * @Author: 潘腾龙
 * @Date: 2023-09-28 21:45:14
 * @LastEditTime: 2024-03-08 17:17:12
 */
import 'dart:ui';

import 'package:flutter/material.dart';

import '../utils/color_utils.dart';

class ColorResource {
  //浅灰-页面背景色
  static var LIGHT_GRAY_PAGE_BACKGROUND_COLOR = ColorUtil.fromHex("#F6F6F6");
  //浅灰-图层背景色
  static var LIGHT_GRAY_LAYER_BACKGROUND_COLOR = ColorUtil.fromHex("#F4F1F1");
  //75%黑色透明度
  static var LIGHT_3F_BACKGROUND_COLOR = ColorUtil.fromHex("#7F000000");
  //浅灰-分割线颜色
  static var LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR = ColorUtil.fromHex("#EBEDF0");

  //浅蓝-二维码页面背景色
  static var PAY_QRCODE_PAGE_BACKGROUND_COLOR = ColorUtil.fromHex("#2396FF");

  //橙色-文字
  static var ORANGE_TITLE_COLOR = ColorUtil.fromHex("#FEA636");
  //红色-文字
  static var RED_TITLE_COLOR = ColorUtil.fromHex("#FF211A");
  //浅红色-文字
  static var RED_EE0A24_COLOR = ColorUtil.fromHex("#EE0A24");
  //深红色-文字
  static var RED_DARK_COLOR = ColorUtil.fromHex("#00AF1E");
  //绿色-文字
  static var GREEN_TITLE_COLOR = ColorUtil.fromHex("#00AF1E");
  //黑色-文字（加粗）
  static var BLACK_BOLD_TITLE_COLOR = ColorUtil.fromHex("#333333");
  //黑色-文字（不加粗）
  static var BLACK_NORMAL_TITLE_COLOR = ColorUtil.fromHex("#323233");
  //灰色-文字
  static var GRAY_TITLE_COLOR = ColorUtil.fromHex("#969799");
  static var GRAY_EDIT_COLOR = ColorUtil.fromHex("#999999");
  static var GRAY_F7F8FA_COLOR = ColorUtil.fromHex("#F7F8FA");
  static var COLOR_F2F3F5_COLOR = ColorUtil.fromHex("#F2F3F5");

  //橙色-通用
  static var ORANGE_COMMON_COLOR = ColorUtil.fromHex("#FF751A");
  //红色-通用
  static var RED_COMMON_COLOR = ColorUtil.fromHex("#FF211A");
  //橙红色-通用（有渐变）
  static List<Color> LINEAR_GRADIENT_COMMON_COLOR = [
    ORANGE_COMMON_COLOR,
    RED_COMMON_COLOR
  ];
  //蓝色-通用（有渐变）
  static List<Color> BLUE_GRADIENT_COMMON_COLOR = [
    Colors.blue,
    Color.fromARGB(255, 2, 64, 114),
  ];
  //蓝色-通用（有渐变）
  static List<Color> YELLOW_GRADIENT_COMMON_COLOR = [
    Colors.yellow,
    Colors.orange,
  ];
  //云商红色-通用（有渐变）
  static List<Color> RED_GRADIENT_COMMON_COLOR = [
    ColorUtil.fromHex('#FF958F'),
    ColorUtil.fromHex('#FF4C41'),
    ColorUtil.fromHex('#FF211A'),
  ];
  //云商红色-通用（有渐变）
  static List<Color> RED_HOT_GRADIENT_COMMON_COLOR = [
    ColorUtil.fromHex('#FFA451'),
    ColorUtil.fromHex('#FF0000'),
    ColorUtil.fromHex('#FF0000'),
  ];
  //按钮外边框
  static var COLOR_DCDEE0_COLOR = ColorUtil.fromHex("#DCDEE0");
  //橙红色-通用（无渐变）
  static var ORANGE_RED_COMMON_COLOR = ColorUtil.fromHex("#FF532F");
  //黑色-通用
  static var BLACK_COMMON_COLOR = ColorUtil.fromHex("#323233");
  //白色-通用
  static var WHITE_COMMON_COLOR = ColorUtil.fromHex("#FFFFFF");
  //灰色-通用
  static var GRAY_COMMON_COLOR = ColorUtil.fromHex("#969799");
  // 浅灰
  static var GRAY_LOW_COMMON_COLOR = ColorUtil.fromHex("#F4F5F8");
  //客户列表导航前分割线
  static var COLOR_E7E7E7_COLOR = ColorUtil.fromHex("#E7E7E7");
  //车销盘点-今日有动销色值
  static var COLOR_3EBC6A_COLOR = ColorUtil.fromHex("#3EBC6A");

  // 你可以添加一个方法来修改 基础颜色
  void setB2BColor() {
    LINEAR_GRADIENT_COMMON_COLOR = [
      ColorUtil.fromHex("#40A9FF"),
      ColorUtil.fromHex("#1976D2")
    ];
    RED_COMMON_COLOR = ColorUtil.fromHex("#1976D2");
  }

  // 你可以添加一个方法来修改 基础颜色
  void setErpColor() {
    RED_COMMON_COLOR = ColorUtil.fromHex("#FF532F");
    LINEAR_GRADIENT_COMMON_COLOR = [
      ORANGE_COMMON_COLOR,
      RED_COMMON_COLOR
    ];

  }
}
