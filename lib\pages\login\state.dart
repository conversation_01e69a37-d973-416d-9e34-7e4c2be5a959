/*
 * @Author: 潘腾龙
 * @Date: 2023-11-30 14:47:47
 * @LastEditTime: 2023-12-06 14:30:39
 */
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginState {

  // 1 账号密码登录 2 手机验证码登录
  RxInt loginType = 1.obs;

  RxBool validateFlag = false.obs;

  // 账号输入
  TextEditingController accountTextController = TextEditingController();
  // 密码输入
  TextEditingController passwordTextController = TextEditingController();

  // 当前展开状态
  RxBool arowDown = false.obs;
  // 用户名（密码登录）
  String usernamePass = '';
  // 密码
  String password = '';
  bool isObscure = true;
  // 用户编码
  String saasUserCode = '';
  // 手机号
  String phoneNumber = '';
  // 验证码
  String msgCode = '';
  bool shouldBack = false;
  // 经销商列表
  List dealerInfoList = [];
  // 经销商编号
  int dealerNo = 0;
  // 经销商名称
  String dealerName = '';
  // 策略ID
  int strategyId = 0;
  // 用户列表
  List dealerUserList = [];
  // 用户名（验证码登录）
  String userNameCode = "";

  List AppLists = [];
  var erpSystemItem = null;
  var b2bSystemItem = null;

  // 获取验证码文字
  RxString msgCodeButtonInfo = '获取验证码'.obs;
  // 倒计时时间
  int msgCodeTime = 120;
  // 获取验证码按钮是否可点击
  bool canTap = true;

  LoginState() {
    ///Initialize variables
  }
}
