group 'me.yohom.amap_map_fluttify'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        mavenCentral()
        
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 31

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        main.jniLibs.srcDir 'libs'
    }
    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
    packagingOptions {
        merge 'res/values/values.xml'
        merge 'AndroidManifest.xml'
        merge 'R.txt'
        merge 'classes.jar'
        merge 'proguard.txt'
    }
    buildTypes {
        release {
            consumerProguardFiles "proguard-rules.pro"
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    compileOnly rootProject.findProject(":foundation_fluttify")
    // flutter plugin dependency
    
    // sdk dependency
    api 'com.amap.api:3dmap:9.2.1'
}
