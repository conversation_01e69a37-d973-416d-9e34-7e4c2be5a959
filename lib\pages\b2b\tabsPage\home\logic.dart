import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/date_Utils.dart';
import 'package:get/get.dart';

import 'package:fuduoduo/base/base_logic.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'state.dart';
import 'package:fuduoduo/utils/storage_common.dart';
class HomePageLogic extends BaseLogic {
  // 数据
  final state = HomePageState();
  final publicState = Get.find<Public>().state;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);
  RefreshController myInforefreshController =
      RefreshController(initialRefresh: false);

  @override
  void onInit() {
    ///初始化应用信息
    super.onInit();
    debugPrint("objepublicState.userInfoct--${publicState.userInfo}");
    // if (publicState.userInfo['employee'] != null && publicState.userInfo['employee']['employeeNo'] != null) {
    //   state.employeeNo = publicState.userInfo['employee']['employeeNo'];
    // }
    // getHomePageTotalData(state.employeeNo, "", "", "");
    getHomePageTotalData(state.employeeNo, "", "", "");
    getTeamData(true);
  }

  /* 首页汇总数据
  * employeeNo 业务员编号
  * startTime 开始时间(年月日)
  * endTime 结束时间(年月日)
  * sheetSource 订单来源
  */
  void getHomePageTotalData(
      String employeeNo, String startTime, String endTime, String sheetSource) {
    employeeNo = "";
    startTime = calendarManager(int.parse(state.dateType ?? "0"));
    endTime = calendarManager(0);
    sheetSource = "";
    var token = SpUtil.getString('access_token');
    if (token == null) return;
    MyDio.get(Apis.getHomePageTotalData, queryParameters: {
      "employeeNo": employeeNo,
      "startTime": startTime,
      "endTime": endTime,
      "sheetSource": sheetSource
    }, successCallBack: (value) {
      debugPrint("cjn--->>>> 首页数据 ${value}");
      debugPrint("cjn--->>>> 首页数据 ${json.encode(value)}");
      myInforefreshController.refreshCompleted();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic data = response.data ?? {};
        state.personalDataInfo = data;
        if (state.dateType == "0") {
          state.todayOrderMount = (data["ywDataCount"] ?? 0.00) +
              (data["dzDataCount"] ?? 0.00) +
              (data["htDataCount"] ?? 0.00) -
              (data["thDataCount"] ?? 0.00);
        } // MyCommonUtils.showToast("数据请求成功，刷新页面");
        update();
      } else if (response.code == "401") {
        MyCommonUtils.showToast('登录失效，请重新登录');
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  //团队业绩
  getTeamData(refresh) {
    if (refresh) {
      state.pageNo = 1;
    } else {
      state.pageNo += 1;
    }
    String startTime = calendarManager(int.parse(state.dateType ?? "0"));
    String endTime = calendarManager(0);
    String sheetSource = "";
    String searchSource = "";
    var token = SpUtil.getString('access_token');
    if (token == null) return;
    MyDio.get(Apis.getHomePageTotalTeamData, queryParameters: {
      "employeeNo": "",
      "startTime": startTime,
      "endTime": endTime,
      "sheetSource": sheetSource,
      "searchSource": searchSource,
      "pageNo": state.pageNo,
      "pageSize": state.pageSize
    }, successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == "200") {
        state.teamTotal = response.data?['total'] ?? 0;
        dynamic data = response.data?["items"] ?? [];
        if (refresh) {
          state.teamDataInfo = data;
          refreshController.refreshCompleted(resetFooterState: true);
          if (state.teamDataInfo.length >= state.teamTotal) {
            refreshController.loadNoData();
          }
        } else {
          state.teamDataInfo.addall(data);
          refreshController.loadComplete();
          if (state.teamDataInfo.length >= state.teamTotal) {
            refreshController.loadNoData();
          }
        }
        update();
      } else if (response.code == "401") {
        MyCommonUtils.showToast('登录失效，请重新登录');
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.msg!);
        if (refresh) {
          refreshController.refreshCompleted(resetFooterState: true);
        } else {
          state.pageNo -= 1;
          refreshController.loadFailed();
        }
      }
    });
  }

  //查询用户信息
  getLocalUserInfo() async {
    var userType = await SecureStorageCommon.save('userType').get();
    Map<String, dynamic>? queryParameters = {};
    print('xxxx=== 4 $userType');

    if (userType == 'b2bSystem') {
      // queryParameters["system"] = 'b2bmart';
      return;
    }
    MyDio.get(Apis.getAppUserInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        dynamic data = value["data"] ?? {};
        SpUtil.putObject('userInfo', data);
      }
      // print("userInfo=>${SpUtil.getObject('userInfo')}");
      update();
    }, showErrMsg: false);
  }

  //查询用户是否启用
  getUserStatusInfo() {
    MyDio.get(Apis.getUserStatusInfo, queryParameters: {},
        successCallBack: (value) {
      if (value["code"] == 200) {
        // 0 停用 1 启用
        if (value["data"] == 1) {
          SpUtil.putBool('StartFlag', true);
        } else {
          SpUtil.putBool('StartFlag', false);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("StartFlag=>${SpUtil.getBool('StartFlag')}");
      update();
    }, showErrMsg: false);
  }

  // 查询云商数据同步开关是否开启 0关闭 1开启
  getCloudFlagInfo() {
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getCloudFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if (value["data"] == 1) {
          SpUtil.putBool("CloudFlag", true);
        } else {
          SpUtil.putBool("CloudFlag", false);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("CloudFlag=>${SpUtil.getBool('CloudFlag')}");
      update();
    }, showErrMsg: false);
  }
}
