import 'package:collection/collection.dart';

import 'b2b_app_customer_list.dart';

class B2bAppCustomerData {
  List<B2bAppCustomerList>? list;
  int? total;
  dynamic rest;

  B2bAppCustomerData({this.list, this.total, this.rest});

  factory B2bAppCustomerData.fromJson(Map<String, dynamic> json) {
    return B2bAppCustomerData(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => B2bAppCustomerList.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int?,
      rest: json['rest'] as dynamic,
    );
  }

  Map<String, dynamic> toJson() => {
        'list': list?.map((e) => e.toJson()).toList(),
        'total': total,
        'rest': rest,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! B2bAppCustomerData) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => list.hashCode ^ total.hashCode ^ rest.hashCode;
}
