import 'package:collection/collection.dart';

class BranchCouponList {
  String? couponId;
  String? sysCode;
  String? couponTemplateId;
  String? couponName;
  num? funcScope;
  num? state;
  String? expirationDateStart;
  String? expirationDateEnd;
  dynamic useTime;
  dynamic relateOrderNo;
  num? spuScope;
  num? discountType;
  num? triggerAmt;
  num? discountAmt;
  dynamic discountPercent;
  dynamic discountLimitAmt;
  num? receiveType;
  num? costFlag;
  num? excludable;
  dynamic spuScopeAs;
  num? supplierId;
  dynamic couponBatchId;
  String? createTime;
  num? sameTypeExcludable;
  dynamic supplierIdList;

  BranchCouponList({
    this.couponId,
    this.sysCode,
    this.couponTemplateId,
    this.couponName,
    this.funcScope,
    this.state,
    this.expirationDateStart,
    this.expirationDateEnd,
    this.useTime,
    this.relateOrderNo,
    this.spuScope,
    this.discountType,
    this.triggerAmt,
    this.discountAmt,
    this.discountPercent,
    this.discountLimitAmt,
    this.receiveType,
    this.costFlag,
    this.excludable,
    this.spuScopeAs,
    this.supplierId,
    this.couponBatchId,
    this.createTime,
    this.sameTypeExcludable,
    this.supplierIdList,
  });

  factory BranchCouponList.fromJson(Map<String, dynamic> json) {
    return BranchCouponList(
      couponId: json['couponId'] as String?,
      sysCode: json['sysCode'] as String?,
      couponTemplateId: json['couponTemplateId'] as String?,
      couponName: json['couponName'] as String?,
      funcScope: json['funcScope'] as num?,
      state: json['state'] as num?,
      expirationDateStart: json['expirationDateStart'] as String?,
      expirationDateEnd: json['expirationDateEnd'] as String?,
      useTime: json['useTime'] as dynamic,
      relateOrderNo: json['relateOrderNo'] as dynamic,
      spuScope: json['spuScope'] as num?,
      discountType: json['discountType'] as num?,
      triggerAmt: json['triggerAmt'] as num?,
      discountAmt: json['discountAmt'] as num?,
      discountPercent: json['discountPercent'] as dynamic,
      discountLimitAmt: json['discountLimitAmt'] as dynamic,
      receiveType: json['receiveType'] as num?,
      costFlag: json['costFlag'] as num?,
      excludable: json['excludable'] as num?,
      spuScopeAs: json['spuScopeAs'] as dynamic,
      supplierId: json['supplierId'] as num?,
      couponBatchId: json['couponBatchId'] as dynamic,
      createTime: json['createTime'] as String?,
      sameTypeExcludable: json['sameTypeExcludable'] as num?,
      supplierIdList: json['supplierIdList'] as dynamic,
    );
  }

  Map<String, dynamic> toJson() => {
        'couponId': couponId,
        'sysCode': sysCode,
        'couponTemplateId': couponTemplateId,
        'couponName': couponName,
        'funcScope': funcScope,
        'state': state,
        'expirationDateStart': expirationDateStart,
        'expirationDateEnd': expirationDateEnd,
        'useTime': useTime,
        'relateOrderNo': relateOrderNo,
        'spuScope': spuScope,
        'discountType': discountType,
        'triggerAmt': triggerAmt,
        'discountAmt': discountAmt,
        'discountPercent': discountPercent,
        'discountLimitAmt': discountLimitAmt,
        'receiveType': receiveType,
        'costFlag': costFlag,
        'excludable': excludable,
        'spuScopeAs': spuScopeAs,
        'supplierId': supplierId,
        'couponBatchId': couponBatchId,
        'createTime': createTime,
        'sameTypeExcludable': sameTypeExcludable,
        'supplierIdList': supplierIdList,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! BranchCouponList) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      couponId.hashCode ^
      sysCode.hashCode ^
      couponTemplateId.hashCode ^
      couponName.hashCode ^
      funcScope.hashCode ^
      state.hashCode ^
      expirationDateStart.hashCode ^
      expirationDateEnd.hashCode ^
      useTime.hashCode ^
      relateOrderNo.hashCode ^
      spuScope.hashCode ^
      discountType.hashCode ^
      triggerAmt.hashCode ^
      discountAmt.hashCode ^
      discountPercent.hashCode ^
      discountLimitAmt.hashCode ^
      receiveType.hashCode ^
      costFlag.hashCode ^
      excludable.hashCode ^
      spuScopeAs.hashCode ^
      supplierId.hashCode ^
      couponBatchId.hashCode ^
      createTime.hashCode ^
      sameTypeExcludable.hashCode ^
      supplierIdList.hashCode;
}
