/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月12日09:29:49
 * 修改时间：2023年09月12日09:29:49
 */

import 'package:extended_image/extended_image.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/resource/string_resource.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:fuduoduo/widget/MyDialog.dart';
import 'package:get/get.dart';
import 'dart:io';

import '../../base/base_stateful.dart';
import '../../resource/color_resource.dart';
import '../../resource/image_resource.dart';
import '../../route/index.dart';
import '../../utils/common_utils.dart';
import 'package:fuduoduo/utils/upgrade_utils.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:fuduoduo/utils/storage.dart';
import 'package:fuduoduo/utils/storage_common.dart';

import 'logic.dart';

import 'dart:convert';
import 'package:fuduoduo/domain/common_other_LoginInfo.dart';
class MyPage extends StatefulWidget {
  MyPage({Key? key}) : super(key: key);

  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends BaseStatefulWidget<MyPage> {
  final logic = Get.put(MyLogic());
  final state = Get.find<MyLogic>().state;
  final publicState = Get.find<Public>().state;
  final publicLogic = Get.find<Public>();

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    print(publicState.userInfo);

    logic.getValidateFlagInfo(false);
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      key: _scaffoldKey,
      appBar: _appBar(context),
      drawer: Container(
        width: screenSize.width * 0.8,
        child: Drawer(
          backgroundColor: Colors.white,
          child: _drawer(context),
        ),
      ),
      body: GetBuilder<MyLogic>(
        id: StringResource.PAGE_ME_ID,
        builder: (_) => _bodyBuilder(context)
      ),
    );
  }
  _drawer(BuildContext context) {

    return GetBuilder<MyLogic>(
        id: StringResource.MY_HEADER_LITE_INFO,
        builder: (controller) {

          var currentTenant = SpUtil.getObject('currentTenant') ?? {};
          var tenants = SpUtil.getObject('tenants');
          List tententList = tenants?['tenants'].toList();

          return Container(
            child: Column(
              children: [
                UserAccountsDrawerHeader(
                  decoration: BoxDecoration(
                    color: ColorResource.WHITE_COMMON_COLOR,
                    gradient: LinearGradient(
                        colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                  ),
                  accountName: Text('当前租户: '),
                  accountEmail: Text(currentTenant['tenantName']),
                ),
                Expanded(
                    child: Container(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: tententList.length,
                        itemBuilder: (context, index) {
                          var item = tententList[index];
                          return _tententItem(context, index, item, currentTenant);
                        },
                      )
                ))
              ],
            ),
          );
        }
    );
  }

  _tententItem(BuildContext context, int index, Map List, Map currentTenant) {
    return InkWell(
        onTap: () {
          Navigator.pop(context); // 关闭侧边菜单
          Get.offAll(() => MyPage());
          logic.reLogin(context, List['tenantCode']);

          logic.update([
            StringResource.PAGE_ME_ID,
            StringResource.MY_HEADER_INFO,
            StringResource.MY_HEADER_LITE_INFO
          ]);
        },
        child: Container(
          color: currentTenant['tenantCode'] == List['tenantCode'] ? ColorResource.GRAY_LOW_COMMON_COLOR : Colors.white,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 100.w,
                height: 100.w,
                child: ExtendedImage.asset(ImageResource.ICON_FAN_CANG),
              ),
              Container(
                padding: EdgeInsets.only(top: 31.w),
                height: 100.w,
                child: Text(List['tenantName']),
              ),
              Container(
                padding: EdgeInsets.only(top: 0.w),
                child: Text(' | '),
              ),
              Container(
                padding: EdgeInsets.only(top: 33.w),
                height: 100.w,
                child: Text(List['tenantCode']),
              ),
            ],
          ),
        )

    );
  }

  _appBar(BuildContext context) {
    return AppBar(
      foregroundColor: Colors.white,
      title: Text(
        "我的",
        style:
            TextStyle(color: ColorResource.BLACK_COMMON_COLOR, fontSize: 36.sp),
      ),
      elevation: 0,
      centerTitle: true,
      backgroundColor: ColorResource.WHITE_COMMON_COLOR,
    );
  }

  _bodyBuilder(BuildContext context) {
    return Container(
      color: ColorResource.LIGHT_GRAY_PAGE_BACKGROUND_COLOR,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(left: 32.w, right: 32.w),
            color: ColorResource.WHITE_COMMON_COLOR,
            child: _userInfoBuilder(context),
          ),
          Visibility(
            visible:
                UserAppModuleUtils().moduleCanShow('logistics_distribution')
                    ? true
                    : UserAppModuleUtils().moduleCanShow('account_for'),
            child: Container(
              padding: EdgeInsets.only(left: 32.w, right: 32.w),
              color: ColorResource.WHITE_COMMON_COLOR,
              child: _handMoneyBuilder(context),
            ),
          ),
          // _pointAndRewardBuilder(context),
          _setUpBuilder(context),
        ],
      ),
    );
  }

  _userInfoBuilder(BuildContext context) {
    // 安全地获取用户信息
    dynamic userInfoMap = SpUtil.getObject('userInfo');
    dynamic employee = userInfoMap?['employee'] ?? {};
    dynamic enterprise = userInfoMap?['enterprise'] ?? {};

    ///====追加租户名并判断是否超长=====///
    String enterpriseStr = '';
    String accountCenter = '';
    String accountRight = '';
    if (null != enterprise && enterprise.containsKey('systemName')) {
      enterpriseStr = '(${enterprise['systemName']})';
      accountCenter = enterpriseStr.split('(')[1].replaceAll(')', '');
      accountRight = ')';
    }
    double width =
        MyCommonUtils.getTextSize(enterpriseStr, TextStyle(), context).width;
    double width1 = MyCommonUtils.getTextSize(
            "部门：${employee?['deptName'] ?? ''}",
            TextStyle(fontSize: 24.sp),
            context)
        .width;
    bool isLong = width > (1.sw - 120.w - 120.w - width1);

    var currentTenant = SpUtil.getObject('currentTenant') ?? {};

    return GetBuilder<MyLogic>(
      id: StringResource.MY_HEADER_INFO,
      builder: (controller) {
        return Container(
          margin: EdgeInsets.only(left: 0.w, right: 0.w, top: 0.w, bottom: 0.w),
          height: 200.w,
          color: ColorResource.WHITE_COMMON_COLOR,
          child: Row(
            children: [
              /* 暂时去掉头像，要不然报图片异常的log
          Container(
            margin: EdgeInsets.only(left: 20.w),
            width: 120.w,
            height: 120.w,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(60.r),
              child: FadeInImage.assetNetwork(
                placeholder: ImageResource.DEFAULT_AVATAR,
                width: 120.w,
                height: 120.w,
                fit: BoxFit.cover,
                image: state.userAvatar,
                imageErrorBuilder: (ctx, err, stackTrace) => Image.asset(
                  ImageResource.DEFAULT_AVATAR,
                  width: 120.w,
                  height: 120.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          */
              Container(
                margin: EdgeInsets.only(left: 20.w),
                width: 120.w,
                height: 120.w,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(60.r),
                  child: Image.asset(
                    ImageResource.DEFAULT_AVATAR,
                    width: 120.w,
                    height: 120.w,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Expanded(
                  child: Container(
                    margin: EdgeInsets.only(left: 20.w),
                    padding: EdgeInsets.only(top: 10.w, bottom: 10.w),
                    height: 160.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          child: Text(
                            "${employee?['employeeName'] ?? ""}",
                            style: TextStyle(
                                color: ColorResource.BLACK_COMMON_COLOR,
                                fontSize: 30.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        // Spacer(),
                        SizedBox(
                          height: 8.h,
                        ),
                        Container(
                          child: Row(
                            children: [
                              Text(
                                "部门：${employee?['deptName'] ?? ""}",
                                style: TextStyle(fontSize: 24.sp),
                              ),
                              isLong
                                  ? Expanded(
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "(${accountCenter}",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Text('${accountRight}')
                                  ],
                                ),
                              )
                                  : Text('$enterpriseStr'),

                            ],
                          ),
                        ),
                        Spacer(),
                        state.userType != 'b2bSystem' ? Container(
                          child: InkWell(
                            onTap: () {

                              _scaffoldKey.currentState?.openDrawer();
                            },
                            child: Row(
                              children: [
                                Text(currentTenant['tenantName'] ?? ''),
                                Container(
                                  width: 30.w,
                                  height: 30.w,
                                  child: ExtendedImage.asset(ImageResource.ICON_OTHER_PAY),
                                ),
                              ],
                            ),
                          ),
                        ) : Offstage(
                          offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                          child: Text(""),
                        )
                      ],
                    ),
                  )),
            ],
          ),
        );
      }
    );


  }

  _handMoneyBuilder(BuildContext context) {
    //临时处理
    var token = SpUtil.getString('access_token');
    bool isNotNull = token?.isNotEmpty ?? false;
    if (isNotNull) {
      logic.getData();
    }

    return Container(
      height: 110.w,
      decoration: BoxDecoration(
          color: ColorResource.ORANGE_RED_COMMON_COLOR,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r))),
      child: Row(
        children: [
          SizedBox(
            width: 40.w,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 12.w,
              ),
              Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 10.w),
                    child: Text(
                      "今日应交账",
                      style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 24.sp,
                      ),
                    ),
                  ),
                  Tooltip(
                      message: StringResource.STRING_ACCOUNT_PROMPT,
                      height: 0, // 不设置 child 时，Tooltip 的固有高度
                      padding: EdgeInsets.all(5), // ToolTip 内边距
                      margin: EdgeInsets.only(
                          left: 20.w, right: 20.w), // ToolTip 外边距
                      verticalOffset: 30.w, // 距离 child 中心点的竖直方向偏移量
                      preferBelow: false, // 设置为 false 时，会展示在 child 上方
                      excludeFromSemantics: false, // 是否使用语义标签
                      triggerMode: TooltipTriggerMode.tap,
                      waitDuration:
                          Duration(seconds: 0), // 指针悬停多久后展示 Tooltip ，默认为 0
                      showDuration: Duration(seconds: 10), // 展示时长，之后消失
                      // 字体样式
                      textStyle: TextStyle(
                          color: ColorResource.BLACK_NORMAL_TITLE_COLOR),
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: ColorResource.GRAY_COMMON_COLOR, width: 1),
                        color: ColorResource.WHITE_COMMON_COLOR,
                      ),
                      child: Icon(
                        Icons.help,
                        size: 30.w,
                        color: Colors.white,
                      ))
                ],
              ),
              Spacer(),
              GetBuilder<MyLogic>(
                builder: (_) => Container(
                  child: RichText(
                      text: TextSpan(children: [
                    TextSpan(
                      text: "￥",
                      style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "${state.EmployeeSheetAmtSum}",
                      style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 36.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ])),
                ),
              ),
              SizedBox(
                height: 6.w,
              )
            ],
          ),
          Spacer(),
          Visibility(
            visible:
                UserAppModuleUtils().moduleCanShow('logistics_distribution')
                    ? true
                    : UserAppModuleUtils().moduleCanShow('account_for'),
            child: InkWell(
              onTap: () {
                int pageSource =
                    UserAppModuleUtils().moduleCanShow('logistics_distribution')
                        ? 1
                        : 0;
                // 跳转去交账界面
                if (pageSource == 1) {
                  //配送员交账
                  Get.toNamed(PageName.DistributionPage);
                } else {
                  //普通交账
                  Get.toNamed(PageName.SubmitAccountPage, parameters: {
                    "allTotalMoney": state.EmployeeSheetAmtSum
                  })?.then((value) {
                    logic.getData();
                  });
                }
              },
              child: Container(
                width: 150.w,
                height: 60.w,
                decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.all(Radius.circular(30.w))),
                child: Center(
                  child: Text(
                    "去交账",
                    style: TextStyle(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 20.w)
        ],
      ),
    );
  }

  _pointAndRewardBuilder(BuildContext context) {
    print("userInfo=>${SpUtil.getObject('userInfo')}");
    dynamic userInfo = SpUtil.getObject('userInfo');
    dynamic employee = userInfo['employee'] ?? {};
    return GetBuilder<MyLogic>(
        id: "myPointAndReward",
        builder: (controller) {
          return Container(
            margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 30.w, right: 30.w, top: 30.h, bottom: 30.h),
                      decoration: BoxDecoration(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 40.w,
                                height: 40.h,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20.r),
                                  child: Image.asset(
                                    ImageResource.MY_CODE,
                                    width: 40.w,
                                    height: 40.h,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(width: 20.w),
                              Text("福分",
                                  style: TextStyle(
                                      color: ColorResource
                                          .BLACK_NORMAL_TITLE_COLOR,
                                      fontSize: 30.sp)),
                            ],
                          ),
                          SizedBox(height: 20.h),
                          Row(
                            children: [
                              Text("200",
                                  style: TextStyle(
                                      color:
                                          ColorResource.BLACK_BOLD_TITLE_COLOR,
                                      fontSize: 36.sp,
                                      fontWeight: FontWeight.w600)),
                              Spacer(),
                              Container(
                                alignment: Alignment.center,
                                width: 150.w,
                                height: 50.h,
                                decoration: BoxDecoration(
                                  color: ColorResource.WHITE_COMMON_COLOR,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(25.r)),
                                  border: Border.all(
                                      color:
                                          ColorResource.ORANGE_RED_COMMON_COLOR,
                                      width: 1),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    MyCommonUtils.showToast("跳转积分商城页面");
                                  },
                                  child: Text(
                                    "兑换好礼",
                                    style: TextStyle(
                                        color: ColorResource
                                            .ORANGE_RED_COMMON_COLOR,
                                        fontSize: 28.sp,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    )),
                SizedBox(width: 20.w),
                Expanded(
                    flex: 1,
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 30.w, right: 30.w, top: 30.h, bottom: 30.h),
                      decoration: BoxDecoration(
                        color: ColorResource.WHITE_COMMON_COLOR,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 40.w,
                                height: 40.h,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(20.r),
                                  child: Image.asset(
                                    ImageResource.MY_CODE,
                                    width: 40.w,
                                    height: 40.h,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(width: 20.w),
                              Text("现金奖励",
                                  style: TextStyle(
                                      color: ColorResource
                                          .BLACK_NORMAL_TITLE_COLOR,
                                      fontSize: 30.sp)),
                            ],
                          ),
                          SizedBox(height: 20.h),
                          Row(
                            children: [
                              Text("￥20.00",
                                  style: TextStyle(
                                      color:
                                          ColorResource.BLACK_BOLD_TITLE_COLOR,
                                      fontSize: 36.sp,
                                      fontWeight: FontWeight.w600)),
                              Spacer(),
                              Container(
                                alignment: Alignment.center,
                                width: 120.w,
                                height: 50.h,
                                decoration: BoxDecoration(
                                  color: ColorResource.WHITE_COMMON_COLOR,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(25.r)),
                                  border: Border.all(
                                      color:
                                          ColorResource.ORANGE_RED_COMMON_COLOR,
                                      width: 1),
                                ),
                                child: InkWell(
                                  onTap: () {
                                    MyCommonUtils.showToast("跳转提现页面");
                                  },
                                  child: Text(
                                    "去提现",
                                    style: TextStyle(
                                        color: ColorResource
                                            .ORANGE_RED_COMMON_COLOR,
                                        fontSize: 28.sp,
                                        fontWeight: FontWeight.w600),
                                  ),
                                ),
                              )
                            ],
                          )
                          // Row(
                          //   children: [
                          //     Text(
                          //         "敬请期待",
                          //         style: TextStyle(color: ColorResource.BLACK_BOLD_TITLE_COLOR, fontSize: 36.sp, fontWeight: FontWeight.w600)
                          //     ),
                          //   ],
                          // )
                        ],
                      ),
                    )),
              ],
            ),
          );
        });
  }

  _setUpBuilder(BuildContext context) {
    // print("userInfo=>${SpUtil.getObject('userInfo')}");
    dynamic userInfo = SpUtil.getObject('userInfo');
    dynamic employee = userInfo['employee'] ?? {};
    return GetBuilder<MyLogic>(
        id: "mySetUp",
        builder: (controller) {
          return Expanded(
              child: SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.only(
                  left: 20.w, right: 20.w, top: 20.w, bottom: 0.w),
              padding: EdgeInsets.only(left: 50.w, right: 50.w),
              decoration: BoxDecoration(
                  color: ColorResource.WHITE_COMMON_COLOR,
                  borderRadius: BorderRadius.all(Radius.circular(20.r))),
              child: Column(
                children: [
                  Visibility(
                      // 1、角色为车销业务员、访销业务员、配送员 2、该经销商开通同福云商
                      visible: (((SpUtil.getObject('userInfo'))?['employee']
                                      ["duty"] ==
                                  "4") ||
                              ((SpUtil.getObject('userInfo'))?['employee']
                                      ["duty"] ==
                                  "2")) &&
                          (SpUtil.getBool("CloudFlag") == true),
                      child: Container(
                        child: _setUpItemBuilder(
                            context, ImageResource.MY_MISSION, "任务中心",
                            rightIcon: ImageResource.ARROW_RIGHT, index: 7),
                      )),
                  Visibility(
                      // 1、当前用户角色为业务员 2、当前用户开通账号 3、该经销商开通同福云商
                      visible: (employee["duty"] == "4") &&
                          (SpUtil.getBool("StartFlag") == true) &&
                          (SpUtil.getBool("CloudFlag") == true),
                      child: Container(
                        child: _setUpItemBuilder(
                            context, ImageResource.MY_CODE, "云商专属邀请码",
                            rightIcon: ImageResource.ARROW_RIGHT, index: 6),
                      )),
                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_BLUETOOTH, "蓝牙打印设置",
                        rightIcon: ImageResource.ARROW_RIGHT, index: 0),
                  ),
                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_SERVICE, "客服电话",
                        rightIcon: ImageResource.ARROW_RIGHT, index: 1),
                  ),
                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_ACCOUNT, "退出登录",
                        rightIcon: ImageResource.ARROW_RIGHT, index: 2),
                  ),
                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_ACCOUNT, "切换账号",
                        rightIcon: ImageResource.ARROW_RIGHT, index: 3),
                  ),
                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_ACCOUNT, "更换手机号",
                        rightIcon: ImageResource.ARROW_RIGHT, index: 4),
                  ),
                  // Visibility(
                  //     // visible: state.validateFlag == false,
                  //     visible:
                  //         !SpUtil.getBool("validateFlag", defValue: false)!,
                  //     child: Container(
                  //       child: _setUpItemBuilder(
                  //           context, ImageResource.MY_ACCOUNT, "验证手机号",
                  //           rightIcon: ImageResource.ARROW_RIGHT, index: 8),
                  //     )),
                  Visibility(
                      visible: UserAppModuleUtils()
                          .moduleCanShow('mine_inventory_inquiry'),
                      child: Container(
                        child: _setUpItemBuilder(
                            context, ImageResource.MY_ACCOUNT, "库存查询",
                            rightIcon: ImageResource.ARROW_RIGHT, index: 5),
                      )),
                  Visibility(
                      visible: state.showDeleteAccount,
                      child: Container(
                        child: _setUpItemBuilder(
                            context, ImageResource.MY_ACCOUNT, "删除账户",
                            rightIcon: ImageResource.ARROW_RIGHT, index: 9),
                      )),

                  Visibility(
                      visible: state.AppNum > 1,
                      child: Container(
                        child: _setUpItemBuilder(
                            context, ImageResource.ICON_DELAY_RECEIPT, "切换到${state.userType == 'b2bSystem' ? 'ERP业务员' : '运营商业务员' }",
                            rightIcon: ImageResource.ARROW_RIGHT, index: 12),
                      )),

                  Container(
                    child: _setUpItemBuilder(
                        context, ImageResource.MY_ACCOUNT, "版本号",
                        content: state.version,
                        index: 10
                    ),
                  ),
                ],
              ),
            ),
          ));
        });
  }

  _setUpItemBuilder(BuildContext context, String leftIcon, String title,
      {int? index, String content = '', String rightIcon = ''}) {
    bool isLastestApp = SpUtil.getBool('isLastestApp') ?? true;
    return InkWell(
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 104.w,
            child: ExtendedImage.asset(leftIcon),
          ),
          Container(
            margin: EdgeInsets.only(left: 20.w),
            alignment: Alignment.centerLeft,
            width: 300.w,
            height: 104.w,
            child: Text(
              title,
              style: TextStyle(
                  color: ColorResource.BLACK_COMMON_COLOR, fontSize: 28.sp),
            ),
          ),
          Spacer(),
          Visibility(
            visible: index == 10 && !isLastestApp,
            child: InkWell(
              onTap: () {
                updateDialog(context);
              },
              child: Container(
                padding: EdgeInsets.all(10),
                child: Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    color: ColorResource.RED_COMMON_COLOR,
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ),

            ),
          ),
          Visibility(
            visible: content.isNotEmpty,
            child: Text(
              content,
              style: TextStyle(
                  color: ColorResource.BLACK_COMMON_COLOR, fontSize: 28.sp),
            ),
          ),
          Visibility(
            visible: rightIcon.isNotEmpty,
            child: Container(
              width: 40.w,
              height: 104.w,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20.r),
                child: ExtendedImage.asset(rightIcon),
              ),
            ),
          )
        ],
      ),
      onTap: () {
        if (index == 0) {
          Get.toNamed(PageName.PrintSettingPage);
        } else if (index == 1) {
          logic.showServicePhoneDialog(context);
        } else if (index == 2) {
          MyDialog.showDialog(() {
            MyDio.delete(Apis.logout, successCallBack: (value) async {
              SpUtil.remove("access_token");
              SpUtil.remove("branchName");
              SpUtil.remove("branchNo");
              await SecureStorage.token().delete?.call();


              // 清空APP 类型的数据
              await SecureStorageCommon.save('userType').delete?.call();
              await SecureStorageCommon.save('userInfo').delete?.call();
              await SecureStorageCommon.save('appLists').delete?.call();

              /// 退出登录，权限清空
              publicState.appModules = [];
              //清仓库
              publicState.branchInfo = {};
              UserAppModuleUtils().removeUserAppModules();
              Get.offNamed(PageName.LOGIN);
            });
          }, content: '确定退出登录？');
        } else if (index == 3) {
          logic.toChangeAccountListPage();
        } else if (index == 4) {
          logic.toChangePhoneNumnerPage();
        } else if (index == 5) {
          logic.toInventoryInquiryPage();
        } else if (index == 6) {
          // MyCommonUtils.showToast("跳转云商专属邀请码页面");
          Get.toNamed(PageName.InvitationCodePage);
        } else if (index == 7) {
          if (state.validateFlag == false) {
            Get.toNamed(PageName.BindPhoneNumberPage,
                parameters: {"fromPage": "MyPage"})?.then((value) {
              if (value != null && value == true) {
                logic.getLocalUserInfo();
                logic.getValidateFlagInfo(true);
              }
            });
          } else {
            logic.getCloudFlagInfo(true);
          }
        } else if (index == 8) {
          logic.toBindPhoneNumberPage();
        } else if (index == 9) {
          MyDialog.showDialog(() {
            MyDio.delete(Apis.logout, successCallBack: (value) {
              SpUtil.remove("access_token");
              SpUtil.remove("branchName");
              SpUtil.remove("branchNo");

              /// 退出登录，权限清空
              publicState.appModules = [];
              //清仓库
              publicState.branchInfo = {};
              UserAppModuleUtils().removeUserAppModules();
              Get.offNamed(PageName.LOGIN);
            });
          }, content: '确定退出删除账户吗？');
        } else if (index == 12) {
          MyDialog.showDialog(() async {
            SpUtil.remove("access_token");
            SpUtil.remove("branchName");
            SpUtil.remove("branchNo");
            await SecureStorage.token().delete?.call();


            // 清空APP 类型的数据
            String _userType = state.userType == 'b2bSystem' ? 'erpSystem' : 'b2bSystem';
            await SecureStorageCommon.save('userType').delete?.call();
            await SecureStorageCommon.save('userInfo').delete?.call();
            // await SecureStorageCommon.save('appLists').delete?.call();
            var filterItem = state.appLists.where((user) => user['id'] == _userType).toList();

            // filterItem = filterItem[0];

            await SecureStorageCommon.save<String>('userType').set(_userType);

            String jsonString = json.encode(filterItem[0]);
            await SecureStorageCommon.save<String>('userInfo').set(jsonString);
            print('filterItem $filterItem  ===  ${filterItem[0]}  === ${filterItem[0]['appName']} ==== ${json.decode(jsonString)['tenantCode']}');


            /// 退出登录，权限清空
            publicState.appModules = [];
            //清仓库
            publicState.branchInfo = {};
            UserAppModuleUtils().removeUserAppModules();
            // Get.offNamed(PageName.LOGIN);
            // if (_userType == 'erpSystem') {
            //   logic.reLogin(context, filterItem[0]['tenantCode']);
            // } else {
            //   logic.reB2bLogin(context, filterItem[0]['tenantCode']);
            // }

            checkApp(context, filterItem[0]);
          }, content: "是否切换到${state.userType == 'b2bSystem' ? 'ERP业务员' : '运营商业务员' }!!!");
        }
      },
    );
  }




  checkApp(BuildContext context, Map filterItem) {
    Map<String, dynamic>? queryAppParameters = {};
    queryAppParameters['saasUserCode'] = filterItem['saasUserCode'];
    queryAppParameters['token'] = filterItem['token'];

    MyDio.get(Apis.getApplications, queryParameters: queryAppParameters,
        successCallBack: (value) async {
          // print('xxxx $value');
          checkUser(value, filterItem);
        }
    );
  }



  checkUser(var value, var filterItem) async {
    // 将 Map 转换为 List 格式，以便在选择页面中使用
    Map<String, dynamic> dataMap = value['data']! as Map<String, dynamic>;
    List<Map<String, dynamic>> appList = [];

    // 转换 erpSystem
    if (dataMap.containsKey('erpSystem')) {
      Map<String, dynamic> erpSystem = dataMap['erpSystem'] as Map<String, dynamic>;
      var _item = {
        'id': 'erpSystem',
        'type': 'ERP业务员',
        'appCode': erpSystem['appCode'],
        'appName': erpSystem['appName'],
        'tenantCode': erpSystem['tenantCode'],
        'token': erpSystem['token'],
        'saasUserCode': filterItem['saasUserCode']
      };
      appList.add(_item);
    }

    // 转换 b2bSystem
    if (dataMap.containsKey('b2bSystem')) {
      Map<String, dynamic> b2bSystem = dataMap['b2bSystem'] as Map<String, dynamic>;
      var _item = {
        'id': 'b2bSystem',
        'type': '运营商业务员',
        'appCode': b2bSystem['appCode'],
        'appName': b2bSystem['appName'],
        'tenantCode': b2bSystem['tenantCode'],
        'token': b2bSystem['token'],
        'saasUserCode': filterItem['saasUserCode']
      };
      appList.add(_item);
    }

    // 临时将 appList 转换为 Map 以保持兼容性，但实际应该修改 state 中的类型定义
    // state1.AppLists = appList;

    String jsonString = json.encode(appList);
    await SecureStorageCommon.save<String>('appLists').set(jsonString);

    if (filterItem['id'] == 'erpSystem') {
      await SecureStorageCommon.save<String>('userType').set('erpSystem');


      String accessToken = dataMap['erpSystem']['token'];
      SpUtil.putString("access_token", accessToken!);
      /// 如果访问令牌不为空，则将其存储在安全存储中。
      if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);




      getLoginInfo();
      // Get.offAndToNamed(PageName.TAB);
    } else if (filterItem['id'] == 'b2bSystem') {
      await SecureStorageCommon.save<String>('userType').set('b2bSystem');

      String accessToken = dataMap['b2bSystem']['token'];
      SpUtil.putString("access_token", accessToken!);
      /// 如果访问令牌不为空，则将其存储在安全存储中。
      if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);


      Get.offAndToNamed(PageName.B2BTAB);
    }
  }


  // erp 登录完成后  获取app 的信息
  getLoginInfo() {
    MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {
      // logic.dismissDialog();

      var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

      // 租户ID
      String tenant_id = '${responseOther.tenantId}';
      SpUtil.putString('tenant_id', tenant_id);

      List moduleArray = responseOther.appModules ?? [];
      if (moduleArray.isEmpty) {
        MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
      }
      debugPrint("moduleList 1 ---- ${moduleArray}");
      List<String> moduleList = [];
      for (var element in moduleArray) {
        moduleList.add(element);
      }
      debugPrint("moduleList ---- ${moduleList}");
      // SpUtil.putStringList("appModules", moduleList);
      // state.appModules = responseOther.appModules!;
      UserAppModuleUtils().saveUserAppModuls(moduleArray);

      SpUtil.putBool("validateFlag", true);

      Get.offAllNamed( PageName.TAB);
    });
  }







  updateDialog(BuildContext context,
      {String okLable = "更新",
        String cancelLable = "关闭",
        String? title,
        VoidCallback? okCallback,
        VoidCallback? cancelCallback}) {
          showDialog(
            context: context,
            builder: (context) {
              String lastest_version = SpUtil.getString('lastest_version') ?? '';
              return AlertDialog(
                backgroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10), // 设置圆角半径为16
                ),
                content: Container(
                  height: 310.h,
                  width: 480.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    children: [
                      title == null
                          ? const SizedBox(
                        height: 0,
                      )
                          : Container(
                        alignment: Alignment.center,
                        child: Text(title),
                      ),
                      Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            child: RichText(
                              text: TextSpan(
                                text: '最新版本号是',
                                style: TextStyle(
                                    color: ColorResource.BLACK_COMMON_COLOR,
                                    fontSize: 35.sp
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: ' ${lastest_version}，',
                                    style: TextStyle(
                                        color: ColorResource.RED_TITLE_COLOR,
                                        fontSize: 35.sp
                                    ),
                                  ),
                                  TextSpan(
                                    text: '点击下方"更新"，下载最新版本的APP。',
                                    style: TextStyle(
                                        color: ColorResource.BLACK_COMMON_COLOR,
                                        fontSize: 35.sp
                                    ),
                                  ),
                                ],
                              ),
                              maxLines: null,  // 允许自动换行
                              overflow: TextOverflow.visible, // 显示所有内容，不截断
                            ),
                          )),
                      SizedBox(
                          height: 70.h,
                          child:  Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              InkWell(
                                child: Container(
                                  padding:
                                  EdgeInsets.symmetric(horizontal: 64.w, vertical: 4.w),
                                  alignment: Alignment.center,
                                  child: Text(cancelLable,
                                      style: TextStyle(
                                          color: Color(0xFF05E6871),
                                          fontSize: 36.sp)),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                onTapUp: (detail) {
                                  Get.back();
                                  cancelCallback?.call();
                                },
                              ),
                              InkWell(
                                child: Container(
                                  width: 250.w,
                                  alignment: Alignment.center,
                                  child: Text(okLable,
                                      style: TextStyle(
                                          color: Color(0xFF02396FF),
                                          fontSize: 36.sp)),
                                  decoration: BoxDecoration(
                                      color: ColorResource.WHITE_COMMON_COLOR,
                                      border: Border(
                                      ),
                                      borderRadius: BorderRadius.circular(5)),

                                ),
                                onTapUp: (detail) {
                                  Map<dynamic, dynamic>? _obj =  SpUtil.getObject('versionObj');

                                  int forced = _obj?['forced'] ?? 0; // 0不更新 1 弱更 2 强更
                                  String content = _obj?['content'] ?? ''; // 更新内容
                                  String url = _obj?['url'] ?? ''; // 下载地址
                                  String version = _obj?['version'] ?? ''; // 新版本号

                                  if (Platform.isAndroid) {
                                    checkPermission(
                                        forced, url, content, version);
                                    return;
                                  }
                                  if (Platform.isIOS) {
                                    launchUrlString(url);
                                    return;
                                  }

                                  // okCallback?.call();
                                },
                              )
                            ],
                          )
                      )
                    ],
                  ),
                ),
              );
            }
        );
  }



  @override
  void dispose() {
    super.dispose();
    Get.delete<MyLogic>();
  }

  @override
  bool get wantKeepAlive => true;
}
