class MonthlyOrderStatsBean {
  int? code;
  String? msg;
  MonthlyOrderStatsData? data;

  MonthlyOrderStatsBean({
    this.code,
    this.msg,
    this.data,
  });

  factory MonthlyOrderStatsBean.fromJson(Map<String, dynamic> json) {
    return MonthlyOrderStatsBean(
      code: _parseToInt(json['code']),
      msg: json['msg']?.toString(),
      data: json['data'] != null
          ? MonthlyOrderStatsData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'data': data?.toJson(),
    };
  }

  // 安全的整数解析方法
  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    if (value is double) return value.toInt();
    return null;
  }
}

class MonthlyOrderStatsData {
  OrderStats? currentMonthOrders;
  OrderStats? previousMonthOrders;

  MonthlyOrderStatsData({
    this.currentMonthOrders,
    this.previousMonthOrders,
  });

  factory MonthlyOrderStatsData.fromJson(Map<String, dynamic> json) {
    return MonthlyOrderStatsData(
      currentMonthOrders: json['currentMonthOrders'] != null
          ? OrderStats.fromJson(json['currentMonthOrders'] as Map<String, dynamic>)
          : null,
      previousMonthOrders: json['previousMonthOrders'] != null
          ? OrderStats.fromJson(json['previousMonthOrders'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentMonthOrders': currentMonthOrders?.toJson(),
      'previousMonthOrders': previousMonthOrders?.toJson(),
    };
  }
}

class OrderStats {
  int? count;
  double? totalAmount;

  OrderStats({
    this.count,
    this.totalAmount,
  });

  factory OrderStats.fromJson(Map<String, dynamic> json) {
    return OrderStats(
      count: _parseToInt(json['count']),
      totalAmount: _parseToDouble(json['totalAmount']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'totalAmount': totalAmount,
    };
  }

  // 安全的整数解析方法
  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    if (value is double) return value.toInt();
    return null;
  }

  // 安全的双精度浮点数解析方法
  static double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }
}
