import 'package:collection/collection.dart';

import 'b2b_app_customer_data.dart';

class B2bAppCustomerPage {
  int? code;
  B2bAppCustomerData? data;
  String? msg;

  B2bAppCustomerPage({this.code, this.data, this.msg});

  factory B2bAppCustomerPage.fromJson(Map<String, dynamic> json) {
    return B2bAppCustomerPage(
      code: json['code'] as int?,
      data: json['data'] == null
          ? null
          : B2bAppCustomerData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
        'msg': msg,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! B2bAppCustomerPage) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => code.hashCode ^ data.hashCode ^ msg.hashCode;
}
