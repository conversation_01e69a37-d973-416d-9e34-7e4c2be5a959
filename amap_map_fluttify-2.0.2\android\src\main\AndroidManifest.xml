<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="me.yohom.amap_map_fluttify">

    <!--允许程序打开网络套接字-->
    <uses-permission android:name="android.permission.INTERNET"/>
    <!--允许程序设置内置sd卡的写权限-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!--允许程序获取网络状态-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <!--允许程序访问WiFi网络信息-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <!--允许程序读写手机状态和身份-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <!--允许程序访问CellID或WiFi热点来获取粗略的位置-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!--如果不加回报 error, Neither user 10426 nor current process has android.permission.WAKE_LOCK., null-->
    <uses-permission android:name="android.permission.WAKE_LOCK"/>

    <application>
        <activity android:name="com.amap.api.maps.offlinemap.OfflineMapActivity" />
    </application>
</manifest>
