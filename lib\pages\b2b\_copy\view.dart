import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/pages/login/view.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/utils/color_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:get/get.dart';

import 'logic.dart';

class B2bChooseTypePage extends StatefulWidget {
  @override
  _B2bChooseTypeState createState() => _B2bChooseTypeState();
}

class _B2bChooseTypeState extends State<B2bChooseTypePage> {
  final logic = Get.put(B2bChooseTypeLogic());
  final state = Get
      .find<B2bChooseTypeLogic>()
      .state;


  @override
  void initState() {
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorResource.WHITE_COMMON_COLOR,
      appBar: MyCommonUtils.customAppBar('请选择您的身份'),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
      child: Container(
          margin: EdgeInsets.only(left: 60.w, right: 60.w),
          child: Column(children: [
            Text('data')
          ]))
    );
  }
}
