class SignInOutFlagBean {
  String? code;
  String? msg;
  SignInOutFlagData? data;

  SignInOutFlagBean({
    this.code,
    this.msg,
    this.data,
  });

  factory SignInOutFlagBean.fromJson(Map<String, dynamic> json) {
    return SignInOutFlagBean(
      code: json['code']?.toString(),
      msg: json['msg']?.toString(),
      data: json['data'] != null
          ? SignInOutFlagData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

class SignInOutFlagData {
  String? signInFlag;     // 签到标识
  String? signOutFlag;    // 签退标识
  String? status;         // 状态
  String? visitLogId;     // 访问日志ID
  String? signInTime;     // 签到时间
  String? signOutTime;    // 签退时间

  SignInOutFlagData({
    this.signInFlag,
    this.signOutFlag,
    this.status,
    this.visitLogId,
    this.signInTime,
    this.signOutTime,
  });

  factory SignInOutFlagData.fromJson(Map<String, dynamic> json) {
    return SignInOutFlagData(
      signInFlag: json['signInFlag']?.toString(),
      signOutFlag: json['signOutFlag']?.toString(),
      status: json['status']?.toString(),
      visitLogId: json['visitLogId']?.toString(),
      signInTime: json['signInTime']?.toString(),
      signOutTime: json['signOutTime']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'signInFlag': signInFlag,
      'signOutFlag': signOutFlag,
      'status': status,
      'visitLogId': visitLogId,
      'signInTime': signInTime,
      'signOutTime': signOutTime,
    };
  }
}
