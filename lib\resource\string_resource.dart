/*
 * @Author: 潘腾龙
 * @Date: 2023-10-27 15:12:05
 * @LastEditTime: 2023-10-27 15:42:05
 */
class StringResource {
  static const WECHAT_PAYMENT = "微信支付";
  static const ALIPAY_PAYMENT = "支付宝支付";
  static const CASH_PAYMENT = "现金支付";

  static const ALIPAY_PAYMENT_NAME = "支付宝";
  static const MIDEA_PAYMENT_NAME = "美的付";
  static const WECHAT_PAYMENT_NAME = "微信";
  static const CASH_PAYMENT_NAME = "现金";
  static const BANK_PAYMENT_NAME = "银联";
  static const JD_PAYMENT_NAME = "京东";

  // 支付方式：1微信,2支付宝,3银联,4京东支付
  static const PAY_TYPE_WECHAT = '1';
  static const PAY_TYPE_ALI = '2';
  static const PAY_TYPE_BANK = '3';
  static const PAY_TYPE_JD = '4';

  // 示例
  static const WIDGET_NEED_UPDATE = "WIDGET_NEED_UPDATE";
  static const ORDER_SALE_BODY_ID = "ORDER_SALE_BODY_ID";
  static const EDIT_ADDRESS_LIST = "EDIT_ADDRESS_LIST";

  static const DEALER_USER_LIST_INFO =
      "DEALER_USER_LIST_INFO";

  static const CUSTOMER_BIND_EMPLOYEE_LIST = "CUSTOMER_BIND_EMPLOYEE_LIST";
  static const SETTLEMENT_ADDRESS_LIST_DATEAIL = "SETTLEMENT_ADDRESS_LIST_DATEAIL";
  static const VEHICLE_SETTLEMENT_CONTENT_BOX = "VEHICLE_SETTLEMENT_CONTENT_BOX";

  static const ORDER_SALE_SEARCH = "ORDER_SALE_SEARCH";
  static const SALE_LASTEST_PRICE_LIST = "SALE_LASTEST_PRICE_LIST";
  static const ORDER_RETURN_SEARCH = "ORDER_RETURN_SEARCH";
  static const ORDER_RETURN_FILTTER_SEARCH = "ORDER_RETURN_FILTTER_SEARCH";
  static const ORDER_GIFT_SEARCH = "ORDER_GIFT_SEARCH";
  static const ORDER_GIFT_FILTTER_SEARCH = "ORDER_GIFT_FILTTER_SEARCH";
  static const ORDER_SALE_FILTTER_SEARCH = "ORDER_SALE_FILTTER_SEARCH";

  static const DEPT_REPAYMENT_BILL_SETTLEMENT_OPTIMIZE_LISTS_INFO = "DEPT_REPAYMENT_BILL_SETTLEMENT_OPTIMIZE_LISTS_INFO";

  static const MISSION_SYSTEM_LIST = "MISSION_SYSTEM_LIST";
  static const REPLEINSH_FAN_ITEM_LISTS = "REPLEINSH_FAN_ITEM_LISTS";
  static const PAGE_ME_ID = "PAGE_ME_ID";
  static const MY_HEADER_INFO = "MY_HEADER_INFO";
  static const MY_HEADER_LITE_INFO = "MY_HEADER_LITE_INFO";
  static const ADDRESS_LIST_DATEAIL = "ADDRESS_LIST_DATEAIL";

  static const DEPT_REPAYMENT_BILL_DETAIL_HEAD_INFO = "DEPT_REPAYMENT_BILL_DETAIL_HEAD_INFO";
  static const DEPT_REPAYMENT_SETTLEMENT_PAY_IMAGE_LISTS = "DEPT_REPAYMENT_SETTLEMENT_PAY_IMAGE_LISTS";

  static const DEPT_REPAYMENT_CUSTOMER_LIST = "DEPT_REPAYMENT_CUSTOMER_LIST";
  static const DEPT_REPAYMENT_BILL_HEAD_INFO = "DEPT_REPAYMENT_BILL_HEAD_INFO";
  static const DEPT_REPAYMENT_BILL_LIST = "DEPT_REPAYMENT_BILL_LIST";
  static const DEPT_REPAYMENT_BILL_LIST_DETAIL = "DEPT_REPAYMENT_BILL_LIST_DETAIL";
  static const DEPT_REPAYMENT_BILL_SETTLEMENT_OPTIMIZE_TOTAL_INFO = "DEPT_REPAYMENT_BILL_SETTLEMENT_OPTIMIZE_TOTAL_INFO";
  static const DEPT_REPAYMENT_BILL_DETAIL_BOTTOM_INFO =
      "DEPT_REPAYMENT_BILL_DETAIL_BOTTOM_INFO";
  static const DEPT_REPAYMENT_BILL_BOTTOM_INFO =
      "DEPT_REPAYMENT_BILL_BOTTOM_INFO";
  static const DEPT_REPAYMENT_REPAYMENT_HEAD_INFO =
      "DEPT_REPAYMENT_REPAYMENT_HEAD_INFO";
  static const DEPT_REPAYMENT_REPAYMENT_LIST = "DEPT_REPAYMENT_REPAYMENT_LIST";
  static const DEPT_REPAYMENT_SETTLEMENT_PAY_LIST =
      "DEPT_REPAYMENT_SETTLEMENT_PAY_LIST";

  static const ORDER_QUERY_SALE_LIST = "ORDER_QUERY_SALE_LIST";
  static const STORE_INFO_ADDRESS_LIST = "STORE_INFO_ADDRESS_LIST";
  static const ORDER_QUERY_RETURN_LIST = "ORDER_QUERY_RETURN_LIST";
  static const ORDER_QUERY_EXCHANGE_LIST = "ORDER_QUERY_EXCHANGE_LIST";
  static const ORDER_QUERY_DETAIL_SALE_HEAD_INFO =
      "ORDER_QUERY_DETAIL_SALE_HEAD_INFO";
  static const ORDER_QUERY_DETAIL_SALE_LIST = "ORDER_QUERY_DETAIL_SALE_LIST";
  static const ORDER_QUERY_DETAIL_RETURN_HEAD_INFO =
      "ORDER_QUERY_DETAIL_RETURN_HEAD_INFO";
  static const ORDER_QUERY_DETAIL_RETURN_LIST =
      "ORDER_QUERY_DETAIL_RETURN_LIST";
  static const ORDER_QUERY_DETAIL_EXCHANGE_HEAD_INFO =
      "ORDER_QUERY_DETAIL_EXCHANGE_HEAD_INFO";
  static const ORDER_QUERY_DETAIL_EXCHANGE_LIST =
      "ORDER_QUERY_DETAIL_EXCHANGE_LIST";

  static const ORDER_MEETING_LIST = "ORDER_MEETING_LIST";
  static const ORDER_MEETING_DETAIL_HEAD_INFO =
      "ORDER_MEETING_DETAIL_HEAD_INFO";
  static const ORDER_MEETING_DETAIL_GOOD_LIST =
      "ORDER_MEETING_DETAIL_GOOD_LIST";
  static const ORDER_MEETING_DETAIL_GIFT_LIST =
      "ORDER_MEETING_DETAIL_GIFT_LIST";
  static const ORDER_MEETING_DETAIL_REMARK_INFO =
      "ORDER_MEETING_DETAIL_REMARK_INFO";
  static const ORDER_MEETING_RECORD_CHARGE_LIST =
      "ORDER_MEETING_RECORD_CHARGE_LIST";
  static const ORDER_MEETING_RECORD_SALE_LIST =
      "ORDER_MEETING_RECORD_SALE_LIST";
  static const ORDER_MEETING_SETTLEMENT_PAY_LIST =
      "ORDER_MEETING_SETTLEMENT_PAY_LIST";

  static const COST_ADMINISTRATION_LIST = "COST_ADMINISTRATION_LIST";
  static const COST_ADMINISTRATION_TOTAL_MONEY =
      "COST_ADMINISTRATION_TOTAL_MONEY";

  static const DEVICE_MANAGEMENT_HEAD_SUMMARY_INFO =
      "DEVICE_MANAGEMENT_HEAD_SUMMARY_INFO";
  static const DEVICE_MANAGEMENT_EXIST_DEVICE_LIST =
      "DEVICE_MANAGEMENT_EXIST_DEVICE_LIST";
  static const DEVICE_MANAGEMENT_CHOOSE_DEVICE_LIST =
      "DEVICE_MANAGEMENT_EXIST_CHOOSE_LIST";

  static const TALLY_PHOTO_HEAD_INFO =
      "TALLY_PHOTO_HEAD_INFO";
  static const TALLY_PHOTO_LIST = "TALLY_PHOTO_LIST";

  static const ADVANCE_PAYMENT_HEAD_INFO = "ADVANCE_PAYMENT_HEAD_INFO";
  static const ADVANCE_PAYMENT_LIST = "ADVANCE_PAYMENT_LIST";
  static const ADVANCE_PAYMENT_SETTLEMENT_PAY_LIST =
      "ADVANCE_PAYMENT_SETTLEMENT_PAY_LIST";

  static const BIND_INVITATION_CODE_INFO = "BIND_INVITATION_CODE_INFO";
  static const BIND_INVITATION_BOTTOM_INFO = "BIND_INVITATION_BOTTOM_INFO";
  static const SHOP_BINDING_SCAN_INFO = "SHOP_BINDING_SCAN_INFO";
  static const SHOP_BINDING_SHOP_INFO = "SHOP_BINDING_SHOP_INFO";
  static const SHOP_BINDING_CODE_INFO = "SHOP_BINDING_CODE_INFO";
  static const SHOP_BINDING_DESCRIPTION_INFO = "SHOP_BINDING_DESCRIPTION_INFO";
  static const SHOP_BINDING_CLOUD_INFO = "SHOP_BINDING_CLOUD_INFO";

  static const Cloud_Unload_List = "Cloud_Unload_List";

  static const Document_Out_List = "Document_Out_List";
  static const Document_In_List = "Document_In_List";

  static const SUBMIT_ACCOUNT_HISTORY_LIST = "SUBMIT_ACCOUNT_HISTORY_LIST";

  //用户协议
  static const STRING_USER_AGREEMENT = "https://tf-shop.tfzhongchukeji.com/frontend/h5-business-activity/fdd-user-agreement.html";
  //隐私协议
  static const STRING_PRIVACY_AGREEMENT = "https://qiniu.tfzhongchukeji.com/app/prod/html/privacy_policy.html";
  static const PRIVACY_AGREEMENT = "        此进销存管理系统主要提供经销商端常规业务的处理，帮助经销商能及时把控业务员，终端门店和商品的销售情况，盈利情况，库存情况，账款情况等服务。"
      "\n        为实现上述目的，在我们在向您提供服务时，需要收集使用您的相关信息及以下权限：访问网络以展示数据情况，读取位置信息以查找附近门店，获取媒体定位以读取完整图片信息，使用相机以拍摄图片，使用存储权限以缓存运营素材，读取手机状态及蓝牙权限以建立签到状态。"
      "\n        我们承诺您的个人信息仅会用于我们所声明的目的并保证我们遵守适用的相关法律、法规及其他规范性文件，详情请查看";
  static const STRING_ACCOUNT_PROMPT = '应交账金额=销售总金额+订货会收款金额+预收款金额+收欠款金额-非欠款支付退款金额-门店费用-业务员费用-欠款支付订单金额-云商账户订单金额-预收账户订单金额-订货会订单金额';
}
