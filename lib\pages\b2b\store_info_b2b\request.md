渠道类型 
/miniapi/system/channel/getChannelList
请求方法
GET
接口字段
{
    "code": 0,
    "data": [
        {
            "channelId": "1",
            "sysCode": "1",
            "channelName": "测试渠道1",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "2",
            "sysCode": "4",
            "channelName": "演示渠道1",
            "memo": "演示",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "3",
            "sysCode": "4",
            "channelName": "演示渠道2",
            "memo": "演示",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "4",
            "sysCode": "5",
            "channelName": "便利店",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "5",
            "sysCode": "5",
            "channelName": "超市",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "6",
            "sysCode": "5",
            "channelName": "杂货",
            "memo": "1",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "7",
            "sysCode": "4",
            "channelName": "0327渠道1",
            "memo": "备注11111111111",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "8",
            "sysCode": "4",
            "channelName": "但是事实",
            "memo": "00000",
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "9",
            "sysCode": "2",
            "channelName": "烟酒",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "10",
            "sysCode": "2",
            "channelName": "美妆",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "11",
            "sysCode": "2",
            "channelName": "化工",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "12",
            "sysCode": "6",
            "channelName": "校园小店",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "13",
            "sysCode": "6",
            "channelName": "批发",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "14",
            "sysCode": "7",
            "channelName": "渠道一号",
            "memo": "测",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "15",
            "sysCode": "7",
            "channelName": "渠道二号",
            "memo": "测",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "16",
            "sysCode": "7",
            "channelName": "渠道三号",
            "memo": "测",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "17",
            "sysCode": "9",
            "channelName": "进件测试渠道-1",
            "memo": "进件测试",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "18",
            "sysCode": "8",
            "channelName": "便利店",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "19",
            "sysCode": "8",
            "channelName": "食杂店",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "20",
            "sysCode": "8",
            "channelName": "连锁店",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "21",
            "sysCode": "4",
            "channelName": "212",
            "memo": "632",
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "22",
            "sysCode": "4",
            "channelName": "666",
            "memo": "2",
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "23",
            "sysCode": "4",
            "channelName": "测试1",
            "memo": null,
            "hdfkSupport": null,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "24",
            "sysCode": "4",
            "channelName": "onion渠道",
            "memo": null,
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "25",
            "sysCode": "4",
            "channelName": "美宜家",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "26",
            "sysCode": "4",
            "channelName": "长沙测试渠道",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "27",
            "sysCode": "13",
            "channelName": "长沙渠道1",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "28",
            "sysCode": "13",
            "channelName": "长沙渠道2",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "29",
            "sysCode": "4",
            "channelName": "厦门",
            "memo": null,
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "30",
            "sysCode": "502597261987545088",
            "channelName": "渠道-SaaS",
            "memo": "SaaS测试数据，勿删",
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "31",
            "sysCode": "4",
            "channelName": "SAAS渠道",
            "memo": null,
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "32",
            "sysCode": "4",
            "channelName": "餐饮",
            "memo": "观山湖区域所有餐饮门店",
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "33",
            "sysCode": "4",
            "channelName": "姚文冠邀请",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "36",
            "sysCode": "595397392074539008",
            "channelName": "渠道管理-1",
            "memo": null,
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "37",
            "sysCode": "595397392074539008",
            "channelName": "渠道管理-1",
            "memo": null,
            "hdfkSupport": 0,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "38",
            "sysCode": "597339494681542656",
            "channelName": "美的渠道",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        },
        {
            "channelId": "39",
            "sysCode": "608397579477778432",
            "channelName": "线上渠道",
            "memo": null,
            "hdfkSupport": 1,
            "deleted": 0,
            "status": 1
        }
    ],
    "msg": ""
}


实现的要求是  和全国分组 这个选项 一样的操作