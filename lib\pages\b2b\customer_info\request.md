1. 接口概述
接口名称：查询门店详细信息接口
接口路径：/member/branch/getBranchInfo/{branchId}

请求方法：GET


2. 请求参数
Header参数
X-Access-Token:xxxxx
Query参数
id：门店ID
3. 响应参数
{
    "code": 0,
    "data": {
        "branchId": "637333272259067906",
        "branchNo": "KH00060", 
        "sysCode": "4",
        "branchName": "1137", 客户名称
"channelId": 1 渠道类型,
        "areaId": "318",  城市区域
        "branchAddr": "陕西省榆林市靖边县中山涧镇",  详细地址
        "groupId": "36",  全国分组
        "contactName": "11370",  联系人
        "contactPhone": "15708431000", 手机号
        "status": 1,
        "auditState": 0,
        "salePriceCode": 1,  价格组
        "hdfkSupport": 1,  是否货到付款
        "firstAreaCityId": "61",
        "secondAreaCityId": "6108",
        "threeAreaCityId": "610824",
        "branchTags": [],
        "provinceName": "陕西省",  
        "cityName": "榆林市",
        "districtName": "靖边县",
        "lifecycleStage": 1,
        "nextBranchLifecycleMessage": "预计28天后进入沉默",
        "isPayOnline": 0,
      	"memo": 备注 111   备注
    },
    "msg": ""
}

接口的branchId  Get.parameters 的branchId



根据接口 来更新页面的字段

