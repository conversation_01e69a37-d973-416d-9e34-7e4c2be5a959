import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:fuduoduo/domain/branch_list_bean.dart';

class B2bCustomerListPageState {
  // 线路数据
  List<dynamic> routeList = [];
  // 客户数据
  List<dynamic> customerList = [];
  // 门店数据列表
  List<BranchInfo> branchList = [];
  bool unbindCustomerListExitFlag = false;
  // 选择的线路id
  String selectedRouteId = "";
  int selectedRouteIndex = 0;
  // 默认选择拜访类型下标
  int selectedTypeIndex = 0;

  // 分页相关
  int currentPage = 1;
  int pageSize = 10;
  int totalCount = 0;
  bool isLoading = false;
  bool hasMore = true;
  bool isRefreshing = false;

  // 搜索条件
  String searchKeyword = "";
  String selectedDistance = "0"; // 0:所有, 5km, 10km

  // 拜访类型
  List<dynamic> typeList = [
    {"name": "所有门店", "type": "0", "distance": "0"},
    {"name": "附近5公里", "type": "1", "distance": "5"},
    {"name": "附近10公里", "type": "2", "distance": "10"},
  ];

  PDuration produceDate = PDuration(
      year: DateTime.now().year,
      month: DateTime.now().month,
      day: DateTime.now().day);

  B2bCustomerListPageState() {
    ///Initialize variables
  }

  dynamic consumerInfo = {}; //门店信息

  // 重置分页状态
  void resetPagination() {
    currentPage = 1;
    totalCount = 0;
    hasMore = true;
    branchList.clear();
  }

  // 更新分页状态
  void updatePagination(int total, int currentDataLength) {
    print('xxx $total $currentDataLength ${branchList.length} ${currentDataLength < pageSize}');
    totalCount = total;
    hasMore = branchList.length < total;
    if (currentDataLength < pageSize) {
      hasMore = false;
    }
  }
}
