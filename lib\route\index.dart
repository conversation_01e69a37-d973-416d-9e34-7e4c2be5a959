/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月11日10:18:54
 * 修改时间：2023年09月11日10:18:54
 */

import 'package:flustars/flustars.dart';
import 'package:fuduoduo/pages/bluetooth/bluetooth_device/bluetooth_device_view.dart';
import 'package:fuduoduo/pages/customer/exchange/exchange_choose/view.dart';
import 'package:fuduoduo/pages/customer/exchange/exchange_details/view.dart';
import 'package:fuduoduo/pages/customer/exchange/exchange_info/view.dart';
import 'package:fuduoduo/pages/customer/exchange/exchange_list/view.dart';
import 'package:fuduoduo/pages/homePages/store_info/store_info_view.dart';
import 'package:fuduoduo/pages/tabsPage/home/<USER>/view.dart';
import 'package:fuduoduo/pages/tabsPage/home/<USER>/view.dart';
import 'package:fuduoduo/route/transition.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';

import 'package:fuduoduo/middleware/router_middleware.dart';

import '../pages/advance_payment/list/view.dart';
import '../pages/advance_payment/pay/view.dart';
import '../pages/advance_payment/settlement/view.dart';
import '../pages/bind_phone_number/view.dart';
import '../pages/binding_optimisation/invitation_code/view.dart';
import '../pages/binding_optimisation/shop_binding/view.dart';
import '../pages/bluetooth/print_setting/print_setting_view.dart';
import '../pages/boss_reportforms/detail/view.dart';
import '../pages/boss_reportforms/home/<USER>';
import '../pages/boss_reportforms/sales_detail/view.dart';
import '../pages/boss_reportforms/sales_detail_item/view.dart';
import '../pages/change_account/account_manage/view.dart';
import '../pages/change_account/change_account_list/view.dart';
import '../pages/change_phone_numner/view.dart';
import '../pages/cloud_commerce_unload/clound_select_goods/view.dart';
import '../pages/cloud_commerce_unload/info/view.dart';
import '../pages/cloud_commerce_unload/list/view.dart';
import '../pages/common/common_scan_page.dart';
import '../pages/common/common_webview_page.dart';
import '../pages/common/common_webviewwithbar_page.dart';
import '../pages/common/inapp_webview_page.dart';
import '../pages/common/inapp_webview_appbar_page.dart';
import '../pages/cost_administration/view.dart';
import '../pages/customer/bind/view.dart';
import '../pages/customer/disposal_goods_car/view.dart';
import '../pages/customer/disposal_goods_sale/view.dart';
import '../pages/customer/info/view.dart';
import '../pages/customer/list/view.dart';
import '../pages/customer/search/view.dart';
import '../pages/debt_repayment/debt_bill_list/view.dart';
import '../pages/debt_repayment/debt_bill_detail/view.dart';
import '../pages/debt_repayment/debt_bill_list_detail/view.dart';
import '../pages/debt_repayment/debt_customer_list/view.dart';
import '../pages/debt_repayment/debt_repayment_list/view.dart';
import '../pages/debt_repayment/debt_repayment_settlement/view.dart';
import '../pages/debt_repayment/debt_repayment_settlement_optimize/view.dart';
import '../pages/debt_repayment/dept_repayment_pay_qrcode/view.dart';
import '../pages/debt_repayment/dept_repayment_pay/view.dart';
import '../pages/device_administration/add/view.dart';
import '../pages/device_administration/choose/view.dart';
import '../pages/device_administration/list/view.dart';
import '../pages/display_manage/cash_record/view.dart';
import '../pages/display_manage/list/view.dart';
import '../pages/display_manage/manage_plan/view.dart';
import '../pages/forget_password/view.dart';
import '../pages/home_report/view.dart';
import '../pages/inventory/allocate/create/view.dart';
import '../pages/inventory/allocate/detail/view.dart';
import '../pages/inventory/allocate/list/view.dart';
import '../pages/inventory/allocate/operation/view.dart';
import '../pages/inventory/allocate/search/view.dart';
import '../pages/inventory/check/create/view.dart';
import '../pages/inventory/check/detail/view.dart';
import '../pages/inventory/check/list/view.dart';
import '../pages/inventory/document_in/detail/view.dart';
import '../pages/inventory/document_in/list/view.dart';
import '../pages/inventory/document_out/create/view.dart';
import '../pages/inventory/document_out/detail/view.dart';
import '../pages/inventory/document_out/list/view.dart';
import '../pages/inventory/document_out/outpage/view.dart';
import '../pages/inventory/search/view.dart';
import '../pages/inventory_inquiry/view.dart';
import '../pages/login/agreement.dart';
import '../pages/login/choose_user/view.dart';
import '../pages/login/view.dart';
import '../pages/logistics_distribution/deliverying_detail/view.dart';
import '../pages/logistics_distribution/discrepancy_input/view.dart';
import '../pages/logistics_distribution/input_oder_detail/view.dart';
import '../pages/logistics_distribution/lgistics_payway/view.dart';
import '../pages/logistics_distribution/logistics_home/view.dart';
import '../pages/logistics_distribution/prepare_delivery_deatil/view.dart';
import '../pages/logistics_distribution/settle_account/view.dart';
import '../pages/logistics_distribution/store_map/view.dart';
import '../pages/mission_system/list/view.dart';
import '../pages/order_meeting/add/view.dart';
import '../pages/order_meeting/choose/view.dart';
import '../pages/order_meeting/detail/view.dart';
import '../pages/order_meeting/list/view.dart';
import '../pages/order_meeting/pay/view.dart';
import '../pages/order_meeting/record/view.dart';
import '../pages/order_meeting/settlement/view.dart';
import '../pages/order_query/detail/exchange/view.dart';
import '../pages/order_query/detail/return/view.dart';
import '../pages/order_query/detail/sale/view.dart';
import '../pages/order_query/list/view.dart';
import '../pages/place_order/view.dart';
import '../pages/replenishment/detail/check_detail/view.dart';
import '../pages/replenishment/detail/replenishment_back_detail/view.dart';
import '../pages/replenishment/document/back_document/view.dart';
import '../pages/replenishment/document/check_document/view.dart';
import '../pages/replenishment/document/replenishment_document/view.dart';
import '../pages/replenishment/replenishment/add/view.dart';
import '../pages/replenishment/replenishment/view.dart';
import '../pages/replenishment/search/view.dart';
import '../pages/submit_account/detail/view.dart';
import '../pages/submit_account/distribution/view.dart';
import '../pages/submit_account/distribution_detail/view.dart';
import '../pages/submit_account/view.dart';
import '../pages/tabsPage/home/<USER>/view.dart';
import '../pages/tabsPage/home/<USER>/view.dart';
import '../pages/tabsPage/home/<USER>/view.dart';
import '../pages/tabsPage/index.dart';
import '../pages/tally_photo/add/view.dart';
import '../pages/tally_photo/detail/view.dart';
import '../pages/tally_photo/list/view.dart';
import '../pages/vehicle_order/carts/view.dart';
import '../pages/vehicle_order/exchange/info/view.dart';
import '../pages/vehicle_order/paySateus/view.dart';
import '../pages/vehicle_order/payWay/view.dart';
import '../pages/vehicle_order/settlement/view.dart';
import '../pages/vehicle_order/address_list/view.dart';
import '../pages/vehicle_order/address_edit/view.dart';
import '../pages/vehicle_order/view.dart';
import '../pages/b2b/tabsPage/index.dart';



// b2b
import '../pages/b2b/choose_type/view.dart';
import '../pages/b2b/store_info_b2b/store_info_view.dart';
import '../pages/b2b/customer_list/view.dart';
import '../pages/b2b/customer_info/view.dart';
import '../pages/b2b/customer_search/view.dart';
import '../pages/b2b/coupon/coupon_view.dart';
import '../pages/b2b/coupon/coupon_binding.dart';
import '../pages/b2b/customer_user/customer_user_view.dart';
import '../pages/b2b/customer_user/customer_user_edit_view.dart';
import '../pages/b2b/customer_user/customer_user_add_view.dart';
import '../pages/b2b/customer_user/customer_user_binding.dart';
import '../pages/b2b/store_carts/store_carts_view.dart';
import '../pages/b2b/store_carts/store_carts_binding.dart';
// b2b




// 页面名称
class PageName {
  static const String HOME = '/';
  static const String LOGIN = '/login'; // 登录
  static const String ChooseUserPage = '/ChooseUserPage'; // 选择用户页面
  static const String TAB = '/tab'; // tab页面
  static const String B2BTAB = '/b2bTab'; // B2B tab页面
  static const String AgreementPage = '/AgreementPage'; // 用户协议

  //=====================首页==========================//
  static const String HOMESecondQuery = '/HOMESecondQuery'; //首页个人业绩二级列表
  static const String todayCollectionPage = '/todayCollectionPage'; //今日收款

  static const String MissionSystemListPage = '/MissionSystemListPage'; // 任务中心页面



  //=====================B2b相关的页面==========================//
  static const String B2bChooseTypePage = '/B2bChooseTypePage'; // 任务中心页面
  //=====================B2b相关的页面==========================//




  //=====================通用==========================//
  static const String CommonScanPage = '/CommonScanPage'; // 通用扫描页面
  static const String CommonWebViewPage = '/CommonWebViewPage'; // 通用WebView页面
  static const String CommonWebViewWithBarPage = '/CommonWebViewWithBarPage'; // 通用WebView页面(带AppBar)
  static const String InAppWebViewPage = '/InAppWebViewPage'; // 通用WebView页面
  static const String InAppWebViewAppbarPage = '/InAppWebViewAppbarPage'; // 通用WebView页面
  //=====================欠款收款==========================//
  static const String DeptCustomerListPage = '/DeptCustomerListPage'; // 欠款客户页面
  static const String DeptBillListPage = '/DeptBillListPage'; // 欠款账单页面
  static const String DeptBillDetailPage = '/DeptBillDetailPage'; // 欠款账单收款页面
  static const String DeptBillListDetailPage = '/DeptBillListDetailPage'; // 欠款账单收款详情页面
  static const String DebtRepaymentSettlementOptimizePage = '/DebtRepaymentSettlementOptimizePage'; // 欠款账单最新的结算页面
  static const String DeptRepaymentPayQrcodePage = '/DeptRepaymentPayQrcodePage'; // 欠款账单二维码支付页面
  static const String DeptRepaymentListPage =
      '/DeptRepaymentListPage'; // 欠款还款页面
  static const String DeptRepaymentSettlementPage =
      '/DeptRepaymentSettlementPage'; // 欠款结算页面
  static const String DeptRepaymentPayPage =
      '/DeptRepaymentPayPage'; // 欠款支付方式页面
  //=====================订单查询==========================//
  static const String OrderQueryListPage = '/OrderQueryListPage'; // 订单查询列表页面
  static const String OrderQuerySaleDetailPage =
      '/OrderQuerySaleDetailPage'; // 订单查询销售详情页面
  static const String OrderQueryReturnDetailPage =
      '/OrderQueryReturnDetailPage'; // 订单查询退货详情页面
  static const String OrderQueryExchangeDetailPage =
      '/OrderQueryExchangeDetailPage'; // 订单查询换货详情页面
  //=====================订货会==========================//
  static const String OrderMeetingListPage = '/OrderMeetingListPage'; // 订货会列表页面
  static const String B2bCouponPage = '/B2bCouponPage'; // b2b 优惠券页面
  static const String OrderMeetingDetailPage =
      '/OrderMeetingDetailPage'; // 订货会详情页面
  static const String OrderMeetingAddPage =
      '/OrderMeetingAddPage'; // 订货会新增页面(已废弃)
  static const String OrderMeetingChoosePage =
      '/OrderMeetingChoosePage'; // 订货会选择页面（已废弃）
  static const String OrderMeetingRecordPage =
      '/OrderMeetingRecordPage'; // 订货会记录页面
  static const String OrderMeetingSettlementPage =
      '/OrderMeetingSettlementPage'; // 订货会结算页面
  static const String OrderMeetingPayPage = '/OrderMeetingPayPage'; // 订货会付款页面
  //=====================费用管理==========================//
  // static const String StoreReconciliationPage = '/StoreReconciliationPage'; // 门店对账页面
  static const String CostAdministrationPage =
      '/CostAdministrationPage'; // 费用管理页面
  //=====================云商绑定==========================//
  static const String InvitationCodePage = '/InvitationCodePage'; // 云商专属邀请码页面
  static const String ShopBindingPage = '/ShopBindingPage'; // 云商绑定页面
  //=====================设备管理==========================//
  static const String DeviceAdministrationListPage =
      '/DeviceAdministrationListPage'; // 已有设备列表页面
  static const String DeviceAdministrationAddPage =
      '/DeviceAdministrationAddPage'; // 设备管理新增设备页面
  static const String DeviceAdministrationChoosePage =
      '/DeviceAdministrationChoosePage'; // 设备管理设备列表页面
  //=====================理货拍照==========================//
  static const String TallyPhotoListPage = '/TallyPhotoListPage'; // 理货拍照列表页面
  static const String TallyPhotoAddPage = '/TallyPhotoAddPage'; // 理货拍照新增页面
  static const String TallyPhotoDetailPage =
      '/TallyPhotoDetailPage'; // 理货拍照详情页面
  //=====================预收款==========================//
  static const String AdvancePaymentListPage =
      '/AdvancePaymentListPage'; // 预收款列表页面
  static const String AdvancePaymentSettlementPage =
      '/AdvancePaymentSettlementPage'; // 预收款结算页面
  static const String AdvancePaymentPayPage =
      '/AdvancePaymentPayPage'; // 预收款支付页面

  static const String CustomerListPage = '/CustomerListPage'; // 客户列表
  static const String B2bCustomerListPage = '/B2bCustomerListPage'; // b2b客户列表
  static const String CustomerInfoPage = '/CustomerInfoPage'; // 客户详情页面
  static const String B2bCustomerInfoPage = '/B2bCustomerInfoPage'; // B2b客户详情页面
  static const String CustomerSearchPage = '/CustomerSearchPage'; // 客户搜索页面
  static const String B2bCustomerSearchPage = '/B2bCustomerSearchPage'; // B2b客户搜索页面
  static const String VehicleOrderPage = '/VehicleOrderPage'; // 访销订单
  static const String VehicleCartsPage = '/VehicleCartsPage'; // 访销订单购物车
  static const String AddressListPage = '/AddressListPage'; // 客户地址列表
  static const String AddressEditPage = '/AddressEditPage'; // 客户地址列表
  static const String VehicleSettlementPage =
      '/VehicleSettlementPage'; // 访销订单结算
  static const String vehiclePayStateusPage =
      '/vehiclePayStateusPage'; // 访销订单成功页

  //=====================车销==========================//
  //车销补货
  static const String ReplenishmentPage = '/ReplenishmentPage'; // 车销补货
  static const String ReplenishmentAddPage = '/ReplenishmentAddPage'; // 补货
  static const String ReplenishmentDocumentPage =
      '/ReplenishmentDocumentPage'; // 补货单
  static const String ReplenishmentDocumentDetailPage =
      '/ReplenishmentDocumentDetailPage'; // 补货单详情
  //车销返仓
  static const String BackDocumentPage = '/BackDocumentPage'; //返仓单
  static const String CheckDocumentPage = '/CheckDocumentPage'; //盘点单
  static const String CheckDetailPage = '/CheckDetailPage'; //盘点单详情
  static const String CheckSearchPage = '/CheckSearchPage'; //盘点搜索页面

  static const String PlaceOrderPage = '/PlaceOrderPage'; // 代客下单

  //=====================库存==========================//
  //入库
  static const String InventoryDocumentInListPage =
      '/InventoryDocumentInListPage'; // 库存单据页面
  static const String InventoryDocumentInDetailPage =
      '/InventoryDocumentInDetailPage'; // 库存单据详情页面
  //出库
  static const String InventoryCreateDocumentOutPage =
      '/InventoryCreateDocumentOutPage'; // 单据出库新建页面
  static const String InventoryDocumentOutListPage =
      '/InventoryDocumentOutListPage'; // 单据出库页面
  static const String InventoryDocumentOutDetailPage =
      '/InventoryDocumentOutDetailPage'; // 单据出库详情页面
  static const String InventoryCreateOutPage =
      '/InventoryCreateOutPage'; // 单据出库详情页面
  //调拨单
  static const String InventoryAllocateListPage =
      '/InventoryAllocateListPage'; // 库存调拨页面
  static const String InventoryAllocateSearchPage =
      '/InventoryAllocateSearchPage'; // 库存调拨页面
  static const String InventoryAllocateCreatePage =
      '/InventoryAllocateCreatePage'; // 库存调拨创建页面
  static const String InventoryAllocateOperationPage =
      '/InventoryAllocateOperationPage'; // 库存调拨手动创建页面
  static const String InventoryAllocateDetailPage =
      '/InventoryAllocateDetailPage'; // 库存调拨详情页面
  //盘点
  static const String InventoryCheckListPage =
      '/InventoryCheckListPage'; // 库存盘点页面
  static const String InventoryCheckDetailPage =
      '/InventoryCheckDetailPage'; // 库存盘点详情页面
  static const String InventoryCheckCreatePage =
      '/InventoryCheckCreatePage'; // 库存创建盘点页面
  //搜索页面
  static const String InventorySearchPage = '/InventorySearchPage'; // 库存创建盘点页面

  static const String SubmitAccountPage = '/SubmitAccountPage'; // 我的-交账界面
  static const String DistributionPage = '/DistributionPage'; // 我的-司机交账界面
  static const String DistributionDetailPage =
      '/DistributionDetailPage'; // 我的-司机交账详情界面
  static const String SubmitAccountDetailPage =
      '/SubmitAccountDetailPage'; // 我的-交账详情界面
  static const String CloudCommerceUnloadPage =
      '/CloudCommerceUnloadPage'; // 云商卸货列表
  static const String CloudCommerceOrderPage =
      '/CloudCommerceOrderPage'; // 云商卸货订单
  static const String CloundSelectGoodsPage =
      '/CloundSelectGoodsPage'; // 云商卸货换品选择商品界面

  static const String DisplayManagePage = '/DisplayManagePage'; // 陈列管理
  static const String ManagePlanPage = '/ManagePlanPage'; // 陈列管理-陈列计划
  static const String CashRecordPage = '/CashRecordPage'; // 陈列管理-兑付记录

  static const String UnknownPage = '/UnknownPage';

  static const String CustomerBindEmployeeListPage =
      '/CustomerBindEmployeeListPage'; // 拓店
  static const String StoreInfoPage = '/StoreInfoPage'; // 拓店
  static const String B2bStoreInfoPage = '/B2bStoreInfoPage'; // 拓店
  static const String VehicleExchangeInfoPage =
      '/VehicleExchangeInfoPage'; // 访销订单购物车
  static const String VehiclePayWayPage = '/VehiclePayWayPage'; // 访销订单购物车

  static const String BossReportFormPage = '/BossReportFormPage'; // 访销订单购物车
  static const String BossDetailPage = '/BossDetailPage'; // 报表详情
  static const String SalesDetailPage = '/SalesDetailPage'; // 业务员销售金额报表详情
  static const String SalesItemDetailPage =
      '/SalesItemDetailPage'; // 业务员销售金额报表详情

  // static const String OrderDetailsPage = '/OrderDetailsPage'; /// 订单查询
  static const String HomeReportPage = '/HomeReportPage';

  /// 打印机
  static const String BluetoothDevicePage = '/BluetoothDevicePage';
  // 打印设置
  static const String PrintSettingPage = '/PrintSettingPage';

  /// 物流配送 ---地图预览
  static const String StoreMapPage = '/StoreMapPage';
  // 物流配送 ---待配送-配送单详情
  static const String PrepareDeliveryDeatilPage = '/PrepareDeliveryDeatilPage';
  // 物流配送 ---配送中-配送单列表
  static const String DeliveryingDetailPage = '/DeliveryingDetailPage';
  // 物流配送 ---配送单详情，可输入
  static const String InputOderDetailPage = '/InputOderDetailPage';
  // 物流配送 ---差异录入，可输入
  static const String DiscrepancyInputPage = '/DiscrepancyInputPage';
  // 物流配送 ---结算
  static const String SettleAccountPage = '/SettleAccountPage';
  // 物流配送 ---出事凭证
  static const String LgisticsPaywayPage = '/LgisticsPaywayPage';
  // 物流配送 ---出事凭证
  static const String LogisticsHomePage = '/LogisticsHomePage';

  // 切换账号--账号列表
  static const String ChangeAccountListPage = '/ChangeAccountListPage';
  // 切换账号--账号管理
  static const String AccountManagePage = '/AccountManagePage';
  // 更换手机号
  static const String ChangePhoneNumnerPage = '/ChangePhoneNumnerPage';
  // 验证手机号
  static const String BindPhoneNumberPage = '/BindPhoneNumberPage';
  // 忘记密码
  static const String ForgetPasswordPage = '/ForgetPasswordPage';

  // 库存查询
  static const String InventoryInquiryPage = '/InventoryInquiryPage';
  // 处理品销售
  static const String DisposalGoodsSalePage = '/DisposalGoodsSalePage';
  // 处理品销售--购物车
  static const String DisposalGoodsCarPage = '/DisposalGoodsCarPage';
  // 处理品销售--结算
  static const String DisposalGoodsSubmitPage = '/DisposalGoodsSubmitPage';
  // 首页-店铺预览
  static const String ShopPreviewPage = '/ShopPreviewPage';
  // 首页-点击店铺预览-选择店铺
  static const String SelectShopPage = '/SelectShopPage';
  // 首页-任务中心
  static const String MissionCenterPage = '/MissionCenterPage';

  /// 兑换
  static const String ExchangeInfo = '/ExchangeInfoPage';
  /// 兑换记录
  static const String ExchangeList = '/ExchangeListPage';
  /// 选择页面， 选择商品或者门店
  static const String ExchangeChoose = '/ExchangeChoosePage';
  /// 兑换 详情页面
  static const String ExchangeDetails = '/ExchangeDetailsPage';

  /// 门店用户管理
  static const String CustomerUserPage = '/CustomerUserPage';
  static const String CustomerUserEditPage = '/CustomerUserEditPage';
  static const String CustomerUserAddPage = '/CustomerUserAddPage';

  /// 蓝牙设置

  /// 门店购物车管理
  static const String StoreCartsPage = '/StoreCartsPage';
}

// 路由列表
class PageRoutes {
  // 初始入口-启动页
  // static final String initRoute = PageName.SPLASH;

  static final List<GetPage<dynamic>> routes = [
    /// 路由页面模板
    // GetPage(
    //   name: PageName.AppraisedPriceNewPage,
    //   page: () => AppraisedPriceNewPage(),
    //   customTransition: MyRouterEnterDirRightToLft(),
    //   transitionDuration: const Duration(milliseconds: 370),
    // ),

    GetPage(
        name: PageName.HOME,
        page: () {
          var token = SpUtil.getString('access_token');
          bool isNotNull = token?.isNotEmpty ?? false;
          print("${isNotNull ? '登录' : '没有登录'}");
          bool validateFlag = SpUtil.getBool("validateFlag",defValue: false)!;
          bool appType = SpUtil.getBool("appType",defValue: false)!;
          print("${(validateFlag == true) ? '手机号已验证' : '手机号未验证'}");

          // 增加b2b 的判断
          var _page = appType ? B2bTabPage() : TabPage();

          return ((validateFlag == true) && isNotNull) ? _page : LoginPage();
        },
        middlewares: [AuthMiddleware()],
        transition: Transition.fadeIn),

    GetPage(
        name: PageName.HOMESecondQuery,
        page: () => HomeSecondQueryPage(),
        middlewares: [AuthMiddleware()],
        transition: Transition.fadeIn
    ),

    GetPage(
        name: PageName.TAB,
        page: () => TabPage(),
        middlewares: [AuthMiddleware()],
        transition: Transition.fadeIn),

    GetPage(
        name: PageName.B2BTAB,
        page: () => B2bTabPage(),
        middlewares: [AuthMiddleware()],
        transition: Transition.fadeIn),

    GetPage(
        name: PageName.LOGIN,
        page: () => LoginPage(),
        middlewares: [AuthMiddleware()],
        transition: Transition.fadeIn),
    GetPage(
      name: PageName.ChooseUserPage,
      page: () => ChooseUserPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    //用户协议
    GetPage(name: PageName.AgreementPage, page: () => AgreementPage(), middlewares: [AuthMiddleware()]),
    GetPage(
      name: PageName.CommonScanPage,
      page: () => CommonScanPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.CommonWebViewPage,
      page: () => CommonWebViewPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.CommonWebViewWithBarPage,
      page: () => CommonWebViewWithBarPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.InAppWebViewPage,
      page: () => InAppWebViewPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.InAppWebViewAppbarPage,
      page: () => InAppWebViewAppbarPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptCustomerListPage,
      page: () => DeptCustomerListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptBillListPage,
      page: () => DeptBillListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptBillDetailPage,
      page: () => DeptBillDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.DeptBillListDetailPage,
      page: () => DeptBillListDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DebtRepaymentSettlementOptimizePage,
      page: () => DebtRepaymentSettlementOptimizePage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptRepaymentPayQrcodePage,
      page: () => DeptRepaymentPayQrcodePage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptRepaymentListPage,
      page: () => DeptRepaymentListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptRepaymentSettlementPage,
      page: () => DeptRepaymentSettlementPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeptRepaymentPayPage,
      page: () => DeptRepaymentPayPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderQueryListPage,
      page: () => OrderQueryListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderQuerySaleDetailPage,
      page: () => OrderQuerySaleDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderQueryReturnDetailPage,
      page: () => OrderQueryReturnDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderQueryExchangeDetailPage,
      page: () => OrderQueryExchangeDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingListPage,
      page: () => OrderMeetingListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingDetailPage,
      page: () => OrderMeetingDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingAddPage,
      page: () => OrderMeetingAddPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingChoosePage,
      page: () => OrderMeetingChoosePage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingRecordPage,
      page: () => OrderMeetingRecordPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingSettlementPage,
      page: () => OrderMeetingSettlementPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.OrderMeetingPayPage,
      page: () => OrderMeetingPayPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    // GetPage(
    //   name: PageName.StoreReconciliationPage,
    //   page: () => StoreReconciliationPage(),
    //   customTransition: MyRouterEnterDirRightToLft(),
    //   transitionDuration: const Duration(milliseconds: 370),
    // ),

    GetPage(
      name: PageName.CostAdministrationPage,
      page: () => CostAdministrationPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.InvitationCodePage,
      page: () => InvitationCodePage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.ShopBindingPage,
      page: () => ShopBindingPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeviceAdministrationListPage,
      page: () => DeviceAdministrationListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeviceAdministrationAddPage,
      page: () => DeviceAdministrationAddPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.DeviceAdministrationChoosePage,
      page: () => DeviceAdministrationChoosePage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.TallyPhotoListPage,
      page: () => TallyPhotoListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.TallyPhotoAddPage,
      page: () => TallyPhotoAddPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.TallyPhotoDetailPage,
      page: () => TallyPhotoDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.AdvancePaymentListPage,
      page: () => AdvancePaymentListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.AdvancePaymentSettlementPage,
      page: () => AdvancePaymentSettlementPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.AdvancePaymentPayPage,
      page: () => AdvancePaymentPayPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),

    GetPage(
      name: PageName.CustomerListPage,
      page: () => CustomerListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.B2bCustomerListPage,
      page: () => B2bCustomerListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.B2bCouponPage,
      page: () => CouponPage(),
      binding: CouponBinding(),
      middlewares: [AuthMiddleware()],
    ),



    GetPage(
      name: PageName.CustomerInfoPage,
      page: () => CustomerInfoPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.B2bCustomerInfoPage,
      page: () => B2bCustomerInfoPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CustomerSearchPage,
      page: () => CustomerSearchPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.B2bCustomerSearchPage,
      page: () => B2bCustomerSearchPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.VehicleOrderPage,
      page: () => VehicleOrderPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.VehicleCartsPage,
      page: () => VehicleCartsPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.AddressListPage,
      page: () => AddressListDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.AddressEditPage,
      page: () => AddressEditDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    //库存
    GetPage(
      name: PageName.InventoryDocumentInListPage,
      page: () => InventoryDocumentInListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryDocumentInDetailPage,
      page: () => InventoryDocumentInDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    //出入库共用创建页面
    GetPage(
      name: PageName.InventoryCreateDocumentOutPage,
      page: () => InventoryCreateDocumentOutPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryDocumentOutListPage,
      page: () => InventoryDocumentOutListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryDocumentOutDetailPage,
      page: () => InventoryDocumentOutDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryCreateOutPage,
      page: () => InventoryCreateOutPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryAllocateListPage,
      page: () => InventoryAllocateListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryAllocateSearchPage,
      page: () => InventoryAllocateSearchPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryAllocateCreatePage,
      page: () => InventoryAllocateCreatePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryAllocateOperationPage,
      page: () => InventoryAllocateOperationPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryAllocateDetailPage,
      page: () => InventoryAllocateDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryCheckListPage,
      page: () => InventoryCheckListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryCheckDetailPage,
      page: () => InventoryCheckDetailPage(),
      middlewares: [AuthMiddleware()],
    ),


    // B2B 相关页面
    GetPage(
      name: PageName.B2bChooseTypePage,
      page: () => B2bChooseTypePage(),
      middlewares: [AuthMiddleware()],
    ),

    // B2B 相关页面



    GetPage(
      name: PageName.InventoryCheckCreatePage,
      page: () => InventoryCheckCreatePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventorySearchPage,
      page: () => InventorySearchPage(),
      middlewares: [AuthMiddleware()],
    ),
    //车销补货
    GetPage(
      name: PageName.ReplenishmentPage,
      page: () => ReplenishmentPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.PlaceOrderPage,
      page: () => PlaceOrderPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ReplenishmentAddPage,
      page: () => ReplenishmentAddPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ReplenishmentDocumentPage,
      page: () => ReplenishmentDocumentPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ReplenishmentDocumentDetailPage,
      page: () => ReplenishmentDocumentDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.BackDocumentPage,
      page: () => BackDocumentPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CheckDocumentPage,
      page: () => CheckDocumentPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CheckDetailPage,
      page: () => CheckDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CheckSearchPage,
      page: () => CheckSearchPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: PageName.SubmitAccountPage,
      page: () => SubmitAccountPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DistributionPage,
      page: () => DistributionPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DistributionDetailPage,
      page: () => DistributionDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.SubmitAccountDetailPage,
      page: () => SubmitAccountDetailPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.CloudCommerceUnloadPage,
      page: () => CloudCommerceUnloadPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CloudCommerceOrderPage,
      page: () => CloudCommerceOrderPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DisplayManagePage,
      page: () => DisplayManagePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ManagePlanPage,
      page: () => ManagePlanPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CashRecordPage,
      page: () => CashRecordPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CustomerBindEmployeeListPage,
      page: () => CustomerBindEmployeeListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.StoreInfoPage,
      page: () => StoreInfoView(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.B2bStoreInfoPage,
      page: () => B2bStoreInfoView(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.VehicleSettlementPage,
      page: () => VehicleSettlementPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.VehicleExchangeInfoPage,
      page: () => VehicleExchangeInfoPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.VehiclePayWayPage,
      page: () => vehiclePayWayPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.vehiclePayStateusPage,
      page: () => vehiclePayStateusPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.BossReportFormPage,
      page: () => BossReportFormPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.BossDetailPage,
      page: () => BossDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.SalesDetailPage,
      page: () => SalesDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.SalesItemDetailPage,
      page: () => SalesItemDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.HomeReportPage,
      page: () => HomeReportPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.PrintSettingPage,
      page: () => PrintSettingPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.BluetoothDevicePage,
      page: () => BluetoothDevicePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.todayCollectionPage,
      page: () => TodayCollectionPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.MissionSystemListPage,
      page: () => MissionSystemListPage(),
      middlewares: [AuthMiddleware()],
      customTransition: MyRouterEnterDirRightToLft(),
      transitionDuration: const Duration(milliseconds: 370),
    ),
    GetPage(
      name: PageName.StoreMapPage,
      page: () => StoreMapPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.PrepareDeliveryDeatilPage,
      page: () => PrepareDeliveryDeatilPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DeliveryingDetailPage,
      page: () => DeliveryingDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InputOderDetailPage,
      page: () => InputOderDetailPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DiscrepancyInputPage,
      page: () => DiscrepancyInputPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: PageName.SettleAccountPage,
      page: () => SettleAccountPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.LgisticsPaywayPage,
      page: () => LgisticsPaywayPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.LogisticsHomePage,
      page: () => LogisticsHomePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ChangeAccountListPage,
      page: () => ChangeAccountListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.AccountManagePage,
      page: () => AccountManagePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ChangePhoneNumnerPage,
      page: () => ChangePhoneNumnerPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.BindPhoneNumberPage,
      page: () => BindPhoneNumberPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ForgetPasswordPage,
      page: () => ForgetPasswordPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.InventoryInquiryPage,
      page: () => InventoryInquiryPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DisposalGoodsSalePage,
      page: () => DisposalGoodsSalePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.DisposalGoodsCarPage,
      page: () => DisposalGoodsCarPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CloundSelectGoodsPage,
      page: () => CloundSelectGoodsPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ShopPreviewPage,
      page: () => ShopPreviewPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.SelectShopPage,
      page: () => SelectShopPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.MissionCenterPage,
      page: () => MissionCenterPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ExchangeInfo,
      page: () => ExchangeInfoPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ExchangeList,
      page: () => ExchangeListPage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ExchangeChoose,
      page: () => ExchangeChoosePage(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.ExchangeDetails,
      page: () => ExchangeDetailsPage(),
      middlewares: [AuthMiddleware()],
    ),

    // 门店用户管理
    GetPage(
      name: PageName.CustomerUserPage,
      page: () => CustomerUserPage(),
      binding: CustomerUserBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CustomerUserEditPage,
      page: () => CustomerUserEditPage(),
      binding: CustomerUserBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: PageName.CustomerUserAddPage,
      page: () => CustomerUserAddPage(),
      binding: CustomerUserBinding(),
      middlewares: [AuthMiddleware()],
    ),

    // 门店购物车管理
    GetPage(
      name: PageName.StoreCartsPage,
      page: () => StoreCartsPage(),
      binding: StoreCartsBinding(),
      middlewares: [AuthMiddleware()],
    ),
    
  ];
}
