//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler3 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.model.PolylineOptions::showPolylineRangeEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::showPolylineRangeEnabled(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.showPolylineRangeEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isShowPolylineRangeEnable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isShowPolylineRangeEnable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isShowPolylineRangeEnable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setPolylineShowRange", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setPolylineShowRange(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setPolylineShowRange(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPolylineShownRangeBegin", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getPolylineShownRangeBegin(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getPolylineShownRangeBegin();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPolylineShownRangeEnd", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getPolylineShownRangeEnd(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getPolylineShownRangeEnd();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setFootPrintTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setFootPrintTexture(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setFootPrintTexture(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getFootPrintTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getFootPrintTexture(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getFootPrintTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setFootPrintGap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setFootPrintGap(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setFootPrintGap(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getFootPrintGap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getFootPrintGap(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getFootPrintGap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setEraseTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var2 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setEraseTexture(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setEraseTexture(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getEraseTexture(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getEraseTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getEraseVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.getEraseVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setEraseColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setEraseColor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setEraseColor(var1, var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getEraseColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getEraseColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Tile::obtain", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                byte[] var2 = (byte[]) ((Map<String, Object>) __args__).get("var2");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Tile::obtain(" + var0 + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Tile __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.Tile.obtain(var0.intValue(), var1.intValue(), var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setAngle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModel@" + __this__ + "::setAngle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAngle(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::getAngle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModel@" + __this__ + "::getAngle(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAngle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setModelFixedLength", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModel@" + __this__ + "::setModelFixedLength(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setModelFixedLength(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setZoomLimit", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModel@" + __this__ + "::setZoomLimit(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZoomLimit(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Gradient::getColors", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Gradient __this__ = (com.amap.api.maps.model.Gradient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Gradient@" + __this__ + "::getColors(" + "" + ")");
                }
            
                // invoke native method
                int[] __result__ = null;
                try {
                    __result__ = __this__.getColors();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Gradient::getStartPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Gradient __this__ = (com.amap.api.maps.model.Gradient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Gradient@" + __this__ + "::getStartPoints(" + "" + ")");
                }
            
                // invoke native method
                float[] __result__ = null;
                try {
                    __result__ = __this__.getStartPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTile", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // ref
                com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileProvider@" + __this__ + "::getTile(" + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Tile __result__ = null;
                try {
                    __result__ = __this__.getTile(var1.intValue(), var2.intValue(), var3.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTileWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileProvider@" + __this__ + "::getTileWidth(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTileWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTileHeight", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileProvider@" + __this__ + "::getTileHeight(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTileHeight();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getCenter", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::getCenter(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getCenter();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::setCenter(" + var1 + var3 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCenter(var1.doubleValue(), var3.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getIntensity", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::getIntensity(" + "" + ")");
                }
            
                // invoke native method
                Double __result__ = null;
                try {
                    __result__ = __this__.getIntensity();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setIntensity", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::setIntensity(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIntensity(var1.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getIndexes", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::getIndexes(" + "" + ")");
                }
            
                // invoke native method
                int[] __result__ = null;
                try {
                    __result__ = __this__.getIndexes();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setIndexes", (__args__, __methodResult__) -> {
                // args
                // ref arg
                int[] var1 = (int[]) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapItem@" + __this__ + "::setIndexes(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIndexes(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::setTargetPoint", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NaviPara@" + __this__ + "::setTargetPoint(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTargetPoint(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::setNaviStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NaviPara@" + __this__ + "::setNaviStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setNaviStyle(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::getTargetPoint", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NaviPara@" + __this__ + "::getTargetPoint(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getTargetPoint();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::getNaviStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NaviPara@" + __this__ + "::getNaviStyle(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getNaviStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::image", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::image(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.image(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::anchor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::anchor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::position__com_amap_api_maps_model_LatLng__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::position(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.position(var1, var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::position__com_amap_api_maps_model_LatLng__double__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::position(" + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.position(var1, var2.floatValue(), var3.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::positionFromBounds", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::positionFromBounds(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.positionFromBounds(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::bearing", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::bearing(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.bearing(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::transparency", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::transparency(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.transparency(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getImage", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getImage(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getImage();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getLocation", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getLocation(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getLocation();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getHeight", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getHeight(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getHeight();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getBounds", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getBounds(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds __result__ = null;
                try {
                    __result__ = __this__.getBounds();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getBearing", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getBearing(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getBearing();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getTransparency", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getTransparency(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getTransparency();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getAnchorU", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getAnchorU(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorU();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getAnchorV", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::getAnchorV(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorV();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptions@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::textureDrawable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::textureDrawable(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.textureDrawable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::vertexData__String", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::vertexData(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.vertexData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::vertexData__List_double___List_double_", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<Float> var1 = (java.util.List<Float>) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                java.util.List<Float> var2 = (java.util.List<Float>) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::vertexData(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.vertexData(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::position", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::position(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.position(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::angle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::angle(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.angle(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getVertext", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getVertext(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<Float> __result__ = null;
                try {
                    __result__ = __this__.getVertext();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getTextrue", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getTextrue(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<Float> __result__ = null;
                try {
                    __result__ = __this__.getTextrue();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getAngle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getAngle(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAngle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getLatLng", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getLatLng(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getLatLng();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getBitmapDescriptor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getBitmapDescriptor(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getBitmapDescriptor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::setModelFixedLength", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::setModelFixedLength(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.setModelFixedLength(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getModelFixedLength", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getModelFixedLength(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getModelFixedLength();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::title", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::title(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.title(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::snippet", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::snippet(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                try {
                    __result__ = __this__.snippet(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getTitle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getTitle(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getTitle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getSnippet", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GL3DModelOptions@" + __this__ + "::getSnippet(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getSnippet();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setPosition(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPosition(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getPosition(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setDimensions__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setDimensions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDimensions(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setImage", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setImage(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setImage(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setDimensions__double__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setDimensions(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDimensions(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getHeight", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getHeight(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getHeight();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setPositionFromBounds", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setPositionFromBounds(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPositionFromBounds(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getBounds", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getBounds(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds __result__ = null;
                try {
                    __result__ = __this__.getBounds();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setBearing", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setBearing(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setBearing(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getBearing", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getBearing(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getBearing();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setTransparency", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::setTransparency(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTransparency(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getTransparency", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::getTransparency(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getTransparency();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlay@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSmoothColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getSmoothColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getSmoothColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSmoothColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setSmoothColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSmoothColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSlowColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getSlowColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getSlowColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSlowColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setSlowColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSlowColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getCongestedColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getCongestedColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getCongestedColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setCongestedColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setCongestedColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCongestedColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSeriousCongestedColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getSeriousCongestedColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getSeriousCongestedColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSeriousCongestedColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setSeriousCongestedColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSeriousCongestedColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getRatio", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getRatio(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getRatio();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setRatio", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setRatio(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRatio(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getTrafficRoadBackgroundColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::getTrafficRoadBackgroundColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTrafficRoadBackgroundColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setTrafficRoadBackgroundColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyTrafficStyle@" + __this__ + "::setTrafficRoadBackgroundColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTrafficRoadBackgroundColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::fromLatLngZoom", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CameraPosition::fromLatLngZoom(" + var0 + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CameraPosition __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.CameraPosition.fromLatLngZoom(var0, var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::builder", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CameraPosition::builder(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.CameraPosition.builder();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::builder__com_amap_api_maps_model_CameraPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CameraPosition var0 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CameraPosition::builder(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.CameraPosition.builder(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptionsCreator __this__ = (com.amap.api.maps.model.TextOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::getCenter", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PoiPara@" + __this__ + "::getCenter(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getCenter();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::setCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PoiPara@" + __this__ + "::setCenter(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCenter(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::getKeywords", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PoiPara@" + __this__ + "::getKeywords(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getKeywords();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::setKeywords", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PoiPara@" + __this__ + "::setKeywords(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setKeywords(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::icons", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::icons(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.icons(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::rotatingIcons", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::rotatingIcons(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.rotatingIcons(var1, var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAngleOffset", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getAngleOffset(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAngleOffset();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isRotatingMode", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isRotatingMode(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isRotatingMode();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getIcons", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getIcons(" + "" + ")");
                }
            
                // invoke native method
                java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> __result__ = null;
                try {
                    __result__ = __this__.getIcons();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::period", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::period(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.period(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getPeriod", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getPeriod(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getPeriod();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isPerspective", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isPerspective(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isPerspective();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::perspective", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::perspective(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.perspective(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::position", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::position(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.position(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setFlat", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::setFlat(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.setFlat(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::icon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::icon(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.icon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::anchor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::anchor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setInfoWindowOffset", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::setInfoWindowOffset(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.setInfoWindowOffset(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::title", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::title(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.title(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::snippet", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::snippet(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.snippet(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::draggable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::draggable(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.draggable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setGps", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::setGps(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.setGps(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getPosition(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getTitle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getTitle(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getTitle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getSnippet", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getSnippet(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getSnippet();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getIcon", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getIcon(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getIcon();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAnchorU", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getAnchorU(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorU();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getInfoWindowOffsetX", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getInfoWindowOffsetX(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getInfoWindowOffsetX();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getInfoWindowOffsetY", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getInfoWindowOffsetY(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getInfoWindowOffsetY();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAnchorV", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getAnchorV(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorV();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isDraggable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isDraggable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isDraggable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isGps", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isGps(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isGps();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isFlat", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isFlat(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isFlat();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::alpha", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::alpha(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.alpha(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAlpha", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getAlpha(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAlpha();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::autoOverturnInfoWindow", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::autoOverturnInfoWindow(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.autoOverturnInfoWindow(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isInfoWindowAutoOverturn", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isInfoWindowAutoOverturn(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isInfoWindowAutoOverturn();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::displayLevel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::displayLevel(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.displayLevel(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getDisplayLevel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getDisplayLevel(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getDisplayLevel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::rotateAngle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::rotateAngle(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.rotateAngle(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getRotateAngle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::getRotateAngle(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getRotateAngle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::infoWindowEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::infoWindowEnable(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.infoWindowEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isInfoWindowEnable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isInfoWindowEnable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isInfoWindowEnable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::belowMaskLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::belowMaskLayer(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.belowMaskLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isBelowMaskLayer", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::isBelowMaskLayer(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isBelowMaskLayer();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MarkerOptions@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setPoints", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setPoints(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPoints(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setWidth", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setWidth(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setWidth(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setTopColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setTopColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTopColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getTopColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getTopColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTopColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setSideColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setSideColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSideColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getSideColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getSideColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getSideColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::set3DModel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::set3DModel(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.set3DModel(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::is3DModel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrow@" + __this__ + "::is3DModel(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.is3DModel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.AnimationSet::addAnimation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.AnimationSet __this__ = (com.amap.api.maps.model.animation.AnimationSet) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.AnimationSet@" + __this__ + "::addAnimation(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addAnimation(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.AnimationSet::cleanAnimation", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.animation.AnimationSet __this__ = (com.amap.api.maps.model.animation.AnimationSet) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.AnimationSet@" + __this__ + "::cleanAnimation(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.cleanAnimation();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setAnimationListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation.AnimationListener var1 = (com.amap.api.maps.model.animation.Animation.AnimationListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::setAnimationListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAnimationListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setDuration", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::setDuration(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDuration(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setFillMode", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::setFillMode(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFillMode(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getFillMode", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::getFillMode(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFillMode();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setRepeatCount", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::setRepeatCount(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRepeatCount(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setRepeatMode", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::setRepeatMode(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRepeatMode(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getRepeatMode", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::getRepeatMode(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getRepeatMode();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getRepeatCount", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::getRepeatCount(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getRepeatCount();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::resetUpdateFlags", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.animation.Animation@" + __this__ + "::resetUpdateFlags(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.resetUpdateFlags();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::builder", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds::builder(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds.Builder __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.LatLngBounds.builder();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::contains__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds@" + __this__ + "::contains(" + var1 + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.contains(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::contains__com_amap_api_maps_model_LatLngBounds", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds@" + __this__ + "::contains(" + var1 + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.contains(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::intersects", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds@" + __this__ + "::intersects(" + var1 + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.intersects(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::including", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds@" + __this__ + "::including(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds __result__ = null;
                try {
                    __result__ = __this__.including(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleDataPath", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::getStyleDataPath(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getStyleDataPath();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleDataPath", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::setStyleDataPath(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                try {
                    __result__ = __this__.setStyleDataPath(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleTexturePath", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::getStyleTexturePath(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getStyleTexturePath();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleTexturePath", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::setStyleTexturePath(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                try {
                    __result__ = __this__.setStyleTexturePath(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleData", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::getStyleData(" + "" + ")");
                }
            
                // invoke native method
                byte[] __result__ = null;
                try {
                    __result__ = __this__.getStyleData();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::setStyleData(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                try {
                    __result__ = __this__.setStyleData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleTextureData", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::getStyleTextureData(" + "" + ")");
                }
            
                // invoke native method
                byte[] __result__ = null;
                try {
                    __result__ = __this__.getStyleTextureData();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleTextureData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::setStyleTextureData(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                try {
                    __result__ = __this__.setStyleTextureData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CustomMapStyleOptions@" + __this__ + "::getStyleId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getStyleId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
        }};
    }
}
