//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler7.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler7)
- (NSDictionary<NSString*, Handler>*) getSubHandler7 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"RefClass::isKindOfMAUserLocationRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAUserLocationRepresentation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMABaseOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MABaseOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMapView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMapView class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOverlayPathRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOverlayPathRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAGroundOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAGroundOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACustomBuildingOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACustomBuildingOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"ObjectFactory::createMAOfflineCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineCity");
            }
        
            MAOfflineCity* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineCity alloc] init];
            } else {
                __this__ = [MAOfflineCity alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineItemNationWide": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineItemNationWide");
            }
        
            MAOfflineItemNationWide* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineItemNationWide alloc] init];
            } else {
                __this__ = [MAOfflineItemNationWide alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiPoint");
            }
        
            MAMultiPoint* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiPoint alloc] init];
            } else {
                __this__ = [MAMultiPoint alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAGroundOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAGroundOverlay");
            }
        
            MAGroundOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAGroundOverlay alloc] init];
            } else {
                __this__ = [MAGroundOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPolygonRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPolygonRenderer");
            }
        
            MAPolygonRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPolygonRenderer alloc] init];
            } else {
                __this__ = [MAPolygonRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPinAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPinAnnotationView");
            }
        
            MAPinAnnotationView* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPinAnnotationView alloc] init];
            } else {
                __this__ = [MAPinAnnotationView alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapNode");
            }
        
            MAHeatMapNode* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapNode alloc] init];
            } else {
                __this__ = [MAHeatMapNode alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapGradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapGradient");
            }
        
            MAHeatMapGradient* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapGradient alloc] init];
            } else {
                __this__ = [MAHeatMapGradient alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapTileOverlay");
            }
        
            MAHeatMapTileOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapTileOverlay alloc] init];
            } else {
                __this__ = [MAHeatMapTileOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapStatus": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapStatus");
            }
        
            MAMapStatus* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMapStatus alloc] init];
            } else {
                __this__ = [MAMapStatus alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPointAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPointAnnotation");
            }
        
            MAPointAnnotation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPointAnnotation alloc] init];
            } else {
                __this__ = [MAPointAnnotation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACircle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACircle");
            }
        
            MACircle* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACircle alloc] init];
            } else {
                __this__ = [MACircle alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAArcRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAArcRenderer");
            }
        
            MAArcRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAArcRenderer alloc] init];
            } else {
                __this__ = [MAArcRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineMapViewController": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineMapViewController");
            }
        
            MAOfflineMapViewController* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineMapViewController alloc] init];
            } else {
                __this__ = [MAOfflineMapViewController alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapCustomStyleOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapCustomStyleOptions");
            }
        
            MAMapCustomStyleOptions* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMapCustomStyleOptions alloc] init];
            } else {
                __this__ = [MAMapCustomStyleOptions alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPolygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPolygon");
            }
        
            MAPolygon* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPolygon alloc] init];
            } else {
                __this__ = [MAPolygon alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleOverlay");
            }
        
            MAParticleOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleOverlay alloc] init];
            } else {
                __this__ = [MAParticleOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPolyline");
            }
        
            MAPolyline* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPolyline alloc] init];
            } else {
                __this__ = [MAPolyline alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiColoredPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiColoredPolylineRenderer");
            }
        
            MAMultiColoredPolylineRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiColoredPolylineRenderer alloc] init];
            } else {
                __this__ = [MAMultiColoredPolylineRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAAnimatedAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAAnimatedAnnotation");
            }
        
            MAAnimatedAnnotation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAAnimatedAnnotation alloc] init];
            } else {
                __this__ = [MAAnimatedAnnotation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiTexturePolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiTexturePolylineRenderer");
            }
        
            MAMultiTexturePolylineRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiTexturePolylineRenderer alloc] init];
            } else {
                __this__ = [MAMultiTexturePolylineRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorGridNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorGridNode");
            }
        
            MAHeatMapVectorGridNode* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorGridNode alloc] init];
            } else {
                __this__ = [MAHeatMapVectorGridNode alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorGrid": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorGrid");
            }
        
            MAHeatMapVectorGrid* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorGrid alloc] init];
            } else {
                __this__ = [MAHeatMapVectorGrid alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorGridOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorGridOverlayOptions");
            }
        
            MAHeatMapVectorGridOverlayOptions* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorGridOverlayOptions alloc] init];
            } else {
                __this__ = [MAHeatMapVectorGridOverlayOptions alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorGridOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorGridOverlay");
            }
        
            MAHeatMapVectorGridOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorGridOverlay alloc] init];
            } else {
                __this__ = [MAHeatMapVectorGridOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineProvince": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineProvince");
            }
        
            MAOfflineProvince* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineProvince alloc] init];
            } else {
                __this__ = [MAOfflineProvince alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorOverlayRender": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorOverlayRender");
            }
        
            MAHeatMapVectorOverlayRender* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorOverlayRender alloc] init];
            } else {
                __this__ = [MAHeatMapVectorOverlayRender alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATileOverlayRenderer");
            }
        
            MATileOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATileOverlayRenderer alloc] init];
            } else {
                __this__ = [MATileOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineItem");
            }
        
            MAOfflineItem* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineItem alloc] init];
            } else {
                __this__ = [MAOfflineItem alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAGeodesicPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAGeodesicPolyline");
            }
        
            MAGeodesicPolyline* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAGeodesicPolyline alloc] init];
            } else {
                __this__ = [MAGeodesicPolyline alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATouchPoi": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATouchPoi");
            }
        
            MATouchPoi* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATouchPoi alloc] init];
            } else {
                __this__ = [MATouchPoi alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPathShowRange": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPathShowRange");
            }
        
            MAPathShowRange data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAPathShowRange)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMVTTileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMVTTileOverlayRenderer");
            }
        
            MAMVTTileOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMVTTileOverlayRenderer alloc] init];
            } else {
                __this__ = [MAMVTTileOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineItemMunicipality": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineItemMunicipality");
            }
        
            MAOfflineItemMunicipality* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineItemMunicipality alloc] init];
            } else {
                __this__ = [MAOfflineItemMunicipality alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorGridOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorGridOverlayRenderer");
            }
        
            MAHeatMapVectorGridOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorGridOverlayRenderer alloc] init];
            } else {
                __this__ = [MAHeatMapVectorGridOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiPolyline");
            }
        
            MAMultiPolyline* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiPolyline alloc] init];
            } else {
                __this__ = [MAMultiPolyline alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATraceManager": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATraceManager");
            }
        
            MATraceManager* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATraceManager alloc] init];
            } else {
                __this__ = [MATraceManager alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiPointOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiPointOverlayRenderer");
            }
        
            MAMultiPointOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiPointOverlayRenderer alloc] init];
            } else {
                __this__ = [MAMultiPointOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAIndoorFloorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAIndoorFloorInfo");
            }
        
            MAIndoorFloorInfo* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAIndoorFloorInfo alloc] init];
            } else {
                __this__ = [MAIndoorFloorInfo alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAIndoorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAIndoorInfo");
            }
        
            MAIndoorInfo* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAIndoorInfo alloc] init];
            } else {
                __this__ = [MAIndoorInfo alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAPolylineRenderer");
            }
        
            MAPolylineRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAPolylineRenderer alloc] init];
            } else {
                __this__ = [MAPolylineRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAAnnotationMoveAnimation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAAnnotationMoveAnimation");
            }
        
            MAAnnotationMoveAnimation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAAnnotationMoveAnimation alloc] init];
            } else {
                __this__ = [MAAnnotationMoveAnimation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAShape": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAShape");
            }
        
            MAShape* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAShape alloc] init];
            } else {
                __this__ = [MAShape alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAAnnotationView");
            }
        
            MAAnnotationView* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAAnnotationView alloc] init];
            } else {
                __this__ = [MAAnnotationView alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATileOverlay");
            }
        
            MATileOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATileOverlay alloc] init];
            } else {
                __this__ = [MATileOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATileOverlayPath": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATileOverlayPath");
            }
        
            MATileOverlayPath data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MATileOverlayPath)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACustomCalloutView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACustomCalloutView");
            }
        
            MACustomCalloutView* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACustomCalloutView alloc] init];
            } else {
                __this__ = [MACustomCalloutView alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineItemCommonCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineItemCommonCity");
            }
        
            MAOfflineItemCommonCity* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineItemCommonCity alloc] init];
            } else {
                __this__ = [MAOfflineItemCommonCity alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOfflineMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOfflineMap");
            }
        
            MAOfflineMap* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOfflineMap alloc] init];
            } else {
                __this__ = [MAOfflineMap alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACircleRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACircleRenderer");
            }
        
            MACircleRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACircleRenderer alloc] init];
            } else {
                __this__ = [MACircleRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleOverlayRenderer");
            }
        
            MAParticleOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleOverlayRenderer alloc] init];
            } else {
                __this__ = [MAParticleOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACoordinateBounds": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACoordinateBounds");
            }
        
            MACoordinateBounds data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateBounds)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACoordinateSpan": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACoordinateSpan");
            }
        
            MACoordinateSpan data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateSpan)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACoordinateRegion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACoordinateRegion");
            }
        
            MACoordinateRegion data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapPoint");
            }
        
            MAMapPoint data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapPoint)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapSize");
            }
        
            MAMapSize data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapSize)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapRect");
            }
        
            MAMapRect data;
        
            NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapRect)];
        
            methodResult(dataValue);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleRandomVelocityGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleRandomVelocityGenerate");
            }
        
            MAParticleRandomVelocityGenerate* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleRandomVelocityGenerate alloc] init];
            } else {
                __this__ = [MAParticleRandomVelocityGenerate alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleRandomColorGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleRandomColorGenerate");
            }
        
            MAParticleRandomColorGenerate* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleRandomColorGenerate alloc] init];
            } else {
                __this__ = [MAParticleRandomColorGenerate alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleConstantRotationGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleConstantRotationGenerate");
            }
        
            MAParticleConstantRotationGenerate* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleConstantRotationGenerate alloc] init];
            } else {
                __this__ = [MAParticleConstantRotationGenerate alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleCurveSizeGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleCurveSizeGenerate");
            }
        
            MAParticleCurveSizeGenerate* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleCurveSizeGenerate alloc] init];
            } else {
                __this__ = [MAParticleCurveSizeGenerate alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleEmissionModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleEmissionModuleOC");
            }
        
            MAParticleEmissionModuleOC* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleEmissionModuleOC alloc] init];
            } else {
                __this__ = [MAParticleEmissionModuleOC alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleSinglePointShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleSinglePointShapeModule");
            }
        
            MAParticleSinglePointShapeModule* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleSinglePointShapeModule alloc] init];
            } else {
                __this__ = [MAParticleSinglePointShapeModule alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleRectShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleRectShapeModule");
            }
        
            MAParticleRectShapeModule* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleRectShapeModule alloc] init];
            } else {
                __this__ = [MAParticleRectShapeModule alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleOverLifeModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleOverLifeModuleOC");
            }
        
            MAParticleOverLifeModuleOC* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleOverLifeModuleOC alloc] init];
            } else {
                __this__ = [MAParticleOverLifeModuleOC alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleOverlayOptions");
            }
        
            MAParticleOverlayOptions* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleOverlayOptions alloc] init];
            } else {
                __this__ = [MAParticleOverlayOptions alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAParticleOverlayOptionsFactory": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAParticleOverlayOptionsFactory");
            }
        
            MAParticleOverlayOptionsFactory* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAParticleOverlayOptionsFactory alloc] init];
            } else {
                __this__ = [MAParticleOverlayOptionsFactory alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMVTTileOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMVTTileOverlayOptions");
            }
        
            MAMVTTileOverlayOptions* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMVTTileOverlayOptions alloc] init];
            } else {
                __this__ = [MAMVTTileOverlayOptions alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMVTTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMVTTileOverlay");
            }
        
            MAMVTTileOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMVTTileOverlay alloc] init];
            } else {
                __this__ = [MAMVTTileOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOverlayRenderer");
            }
        
            MAOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOverlayRenderer alloc] init];
            } else {
                __this__ = [MAOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAUserLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAUserLocation");
            }
        
            MAUserLocation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAUserLocation alloc] init];
            } else {
                __this__ = [MAUserLocation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorNode");
            }
        
            MAHeatMapVectorNode* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorNode alloc] init];
            } else {
                __this__ = [MAHeatMapVectorNode alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorItem");
            }
        
            MAHeatMapVectorItem* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorItem alloc] init];
            } else {
                __this__ = [MAHeatMapVectorItem alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorOverlayOptions");
            }
        
            MAHeatMapVectorOverlayOptions* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorOverlayOptions alloc] init];
            } else {
                __this__ = [MAHeatMapVectorOverlayOptions alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAHeatMapVectorOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAHeatMapVectorOverlay");
            }
        
            MAHeatMapVectorOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAHeatMapVectorOverlay alloc] init];
            } else {
                __this__ = [MAHeatMapVectorOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiPointItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiPointItem");
            }
        
            MAMultiPointItem* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiPointItem alloc] init];
            } else {
                __this__ = [MAMultiPointItem alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMultiPointOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMultiPointOverlay");
            }
        
            MAMultiPointOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMultiPointOverlay alloc] init];
            } else {
                __this__ = [MAMultiPointOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACustomBuildingOverlayOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACustomBuildingOverlayOption");
            }
        
            MACustomBuildingOverlayOption* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACustomBuildingOverlayOption alloc] init];
            } else {
                __this__ = [MACustomBuildingOverlayOption alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACustomBuildingOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACustomBuildingOverlay");
            }
        
            MACustomBuildingOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACustomBuildingOverlay alloc] init];
            } else {
                __this__ = [MACustomBuildingOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATracePoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATracePoint");
            }
        
            MATracePoint* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATracePoint alloc] init];
            } else {
                __this__ = [MATracePoint alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMATraceLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMATraceLocation");
            }
        
            MATraceLocation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MATraceLocation alloc] init];
            } else {
                __this__ = [MATraceLocation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAArc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAArc");
            }
        
            MAArc* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAArc alloc] init];
            } else {
                __this__ = [MAArc alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAUserLocationRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAUserLocationRepresentation");
            }
        
            MAUserLocationRepresentation* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAUserLocationRepresentation alloc] init];
            } else {
                __this__ = [MAUserLocationRepresentation alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMABaseOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMABaseOverlay");
            }
        
            MABaseOverlay* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MABaseOverlay alloc] init];
            } else {
                __this__ = [MABaseOverlay alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAMapView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAMapView");
            }
        
            MAMapView* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAMapView alloc] init];
            } else {
                __this__ = [MAMapView alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAOverlayPathRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAOverlayPathRenderer");
            }
        
            MAOverlayPathRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAOverlayPathRenderer alloc] init];
            } else {
                __this__ = [MAOverlayPathRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMAGroundOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMAGroundOverlayRenderer");
            }
        
            MAGroundOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MAGroundOverlayRenderer alloc] init];
            } else {
                __this__ = [MAGroundOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::createMACustomBuildingOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"ObjectFactory::createMACustomBuildingOverlayRenderer");
            }
        
            MACustomBuildingOverlayRenderer* __this__;
            if ([((NSDictionary<NSString*, id>*) args)[@"init"] boolValue]) {
                __this__ = [[MACustomBuildingOverlayRenderer alloc] init];
            } else {
                __this__ = [MACustomBuildingOverlayRenderer alloc];
            }
        
            methodResult(__this__);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineCity* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineCity alloc] init];
                } else {
                    __this__ = [MAOfflineCity alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineItemNationWide": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineItemNationWide* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineItemNationWide alloc] init];
                } else {
                    __this__ = [MAOfflineItemNationWide alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiPoint* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiPoint alloc] init];
                } else {
                    __this__ = [MAMultiPoint alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAGroundOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAGroundOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAGroundOverlay alloc] init];
                } else {
                    __this__ = [MAGroundOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPolygonRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPolygonRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPolygonRenderer alloc] init];
                } else {
                    __this__ = [MAPolygonRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPinAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPinAnnotationView* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPinAnnotationView alloc] init];
                } else {
                    __this__ = [MAPinAnnotationView alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapNode* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapNode alloc] init];
                } else {
                    __this__ = [MAHeatMapNode alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapGradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapGradient* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapGradient alloc] init];
                } else {
                    __this__ = [MAHeatMapGradient alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapTileOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapTileOverlay alloc] init];
                } else {
                    __this__ = [MAHeatMapTileOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapStatus": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMapStatus* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMapStatus alloc] init];
                } else {
                    __this__ = [MAMapStatus alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPointAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPointAnnotation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPointAnnotation alloc] init];
                } else {
                    __this__ = [MAPointAnnotation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACircle": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACircle* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACircle alloc] init];
                } else {
                    __this__ = [MACircle alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAArcRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAArcRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAArcRenderer alloc] init];
                } else {
                    __this__ = [MAArcRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineMapViewController": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineMapViewController* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineMapViewController alloc] init];
                } else {
                    __this__ = [MAOfflineMapViewController alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapCustomStyleOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMapCustomStyleOptions* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMapCustomStyleOptions alloc] init];
                } else {
                    __this__ = [MAMapCustomStyleOptions alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPolygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPolygon* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPolygon alloc] init];
                } else {
                    __this__ = [MAPolygon alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleOverlay alloc] init];
                } else {
                    __this__ = [MAParticleOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPolyline* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPolyline alloc] init];
                } else {
                    __this__ = [MAPolyline alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiColoredPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiColoredPolylineRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiColoredPolylineRenderer alloc] init];
                } else {
                    __this__ = [MAMultiColoredPolylineRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAAnimatedAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAAnimatedAnnotation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAAnimatedAnnotation alloc] init];
                } else {
                    __this__ = [MAAnimatedAnnotation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiTexturePolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiTexturePolylineRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiTexturePolylineRenderer alloc] init];
                } else {
                    __this__ = [MAMultiTexturePolylineRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorGridNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorGridNode* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorGridNode alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorGridNode alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorGrid": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorGrid* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorGrid alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorGrid alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorGridOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorGridOverlayOptions* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorGridOverlayOptions alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorGridOverlayOptions alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorGridOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorGridOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorGridOverlay alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorGridOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineProvince": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineProvince* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineProvince alloc] init];
                } else {
                    __this__ = [MAOfflineProvince alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorOverlayRender": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorOverlayRender* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorOverlayRender alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorOverlayRender alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATileOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATileOverlayRenderer alloc] init];
                } else {
                    __this__ = [MATileOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineItem* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineItem alloc] init];
                } else {
                    __this__ = [MAOfflineItem alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAGeodesicPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAGeodesicPolyline* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAGeodesicPolyline alloc] init];
                } else {
                    __this__ = [MAGeodesicPolyline alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATouchPoi": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATouchPoi* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATouchPoi alloc] init];
                } else {
                    __this__ = [MATouchPoi alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPathShowRange": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MAPathShowRange data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAPathShowRange)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMVTTileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMVTTileOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMVTTileOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAMVTTileOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineItemMunicipality": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineItemMunicipality* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineItemMunicipality alloc] init];
                } else {
                    __this__ = [MAOfflineItemMunicipality alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorGridOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorGridOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorGridOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorGridOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiPolyline* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiPolyline alloc] init];
                } else {
                    __this__ = [MAMultiPolyline alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATraceManager": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATraceManager* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATraceManager alloc] init];
                } else {
                    __this__ = [MATraceManager alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiPointOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiPointOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiPointOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAMultiPointOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAIndoorFloorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAIndoorFloorInfo* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAIndoorFloorInfo alloc] init];
                } else {
                    __this__ = [MAIndoorFloorInfo alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAIndoorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAIndoorInfo* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAIndoorInfo alloc] init];
                } else {
                    __this__ = [MAIndoorInfo alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAPolylineRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAPolylineRenderer alloc] init];
                } else {
                    __this__ = [MAPolylineRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAAnnotationMoveAnimation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAAnnotationMoveAnimation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAAnnotationMoveAnimation alloc] init];
                } else {
                    __this__ = [MAAnnotationMoveAnimation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAShape": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAShape* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAShape alloc] init];
                } else {
                    __this__ = [MAShape alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAAnnotationView* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAAnnotationView alloc] init];
                } else {
                    __this__ = [MAAnnotationView alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATileOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATileOverlay alloc] init];
                } else {
                    __this__ = [MATileOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATileOverlayPath": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MATileOverlayPath data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MATileOverlayPath)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACustomCalloutView": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACustomCalloutView* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACustomCalloutView alloc] init];
                } else {
                    __this__ = [MACustomCalloutView alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineItemCommonCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineItemCommonCity* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineItemCommonCity alloc] init];
                } else {
                    __this__ = [MAOfflineItemCommonCity alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOfflineMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOfflineMap* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOfflineMap alloc] init];
                } else {
                    __this__ = [MAOfflineMap alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACircleRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACircleRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACircleRenderer alloc] init];
                } else {
                    __this__ = [MACircleRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAParticleOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACoordinateBounds": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MACoordinateBounds data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateBounds)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACoordinateSpan": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MACoordinateSpan data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateSpan)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACoordinateRegion": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MACoordinateRegion data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MACoordinateRegion)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MAMapPoint data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapPoint)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MAMapSize data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapSize)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSValue*>* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < [(NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"] integerValue]; __i__++) {
                MAMapRect data;
        
                NSValue* dataValue = [NSValue value:&data withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:dataValue];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleRandomVelocityGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleRandomVelocityGenerate* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleRandomVelocityGenerate alloc] init];
                } else {
                    __this__ = [MAParticleRandomVelocityGenerate alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleRandomColorGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleRandomColorGenerate* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleRandomColorGenerate alloc] init];
                } else {
                    __this__ = [MAParticleRandomColorGenerate alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleConstantRotationGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleConstantRotationGenerate* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleConstantRotationGenerate alloc] init];
                } else {
                    __this__ = [MAParticleConstantRotationGenerate alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleCurveSizeGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleCurveSizeGenerate* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleCurveSizeGenerate alloc] init];
                } else {
                    __this__ = [MAParticleCurveSizeGenerate alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleEmissionModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleEmissionModuleOC* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleEmissionModuleOC alloc] init];
                } else {
                    __this__ = [MAParticleEmissionModuleOC alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleSinglePointShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleSinglePointShapeModule* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleSinglePointShapeModule alloc] init];
                } else {
                    __this__ = [MAParticleSinglePointShapeModule alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleRectShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleRectShapeModule* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleRectShapeModule alloc] init];
                } else {
                    __this__ = [MAParticleRectShapeModule alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleOverLifeModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleOverLifeModuleOC* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleOverLifeModuleOC alloc] init];
                } else {
                    __this__ = [MAParticleOverLifeModuleOC alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleOverlayOptions* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleOverlayOptions alloc] init];
                } else {
                    __this__ = [MAParticleOverlayOptions alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAParticleOverlayOptionsFactory": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAParticleOverlayOptionsFactory* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAParticleOverlayOptionsFactory alloc] init];
                } else {
                    __this__ = [MAParticleOverlayOptionsFactory alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMVTTileOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMVTTileOverlayOptions* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMVTTileOverlayOptions alloc] init];
                } else {
                    __this__ = [MAMVTTileOverlayOptions alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMVTTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMVTTileOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMVTTileOverlay alloc] init];
                } else {
                    __this__ = [MAMVTTileOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAUserLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAUserLocation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAUserLocation alloc] init];
                } else {
                    __this__ = [MAUserLocation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorNode* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorNode alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorNode alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorItem* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorItem alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorItem alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorOverlayOptions* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorOverlayOptions alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorOverlayOptions alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAHeatMapVectorOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAHeatMapVectorOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAHeatMapVectorOverlay alloc] init];
                } else {
                    __this__ = [MAHeatMapVectorOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiPointItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiPointItem* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiPointItem alloc] init];
                } else {
                    __this__ = [MAMultiPointItem alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMultiPointOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMultiPointOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMultiPointOverlay alloc] init];
                } else {
                    __this__ = [MAMultiPointOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACustomBuildingOverlayOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACustomBuildingOverlayOption* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACustomBuildingOverlayOption alloc] init];
                } else {
                    __this__ = [MACustomBuildingOverlayOption alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACustomBuildingOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACustomBuildingOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACustomBuildingOverlay alloc] init];
                } else {
                    __this__ = [MACustomBuildingOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATracePoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATracePoint* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATracePoint alloc] init];
                } else {
                    __this__ = [MATracePoint alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMATraceLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MATraceLocation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MATraceLocation alloc] init];
                } else {
                    __this__ = [MATraceLocation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAArc": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAArc* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAArc alloc] init];
                } else {
                    __this__ = [MAArc alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAUserLocationRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAUserLocationRepresentation* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAUserLocationRepresentation alloc] init];
                } else {
                    __this__ = [MAUserLocationRepresentation alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMABaseOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MABaseOverlay* __this__;
                if ([init boolValue]) {
                    __this__ = [[MABaseOverlay alloc] init];
                } else {
                    __this__ = [MABaseOverlay alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAMapView": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAMapView* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAMapView alloc] init];
                } else {
                    __this__ = [MAMapView alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAOverlayPathRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAOverlayPathRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAOverlayPathRenderer alloc] init];
                } else {
                    __this__ = [MAOverlayPathRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMAGroundOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MAGroundOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MAGroundOverlayRenderer alloc] init];
                } else {
                    __this__ = [MAGroundOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"ObjectFactory::create_batchMACustomBuildingOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray<NSObject*>* resultList = [NSMutableArray array];
        
            NSNumber* length = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"length"];
            NSNumber* init = (NSNumber*) ((NSDictionary<NSString*, NSObject*>*) argsBatch)[@"init"];
            for (NSUInteger __i__ = 0; __i__ < [length integerValue]; __i__++) {
                MACustomBuildingOverlayRenderer* __this__;
                if ([init boolValue]) {
                    __this__ = [[MACustomBuildingOverlayRenderer alloc] init];
                } else {
                    __this__ = [MACustomBuildingOverlayRenderer alloc];
                }
                [resultList addObject:__this__];
            }
        
            methodResult(resultList);
        
            if (enableLog) NSLog(@"HEAP: %@", HEAP);
        },
        
        @"MATraceDelegate::createAnonymous__": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            id<MATraceDelegate> __result__ = [[MATraceDelegate_Anonymous alloc] initWithFlutterPluginRegistrar:registrar];
            methodResult(__result__);
        },
        @"MAMultiPointOverlayRendererDelegate::createAnonymous__": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            id<MAMultiPointOverlayRendererDelegate> __result__ = [[MAMultiPointOverlayRendererDelegate_Anonymous alloc] initWithFlutterPluginRegistrar:registrar];
            methodResult(__result__);
        },
        @"MAMapViewDelegate::createAnonymous__": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            id<MAMapViewDelegate> __result__ = [[MAMapViewDelegate_Anonymous alloc] initWithFlutterPluginRegistrar:registrar];
            methodResult(__result__);
        },
        @"MAPathShowRangeMake::MAPathShowRangeMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPathShowRangeMake::MAPathShowRangeMake(%@)", args);
            }
        
            // args
            // jsonable arg
            float begin = [args[@"begin"] floatValue];
            // jsonable arg
            float end = [args[@"end"] floatValue];
        
            // ref
        
        
            // invoke native method
            MAPathShowRange result = MAPathShowRangeMake(begin, end);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAPathShowRange)];
        
            methodResult(__result__);
        },
        @"MACoordinateBoundsMake::MACoordinateBoundsMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateBoundsMake::MACoordinateBoundsMake(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* northEastValue = (NSValue*) args[@"northEast"];
            CLLocationCoordinate2D northEast;
            if (northEastValue != nil && (NSNull*) northEastValue != [NSNull null]) {
              [northEastValue getValue:&northEast];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"northEast不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* southWestValue = (NSValue*) args[@"southWest"];
            CLLocationCoordinate2D southWest;
            if (southWestValue != nil && (NSNull*) southWestValue != [NSNull null]) {
              [southWestValue getValue:&southWest];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"southWest不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MACoordinateBounds result = MACoordinateBoundsMake(northEast, southWest);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateBounds)];
        
            methodResult(__result__);
        },
        @"MACoordinateSpanMake::MACoordinateSpanMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateSpanMake::MACoordinateSpanMake(%@)", args);
            }
        
            // args
            // jsonable arg
            CLLocationDegrees latitudeDelta = [args[@"latitudeDelta"] doubleValue];
            // jsonable arg
            CLLocationDegrees longitudeDelta = [args[@"longitudeDelta"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MACoordinateSpan result = MACoordinateSpanMake(latitudeDelta, longitudeDelta);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateSpan)];
        
            methodResult(__result__);
        },
        @"MACoordinateRegionMake::MACoordinateRegionMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateRegionMake::MACoordinateRegionMake(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
            CLLocationCoordinate2D centerCoordinate;
            if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
              [centerCoordinateValue getValue:&centerCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"centerCoordinate不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* spanValue = (NSValue*) args[@"span"];
            MACoordinateSpan span;
            if (spanValue != nil && (NSNull*) spanValue != [NSNull null]) {
              [spanValue getValue:&span];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"span不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MACoordinateRegion result = MACoordinateRegionMake(centerCoordinate, span);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        @"MACoordinateRegionMakeWithDistance::MACoordinateRegionMakeWithDistance": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateRegionMakeWithDistance::MACoordinateRegionMakeWithDistance(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
            CLLocationCoordinate2D centerCoordinate;
            if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
              [centerCoordinateValue getValue:&centerCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"centerCoordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            CLLocationDistance latitudinalMeters = [args[@"latitudinalMeters"] doubleValue];
            // jsonable arg
            CLLocationDistance longitudinalMeters = [args[@"longitudinalMeters"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MACoordinateRegion result = MACoordinateRegionMakeWithDistance(centerCoordinate, latitudinalMeters, longitudinalMeters);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        @"MAMapPointForCoordinate::MAMapPointForCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapPointForCoordinate::MAMapPointForCoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAMapPoint result = MAMapPointForCoordinate(coordinate);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"MACoordinateForMapPoint::MACoordinateForMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateForMapPoint::MACoordinateForMapPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapPointValue = (NSValue*) args[@"mapPoint"];
            MAMapPoint mapPoint;
            if (mapPointValue != nil && (NSNull*) mapPointValue != [NSNull null]) {
              [mapPointValue getValue:&mapPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapPoint不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            CLLocationCoordinate2D result = MACoordinateForMapPoint(mapPoint);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        @"MACoordinateRegionForMapRect::MACoordinateRegionForMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACoordinateRegionForMapRect::MACoordinateRegionForMapRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MACoordinateRegion result = MACoordinateRegionForMapRect(rect);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        @"MAMapRectForCoordinateRegion::MAMapRectForCoordinateRegion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectForCoordinateRegion::MAMapRectForCoordinateRegion(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* regionValue = (NSValue*) args[@"region"];
            MACoordinateRegion region;
            if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
              [regionValue getValue:&region];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"region不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAMapRect result = MAMapRectForCoordinateRegion(region);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMetersPerMapPointAtLatitude::MAMetersPerMapPointAtLatitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMetersPerMapPointAtLatitude::MAMetersPerMapPointAtLatitude(%@)", args);
            }
        
            // args
            // jsonable arg
            CLLocationDegrees latitude = [args[@"latitude"] doubleValue];
        
            // ref
        
        
            // invoke native method
            CLLocationDistance result = MAMetersPerMapPointAtLatitude(latitude);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapPointsPerMeterAtLatitude::MAMapPointsPerMeterAtLatitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapPointsPerMeterAtLatitude::MAMapPointsPerMeterAtLatitude(%@)", args);
            }
        
            // args
            // jsonable arg
            CLLocationDegrees latitude = [args[@"latitude"] doubleValue];
        
            // ref
        
        
            // invoke native method
            double result = MAMapPointsPerMeterAtLatitude(latitude);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMetersBetweenMapPoints::MAMetersBetweenMapPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMetersBetweenMapPoints::MAMetersBetweenMapPoints(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* aValue = (NSValue*) args[@"a"];
            MAMapPoint a;
            if (aValue != nil && (NSNull*) aValue != [NSNull null]) {
              [aValue getValue:&a];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"a不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* bValue = (NSValue*) args[@"b"];
            MAMapPoint b;
            if (bValue != nil && (NSNull*) bValue != [NSNull null]) {
              [bValue getValue:&b];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"b不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            CLLocationDistance result = MAMetersBetweenMapPoints(a, b);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAreaBetweenCoordinates::MAAreaBetweenCoordinates": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAreaBetweenCoordinates::MAAreaBetweenCoordinates(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* northEastValue = (NSValue*) args[@"northEast"];
            CLLocationCoordinate2D northEast;
            if (northEastValue != nil && (NSNull*) northEastValue != [NSNull null]) {
              [northEastValue getValue:&northEast];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"northEast不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* southWestValue = (NSValue*) args[@"southWest"];
            CLLocationCoordinate2D southWest;
            if (southWestValue != nil && (NSNull*) southWestValue != [NSNull null]) {
              [southWestValue getValue:&southWest];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"southWest不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAAreaBetweenCoordinates(northEast, southWest);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectInset::MAMapRectInset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectInset::MAMapRectInset(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
            // jsonable arg
            double dx = [args[@"dx"] doubleValue];
            // jsonable arg
            double dy = [args[@"dy"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MAMapRect result = MAMapRectInset(rect, dx, dy);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMapRectUnion::MAMapRectUnion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectUnion::MAMapRectUnion(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rect1Value = (NSValue*) args[@"rect1"];
            MAMapRect rect1;
            if (rect1Value != nil && (NSNull*) rect1Value != [NSNull null]) {
              [rect1Value getValue:&rect1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* rect2Value = (NSValue*) args[@"rect2"];
            MAMapRect rect2;
            if (rect2Value != nil && (NSNull*) rect2Value != [NSNull null]) {
              [rect2Value getValue:&rect2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAMapRect result = MAMapRectUnion(rect1, rect2);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
    };
}

@end
