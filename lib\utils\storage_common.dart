import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class Handler<T> {
  Future<T?> Function() get;
  Future<void> Function(String) set;
  Future<void> Function()? delete;
  Handler({required this.get, required this.set, this.delete});
}

const storage = FlutterSecureStorage();

// 数据持久化仓库
class SecureStorageCommon {
  static Handler save<T>(String key) {
    String storageKey = key;
    return Handler<T>(
      get: () {
        return (storage.read(
              key: storageKey,
            ) ??
            '') as Future<T?>;
      },
      set: (arg) {
        // 这里应该写入你的数据
        return storage.write(
          key: storageKey,
          value: arg,
        );
      },
      delete: () {
        // 这里应该写入你的数据
        return storage.delete(
          key: storageKey,
        );
      },
    );
  }
}
