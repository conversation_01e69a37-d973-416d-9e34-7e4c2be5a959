//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "MAAnnotationViewFactory.h"
#import "AmapMapFluttifyPlugin.h"
#import <objc/runtime.h>
#import "FluttifyMessageCodec.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation MAAnnotationViewFactory {
}

- (instancetype)initWithRegistrar:(NSObject <FlutterPluginRegistrar> *)registrar {
  self = [super init];
  if (self) {
    _registrar = registrar;
  }

  return self;
}

- (NSObject<FlutterMessageCodec>*)createArgsCodec {
  return [FlutterStandardMessageCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]];
}

- (NSObject <FlutterPlatformView> *)createWithFrame:(CGRect)frame viewIdentifier:(int64_t)viewId arguments:(id _Nullable)args {
  return [[MAAnnotationViewPlatformView alloc] initWithViewId:viewId frame: frame registrar:_registrar arguments: args];
}

@end

@implementation MAAnnotationViewPlatformView {
  int64_t _viewId;
  CGRect _frame;
  NSDictionary<NSString *, Handler>* _handlerMap;
  MAAnnotationView* _view;
  id _args;
}

- (instancetype)initWithViewId:(int64_t)viewId frame:(CGRect)frame registrar:(NSObject <FlutterPluginRegistrar> *)registrar arguments:(id _Nullable)args {
  self = [super init];
  if (self) {
    _viewId = viewId;
    _registrar = registrar;
    _frame = frame;
    _args = args;
  }

  return self;
}

- (UIView *)view {
  __weak __typeof(self)weakSelf = self;
  if (_view == nil) {
    NSDictionary<NSString*, id>* params = (NSDictionary<NSString*, id>*) _args;

    _view = [[MAAnnotationView alloc] initWithFrame:_frame];

    ////////////////////////////////初始化UiKitView////////////////////////////////////////

    ////////////////////////////////初始化UiKitView////////////////////////////////////////

    // 这里用一个magic number调整一下id
    // 同时存放viewId和refId的对象, 供后续viewId转refId使用
    HEAP[[NSString stringWithFormat:@"%@", @(2147483647 - _viewId)]] = _view;
    HEAP[[NSString stringWithFormat:@"%@:%@", @"MAAnnotationView", @(_view.hash)]] = _view;
  }
  return _view;
}

@end
