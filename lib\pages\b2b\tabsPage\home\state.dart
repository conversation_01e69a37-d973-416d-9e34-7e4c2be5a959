/*
 * @Date: 2023-12-06 10:17:24
 * @LastEditors: 潘腾龙
 * @LastEditTime: 2024-03-13 19:31:13
 * @FilePath: /tongfu-salesman-app/lib/pages/tabsPage/home/<USER>
 */
import 'package:get/get.dart';

class HomePageState {
  String employeeNo = ''; //业务员编号

  int missionCount = 2;

  ///首页汇总数据（个人业绩）
  dynamic personalDataInfo = {};

  ///首页汇总数据（团队业绩）
  dynamic teamDataInfo = [];
  num teamTotal = 0;

  /// 今日订单金额（元）
  double todayOrderMount = 0.00;

  ///团队业绩的请求参数pageNo
  int pageNo = 0;

  ///团队业绩的请求参数pageSize
  int pageSize = 10;

  ///日期类型（0今日，1，本周，2，本月）
  String? dateType = '0';

  RxBool showShopScan = false.obs;

  // cardList

  ///日期类型（0 个人业绩 1 团队业绩）
  ///
  String? yeJiType = '0';

  HomePageState() {
    // 初始数据
  }
}
