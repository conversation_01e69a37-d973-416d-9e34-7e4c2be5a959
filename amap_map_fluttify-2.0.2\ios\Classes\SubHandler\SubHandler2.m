//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler2.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler2)
- (NSDictionary<NSString*, Handler>*) getSubHandler2 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAHeatMapVectorGridOverlay::get_option": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlay::get_option");
            }
        
            // ref object
            MAHeatMapVectorGridOverlay* ref = (MAHeatMapVectorGridOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorGridOverlayOptions* result = ref.option;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineProvince::get_cities": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineProvince::get_cities");
            }
        
            // ref object
            MAOfflineProvince* ref = (MAOfflineProvince*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = ref.cities;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayRender::get_heatOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayRender::get_heatOverlay");
            }
        
            // ref object
            MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorOverlay* result = ref.heatOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MATileOverlayRenderer::get_tileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayRenderer::get_tileOverlay");
            }
        
            // ref object
            MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MATileOverlay* result = ref.tileOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_name": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_name");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.name;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_jianpin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_jianpin");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.jianpin;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_pinyin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_pinyin");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.pinyin;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_adcode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_adcode");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.adcode;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_size": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_size");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            long long result = ref.size;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_itemStatus": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_itemStatus");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOfflineItemStatus result = ref.itemStatus;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOfflineItem::get_downloadedSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItem::get_downloadedSize");
            }
        
            // ref object
            MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            long long result = ref.downloadedSize;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATouchPoi::get_name": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATouchPoi::get_name");
            }
        
            // ref object
            MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.name;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MATouchPoi::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATouchPoi::get_coordinate");
            }
        
            // ref object
            MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MATouchPoi::get_uid": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATouchPoi::get_uid");
            }
        
            // ref object
            MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.uid;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAPathShowRange::get_begin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPathShowRange::get_begin");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAPathShowRange ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            float result = ref.begin;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPathShowRange::get_end": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPathShowRange::get_end");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAPathShowRange ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            float result = ref.end;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayRenderer::get_heatOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayRenderer::get_heatOverlay");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayRenderer* ref = (MAHeatMapVectorGridOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorGridOverlay* result = ref.heatOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPolyline::get_drawStyleIndexes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPolyline::get_drawStyleIndexes");
            }
        
            // ref object
            MAMultiPolyline* ref = (MAMultiPolyline*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<NSNumber*>* result = ref.drawStyleIndexes;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointOverlayRenderer::get_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::get_icon");
            }
        
            // ref object
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.icon;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointOverlayRenderer::get_pointSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::get_pointSize");
            }
        
            // ref object
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.pointSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAMultiPointOverlayRenderer::get_anchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::get_anchor");
            }
        
            // ref object
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.anchor;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMultiPointOverlayRenderer::get_multiPointOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::get_multiPointOverlay");
            }
        
            // ref object
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiPointOverlay* result = ref.multiPointOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorFloorInfo::get_floorName": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorFloorInfo::get_floorName");
            }
        
            // ref object
            MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.floorName;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorFloorInfo::get_floorIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorFloorInfo::get_floorIndex");
            }
        
            // ref object
            MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            int result = ref.floorIndex;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAIndoorFloorInfo::get_floorNona": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorFloorInfo::get_floorNona");
            }
        
            // ref object
            MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.floorNona;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorFloorInfo::get_isPark": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorFloorInfo::get_isPark");
            }
        
            // ref object
            MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.isPark;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_cnName": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_cnName");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.cnName;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_enName": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_enName");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.enName;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_poiID": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_poiID");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.poiID;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_buildingType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_buildingType");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.buildingType;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_activeFloorIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_activeFloorIndex");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            int result = ref.activeFloorIndex;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_activeFloorInfoIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_activeFloorInfoIndex");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            int result = ref.activeFloorInfoIndex;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_floorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_floorInfo");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = ref.floorInfo;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_numberOfFloor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_numberOfFloor");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            int result = ref.numberOfFloor;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAIndoorInfo::get_numberOfParkFloor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAIndoorInfo::get_numberOfParkFloor");
            }
        
            // ref object
            MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            int result = ref.numberOfParkFloor;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_polyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_polyline");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPolyline* result = ref.polyline;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_is3DArrowLine": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_is3DArrowLine");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.is3DArrowLine;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_sideColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_sideColor");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.sideColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_userInteractionEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_userInteractionEnabled");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.userInteractionEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_hitTestInset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_hitTestInset");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.hitTestInset;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_showRangeEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_showRangeEnabled");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showRangeEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPolylineRenderer::get_showRange": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::get_showRange");
            }
        
            // ref object
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPathShowRange result = ref.showRange;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAPathShowRange)];
        
            methodResult(__result__);
        },
        
        @"MAShape::get_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAShape::get_title");
            }
        
            // ref object
            MAShape* ref = (MAShape*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.title;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAShape::get_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAShape::get_subtitle");
            }
        
            // ref object
            MAShape* ref = (MAShape*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.subtitle;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_reuseIdentifier": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_reuseIdentifier");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.reuseIdentifier;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_zIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_zIndex");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = ref.zIndex;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_annotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_annotation");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            id<MAAnnotation> result = ref.annotation;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_image": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_image");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.image;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_imageView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_imageView");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImageView* result = ref.imageView;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_customCalloutView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_customCalloutView");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACustomCalloutView* result = ref.customCalloutView;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_centerOffset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_centerOffset");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.centerOffset;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_calloutOffset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_calloutOffset");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.calloutOffset;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_isEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_isEnabled");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.enabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_isHighlighted": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_isHighlighted");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.highlighted;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_isSelected": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_isSelected");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.selected;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_canShowCallout": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_canShowCallout");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.canShowCallout;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_leftCalloutAccessoryView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_leftCalloutAccessoryView");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIView* result = ref.leftCalloutAccessoryView;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_rightCalloutAccessoryView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_rightCalloutAccessoryView");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIView* result = ref.rightCalloutAccessoryView;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_isDraggable": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_isDraggable");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.draggable;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_dragState": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_dragState");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAAnnotationViewDragState result = ref.dragState;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnnotationView::get_canAdjustPositon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::get_canAdjustPositon");
            }
        
            // ref object
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.canAdjustPositon;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_tileSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_tileSize");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.tileSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_minimumZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_minimumZ");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = ref.minimumZ;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_maximumZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_maximumZ");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = ref.maximumZ;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_URLTemplate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_URLTemplate");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.URLTemplate;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_canReplaceMapContent": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_canReplaceMapContent");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.canReplaceMapContent;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlay::get_disableOffScreenTileLoading": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::get_disableOffScreenTileLoading");
            }
        
            // ref object
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.disableOffScreenTileLoading;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlayPath::get_x": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::get_x");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            NSInteger result = ref.x;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlayPath::get_y": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::get_y");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            NSInteger result = ref.y;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlayPath::get_contentScaleFactor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::get_contentScaleFactor");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CGFloat result = ref.contentScaleFactor;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlayPath::get_index": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::get_index");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            NSInteger result = ref.index;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATileOverlayPath::get_requestId": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::get_requestId");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            NSInteger result = ref.requestId;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACustomCalloutView::get_customView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomCalloutView::get_customView");
            }
        
            // ref object
            MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIView* result = ref.customView;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomCalloutView::get_userData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomCalloutView::get_userData");
            }
        
            // ref object
            MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSObject* result = ref.userData;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineItemCommonCity::get_province": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItemCommonCity::get_province");
            }
        
            // ref object
            MAOfflineItemCommonCity* ref = (MAOfflineItemCommonCity*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOfflineItem* result = ref.province;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMap::get_provinces": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMap::get_provinces");
            }
        
            // ref object
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAOfflineProvince*>* result = ref.provinces;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMap::get_municipalities": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMap::get_municipalities");
            }
        
            // ref object
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAOfflineItemMunicipality*>* result = ref.municipalities;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMap::get_nationWide": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMap::get_nationWide");
            }
        
            // ref object
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOfflineItemNationWide* result = ref.nationWide;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMap::get_cities": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMap::get_cities");
            }
        
            // ref object
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAOfflineCity*>* result = ref.cities;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMap::get_version": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMap::get_version");
            }
        
            // ref object
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.version;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACircleRenderer::get_circle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACircleRenderer::get_circle");
            }
        
            // ref object
            MACircleRenderer* ref = (MACircleRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACircle* result = ref.circle;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayRenderer::get_particleOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayRenderer::get_particleOverlay");
            }
        
            // ref object
            MAParticleOverlayRenderer* ref = (MAParticleOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleOverlay* result = ref.particleOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACoordinateBounds::get_northEast": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateBounds::get_northEast");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateBounds ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CLLocationCoordinate2D result = ref.northEast;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MACoordinateBounds::get_southWest": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateBounds::get_southWest");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateBounds ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CLLocationCoordinate2D result = ref.southWest;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MACoordinateSpan::get_latitudeDelta": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateSpan::get_latitudeDelta");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateSpan ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CLLocationDegrees result = ref.latitudeDelta;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACoordinateSpan::get_longitudeDelta": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateSpan::get_longitudeDelta");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateSpan ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CLLocationDegrees result = ref.longitudeDelta;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACoordinateRegion::get_center": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateRegion::get_center");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateRegion ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            CLLocationCoordinate2D result = ref.center;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MACoordinateRegion::get_span": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateRegion::get_span");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateRegion ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            MACoordinateSpan result = ref.span;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateSpan)];
        
            methodResult(__result__);
        },
        
        @"MAMapPoint::get_x": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapPoint::get_x");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapPoint ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            double result = ref.x;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapPoint::get_y": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapPoint::get_y");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapPoint ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            double result = ref.y;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapSize::get_width": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapSize::get_width");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapSize ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            double result = ref.width;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapSize::get_height": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapSize::get_height");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapSize ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            double result = ref.height;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapRect::get_origin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapRect::get_origin");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapRect ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            MAMapPoint result = ref.origin;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMapRect::get_size": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapRect::get_size");
            }
        
            // ref object
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapRect ref;
            [dataValue getValue:&ref];
        
            // invoke native method
            MAMapSize result = ref.size;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapSize)];
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_visibile": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_visibile");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.visibile;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_duration");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSTimeInterval result = ref.duration;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_loop": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_loop");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.loop;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_maxParticles": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_maxParticles");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = ref.maxParticles;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_icon");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.icon;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_startParticleSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_startParticleSize");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.startParticleSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleLifeTime": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleLifeTime");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSTimeInterval result = ref.particleLifeTime;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleStartColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleStartColor");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            id<MAParticleColorGenerate> result = ref.particleStartColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleStartSpeed": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleStartSpeed");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            id<MAParticleVelocityGenerate> result = ref.particleStartSpeed;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleEmissionModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleEmissionModule");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleEmissionModuleOC* result = ref.particleEmissionModule;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleShapeModule");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            id<MAParticleShapeModule> result = ref.particleShapeModule;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlayOptions::get_particleOverLifeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::get_particleOverLifeModule");
            }
        
            // ref object
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleOverLifeModuleOC* result = ref.particleOverLifeModule;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMVTTileOverlayOptions::get_url": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::get_url");
            }
        
            // ref object
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.url;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMVTTileOverlayOptions::get_key": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::get_key");
            }
        
            // ref object
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.key;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMVTTileOverlayOptions::get_Id": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::get_Id");
            }
        
            // ref object
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.Id;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMVTTileOverlayOptions::get_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::get_visible");
            }
        
            // ref object
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.visible;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMVTTileOverlay::get_option": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlay::get_option");
            }
        
            // ref object
            MAMVTTileOverlay* ref = (MAMVTTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMVTTileOverlayOptions* result = ref.option;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOverlayRenderer::get_overlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::get_overlay");
            }
        
            // ref object
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            id<MAOverlay> result = ref.overlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOverlayRenderer::get_strokeImage": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::get_strokeImage");
            }
        
            // ref object
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.strokeImage;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOverlayRenderer::get_strokeTextureID": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::get_strokeTextureID");
            }
        
            // ref object
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            GLuint result = ref.strokeTextureID;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayRenderer::get_alpha": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::get_alpha");
            }
        
            // ref object
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.alpha;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayRenderer::get_contentScale": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::get_contentScale");
            }
        
            // ref object
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.contentScale;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocation::get_isUpdating": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocation::get_isUpdating");
            }
        
            // ref object
            MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.updating;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocation::get_location": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocation::get_location");
            }
        
            // ref object
            MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocation* result = ref.location;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAUserLocation::get_heading": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocation::get_heading");
            }
        
            // ref object
            MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLHeading* result = ref.heading;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorNode::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorNode::get_coordinate");
            }
        
            // ref object
            MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorNode::get_weight": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorNode::get_weight");
            }
        
            // ref object
            MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = ref.weight;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorItem::get_center": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorItem::get_center");
            }
        
            // ref object
            MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapPoint result = ref.center;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorItem::get_intensity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorItem::get_intensity");
            }
        
            // ref object
            MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = ref.intensity;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorItem::get_nodeIndices": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorItem::get_nodeIndices");
            }
        
            // ref object
            MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<NSNumber*>* result = ref.nodeIndices;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_type": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_type");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapType result = ref.type;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_visible");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.visible;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_inputNodes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_inputNodes");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAHeatMapVectorNode*>* result = ref.inputNodes;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_size": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_size");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDistance result = ref.size;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_gap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_gap");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.gap;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_colors": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_colors");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<UIColor*>* result = ref.colors;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_startPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_startPoints");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<NSNumber*>* result = ref.startPoints;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_opacity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::get_opacity");
            }
        
            // ref object
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.opacity;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorOverlay::get_option": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlay::get_option");
            }
        
            // ref object
            MAHeatMapVectorOverlay* ref = (MAHeatMapVectorOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorOverlayOptions* result = ref.option;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointItem::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::get_coordinate");
            }
        
            // ref object
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAMultiPointItem::get_customID": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::get_customID");
            }
        
            // ref object
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.customID;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointItem::get_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::get_title");
            }
        
            // ref object
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.title;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointItem::get_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::get_subtitle");
            }
        
            // ref object
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.subtitle;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPointOverlay::get_items": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlay::get_items");
            }
        
            // ref object
            MAMultiPointOverlay* ref = (MAMultiPointOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAMultiPointItem*>* result = ref.items;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayOption::get_height": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::get_height");
            }
        
            // ref object
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.height;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayOption::get_heightScale": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::get_heightScale");
            }
        
            // ref object
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.heightScale;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayOption::get_topColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::get_topColor");
            }
        
            // ref object
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.topColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayOption::get_sideColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::get_sideColor");
            }
        
            // ref object
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.sideColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayOption::get_visibile": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::get_visibile");
            }
        
            // ref object
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.visibile;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlay::get_defaultOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlay::get_defaultOption");
            }
        
            // ref object
            MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACustomBuildingOverlayOption* result = ref.defaultOption;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlay::get_customOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlay::get_customOptions");
            }
        
            // ref object
            MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MACustomBuildingOverlayOption*>* result = ref.customOptions;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MATracePoint::get_latitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATracePoint::get_latitude");
            }
        
            // ref object
            MATracePoint* ref = (MATracePoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDegrees result = ref.latitude;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATracePoint::get_longitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATracePoint::get_longitude");
            }
        
            // ref object
            MATracePoint* ref = (MATracePoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDegrees result = ref.longitude;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATraceLocation::get_loc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::get_loc");
            }
        
            // ref object
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.loc;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MATraceLocation::get_angle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::get_angle");
            }
        
            // ref object
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            double result = ref.angle;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATraceLocation::get_speed": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::get_speed");
            }
        
            // ref object
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            double result = ref.speed;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MATraceLocation::get_time": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::get_time");
            }
        
            // ref object
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            double result = ref.time;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAArc::get_startCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::get_startCoordinate");
            }
        
            // ref object
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.startCoordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAArc::get_passedCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::get_passedCoordinate");
            }
        
            // ref object
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.passedCoordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAArc::get_endCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::get_endCoordinate");
            }
        
            // ref object
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.endCoordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_showsAccuracyRing": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_showsAccuracyRing");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsAccuracyRing;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_showsHeadingIndicator": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_showsHeadingIndicator");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsHeadingIndicator;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_fillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_fillColor");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.fillColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_strokeColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_strokeColor");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.strokeColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_lineWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_lineWidth");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.lineWidth;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_locationDotBgColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_locationDotBgColor");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.locationDotBgColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_locationDotFillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_locationDotFillColor");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.locationDotFillColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_enablePulseAnnimation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_enablePulseAnnimation");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.enablePulseAnnimation;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAUserLocationRepresentation::get_image": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::get_image");
            }
        
            // ref object
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.image;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MABaseOverlay::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MABaseOverlay::get_coordinate");
            }
        
            // ref object
            MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MABaseOverlay::get_boundingMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MABaseOverlay::get_boundingMapRect");
            }
        
            // ref object
            MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = ref.boundingMapRect;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_metalEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_metalEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [MAMapView metalEnabled];
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_terrainEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_terrainEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [MAMapView terrainEnabled];
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_mapType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_mapType");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapType result = ref.mapType;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_centerCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_centerCoordinate");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.centerCoordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_region": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_region");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACoordinateRegion result = ref.region;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_visibleMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_visibleMapRect");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = ref.visibleMapRect;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_limitRegion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_limitRegion");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACoordinateRegion result = ref.limitRegion;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_limitMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_limitMapRect");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = ref.limitMapRect;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_zoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_zoomLevel");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.zoomLevel;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_minZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_minZoomLevel");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.minZoomLevel;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_maxZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_maxZoomLevel");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.maxZoomLevel;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_rotationDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_rotationDegree");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.rotationDegree;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_cameraDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_cameraDegree");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.cameraDegree;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_zoomingInPivotsAroundAnchorPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_zoomingInPivotsAroundAnchorPoint");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.zoomingInPivotsAroundAnchorPoint;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isZoomEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isZoomEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.zoomEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isScrollEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isScrollEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.scrollEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isRotateEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isRotateEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.rotateEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isRotateCameraEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isRotateCameraEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.rotateCameraEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowsBuildings": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowsBuildings");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsBuildings;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowsLabels": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowsLabels");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsLabels;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowTraffic": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowTraffic");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showTraffic;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_touchPOIEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_touchPOIEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.touchPOIEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_showsCompass": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_showsCompass");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsCompass;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_compassOrigin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_compassOrigin");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.compassOrigin;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_compassSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_compassSize");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.compassSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_showsScale": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_showsScale");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsScale;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_scaleOrigin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_scaleOrigin");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.scaleOrigin;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_scaleSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_scaleSize");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.scaleSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_logoCenter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_logoCenter");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.logoCenter;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_logoSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_logoSize");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.logoSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_metersPerPointForCurrentZoom": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_metersPerPointForCurrentZoom");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            double result = ref.metersPerPointForCurrentZoom;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isAbroad": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isAbroad");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.isAbroad;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_maxRenderFrame": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_maxRenderFrame");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSUInteger result = ref.maxRenderFrame;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isAllowDecreaseFrame": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isAllowDecreaseFrame");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.isAllowDecreaseFrame;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_openGLESDisabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_openGLESDisabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.openGLESDisabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
    };
}

@end
