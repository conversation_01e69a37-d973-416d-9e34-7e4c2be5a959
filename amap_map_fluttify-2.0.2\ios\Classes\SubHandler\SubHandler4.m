//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler4.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler4)
- (NSDictionary<NSString*, Handler>*) getSubHandler4 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAOverlayRenderer::get_strokeTextureID_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                GLuint result = ref.strokeTextureID;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayRenderer::get_alpha_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.alpha;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayRenderer::get_contentScale_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.contentScale;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocation::get_isUpdating_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.updating;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocation::get_location_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocation* result = ref.location;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocation::get_heading_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocation* ref = (MAUserLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLHeading* result = ref.heading;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorNode::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorNode::get_weight_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                float result = ref.weight;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorItem::get_center_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapPoint result = ref.center;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorItem::get_intensity_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                float result = ref.intensity;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorItem::get_nodeIndices_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorItem* ref = (MAHeatMapVectorItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<NSNumber*>* result = ref.nodeIndices;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_type_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapType result = ref.type;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_visible_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.visible;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_inputNodes_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAHeatMapVectorNode*>* result = ref.inputNodes;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_size_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDistance result = ref.size;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_gap_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.gap;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_colors_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<UIColor*>* result = ref.colors;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_startPoints_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<NSNumber*>* result = ref.startPoints;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayOptions::get_opacity_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.opacity;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlay::get_option_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlay* ref = (MAHeatMapVectorOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapVectorOverlayOptions* result = ref.option;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointItem::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointItem::get_customID_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.customID;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointItem::get_title_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.title;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointItem::get_subtitle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.subtitle;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointOverlay::get_items_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointOverlay* ref = (MAMultiPointOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAMultiPointItem*>* result = ref.items;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayOption::get_height_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.height;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayOption::get_heightScale_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.heightScale;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayOption::get_topColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.topColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayOption::get_sideColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.sideColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayOption::get_visibile_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.visibile;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlay::get_defaultOption_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACustomBuildingOverlayOption* result = ref.defaultOption;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlay::get_customOptions_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MACustomBuildingOverlayOption*>* result = ref.customOptions;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATracePoint::get_latitude_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATracePoint* ref = (MATracePoint*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDegrees result = ref.latitude;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATracePoint::get_longitude_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATracePoint* ref = (MATracePoint*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDegrees result = ref.longitude;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATraceLocation::get_loc_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.loc;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATraceLocation::get_angle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                double result = ref.angle;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATraceLocation::get_speed_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                double result = ref.speed;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATraceLocation::get_time_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                double result = ref.time;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAArc::get_startCoordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAArc* ref = (MAArc*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.startCoordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAArc::get_passedCoordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAArc* ref = (MAArc*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.passedCoordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAArc::get_endCoordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAArc* ref = (MAArc*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.endCoordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_showsAccuracyRing_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsAccuracyRing;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_showsHeadingIndicator_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsHeadingIndicator;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_fillColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.fillColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_strokeColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.strokeColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_lineWidth_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.lineWidth;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_locationDotBgColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.locationDotBgColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_locationDotFillColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.locationDotFillColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_enablePulseAnnimation_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.enablePulseAnnimation;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAUserLocationRepresentation::get_image_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.image;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MABaseOverlay::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MABaseOverlay::get_boundingMapRect_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapRect result = ref.boundingMapRect;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_metalEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = [MAMapView metalEnabled];
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_terrainEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = [MAMapView terrainEnabled];
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_mapType_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapType result = ref.mapType;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_centerCoordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.centerCoordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_region_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACoordinateRegion result = ref.region;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_visibleMapRect_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapRect result = ref.visibleMapRect;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_limitRegion_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACoordinateRegion result = ref.limitRegion;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_limitMapRect_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapRect result = ref.limitMapRect;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_zoomLevel_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.zoomLevel;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_minZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.minZoomLevel;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_maxZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.maxZoomLevel;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_rotationDegree_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.rotationDegree;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_cameraDegree_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.cameraDegree;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_zoomingInPivotsAroundAnchorPoint_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.zoomingInPivotsAroundAnchorPoint;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isZoomEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.zoomEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isScrollEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.scrollEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isRotateEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.rotateEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isRotateCameraEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.rotateCameraEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowsBuildings_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsBuildings;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowsLabels_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsLabels;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowTraffic_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showTraffic;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_touchPOIEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.touchPOIEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_showsCompass_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsCompass;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_compassOrigin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.compassOrigin;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_compassSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.compassSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_showsScale_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsScale;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_scaleOrigin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.scaleOrigin;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_scaleSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.scaleSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_logoCenter_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.logoCenter;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_logoSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.logoSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_metersPerPointForCurrentZoom_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                double result = ref.metersPerPointForCurrentZoom;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isAbroad_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.isAbroad;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_maxRenderFrame_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSUInteger result = ref.maxRenderFrame;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isAllowDecreaseFrame_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.isAllowDecreaseFrame;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_openGLESDisabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.openGLESDisabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_renderringDisabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.renderringDisabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.screenAnchor;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowsWorldMap_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSNumber* result = ref.showsWorldMap;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_mapLanguage_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSNumber* result = ref.mapLanguage;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_annotations_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray* result = ref.annotations;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_selectedAnnotations_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray* result = ref.selectedAnnotations;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_annotationVisibleRect_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGRect result = ref.annotationVisibleRect;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_showsUserLocation_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsUserLocation;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_userLocation_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAUserLocation* result = ref.userLocation;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_customizeUserLocationAccuracyCircleRepresentation_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.customizeUserLocationAccuracyCircleRepresentation;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_userLocationAccuracyCircle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACircle* result = ref.userLocationAccuracyCircle;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_userTrackingMode_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAUserTrackingMode result = ref.userTrackingMode;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isUserLocationVisible_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.userLocationVisible;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_distanceFilter_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDistance result = ref.distanceFilter;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_desiredAccuracy_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationAccuracy result = ref.desiredAccuracy;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_headingFilter_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDegrees result = ref.headingFilter;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_pausesLocationUpdatesAutomatically_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.pausesLocationUpdatesAutomatically;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_allowsBackgroundLocationUpdates_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.allowsBackgroundLocationUpdates;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_overlays_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray* result = ref.overlays;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowsIndoorMap_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsIndoorMap;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_isShowsIndoorMapControl_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showsIndoorMapControl;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_indoorMapControlSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.indoorMapControlSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapView::get_customMapStyleEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.customMapStyleEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_fillColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.fillColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_strokeColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.strokeColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_lineWidth_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.lineWidth;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_lineJoinType_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MALineJoinType result = ref.lineJoinType;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_lineCapType_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MALineCapType result = ref.lineCapType;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_miterLimit_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.miterLimit;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayPathRenderer::get_lineDashType_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MALineDashType result = ref.lineDashType;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlayRenderer::get_groundOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAGroundOverlayRenderer* ref = (MAGroundOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAGroundOverlay* result = ref.groundOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomBuildingOverlayRenderer::get_customBuildingOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomBuildingOverlayRenderer* ref = (MACustomBuildingOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACustomBuildingOverlay* result = ref.customBuildingOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlay::set_alpha": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlay::set_alpha");
            }
        
            // args
            // jsonable arg
            CGFloat alpha = [args[@"alpha"] floatValue];
        
            // ref
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.alpha = alpha;
            methodResult(@"success");
        },
        
        @"MAPinAnnotationView::set_pinColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPinAnnotationView::set_pinColor");
            }
        
            // args
            // enum arg
            MAPinAnnotationColor pinColor = (MAPinAnnotationColor) [args[@"pinColor"] integerValue];
        
            // ref
            MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.pinColor = pinColor;
            methodResult(@"success");
        },
        
        @"MAPinAnnotationView::set_animatesDrop": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPinAnnotationView::set_animatesDrop");
            }
        
            // args
            // jsonable arg
            BOOL animatesDrop = [args[@"animatesDrop"] boolValue];
        
            // ref
            MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.animatesDrop = animatesDrop;
            methodResult(@"success");
        },
        
        @"MAHeatMapNode::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapNode::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MAHeatMapNode::set_intensity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapNode::set_intensity");
            }
        
            // args
            // jsonable arg
            float intensity = [args[@"intensity"] floatValue];
        
            // ref
            MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.intensity = intensity;
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_data": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::set_data");
            }
        
            // args
            // list arg
            NSArray<MAHeatMapNode*>* data = (NSArray<MAHeatMapNode*>*) args[@"data"];
        
            // ref
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.data = data;
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::set_radius");
            }
        
            // args
            // jsonable arg
            NSInteger radius = [args[@"radius"] longValue];
        
            // ref
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.radius = radius;
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_opacity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::set_opacity");
            }
        
            // args
            // jsonable arg
            CGFloat opacity = [args[@"opacity"] floatValue];
        
            // ref
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.opacity = opacity;
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_gradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::set_gradient");
            }
        
            // args
            // ref arg
            MAHeatMapGradient* gradient = (MAHeatMapGradient*) (args[@"gradient"] == [NSNull null] ? nil : args[@"gradient"]);
        
            // ref
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.gradient = gradient;
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_allowRetinaAdapting": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::set_allowRetinaAdapting");
            }
        
            // args
            // jsonable arg
            BOOL allowRetinaAdapting = [args[@"allowRetinaAdapting"] boolValue];
        
            // ref
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.allowRetinaAdapting = allowRetinaAdapting;
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_centerCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::set_centerCoordinate");
            }
        
            // args
            // struct arg
            NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
            CLLocationCoordinate2D centerCoordinate;
            if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
              [centerCoordinateValue getValue:&centerCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"centerCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.centerCoordinate = centerCoordinate;
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_zoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::set_zoomLevel");
            }
        
            // args
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.zoomLevel = zoomLevel;
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_rotationDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::set_rotationDegree");
            }
        
            // args
            // jsonable arg
            CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.rotationDegree = rotationDegree;
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_cameraDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::set_cameraDegree");
            }
        
            // args
            // jsonable arg
            CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.cameraDegree = cameraDegree;
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::set_screenAnchor");
            }
        
            // args
            // struct arg
            NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
            CGPoint screenAnchor;
            if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
              [screenAnchorValue getValue:&screenAnchor];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"screenAnchor不能为null"]);
              return;
            }
        
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.screenAnchor = screenAnchor;
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_lockedToScreen": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::set_lockedToScreen");
            }
        
            // args
            // jsonable arg
            BOOL lockedToScreen = [args[@"lockedToScreen"] boolValue];
        
            // ref
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lockedToScreen = lockedToScreen;
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_lockedScreenPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::set_lockedScreenPoint");
            }
        
            // args
            // struct arg
            NSValue* lockedScreenPointValue = (NSValue*) args[@"lockedScreenPoint"];
            CGPoint lockedScreenPoint;
            if (lockedScreenPointValue != nil && (NSNull*) lockedScreenPointValue != [NSNull null]) {
              [lockedScreenPointValue getValue:&lockedScreenPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"lockedScreenPoint不能为null"]);
              return;
            }
        
        
            // ref
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lockedScreenPoint = lockedScreenPoint;
            methodResult(@"success");
        },
        
        @"MACircle::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACircle::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MACircle* ref = (MACircle*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MACircle::set_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACircle::set_radius");
            }
        
            // args
            // jsonable arg
            CLLocationDistance radius = [args[@"radius"] doubleValue];
        
            // ref
            MACircle* ref = (MACircle*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.radius = radius;
            methodResult(@"success");
        },
        
        @"MAAnnotation::set_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotation::set_title");
            }
        
            // args
            // jsonable arg
            NSString* title = (NSString*) args[@"title"];
        
            // ref
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.title = title;
            methodResult(@"success");
        },
        
        @"MAAnnotation::set_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotation::set_subtitle");
            }
        
            // args
            // jsonable arg
            NSString* subtitle = (NSString*) args[@"subtitle"];
        
            // ref
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.subtitle = subtitle;
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::set_styleData");
            }
        
            // args
            // ref arg
            NSData* styleData = (NSData*) (args[@"styleData"] == [NSNull null] ? nil : args[@"styleData"]);
        
            // ref
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.styleData = styleData;
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleDataOverseaPath": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::set_styleDataOverseaPath");
            }
        
            // args
            // jsonable arg
            NSString* styleDataOverseaPath = (NSString*) args[@"styleDataOverseaPath"];
        
            // ref
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.styleDataOverseaPath = styleDataOverseaPath;
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleId": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::set_styleId");
            }
        
            // args
            // jsonable arg
            NSString* styleId = (NSString*) args[@"styleId"];
        
            // ref
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.styleId = styleId;
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleTextureData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::set_styleTextureData");
            }
        
            // args
            // ref arg
            NSData* styleTextureData = (NSData*) (args[@"styleTextureData"] == [NSNull null] ? nil : args[@"styleTextureData"]);
        
            // ref
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.styleTextureData = styleTextureData;
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleExtraData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::set_styleExtraData");
            }
        
            // args
            // ref arg
            NSData* styleExtraData = (NSData*) (args[@"styleExtraData"] == [NSNull null] ? nil : args[@"styleExtraData"]);
        
            // ref
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.styleExtraData = styleExtraData;
            methodResult(@"success");
        },
        
        @"MAMultiColoredPolylineRenderer::set_strokeColors": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiColoredPolylineRenderer::set_strokeColors");
            }
        
            // args
            // list arg
            NSArray<UIColor*>* strokeColors = (NSArray<UIColor*>*) args[@"strokeColors"];
        
            // ref
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.strokeColors = strokeColors;
            methodResult(@"success");
        },
        
        @"MAMultiColoredPolylineRenderer::set_gradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiColoredPolylineRenderer::set_gradient");
            }
        
            // args
            // jsonable arg
            BOOL gradient = [args[@"gradient"] boolValue];
        
            // ref
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.gradient = gradient;
            methodResult(@"success");
        },
        
        @"MAAnimatedAnnotation::set_movingDirection": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnimatedAnnotation::set_movingDirection");
            }
        
            // args
            // jsonable arg
            CLLocationDirection movingDirection = [args[@"movingDirection"] doubleValue];
        
            // ref
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.movingDirection = movingDirection;
            methodResult(@"success");
        },
        
        @"MAMultiTexturePolylineRenderer::set_strokeTextureImages": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiTexturePolylineRenderer::set_strokeTextureImages");
            }
        
            // args
            // list arg
            NSArray<UIImage*>* strokeTextureImages = (NSArray<UIImage*>*) args[@"strokeTextureImages"];
        
            // ref
            MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.strokeTextureImages = strokeTextureImages;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridNode::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridNode::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAHeatMapVectorGridNode* ref = (MAHeatMapVectorGridNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGrid::set_inputNodes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGrid::set_inputNodes");
            }
        
            // args
            // list arg
            NSArray<MAHeatMapVectorGridNode*>* inputNodes = (NSArray<MAHeatMapVectorGridNode*>*) args[@"inputNodes"];
        
            // ref
            MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.inputNodes = inputNodes;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGrid::set_color": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGrid::set_color");
            }
        
            // args
            // ref arg
            UIColor* color = (UIColor*) (args[@"color"] == [NSNull null] ? nil : args[@"color"]);
        
            // ref
            MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.color = color;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_type": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::set_type");
            }
        
            // args
            // enum arg
            MAHeatMapType type = (MAHeatMapType) [args[@"type"] integerValue];
        
            // ref
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.type = type;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::set_visible");
            }
        
            // args
            // jsonable arg
            BOOL visible = [args[@"visible"] boolValue];
        
            // ref
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visible = visible;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_inputGrids": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::set_inputGrids");
            }
        
            // args
            // list arg
            NSArray<MAHeatMapVectorGrid*>* inputGrids = (NSArray<MAHeatMapVectorGrid*>*) args[@"inputGrids"];
        
            // ref
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.inputGrids = inputGrids;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_minZoom": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::set_minZoom");
            }
        
            // args
            // jsonable arg
            CGFloat minZoom = [args[@"minZoom"] floatValue];
        
            // ref
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.minZoom = minZoom;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_maxZoom": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::set_maxZoom");
            }
        
            // args
            // jsonable arg
            CGFloat maxZoom = [args[@"maxZoom"] floatValue];
        
            // ref
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.maxZoom = maxZoom;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlay::set_option": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlay::set_option");
            }
        
            // args
            // ref arg
            MAHeatMapVectorGridOverlayOptions* option = (MAHeatMapVectorGridOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
            MAHeatMapVectorGridOverlay* ref = (MAHeatMapVectorGridOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.option = option;
            methodResult(@"success");
        },
        
        @"MAPathShowRange::set_begin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPathShowRange::set_begin");
            }
        
            // args
            // jsonable arg
            float begin = [args[@"begin"] floatValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAPathShowRange ref;
            [dataValue getValue:&ref];
        
            ref.begin = begin;
            methodResult(@"success");
        },
        
        @"MAPathShowRange::set_end": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPathShowRange::set_end");
            }
        
            // args
            // jsonable arg
            float end = [args[@"end"] floatValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAPathShowRange ref;
            [dataValue getValue:&ref];
        
            ref.end = end;
            methodResult(@"success");
        },
        
        @"MAMultiPolyline::set_drawStyleIndexes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPolyline::set_drawStyleIndexes");
            }
        
            // args
            // jsonable arg
            NSArray<NSNumber*>* drawStyleIndexes = (NSArray<NSNumber*>*) args[@"drawStyleIndexes"];
        
            // ref
            MAMultiPolyline* ref = (MAMultiPolyline*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.drawStyleIndexes = drawStyleIndexes;
            methodResult(@"success");
        },
        
        @"MATraceManager::set_delegate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceManager::set_delegate");
            }
        
            // args
            // ref arg
            id<MATraceDelegate> delegate = (id<MATraceDelegate>) (args[@"delegate"] == [NSNull null] ? nil : args[@"delegate"]);
        
            // ref
            MATraceManager* ref = (MATraceManager*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.delegate = delegate;
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_delegate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::set_delegate");
            }
        
            // args
            // ref arg
            id<MAMultiPointOverlayRendererDelegate> delegate = (id<MAMultiPointOverlayRendererDelegate>) (args[@"delegate"] == [NSNull null] ? nil : args[@"delegate"]);
        
            // ref
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.delegate = delegate;
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::set_icon");
            }
        
            // args
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.icon = icon;
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_pointSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::set_pointSize");
            }
        
            // args
            // struct arg
            NSValue* pointSizeValue = (NSValue*) args[@"pointSize"];
            CGSize pointSize;
            if (pointSizeValue != nil && (NSNull*) pointSizeValue != [NSNull null]) {
              [pointSizeValue getValue:&pointSize];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"pointSize不能为null"]);
              return;
            }
        
        
            // ref
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.pointSize = pointSize;
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_anchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointOverlayRenderer::set_anchor");
            }
        
            // args
            // struct arg
            NSValue* anchorValue = (NSValue*) args[@"anchor"];
            CGPoint anchor;
            if (anchorValue != nil && (NSNull*) anchorValue != [NSNull null]) {
              [anchorValue getValue:&anchor];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"anchor不能为null"]);
              return;
            }
        
        
            // ref
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.anchor = anchor;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_is3DArrowLine": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_is3DArrowLine");
            }
        
            // args
            // jsonable arg
            BOOL is3DArrowLine = [args[@"is3DArrowLine"] boolValue];
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.is3DArrowLine = is3DArrowLine;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_sideColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_sideColor");
            }
        
            // args
            // ref arg
            UIColor* sideColor = (UIColor*) (args[@"sideColor"] == [NSNull null] ? nil : args[@"sideColor"]);
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.sideColor = sideColor;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_userInteractionEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_userInteractionEnabled");
            }
        
            // args
            // jsonable arg
            BOOL userInteractionEnabled = [args[@"userInteractionEnabled"] boolValue];
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.userInteractionEnabled = userInteractionEnabled;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_hitTestInset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_hitTestInset");
            }
        
            // args
            // jsonable arg
            CGFloat hitTestInset = [args[@"hitTestInset"] floatValue];
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.hitTestInset = hitTestInset;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_showRangeEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_showRangeEnabled");
            }
        
            // args
            // jsonable arg
            BOOL showRangeEnabled = [args[@"showRangeEnabled"] boolValue];
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showRangeEnabled = showRangeEnabled;
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_showRange": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolylineRenderer::set_showRange");
            }
        
            // args
            // struct arg
            NSValue* showRangeValue = (NSValue*) args[@"showRange"];
            MAPathShowRange showRange;
            if (showRangeValue != nil && (NSNull*) showRangeValue != [NSNull null]) {
              [showRangeValue getValue:&showRange];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"showRange不能为null"]);
              return;
            }
        
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showRange = showRange;
            methodResult(@"success");
        },
        
        @"MAShape::set_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAShape::set_title");
            }
        
            // args
            // jsonable arg
            NSString* title = (NSString*) args[@"title"];
        
            // ref
            MAShape* ref = (MAShape*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.title = title;
            methodResult(@"success");
        },
        
        @"MAShape::set_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAShape::set_subtitle");
            }
        
            // args
            // jsonable arg
            NSString* subtitle = (NSString*) args[@"subtitle"];
        
            // ref
            MAShape* ref = (MAShape*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.subtitle = subtitle;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_zIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_zIndex");
            }
        
            // args
            // jsonable arg
            NSInteger zIndex = [args[@"zIndex"] longValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.zIndex = zIndex;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_annotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_annotation");
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.annotation = annotation;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_image": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_image");
            }
        
            // args
            // ref arg
            UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.image = image;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_customCalloutView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_customCalloutView");
            }
        
            // args
            // ref arg
            MACustomCalloutView* customCalloutView = (MACustomCalloutView*) (args[@"customCalloutView"] == [NSNull null] ? nil : args[@"customCalloutView"]);
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.customCalloutView = customCalloutView;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_centerOffset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_centerOffset");
            }
        
            // args
            // struct arg
            NSValue* centerOffsetValue = (NSValue*) args[@"centerOffset"];
            CGPoint centerOffset;
            if (centerOffsetValue != nil && (NSNull*) centerOffsetValue != [NSNull null]) {
              [centerOffsetValue getValue:&centerOffset];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"centerOffset不能为null"]);
              return;
            }
        
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.centerOffset = centerOffset;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_calloutOffset": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_calloutOffset");
            }
        
            // args
            // struct arg
            NSValue* calloutOffsetValue = (NSValue*) args[@"calloutOffset"];
            CGPoint calloutOffset;
            if (calloutOffsetValue != nil && (NSNull*) calloutOffsetValue != [NSNull null]) {
              [calloutOffsetValue getValue:&calloutOffset];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"calloutOffset不能为null"]);
              return;
            }
        
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.calloutOffset = calloutOffset;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_enabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_enabled");
            }
        
            // args
            // jsonable arg
            BOOL enabled = [args[@"enabled"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.enabled = enabled;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_highlighted": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_highlighted");
            }
        
            // args
            // jsonable arg
            BOOL highlighted = [args[@"highlighted"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.highlighted = highlighted;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_selected": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_selected");
            }
        
            // args
            // jsonable arg
            BOOL selected = [args[@"selected"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.selected = selected;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_canShowCallout": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_canShowCallout");
            }
        
            // args
            // jsonable arg
            BOOL canShowCallout = [args[@"canShowCallout"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.canShowCallout = canShowCallout;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_leftCalloutAccessoryView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_leftCalloutAccessoryView");
            }
        
            // args
            // ref arg
            UIView* leftCalloutAccessoryView = (UIView*) (args[@"leftCalloutAccessoryView"] == [NSNull null] ? nil : args[@"leftCalloutAccessoryView"]);
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.leftCalloutAccessoryView = leftCalloutAccessoryView;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_rightCalloutAccessoryView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_rightCalloutAccessoryView");
            }
        
            // args
            // ref arg
            UIView* rightCalloutAccessoryView = (UIView*) (args[@"rightCalloutAccessoryView"] == [NSNull null] ? nil : args[@"rightCalloutAccessoryView"]);
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.rightCalloutAccessoryView = rightCalloutAccessoryView;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_draggable": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_draggable");
            }
        
            // args
            // jsonable arg
            BOOL draggable = [args[@"draggable"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.draggable = draggable;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_dragState": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_dragState");
            }
        
            // args
            // enum arg
            MAAnnotationViewDragState dragState = (MAAnnotationViewDragState) [args[@"dragState"] integerValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.dragState = dragState;
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_canAdjustPositon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotationView::set_canAdjustPositon");
            }
        
            // args
            // jsonable arg
            BOOL canAdjustPositon = [args[@"canAdjustPositon"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.canAdjustPositon = canAdjustPositon;
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_tileSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::set_tileSize");
            }
        
            // args
            // struct arg
            NSValue* tileSizeValue = (NSValue*) args[@"tileSize"];
            CGSize tileSize;
            if (tileSizeValue != nil && (NSNull*) tileSizeValue != [NSNull null]) {
              [tileSizeValue getValue:&tileSize];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"tileSize不能为null"]);
              return;
            }
        
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.tileSize = tileSize;
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_minimumZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::set_minimumZ");
            }
        
            // args
            // jsonable arg
            NSInteger minimumZ = [args[@"minimumZ"] longValue];
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.minimumZ = minimumZ;
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_maximumZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::set_maximumZ");
            }
        
            // args
            // jsonable arg
            NSInteger maximumZ = [args[@"maximumZ"] longValue];
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.maximumZ = maximumZ;
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_canReplaceMapContent": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::set_canReplaceMapContent");
            }
        
            // args
            // jsonable arg
            BOOL canReplaceMapContent = [args[@"canReplaceMapContent"] boolValue];
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.canReplaceMapContent = canReplaceMapContent;
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_disableOffScreenTileLoading": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlay::set_disableOffScreenTileLoading");
            }
        
            // args
            // jsonable arg
            BOOL disableOffScreenTileLoading = [args[@"disableOffScreenTileLoading"] boolValue];
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.disableOffScreenTileLoading = disableOffScreenTileLoading;
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_x": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::set_x");
            }
        
            // args
            // jsonable arg
            NSInteger x = [args[@"x"] longValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            ref.x = x;
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_y": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::set_y");
            }
        
            // args
            // jsonable arg
            NSInteger y = [args[@"y"] longValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            ref.y = y;
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_contentScaleFactor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::set_contentScaleFactor");
            }
        
            // args
            // jsonable arg
            CGFloat contentScaleFactor = [args[@"contentScaleFactor"] floatValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            ref.contentScaleFactor = contentScaleFactor;
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_index": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::set_index");
            }
        
            // args
            // jsonable arg
            NSInteger index = [args[@"index"] longValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            ref.index = index;
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_requestId": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATileOverlayPath::set_requestId");
            }
        
            // args
            // jsonable arg
            NSInteger requestId = [args[@"requestId"] longValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MATileOverlayPath ref;
            [dataValue getValue:&ref];
        
            ref.requestId = requestId;
            methodResult(@"success");
        },
        
    };
}

@end
