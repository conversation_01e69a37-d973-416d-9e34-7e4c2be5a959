import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'customer_user_logic.dart';
import 'package:fuduoduo/resource/string_resource.dart';
import '../../../resource/color_resource.dart';

class CustomerUserAddPage extends StatelessWidget {
  const CustomerUserAddPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<CustomerUserLogic>();
    final state = logic.state;

    // 创建FocusNode
    final userNumberFocusNode = FocusNode();
    final userNameFocusNode = FocusNode();
    final passwordFocusNode = FocusNode();

    // 添加失焦监听
    userNumberFocusNode.addListener(() {
      if (!userNumberFocusNode.hasFocus) {
        logic.validateUserNumber();
      }
    });

    userNameFocusNode.addListener(() {
      if (!userNameFocusNode.hasFocus) {
        logic.validateUserName();
      }
    });

    passwordFocusNode.addListener(() {
      if (!passwordFocusNode.hasFocus) {
        logic.validatePassword();
      }
    });

    return Scaffold(
        backgroundColor: ColorResource.WHITE_COMMON_COLOR,
        appBar: AppBar(
          title: const Text('新增用户'),
          centerTitle: true,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight),
            ),
          ),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        body: GestureDetector(
          onTap: () => logic.closeStoreDropdown(),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 表单容器
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 用户账号
                        _buildFormField(
                          label: '用户账号',
                          isRequired: true,
                          child: Obx(() {
                            return TextField(
                              controller: state.userNumber.value,
                              focusNode: userNumberFocusNode,
                              // onChanged: (value) =>
                              //     state.userNumber.value.text = value,
                              decoration: InputDecoration(
                                hintText: '请输入手机号',
                                hintStyle:
                                    const TextStyle(color: Color(0xFF999999)),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF4A90E2)),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFFF4444)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                errorText: state.userNumberError.value.isEmpty
                                    ? null
                                    : state.userNumberError.value,
                              ),
                              keyboardType: TextInputType.phone,
                            );
                          }),
                        ),

                        const SizedBox(height: 16),

                        // 用户名称
                        _buildFormField(
                          label: '用户名称',
                          isRequired: true,
                          child: Obx(() {
                            return TextField(
                              controller: state.userName.value,
                              focusNode: userNameFocusNode,
                              onChanged: (value) =>
                                  state.userName.value.text = value,
                              decoration: InputDecoration(
                                hintText: '请输入用户名',
                                hintStyle:
                                    const TextStyle(color: Color(0xFF999999)),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF4A90E2)),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFFF4444)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                errorText: state.userNameError.value.isEmpty
                                    ? null
                                    : state.userNameError.value,
                              ),
                            );
                          }),
                        ),

                        const SizedBox(height: 16),

                        // 登录密码
                        _buildFormField(
                          label: '登录密码',
                          isRequired: true,
                          child: Obx(() {
                            return TextField(
                              controller: state.password.value,
                              focusNode: passwordFocusNode,
                              onChanged: (value) =>
                                  state.password.value.text = value,
                              obscureText: !state.showPassword.value,
                              inputFormatters: [
                                // 禁止输入中文字符，只允许ASCII字符
                                FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?`~]')),
                              ],
                              decoration: InputDecoration(
                                hintText: '请输入密码',
                                hintStyle:
                                    const TextStyle(color: Color(0xFF999999)),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF4A90E2)),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFFF4444)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                errorText: state.passwordError.value.isEmpty
                                    ? null
                                    : state.passwordError.value,
                                suffixIcon: IconButton(
                                  onPressed: logic.togglePasswordVisibility,
                                  icon: Icon(
                                    state.showPassword.value
                                        ? Icons.visibility
                                        : Icons.visibility_off,
                                    color: const Color(0xFF999999),
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),

                        // 密码规则提示 - 与store_info_b2b保持一致
                        Obx(() {
                          bool hasError = state.passwordError.value.isNotEmpty;
                          return !hasError
                              ? Container(
                                  padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 8.h),
                                  child: Text(
                                    '必须包含字母、数字和特殊字符，长度在8到16个字符之间',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 23.sp,
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink();
                        }),

                        const SizedBox(height: 16),

                        // 备注
                        _buildFormField(
                          label: '备注',
                          child: Obx(() {
                            return TextField(
                              key: ValueKey(state.address.value),
                              controller: state.address.value,
                              onChanged: (value) =>
                                  state.address.value.text = value,
                              decoration: InputDecoration(
                                hintText: '可备注账户',
                                hintStyle:
                                    const TextStyle(color: Color(0xFF999999)),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE0E0E0)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF4A90E2)),
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                              ),
                              maxLines: 3,
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // 保存按钮
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Get.back(),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: const BorderSide(color: Color(0xFFE0E0E0)),
                          ),
                        ),
                        child: const Text(
                          '取消',
                          style: TextStyle(
                            color: Color(0xFF666666),
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: logic.saveUser,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4A90E2),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '保存',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildFormField(
      {required String label, required Widget child, bool isRequired = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (isRequired)
              const Text(
                '* ',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFFFF4444),
                ),
              ),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }
}
