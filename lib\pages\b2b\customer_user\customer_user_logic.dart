import 'dart:async';

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import '../../../api/config.dart';
import '../../../common/apis.dart';
import '../../../common/dio_utils.dart';
import '../../../response_data_models/b2b/b2bAppCustomer/b2b_app_customer_page/b2b_app_customer_list.dart';
import '../../../response_data_models/b2b/b2bAppCustomer/b2b_app_customer_page/b2b_app_customer_page.dart';
import '../customer_info/logic.dart';
import 'customer_user_state.dart';
import '../../../route/index.dart';
import '../../../utils/common_utils.dart';
import 'package:fuduoduo/widget/MyDialog.dart';
import 'package:fuduoduo/resource/string_resource.dart';

class CustomerUserLogic extends GetxController {
  final state = CustomerUserState();
  final infoPageState = Get.find<B2bCustomerInfoPageLogic>().state;

  @override
  void onInit() {
    super.onInit();
    loadUserList();
  }

  Future<void> onRefresh() async {
    loadUserList();
  }

  // 加载更多数据
  void loadMore() {
    if (state.hasMore.value && !state.isLoadingMore.value) {
      loadUserList(isRefresh: false);
    }
  }

  // 加载用户列表
  void loadUserList({bool isRefresh = true}) {
    if (isRefresh) {
      state.currentPage.value = 1;
    } else {
      if (!state.hasMore.value) return;
      state.isLoadingMore.value = true;
      state.currentPage++;
    }
    print('加载用户列表');
    SmartDialog.showLoading(msg: "加载中...");

    MyDio.get(
      Apis.b2bAppCustomerPage,
      queryParameters: {
        'pageNo': state.currentPage.value,
        'pageSize': state.pageSize.value,
        'branchId': infoPageState.branchId,
        'keyword': state.searchKeyword.value,
      },
      successCallBack: (value) {
        state.isLoadingMore.value = false;
        SmartDialog.dismiss();

        var res = B2bAppCustomerPage.fromJson(commonModalFromJson(value));

        if (isRefresh) {
          state.userList.value = res.data?.list ?? [];
        } else {
          state.userList.value = [...state.userList, ...res.data?.list ?? []];
        }
        state.total.value = res.data?.total ?? 0;
        state.hasMore.value = state.userList.length < state.total.value;
      },
      failCallBack: () {
        state.isLoadingMore.value = false;
        SmartDialog.dismiss();
        MyCommonUtils.showToast("加载用户列表失败，请重试");
      },
    );
  }

  // 搜索用户
  void searchUsers(String keyword) {
    state.searchKeyword.value = keyword;
    // 如果定时器不存在或已过期，则立即执行搜索逻辑
    if (state.throttleTimer == null || !state.throttleTimer!.isActive) {
      loadUserList();
    }
    state.throttleTimer?.cancel();

    state.throttleTimer = Timer(
      Duration(milliseconds: state.throttleDuration.value),
      () => loadUserList(),
    );
  }

  // 编辑用户
  void editUser(B2bAppCustomerList user) {
    state.editingUser.value = user;
    state.userNumber.value.text = user.memberPhone ?? '';
    state.userName.value.text = user.memberName ?? '';
    // 将单个门店转换为列表（兼容旧数据）
    state.selectedStores.value = [user.branchName ?? ''];
    state.address.value.text = user.memo ?? '';
    clearFormErrors();

    Get.toNamed(PageName.CustomerUserEditPage)!.then((v) async {
      loadUserList();
    });
  }

  // 新增用户
  void addUser() {
    state.editingUser.value = null;
    clearFormData();
    clearFormErrors();
    Get.toNamed(PageName.CustomerUserAddPage)!.then((v) async {
      loadUserList();
    });
  }

  // 停用用户确认
  void showDeactivateConfirm(B2bAppCustomerList user) {
    MyDialog.showDialog(() {
      // logic.delete(index);
      deactivateUser(user);
    }, content: "确定要停用该用户吗？");
  }

  // 启动用户确认
  void showNoDeactivateConfirm(B2bAppCustomerList user) {
    MyDialog.showDialog(() {
      // logic.delete(index);
      deNoactivateUser(user);
    }, content: "确定要启用该用户吗？");
  }

  // 停用用户
  void deactivateUser(B2bAppCustomerList user) {
    final index = state.userList.indexWhere((u) => u.memberId == user.memberId);
    if (index != -1) {
      // 请求接口
      MyDio.put(
        Apis.b2bAppCustomerDisabler + '${user.memberId}',
        queryParameters: {},
        successCallBack: (value) {
          loadUserList();
          MyCommonUtils.showToast('用户已停用');
        },
        failCallBack: (error) {
          MyCommonUtils.showToast('停用用户失败，请重试');
        },
      );
    }
  }

  // 启动用户
  void deNoactivateUser(B2bAppCustomerList user) {
    final index = state.userList.indexWhere((u) => u.memberId == user.memberId);
    if (index != -1) {
      // 请求接口
      MyDio.put(
        Apis.b2bAppCustomerEnable + '${user.memberId}',
        queryParameters: {},
        successCallBack: (value) {
          loadUserList();
          MyCommonUtils.showToast('用户已启用');
        },
        failCallBack: (error) {
          MyCommonUtils.showToast('启用用户失败，请重试');
        },
      );
    }
  }

  // 清空表单数据
  void clearFormData() {
    state.userNumber.value.text = '';
    state.userName.value.text = '';
    state.selectedStores.clear();
    state.address.value.text = '';
    state.password.value.text = '';
    // state.confirmPassword.value = '';
  }

  // 清空表单错误
  void clearFormErrors() {
    state.userNumberError.value = '';
    state.userNameError.value = '';
    state.selectedStoresError.value = '';
    state.passwordError.value = '';
    state.confirmPasswordError.value = '';
  }

  // 表单验证
  bool validateForm() {
    bool isValid = true;
    clearFormErrors();

    // 验证用户账号（手机号）
    if (state.userNumber.value.text.isEmpty) {
      state.userNumberError.value = '请输入用户账号';
      isValid = false;
    } else if (!_isValidPhoneNumber(state.userNumber.value.text)) {
      state.userNumberError.value = '请输入正确的手机号';
      isValid = false;
    }

    // 验证用户名称
    if (state.userName.value.text.isEmpty) {
      state.userNameError.value = '请输入用户名称';
      isValid = false;
    }

    // 验证管理门店
    // if (state.selectedStores.isEmpty) {
    //   state.selectedStoresError.value = '请选择管理门店';
    //   isValid = false;
    // }

    // 新增用户需要验证密码
    if (state.editingUser.value == null) {
      if (state.password.value.text.isEmpty) {
        state.passwordError.value = '请输入密码';
        isValid = false;
      } else if (!_isValidPassword(state.password.value.text)) {
        // state.passwordError.value = '密码必须包含字母、数字和特殊字符，长度在8到16个字符之间';
        state.passwordError.value = '请按规则设置密码';
        isValid = false;
      }

      // if (state.confirmPassword.value != state.password.value) {
      //   state.confirmPasswordError.value = '两次输入的密码不一致';
      //   isValid = false;
      // }
    }

    return isValid;
  }

  // 密码校验
  bool _isValidPassword(String password) {
    // 密码必须包含字母、数字和特殊字符，长度在8到16个字符之间
    if (password.length < 8 || password.length > 16) {
      return false;
    }

    // 检查是否包含至少一个字母
    bool hasLetter = RegExp(r'[a-zA-Z]').hasMatch(password);
    // 检查是否包含至少一个数字
    bool hasDigit = RegExp(r'[0-9]').hasMatch(password);
    // 检查是否包含至少一个特殊字符
    bool hasSpecial = RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);

    return hasLetter && hasDigit && hasSpecial;
  }

  // 单独验证用户账号（手机号）
  void validateUserNumber() {
    if (state.userNumber.value.text.isEmpty) {
      state.userNumberError.value = '请输入用户账号';
    } else if (!_isValidPhoneNumber(state.userNumber.value.text)) {
      state.userNumberError.value = '请输入正确的手机号';
    } else {
      state.userNumberError.value = '';
    }
  }

  // 手机号校验
  bool _isValidPhoneNumber(String phone) {
    // 中国大陆手机号正则表达式
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }

  // 单独验证用户名称
  void validateUserName() {
    if (state.userName.value.text.isEmpty) {
      state.userNameError.value = '请输入用户名称';
    } else {
      state.userNameError.value = '';
    }
  }

  // 单独验证管理门店
  void validateSelectedStores() {
    if (state.selectedStores.isEmpty) {
      state.selectedStoresError.value = '请选择管理门店';
    } else {
      state.selectedStoresError.value = '';
    }
  }

  // 单独验证密码
  void validatePassword() {
    if (state.password.value.text.isEmpty) {
      state.passwordError.value = '请输入密码';
    } else if (!_isValidPassword(state.password.value.text)) {
      state.passwordError.value = '必须包含字母、数字和特殊字符，长度在8到16个字符之间';
    } else {
      state.passwordError.value = '';
    }
  }

  // 保存用户
  void saveUser() {
    if (!validateForm()) return;
    if (state.editingUser.value != null) {
      // 编辑用户
      final index = state.userList
          .indexWhere((u) => u.memberId == state.editingUser.value!.memberId);
      if (index != -1) {
        MyDio.put(
          Apis.b2bAppUserEdit,
          queryParameters: {
            "memberId": state.editingUser.value!.memberId,
            "memberPhone": state.userNumber.value.text,
            "userName": state.userNumber.value.text,
            "memberName": state.userName.value.text,
            "branchIds": [infoPageState.branchId],
            "memo": state.address.value.text,
          },
          successCallBack: (value) {
            Get.back();
            MyCommonUtils.showToast('用户信息已更新');
          },
          failCallBack: (error) {
            MyCommonUtils.showToast('更新用户信息失败，请重试');
          },
        );
      }
    } else {
      // 新增用户
      MyDio.post(
        Apis.b2bAppCustomerMember,
        queryParameters: {
          "memberPhone": state.userNumber.value.text,
          "userName": state.userNumber.value.text,
          "memberName": state.userName.value.text,
          "branchIds": [infoPageState.branchId],
          "password": state.password.value.text,
          "memo": state.address.value.text,
          'channel': 1,
        },
        successCallBack: (value) {
          Get.back();
          MyCommonUtils.showToast('用户已添加');
        },
        failCallBack: (error) {
          MyCommonUtils.showToast('添加用户失败，请重试');
        },
      );
    }
  }

  // 切换密码显示状态
  void togglePasswordVisibility() {
    state.showPassword.value = !state.showPassword.value;
  }

  void toggleConfirmPasswordVisibility() {
    state.showConfirmPassword.value = !state.showConfirmPassword.value;
  }

  // 门店选择相关方法
  void toggleStoreSelection(String storeName) {
    if (state.selectedStores.contains(storeName)) {
      state.selectedStores.remove(storeName);
    } else {
      state.selectedStores.add(storeName);
    }
    // 选择门店后清除错误信息
    if (state.selectedStores.isNotEmpty) {
      state.selectedStoresError.value = '';
    }

    update([
      StringResource.B2B_CUSTOMER_USER_STORE_EDIT,
      StringResource.B2B_CUSTOMER_USER_STORE_ADD
    ]);
  }

  void removeSelectedStore(String storeName) {
    state.selectedStores.remove(storeName);
    // 移除门店后重新验证
    validateSelectedStores();
  }

  bool isStoreSelected(String storeName) {
    return state.selectedStores.contains(storeName);
  }

  // 切换门店下拉框展开状态
  void toggleStoreDropdown() {
    state.isStoreDropdownExpanded.value = !state.isStoreDropdownExpanded.value;
  }

  // 关闭门店下拉框
  void closeStoreDropdown() {
    state.isStoreDropdownExpanded.value = false;
    // 失焦时验证门店选择
    validateSelectedStores();
  }

  // 展开门店下拉框
  void openStoreDropdown() {
    state.isStoreDropdownExpanded.value = true;
  }
}
