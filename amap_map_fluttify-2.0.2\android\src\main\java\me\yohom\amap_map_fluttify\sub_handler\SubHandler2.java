//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler2 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindow", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.Marker var1 = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter __this__ = (com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter@" + __this__ + "::getOverturnInfoWindow(" + var1 + ")");
                }
            
                // invoke native method
                android.view.View __result__ = null;
                try {
                    __result__ = __this__.getOverturnInfoWindow(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindowClick", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.Marker var1 = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter __this__ = (com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter@" + __this__ + "::getOverturnInfoWindowClick(" + var1 + ")");
                }
            
                // invoke native method
                android.view.View __result__ = null;
                try {
                    __result__ = __this__.getOverturnInfoWindowClick(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setPoints", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setPoints(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPoints(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setHoleOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.BaseHoleOptions> var1 = (java.util.List<com.amap.api.maps.model.BaseHoleOptions>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setHoleOptions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setHoleOptions(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getHoleOptions", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getHoleOptions(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.BaseHoleOptions> __result__ = null;
                try {
                    __result__ = __this__.getHoleOptions();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setStrokeWidth", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setStrokeWidth(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStrokeWidth(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getStrokeWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getStrokeWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getStrokeWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setStrokeColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setStrokeColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStrokeColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getStrokeColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getStrokeColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getStrokeColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setFillColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setFillColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFillColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getFillColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getFillColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFillColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polygon::contains", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polygon __this__ = (com.amap.api.maps.model.Polygon) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polygon@" + __this__ + "::contains(" + var1 + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.contains(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::data", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.Collection<com.amap.api.maps.model.LatLng> var1 = (java.util.Collection<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::data(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.data(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::weightedData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.Collection<com.amap.api.maps.model.WeightedLatLng> var1 = (java.util.Collection<com.amap.api.maps.model.WeightedLatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::weightedData(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.weightedData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::size", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::size(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.size(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::gradient", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.Gradient var1 = (com.amap.api.maps.model.Gradient) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::gradient(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.gradient(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::opacity", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::opacity(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.opacity(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::maxIntensity", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::maxIntensity(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.maxIntensity(var1.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::maxZoom", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::maxZoom(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.maxZoom(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::minZoom", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::minZoom(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.minZoom(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::gap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::gap(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.gap(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::type", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::type(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.type(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getGradient", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getGradient(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Gradient __result__ = null;
                try {
                    __result__ = __this__.getGradient();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getData", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getData(" + "" + ")");
                }
            
                // invoke native method
                java.util.Collection<com.amap.api.maps.model.WeightedLatLng> __result__ = null;
                try {
                    __result__ = __this__.getData();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getSize", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getSize(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getSize();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getOpacity", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getOpacity(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getOpacity();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getMaxIntensity", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getMaxIntensity(" + "" + ")");
                }
            
                // invoke native method
                Double __result__ = null;
                try {
                    __result__ = __this__.getMaxIntensity();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getMaxZoom", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getMaxZoom(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getMaxZoom();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getMinZoom", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getMinZoom(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getMinZoom();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getGap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getGap(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getGap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getType(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayerOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayerOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptor::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BitmapDescriptor __this__ = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptor@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptor::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BitmapDescriptor __this__ = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptor@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptor::getBitmap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BitmapDescriptor __this__ = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptor@" + __this__ + "::getBitmap(" + "" + ")");
                }
            
                // invoke native method
                android.graphics.Bitmap __result__ = null;
                try {
                    __result__ = __this__.getBitmap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptor::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BitmapDescriptor __this__ = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptor@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptor::getHeight", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BitmapDescriptor __this__ = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptor@" + __this__ + "::getHeight(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getHeight();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolygonHoleOptions::addAll", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolygonHoleOptions __this__ = (com.amap.api.maps.model.PolygonHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolygonHoleOptions@" + __this__ + "::addAll(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolygonHoleOptions __result__ = null;
                try {
                    __result__ = __this__.addAll(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolygonHoleOptions::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolygonHoleOptions __this__ = (com.amap.api.maps.model.PolygonHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolygonHoleOptions@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineCapType::valueOf", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.AMapPara.LineCapType::valueOf(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.AMapPara.LineCapType __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.AMapPara.LineCapType.valueOf(var0.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineCapType::getTypeValue", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.AMapPara.LineCapType __this__ = (com.amap.api.maps.model.AMapPara.LineCapType) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.AMapPara.LineCapType@" + __this__ + "::getTypeValue(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTypeValue();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptionsCreator __this__ = (com.amap.api.maps.model.TileOverlayOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Poi::getName", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Poi __this__ = (com.amap.api.maps.model.Poi) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Poi@" + __this__ + "::getName(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getName();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Poi::getCoordinate", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Poi __this__ = (com.amap.api.maps.model.Poi) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Poi@" + __this__ + "::getCoordinate(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getCoordinate();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Poi::getPoiId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Poi __this__ = (com.amap.api.maps.model.Poi) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Poi@" + __this__ + "::getPoiId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getPoiId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::myLocationIcon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::myLocationIcon(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.myLocationIcon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::anchor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::anchor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::radiusFillColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::radiusFillColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.radiusFillColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::strokeColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::strokeColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.strokeColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::strokeWidth", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::strokeWidth(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.strokeWidth(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::myLocationType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::myLocationType(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.myLocationType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::interval", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::interval(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.interval(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::showMyLocation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::showMyLocation(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.showMyLocation(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getMyLocationIcon", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getMyLocationIcon(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getMyLocationIcon();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getAnchorU", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getAnchorU(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorU();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getAnchorV", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getAnchorV(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorV();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getRadiusFillColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getRadiusFillColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getRadiusFillColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getStrokeColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getStrokeColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getStrokeColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getStrokeWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getStrokeWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getStrokeWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getMyLocationType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getMyLocationType(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getMyLocationType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::getInterval", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::getInterval(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getInterval();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MyLocationStyle::isMyLocationShowing", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MyLocationStyle __this__ = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MyLocationStyle@" + __this__ + "::isMyLocationShowing(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isMyLocationShowing();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RouteOverlay::removeRouteName", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RouteOverlay __this__ = (com.amap.api.maps.model.RouteOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RouteOverlay@" + __this__ + "::removeRouteName(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeRouteName();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RouteOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RouteOverlay __this__ = (com.amap.api.maps.model.RouteOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RouteOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngCreator __this__ = (com.amap.api.maps.model.LatLngCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CircleHoleOptions::center", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CircleHoleOptions __this__ = (com.amap.api.maps.model.CircleHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CircleHoleOptions@" + __this__ + "::center(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CircleHoleOptions __result__ = null;
                try {
                    __result__ = __this__.center(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CircleHoleOptions::radius", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CircleHoleOptions __this__ = (com.amap.api.maps.model.CircleHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CircleHoleOptions@" + __this__ + "::radius(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CircleHoleOptions __result__ = null;
                try {
                    __result__ = __this__.radius(var1.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CircleHoleOptions::getCenter", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CircleHoleOptions __this__ = (com.amap.api.maps.model.CircleHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CircleHoleOptions@" + __this__ + "::getCenter(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getCenter();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CircleHoleOptions::getRadius", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CircleHoleOptions __this__ = (com.amap.api.maps.model.CircleHoleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CircleHoleOptions@" + __this__ + "::getRadius(" + "" + ")");
                }
            
                // invoke native method
                Double __result__ = null;
                try {
                    __result__ = __this__.getRadius();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setPosition(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPosition(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getPosition(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setText", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setText(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setText(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getText", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getText(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getText();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setBackgroundColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setBackgroundColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setBackgroundColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getBackgroundColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getBackgroundColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getBackgroundColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setFontColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setFontColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFontColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getFontColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getFontColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFontColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setFontSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setFontSize(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFontSize(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getFontSize", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getFontSize(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFontSize();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setAlign", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setAlign(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAlign(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getAlignX", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getAlignX(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getAlignX();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getAlignY", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getAlignY(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getAlignY();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setObject", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.lang.Object var1 = (java.lang.Object) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setObject(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setObject(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getObject", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getObject(" + "" + ")");
                }
            
                // invoke native method
                java.lang.Object __result__ = null;
                try {
                    __result__ = __this__.getObject();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setRotate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setRotate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRotate(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getRotate", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getRotate(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getRotate();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Text::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Text@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds.Builder::include", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.LatLngBounds.Builder __this__ = (com.amap.api.maps.model.LatLngBounds.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds.Builder@" + __this__ + "::include(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds.Builder __result__ = null;
                try {
                    __result__ = __this__.include(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds.Builder::build", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.LatLngBounds.Builder __this__ = (com.amap.api.maps.model.LatLngBounds.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.LatLngBounds.Builder@" + __this__ + "::build(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLngBounds __result__ = null;
                try {
                    __result__ = __this__.build();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getHeatMapItem", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::getHeatMapItem(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapItem __result__ = null;
                try {
                    __result__ = __this__.getHeatMapItem(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getOptions", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::getOptions(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                try {
                    __result__ = __this__.getOptions();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.HeatMapLayerOptions var1 = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapLayer@" + __this__ + "::setOptions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOptions(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::add__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::add(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.add(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::addAll", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::addAll(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.addAll(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::width", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::width(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.width(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::topColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::topColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.topColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::sideColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::sideColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.sideColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::set3DModel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::set3DModel(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.set3DModel(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getTopColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::getTopColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTopColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getSideColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::getSideColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getSideColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::is3DModel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::is3DModel(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.is3DModel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::setPoints", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::setPoints(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPoints(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.NavigateArrowOptions@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.ColorLatLng::getColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.ColorLatLng __this__ = (com.amap.api.maps.model.ColorLatLng) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.ColorLatLng@" + __this__ + "::getColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.ColorLatLng::getLatLngs", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.ColorLatLng __this__ = (com.amap.api.maps.model.ColorLatLng) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.ColorLatLng@" + __this__ + "::getLatLngs(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getLatLngs();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromResource", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromResource(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromResource(var0.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromView", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.view.View var0 = (android.view.View) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromView(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromView(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromPath", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromPath(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromPath(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromAsset", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromAsset(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromAsset(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromFile", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromFile(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromFile(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.defaultMarker();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.defaultMarker(var0.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromBitmap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.graphics.Bitmap var0 = (android.graphics.Bitmap) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::fromBitmap(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromBitmap(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::getContext", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BitmapDescriptorFactory::getContext(" + "" + ")");
                }
            
                // invoke native method
                android.content.Context __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.getContext();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getUrl", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileProvider@" + __this__ + "::getUrl(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getUrl();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getKey", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileProvider@" + __this__ + "::getKey(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getKey();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileProvider@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineJoinType::getTypeValue", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.AMapPara.LineJoinType __this__ = (com.amap.api.maps.model.AMapPara.LineJoinType) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.AMapPara.LineJoinType@" + __this__ + "::getTypeValue(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTypeValue();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineJoinType::valueOf", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.AMapPara.LineJoinType::valueOf(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.AMapPara.LineJoinType __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.AMapPara.LineJoinType.valueOf(var0.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::anchor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::anchor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getAnchorU", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::getAnchorU(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorU();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getAnchorV", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::getAnchorV(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorV();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::icon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::icon(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.icon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getIcon", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::getIcon(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getIcon();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::setMultiPointItems", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.MultiPointItem> var1 = (java.util.List<com.amap.api.maps.model.MultiPointItem>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::setMultiPointItems(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMultiPointItems(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getMultiPointItems", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::getMultiPointItems(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.MultiPointItem> __result__ = null;
                try {
                    __result__ = __this__.getMultiPointItems();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::setEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::setEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlayOptions@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PoiCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PoiCreator __this__ = (com.amap.api.maps.model.PoiCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PoiCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Poi[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setUseTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setUseTexture(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setUseTexture(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setCustomTexture(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setCustomTexture(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getCustomTexture(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getCustomTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTextureList", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.List<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setCustomTextureList(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setCustomTextureList(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTextureList", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getCustomTextureList(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.BitmapDescriptor> __result__ = null;
                try {
                    __result__ = __this__.getCustomTextureList();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTextureIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setCustomTextureIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setCustomTextureIndex(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTextureIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getCustomTextureIndex(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<Integer> __result__ = null;
                try {
                    __result__ = __this__.getCustomTextureIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::colorValues", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::colorValues(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.colorValues(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getColorValues", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getColorValues(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<Integer> __result__ = null;
                try {
                    __result__ = __this__.getColorValues();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::useGradient", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::useGradient(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.useGradient(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isUseGradient", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isUseGradient(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isUseGradient();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isUseTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isUseTexture(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isUseTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isGeodesic", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isGeodesic(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isGeodesic();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::add__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::add(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.add(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::addAll", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::addAll(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.addAll(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::width", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::width(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.width(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::color", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::color(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.color(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::geodesic", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::geodesic(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.geodesic(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setDottedLine", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setDottedLine(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setDottedLine(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isDottedLine", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isDottedLine(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isDottedLine();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setDottedLineType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setDottedLineType(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setDottedLineType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::lineCapType", (__args__, __methodResult__) -> {
                // args
                // enum arg
                com.amap.api.maps.model.PolylineOptions.LineCapType var1 = com.amap.api.maps.model.PolylineOptions.LineCapType.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::lineCapType(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.lineCapType(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::lineJoinType", (__args__, __methodResult__) -> {
                // args
                // enum arg
                com.amap.api.maps.model.PolylineOptions.LineJoinType var1 = com.amap.api.maps.model.PolylineOptions.LineJoinType.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::lineJoinType(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.lineJoinType(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getLineCapType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getLineCapType(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions.LineCapType __result__ = null;
                try {
                    __result__ = __this__.getLineCapType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getLineJoinType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getLineJoinType(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions.LineJoinType __result__ = null;
                try {
                    __result__ = __this__.getLineJoinType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getDottedLineType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getDottedLineType(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getDottedLineType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::transparency", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::transparency(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.transparency(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getTransparency", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getTransparency(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getTransparency();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::aboveMaskLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::aboveMaskLayer(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.aboveMaskLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isAboveMaskLayer", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::isAboveMaskLayer(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isAboveMaskLayer();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setPoints", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setPoints(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPoints(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRatio", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getShownRatio(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getShownRatio();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setShownRatio", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setShownRatio(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setShownRatio(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setShownRange", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::setShownRange(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.setShownRange(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRangeBegin", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getShownRangeBegin(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getShownRangeBegin();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRangeEnd", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptions@" + __this__ + "::getShownRangeEnd(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getShownRangeEnd();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
        }};
    }
}
