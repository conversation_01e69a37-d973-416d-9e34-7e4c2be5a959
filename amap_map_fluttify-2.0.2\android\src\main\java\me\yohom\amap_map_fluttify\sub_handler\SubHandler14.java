//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler14 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            put("RefClass::isKindOfcom_amap_api_maps_model_LatLngBounds", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.LatLngBounds);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CustomMapStyleOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CustomMapStyleOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileOverlaySource", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileOverlaySource);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CrossOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CrossOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_VisibleRegionCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.VisibleRegionCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.LatLng);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileProjection", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileProjection);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_AMapPara", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.AMapPara);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CameraPosition_Builder", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CameraPosition.Builder);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BasePointOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BasePointOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CircleOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CircleOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Arc", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Arc);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_AMapCameraInfo", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.AMapCameraInfo);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CrossOverlay_UpdateItem", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CrossOverlay.UpdateItem);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Circle", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Circle);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PolygonOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PolygonOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_WeightedLatLng", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.WeightedLatLng);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MVTTileOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MVTTileOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MyLocationStyleCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MyLocationStyleCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MarkerOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MarkerOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MultiPointItem", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MultiPointItem);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_LatLngBoundsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.LatLngBoundsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_ArcOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.ArcOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BitmapDescriptorCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BitmapDescriptorCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MVTTileOverlayOptions_Builder", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MVTTileOverlayOptions.Builder);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CircleOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CircleOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_NavigateArrowOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.NavigateArrowOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_ImageOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.ImageOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatMapGridLayerOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatMapGridLayerOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_RuntimeRemoteException", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.RuntimeRemoteException);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_AMapGLOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.AMapGLOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatmapTileProvider_Builder", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatmapTileProvider.Builder);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MultiPointOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MultiPointOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BaseOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BaseOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PolylineOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PolylineOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BaseOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BaseOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Marker", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Marker);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatMapGridLayer", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatMapGridLayer);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_RoutePara", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.RoutePara);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_ArcOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.ArcOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CameraPositionCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CameraPositionCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CrossOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CrossOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleOverLifeModule", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleOverLifeModule);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ConstantRotationOverLife", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ConstantRotationOverLife);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_SinglePointParticleShape", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.SinglePointParticleShape);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_RandomVelocityBetweenTwoConstants", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.RandomVelocityBetweenTwoConstants);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_CurveSizeOverLife", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.CurveSizeOverLife);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleEmissionModule", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleEmissionModule);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_SizeOverLife", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.SizeOverLife);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_RectParticleShape", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.RectParticleShape);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ColorGenerate", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ColorGenerate);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_VelocityGenerate", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.VelocityGenerate);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_RotationOverLife", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.RotationOverLife);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_RandomColorBetWeenTwoConstants", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.RandomColorBetWeenTwoConstants);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleShapeModule", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleShapeModule);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleOverlayOptionsFactory", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_particle_ParticleOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.particle.ParticleOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MVTTileOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MVTTileOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BaseHoleOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BaseHoleOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TextOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TextOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PolygonOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PolygonOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileProjectionCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileProjectionCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_GroundOverlayOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.GroundOverlayOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BaseOptions_BaseUpdateFlags", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BaseOptions.BaseUpdateFlags);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_IndoorBuildingInfo", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.IndoorBuildingInfo);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Polyline", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Polyline);
            });
            put("RefClass::isKindOfcom_amap_api_maps_TextureMapView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.TextureMapView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_AMapUtils", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.AMapUtils);
            });
            put("RefClass::isKindOfcom_amap_api_maps_TextureMapFragment", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.TextureMapFragment);
            });
            put("RefClass::isKindOfcom_amap_api_maps_InfoWindowAnimationManager", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.InfoWindowAnimationManager);
            });
            put("RefClass::isKindOfcom_amap_api_maps_WearMapView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.WearMapView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_AMap", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.AMap);
            });
            put("RefClass::isKindOfcom_amap_api_maps_MapView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.MapView);
            });
            put("RefClass::isKindOfcom_amap_api_trace_TraceLocation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.trace.TraceLocation);
            });
            put("RefClass::isKindOfcom_amap_api_trace_LBSTraceClient", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.trace.LBSTraceClient);
            });
            put("RefClass::isKindOfcom_amap_api_trace_TraceOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.trace.TraceOverlay);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_offlineservice_AMapPermissionActivity__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_offlineservice_AMapPermissionActivity__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.offlineservice.AMapPermissionActivity __obj__ = new com.amap.api.offlineservice.AMapPermissionActivity();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_SupportMapFragment__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_SupportMapFragment__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.SupportMapFragment __obj__ = new com.amap.api.maps.SupportMapFragment();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_InfoWindowParams__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_InfoWindowParams__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.InfoWindowParams __obj__ = new com.amap.api.maps.InfoWindowParams();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_CameraUpdateFactory__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_CameraUpdateFactory__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.CameraUpdateFactory __obj__ = new com.amap.api.maps.CameraUpdateFactory();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_AMapException__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_AMapException__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.AMapException __obj__ = new com.amap.api.maps.AMapException(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_AMapException__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_AMapException__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.AMapException __obj__ = new com.amap.api.maps.AMapException();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_AMapOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_AMapOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.AMapOptions __obj__ = new com.amap.api.maps.AMapOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_AMapOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_AMapOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.AMapOptionsCreator __obj__ = new com.amap.api.maps.AMapOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_CoordinateConverter__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_CoordinateConverter__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.CoordinateConverter __obj__ = new com.amap.api.maps.CoordinateConverter(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_utils_SpatialRelationUtil__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_utils_SpatialRelationUtil__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.utils.SpatialRelationUtil __obj__ = new com.amap.api.maps.utils.SpatialRelationUtil();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_utils_overlay_MovingPointOverlay__com_amap_api_maps_AMap__com_amap_api_maps_model_BasePointOverlay", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_utils_overlay_MovingPointOverlay__com_amap_api_maps_AMap__com_amap_api_maps_model_BasePointOverlay");
                }
            
                // args
                // ref arg
                com.amap.api.maps.AMap var1 = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.BasePointOverlay var2 = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.utils.overlay.MovingPointOverlay __obj__ = new com.amap.api.maps.utils.overlay.MovingPointOverlay(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_utils_overlay_SmoothMoveMarker__com_amap_api_maps_AMap", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_utils_overlay_SmoothMoveMarker__com_amap_api_maps_AMap");
                }
            
                // args
                // ref arg
                com.amap.api.maps.AMap var1 = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.utils.overlay.SmoothMoveMarker __obj__ = new com.amap.api.maps.utils.overlay.SmoothMoveMarker(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_TextureSupportMapFragment__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_TextureSupportMapFragment__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.TextureSupportMapFragment __obj__ = new com.amap.api.maps.TextureSupportMapFragment();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_MapFragment__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_MapFragment__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.MapFragment __obj__ = new com.amap.api.maps.MapFragment();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_SwipeDismissView__android_content_Context__android_view_View", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_SwipeDismissView__android_content_Context__android_view_View");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                android.view.View var2 = (android.view.View) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.SwipeDismissView __obj__ = new com.amap.api.maps.SwipeDismissView(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_MapsInitializer__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_MapsInitializer__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.MapsInitializer __obj__ = new com.amap.api.maps.MapsInitializer();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_OfflineMapStatus__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_OfflineMapStatus__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.OfflineMapStatus __obj__ = new com.amap.api.maps.offlinemap.OfflineMapStatus();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_OfflineMapProvince__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_OfflineMapProvince__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.OfflineMapProvince __obj__ = new com.amap.api.maps.offlinemap.OfflineMapProvince();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_DownloadProgressView__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_DownloadProgressView__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.offlinemap.DownloadProgressView __obj__ = new com.amap.api.maps.offlinemap.DownloadProgressView(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_Province__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_Province__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.Province __obj__ = new com.amap.api.maps.offlinemap.Province();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_OfflineMapCity__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_OfflineMapCity__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.OfflineMapCity __obj__ = new com.amap.api.maps.offlinemap.OfflineMapCity();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_OfflineMapActivity__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_OfflineMapActivity__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.OfflineMapActivity __obj__ = new com.amap.api.maps.offlinemap.OfflineMapActivity();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_City__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_City__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.offlinemap.City __obj__ = new com.amap.api.maps.offlinemap.City();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_DownLoadListView__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_DownLoadListView__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.offlinemap.DownLoadListView __obj__ = new com.amap.api.maps.offlinemap.DownLoadListView(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_DownLoadExpandListView__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_DownLoadExpandListView__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.offlinemap.DownLoadExpandListView __obj__ = new com.amap.api.maps.offlinemap.DownLoadExpandListView(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_offlinemap_CityExpandView__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_offlinemap_CityExpandView__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.offlinemap.CityExpandView __obj__ = new com.amap.api.maps.offlinemap.CityExpandView(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_HeatMapLayerOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_HeatMapLayerOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.HeatMapLayerOptions __obj__ = new com.amap.api.maps.model.HeatMapLayerOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PolygonHoleOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PolygonHoleOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PolygonHoleOptions __obj__ = new com.amap.api.maps.model.PolygonHoleOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TileOverlayOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TileOverlayOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.TileOverlayOptionsCreator __obj__ = new com.amap.api.maps.model.TileOverlayOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_Poi__String__com_amap_api_maps_model_LatLng__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_Poi__String__com_amap_api_maps_model_LatLng__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.LatLng var2 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                String var3 = (String) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.Poi __obj__ = new com.amap.api.maps.model.Poi(var1, var2, var3);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MyLocationStyle__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MyLocationStyle__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MyLocationStyle __obj__ = new com.amap.api.maps.model.MyLocationStyle();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_VisibleRegion__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLngBounds", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_VisibleRegion__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLngBounds");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.LatLng var2 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                com.amap.api.maps.model.LatLng var3 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                com.amap.api.maps.model.LatLng var4 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                com.amap.api.maps.model.LatLngBounds var5 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var5");
            
                // create target object
                com.amap.api.maps.model.VisibleRegion __obj__ = new com.amap.api.maps.model.VisibleRegion(var1, var2, var3, var4, var5);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLngCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLngCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.LatLngCreator __obj__ = new com.amap.api.maps.model.LatLngCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CircleHoleOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CircleHoleOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CircleHoleOptions __obj__ = new com.amap.api.maps.model.CircleHoleOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_Text__com_amap_api_maps_model_Marker__com_amap_api_maps_model_TextOptions", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_Text__com_amap_api_maps_model_Marker__com_amap_api_maps_model_TextOptions");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.Marker var1 = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.TextOptions var2 = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.Text __obj__ = new com.amap.api.maps.model.Text(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLngBounds_Builder__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLngBounds_Builder__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.LatLngBounds.Builder __obj__ = new com.amap.api.maps.model.LatLngBounds.Builder();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_NavigateArrowOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_NavigateArrowOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.NavigateArrowOptions __obj__ = new com.amap.api.maps.model.NavigateArrowOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_ColorLatLng__java_util_List_com_amap_api_maps_model_LatLng___int", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_ColorLatLng__java_util_List_com_amap_api_maps_model_LatLng___int");
                }
            
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.ColorLatLng __obj__ = new com.amap.api.maps.model.ColorLatLng(var1, var2.intValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_BitmapDescriptorFactory__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_BitmapDescriptorFactory__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.BitmapDescriptorFactory __obj__ = new com.amap.api.maps.model.BitmapDescriptorFactory();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MVTTileProvider__String__String__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MVTTileProvider__String__String__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                String var2 = (String) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                String var3 = (String) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.MVTTileProvider __obj__ = new com.amap.api.maps.model.MVTTileProvider(var1, var2, var3);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MultiPointOverlayOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MultiPointOverlayOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MultiPointOverlayOptions __obj__ = new com.amap.api.maps.model.MultiPointOverlayOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PoiCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PoiCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PoiCreator __obj__ = new com.amap.api.maps.model.PoiCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PolylineOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PolylineOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PolylineOptions __obj__ = new com.amap.api.maps.model.PolylineOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_Tile__int__int__byteArray", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_Tile__int__int__byteArray");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                byte[] var3 = (byte[]) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.Tile __obj__ = new com.amap.api.maps.model.Tile(var1.intValue(), var2.intValue(), var3);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_Tile__int__int__byteArray__boolean", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_Tile__int__int__byteArray__boolean");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                byte[] var3 = (byte[]) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                boolean var4 = (boolean) ((Map<String, Object>) __args__).get("var4");
            
                // create target object
                com.amap.api.maps.model.Tile __obj__ = new com.amap.api.maps.model.Tile(var1.intValue(), var2.intValue(), var3, var4);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_Gradient__intArray__floatArray", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_Gradient__intArray__floatArray");
                }
            
                // args
                // ref arg
                int[] var1 = (int[]) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                float[] var2 = (float[]) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.Gradient __obj__ = new com.amap.api.maps.model.Gradient(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_HeatMapItem__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_HeatMapItem__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.HeatMapItem __obj__ = new com.amap.api.maps.model.HeatMapItem();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_NaviPara__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_NaviPara__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.NaviPara __obj__ = new com.amap.api.maps.model.NaviPara();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_GroundOverlayOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_GroundOverlayOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.GroundOverlayOptions __obj__ = new com.amap.api.maps.model.GroundOverlayOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_GL3DModelOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_GL3DModelOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.GL3DModelOptions __obj__ = new com.amap.api.maps.model.GL3DModelOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MyTrafficStyle__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MyTrafficStyle__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MyTrafficStyle __obj__ = new com.amap.api.maps.model.MyTrafficStyle();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CameraPosition__com_amap_api_maps_model_LatLng__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CameraPosition__com_amap_api_maps_model_LatLng__float__float__float");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
            
                // create target object
                com.amap.api.maps.model.CameraPosition __obj__ = new com.amap.api.maps.model.CameraPosition(var1, var2.floatValue(), var3.floatValue(), var4.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TextOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TextOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.TextOptionsCreator __obj__ = new com.amap.api.maps.model.TextOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PoiPara__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PoiPara__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PoiPara __obj__ = new com.amap.api.maps.model.PoiPara();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MarkerOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MarkerOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MarkerOptions __obj__ = new com.amap.api.maps.model.MarkerOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_AnimationSet__boolean", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_AnimationSet__boolean");
                }
            
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.animation.AnimationSet __obj__ = new com.amap.api.maps.model.animation.AnimationSet(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_RotateAnimation__float__float__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_RotateAnimation__float__float__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
            
                // create target object
                com.amap.api.maps.model.animation.RotateAnimation __obj__ = new com.amap.api.maps.model.animation.RotateAnimation(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue(), var5.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_RotateAnimation__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_RotateAnimation__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.animation.RotateAnimation __obj__ = new com.amap.api.maps.model.animation.RotateAnimation(var1.floatValue(), var2.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_TranslateAnimation__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_TranslateAnimation__com_amap_api_maps_model_LatLng");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.animation.TranslateAnimation __obj__ = new com.amap.api.maps.model.animation.TranslateAnimation(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_EmergeAnimation__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_EmergeAnimation__com_amap_api_maps_model_LatLng");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.animation.EmergeAnimation __obj__ = new com.amap.api.maps.model.animation.EmergeAnimation(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_AlphaAnimation__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_AlphaAnimation__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.animation.AlphaAnimation __obj__ = new com.amap.api.maps.model.animation.AlphaAnimation(var1.floatValue(), var2.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_animation_ScaleAnimation__float__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_animation_ScaleAnimation__float__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
            
                // create target object
                com.amap.api.maps.model.animation.ScaleAnimation __obj__ = new com.amap.api.maps.model.animation.ScaleAnimation(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLngBounds__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLngBounds__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.LatLng var2 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.LatLngBounds __obj__ = new com.amap.api.maps.model.LatLngBounds(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CustomMapStyleOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CustomMapStyleOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CustomMapStyleOptions __obj__ = new com.amap.api.maps.model.CustomMapStyleOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TileOverlaySource__int__int__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TileOverlaySource__int__int__String");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                String var3 = (String) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.TileOverlaySource __obj__ = new com.amap.api.maps.model.TileOverlaySource(var1.intValue(), var2.intValue(), var3);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CrossOverlayOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CrossOverlayOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CrossOverlayOptions __obj__ = new com.amap.api.maps.model.CrossOverlayOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_VisibleRegionCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_VisibleRegionCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.VisibleRegionCreator __obj__ = new com.amap.api.maps.model.VisibleRegionCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLng__double__double", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLng__double__double");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.LatLng __obj__ = new com.amap.api.maps.model.LatLng(var1.doubleValue(), var3.doubleValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLng__double__double__boolean", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLng__double__double__boolean");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                boolean var5 = (boolean) ((Map<String, Object>) __args__).get("var5");
            
                // create target object
                com.amap.api.maps.model.LatLng __obj__ = new com.amap.api.maps.model.LatLng(var1.doubleValue(), var3.doubleValue(), var5);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TileProjection__int__int__int__int__int__int", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TileProjection__int__int__int__int__int__int");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                // ref arg
                Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
            
                // create target object
                com.amap.api.maps.model.TileProjection __obj__ = new com.amap.api.maps.model.TileProjection(var1.intValue(), var2.intValue(), var3.intValue(), var4.intValue(), var5.intValue(), var6.intValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_AMapPara__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_AMapPara__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.AMapPara __obj__ = new com.amap.api.maps.model.AMapPara();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CameraPosition_Builder__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CameraPosition_Builder__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CameraPosition.Builder __obj__ = new com.amap.api.maps.model.CameraPosition.Builder();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CameraPosition_Builder__com_amap_api_maps_model_CameraPosition", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CameraPosition_Builder__com_amap_api_maps_model_CameraPosition");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.CameraPosition var1 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.CameraPosition.Builder __obj__ = new com.amap.api.maps.model.CameraPosition.Builder(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CircleOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CircleOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CircleOptionsCreator __obj__ = new com.amap.api.maps.model.CircleOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_AMapCameraInfo__float__float__float__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_AMapCameraInfo__float__float__float__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                // ref arg
                Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
            
                // create target object
                com.amap.api.maps.model.AMapCameraInfo __obj__ = new com.amap.api.maps.model.AMapCameraInfo(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue(), var5.floatValue(), var6.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CrossOverlay_UpdateItem__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CrossOverlay_UpdateItem__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CrossOverlay.UpdateItem __obj__ = new com.amap.api.maps.model.CrossOverlay.UpdateItem();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PolygonOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PolygonOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PolygonOptions __obj__ = new com.amap.api.maps.model.PolygonOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_WeightedLatLng__com_amap_api_maps_model_LatLng__double", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_WeightedLatLng__com_amap_api_maps_model_LatLng__double");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.WeightedLatLng __obj__ = new com.amap.api.maps.model.WeightedLatLng(var1, var2.doubleValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_WeightedLatLng__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_WeightedLatLng__com_amap_api_maps_model_LatLng");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.WeightedLatLng __obj__ = new com.amap.api.maps.model.WeightedLatLng(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MyLocationStyleCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MyLocationStyleCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MyLocationStyleCreator __obj__ = new com.amap.api.maps.model.MyLocationStyleCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MarkerOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MarkerOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MarkerOptionsCreator __obj__ = new com.amap.api.maps.model.MarkerOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MultiPointItem__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MultiPointItem__com_amap_api_maps_model_LatLng");
                }
            
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.MultiPointItem __obj__ = new com.amap.api.maps.model.MultiPointItem(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_LatLngBoundsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_LatLngBoundsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.LatLngBoundsCreator __obj__ = new com.amap.api.maps.model.LatLngBoundsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_ArcOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_ArcOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.ArcOptions __obj__ = new com.amap.api.maps.model.ArcOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_BitmapDescriptorCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_BitmapDescriptorCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.BitmapDescriptorCreator __obj__ = new com.amap.api.maps.model.BitmapDescriptorCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MVTTileOverlayOptions_Builder__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MVTTileOverlayOptions_Builder__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.MVTTileOverlayOptions.Builder __obj__ = new com.amap.api.maps.model.MVTTileOverlayOptions.Builder();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CircleOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CircleOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CircleOptions __obj__ = new com.amap.api.maps.model.CircleOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_NavigateArrowOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_NavigateArrowOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.NavigateArrowOptionsCreator __obj__ = new com.amap.api.maps.model.NavigateArrowOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_ImageOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_ImageOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.ImageOptions __obj__ = new com.amap.api.maps.model.ImageOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_HeatMapGridLayerOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_HeatMapGridLayerOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.HeatMapGridLayerOptions __obj__ = new com.amap.api.maps.model.HeatMapGridLayerOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_RuntimeRemoteException__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_RuntimeRemoteException__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.RuntimeRemoteException __obj__ = new com.amap.api.maps.model.RuntimeRemoteException(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_HeatmapTileProvider_Builder__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_HeatmapTileProvider_Builder__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.HeatmapTileProvider.Builder __obj__ = new com.amap.api.maps.model.HeatmapTileProvider.Builder();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_BaseOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_BaseOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.BaseOptions __obj__ = new com.amap.api.maps.model.BaseOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PolylineOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PolylineOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PolylineOptionsCreator __obj__ = new com.amap.api.maps.model.PolylineOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_BaseOverlay__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_BaseOverlay__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.BaseOverlay __obj__ = new com.amap.api.maps.model.BaseOverlay(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TileOverlayOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TileOverlayOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.TileOverlayOptions __obj__ = new com.amap.api.maps.model.TileOverlayOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_RoutePara__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_RoutePara__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.RoutePara __obj__ = new com.amap.api.maps.model.RoutePara();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_ArcOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_ArcOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.ArcOptionsCreator __obj__ = new com.amap.api.maps.model.ArcOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_CameraPositionCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_CameraPositionCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.CameraPositionCreator __obj__ = new com.amap.api.maps.model.CameraPositionCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_ParticleOverLifeModule__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_ParticleOverLifeModule__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.particle.ParticleOverLifeModule __obj__ = new com.amap.api.maps.model.particle.ParticleOverLifeModule();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_ConstantRotationOverLife__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_ConstantRotationOverLife__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.model.particle.ConstantRotationOverLife __obj__ = new com.amap.api.maps.model.particle.ConstantRotationOverLife(var1.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_SinglePointParticleShape__float__float__float__boolean", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_SinglePointParticleShape__float__float__float__boolean");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                boolean var4 = (boolean) ((Map<String, Object>) __args__).get("var4");
            
                // create target object
                com.amap.api.maps.model.particle.SinglePointParticleShape __obj__ = new com.amap.api.maps.model.particle.SinglePointParticleShape(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_SinglePointParticleShape__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_SinglePointParticleShape__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.particle.SinglePointParticleShape __obj__ = new com.amap.api.maps.model.particle.SinglePointParticleShape(var1.floatValue(), var2.floatValue(), var3.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_RandomVelocityBetweenTwoConstants__float__float__float__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_RandomVelocityBetweenTwoConstants__float__float__float__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                // ref arg
                Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
            
                // create target object
                com.amap.api.maps.model.particle.RandomVelocityBetweenTwoConstants __obj__ = new com.amap.api.maps.model.particle.RandomVelocityBetweenTwoConstants(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue(), var5.floatValue(), var6.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_CurveSizeOverLife__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_CurveSizeOverLife__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.particle.CurveSizeOverLife __obj__ = new com.amap.api.maps.model.particle.CurveSizeOverLife(var1.floatValue(), var2.floatValue(), var3.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_ParticleEmissionModule__int__int", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_ParticleEmissionModule__int__int");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.model.particle.ParticleEmissionModule __obj__ = new com.amap.api.maps.model.particle.ParticleEmissionModule(var1.intValue(), var2.intValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_RectParticleShape__float__float__float__float__boolean", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_RectParticleShape__float__float__float__float__boolean");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                boolean var5 = (boolean) ((Map<String, Object>) __args__).get("var5");
            
                // create target object
                com.amap.api.maps.model.particle.RectParticleShape __obj__ = new com.amap.api.maps.model.particle.RectParticleShape(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue(), var5);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_RandomColorBetWeenTwoConstants__float__float__float__float__float__float__float__float", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_RandomColorBetWeenTwoConstants__float__float__float__float__float__float__float__float");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                // ref arg
                Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
                // ref arg
                Number var7 = (Number) ((Map<String, Object>) __args__).get("var7");
                // ref arg
                Number var8 = (Number) ((Map<String, Object>) __args__).get("var8");
            
                // create target object
                com.amap.api.maps.model.particle.RandomColorBetWeenTwoConstants __obj__ = new com.amap.api.maps.model.particle.RandomColorBetWeenTwoConstants(var1.floatValue(), var2.floatValue(), var3.floatValue(), var4.floatValue(), var5.floatValue(), var6.floatValue(), var7.floatValue(), var8.floatValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_ParticleOverlayOptionsFactory__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_ParticleOverlayOptionsFactory__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory __obj__ = new com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_particle_ParticleOverlayOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_particle_ParticleOverlayOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.particle.ParticleOverlayOptions __obj__ = new com.amap.api.maps.model.particle.ParticleOverlayOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_MVTTileOverlayOptions__String__String__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_MVTTileOverlayOptions__String__String__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                String var2 = (String) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                String var3 = (String) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.maps.model.MVTTileOverlayOptions __obj__ = new com.amap.api.maps.model.MVTTileOverlayOptions(var1, var2, var3);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TextOptions__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TextOptions__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.TextOptions __obj__ = new com.amap.api.maps.model.TextOptions();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_PolygonOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_PolygonOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.PolygonOptionsCreator __obj__ = new com.amap.api.maps.model.PolygonOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_TileProjectionCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_TileProjectionCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.TileProjectionCreator __obj__ = new com.amap.api.maps.model.TileProjectionCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_GroundOverlayOptionsCreator__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_GroundOverlayOptionsCreator__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.GroundOverlayOptionsCreator __obj__ = new com.amap.api.maps.model.GroundOverlayOptionsCreator();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_BaseOptions_BaseUpdateFlags__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_BaseOptions_BaseUpdateFlags__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.BaseOptions.BaseUpdateFlags __obj__ = new com.amap.api.maps.model.BaseOptions.BaseUpdateFlags();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_model_IndoorBuildingInfo__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_model_IndoorBuildingInfo__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.model.IndoorBuildingInfo __obj__ = new com.amap.api.maps.model.IndoorBuildingInfo();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_TextureMapView__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_TextureMapView__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.maps.TextureMapView __obj__ = new com.amap.api.maps.TextureMapView(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_TextureMapView__android_content_Context__com_amap_api_maps_AMapOptions", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_TextureMapView__android_content_Context__com_amap_api_maps_AMapOptions");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.AMapOptions var2 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.maps.TextureMapView __obj__ = new com.amap.api.maps.TextureMapView(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_AMapUtils__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_AMapUtils__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.AMapUtils __obj__ = new com.amap.api.maps.AMapUtils();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_maps_TextureMapFragment__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_maps_TextureMapFragment__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.maps.TextureMapFragment __obj__ = new com.amap.api.maps.TextureMapFragment();
            
                __methodResult__.success(__obj__);
            });
        }};
    }
}
