import 'package:collection/collection.dart';

import 'item_list.dart';

class SupplierGroupList {
  List<dynamic>? activityList;
  String? supplierName;
  String? supplierId;
  dynamic shuttingStartTime;
  dynamic shuttingEndTime;
  double? minAmt;
  num? globalMinAmt;
  bool? minAmtQualified;
  bool? debt;
  num? totalAmt;
  String? productDistributionLabel;
  num? isNegativeStock;
  List<ItemList>? itemList;

  SupplierGroupList({
    this.activityList,
    this.supplierName,
    this.supplierId,
    this.shuttingStartTime,
    this.shuttingEndTime,
    this.minAmt,
    this.globalMinAmt,
    this.minAmtQualified,
    this.debt,
    this.totalAmt,
    this.productDistributionLabel,
    this.isNegativeStock,
    this.itemList,
  });

  factory SupplierGroupList.fromJson(Map<String, dynamic> json) {
    return SupplierGroupList(
      activityList: json['activityList'] as List<dynamic>?,
      supplierName: json['supplierName'] as String?,
      supplierId: json['supplierId'] as String?,
      shuttingStartTime: json['shuttingStartTime'] as dynamic,
      shuttingEndTime: json['shuttingEndTime'] as dynamic,
      minAmt: (json['minAmt'] as num?)?.toDouble(),
      globalMinAmt: json['globalMinAmt'] as num?,
      minAmtQualified: json['minAmtQualified'] as bool?,
      debt: json['debt'] as bool?,
      totalAmt: json['totalAmt'] as num?,
      productDistributionLabel: json['productDistributionLabel'] as String?,
      isNegativeStock: json['isNegativeStock'] as num?,
      itemList: (json['itemList'] as List<dynamic>?)
          ?.map((e) => ItemList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'activityList': activityList,
        'supplierName': supplierName,
        'supplierId': supplierId,
        'shuttingStartTime': shuttingStartTime,
        'shuttingEndTime': shuttingEndTime,
        'minAmt': minAmt,
        'globalMinAmt': globalMinAmt,
        'minAmtQualified': minAmtQualified,
        'debt': debt,
        'totalAmt': totalAmt,
        'productDistributionLabel': productDistributionLabel,
        'isNegativeStock': isNegativeStock,
        'itemList': itemList?.map((e) => e.toJson()).toList(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! SupplierGroupList) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      activityList.hashCode ^
      supplierName.hashCode ^
      supplierId.hashCode ^
      shuttingStartTime.hashCode ^
      shuttingEndTime.hashCode ^
      minAmt.hashCode ^
      globalMinAmt.hashCode ^
      minAmtQualified.hashCode ^
      debt.hashCode ^
      totalAmt.hashCode ^
      productDistributionLabel.hashCode ^
      isNegativeStock.hashCode ^
      itemList.hashCode;
}
