import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/branch_list_bean.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:flustars/flustars.dart';
import '../../../base/base_logic.dart';
import 'state.dart';

class B2bCustomerSearchPageLogic extends BaseLogic {
  final B2bCustomerSearchPageState state = B2bCustomerSearchPageState();

  AmapController? controller;
  Location? _location; // 用户定位信息

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onInit() {
    super.onInit();
  }

  void confirmSearch(searchText) {
    state.searchText = searchText;
    searchBranches(searchText);
  }

  mapAddMarker(latitude, longitude) async {
    double lat = double.parse(latitude);
    double long = double.parse(longitude);
    await controller?.clear();
    var marker = MarkerOption(
        coordinate: LatLng(lat, long),
        visible: true,
        infoWindowEnabled: true,
        widget: const Icon(Icons.location_on, size: 24, color: Colors.red));
    controller?.addMarker(marker);
  }

  /// 搜索门店
  void searchBranches(String keyword) {
    state.searchKeyword = keyword;
    getBranchList(isRefresh: true);
  }

  /// 获取门店列表数据
  /// [isRefresh] 是否是刷新操作，true=刷新，false=加载更多
  void getBranchList({bool isRefresh = true}) {
    if (state.isLoading) return;

    if (isRefresh) {
      state.resetPagination();
      state.isRefreshing = true;
    } else {
      if (!state.hasMore) return;
      state.currentPage++;
    }

    state.isLoading = true;

    // 构建请求参数
    final request = BranchListRequest(
      pageNo: state.currentPage,
      pageSize: state.pageSize,
      isOpenSeas: 0, // 私有客户
      distance: "0", // 搜索页面不限制距离
    );

    // 如果有搜索关键词，使用 searchName 字段
    if (state.searchKeyword.isNotEmpty) {
      request.searchName = state.searchKeyword;
    }

    // 获取用户信息中的业务员ID
    dynamic userInfo = SpUtil.getObject('userInfo');
    if (userInfo['colonelId'] != null) {
      request.colonelId = int.tryParse(userInfo['colonelId'].toString());
    }

    SmartDialog.showLoading(msg: "搜索中...");

    // 调用API
    MyDio.get(
      Apis.b2bAppLists,
      queryParameters: request.toJson(),
      successCallBack: (value) {
        state.isLoading = false;
        state.isRefreshing = false;
        SmartDialog.dismiss();

        try {
          final response = BranchListBean.fromJson(value);
          if (response.code == 200 || response.code == 0) {
            final branchData = response.data;
            if (branchData != null && branchData.list != null) {
              if (isRefresh) {
                state.branchList = branchData.list!;
              } else {
                state.branchList.addAll(branchData.list!);
              }

              state.updatePagination(branchData.total ?? 0, branchData.list!.length);

              // 同时更新旧的CustomerSearch以保持兼容性
              state.CustomerSearch = state.branchList.map((branch) => {
                'branchId': branch.branchId,
                'branchNo': branch.branchNo,
                'branchName': branch.branchName,
                'branchAddr': branch.branchAddr,
                'longitude': branch.longitude,
                'latitude': branch.latitude,
                'contactPhone': branch.contactPhone,
                'lastVisitDay': branch.lastVisitDays,
                'lastActivityDay': branch.lastOrderDays,
                'lastVisitDays': branch.lastVisitDays,
                'lastOrderDays': branch.lastOrderDays,
                'branchImages': branch.branchImages,
                'imgUrls': branch.branchImages, // 保持向后兼容
              }).toList();

              printLong("门店搜索成功: ${state.branchList.length}条数据");
            } else {
              if (isRefresh) {
                state.branchList.clear();
                state.CustomerSearch.clear();
              }
            }
          } else {
            MyCommonUtils.showToast(response.msg ?? "搜索门店失败");
          }
        } catch (e) {
          print("解析门店搜索数据失败: $e");
          MyCommonUtils.showToast("数据解析失败");
        }

        update();
      },
      failCallBack: (error) {
        state.isLoading = false;
        state.isRefreshing = false;
        SmartDialog.dismiss();
        MyCommonUtils.showToast("网络请求失败");
        update();
      },
    );
  }

  /// 下拉刷新
  Future<void> onRefresh() async {
    return Future(() {
      getBranchList(isRefresh: true);
    });
  }

  /// 上拉加载更多
  void loadMore() {
    if (state.hasMore && !state.isLoading) {
      getBranchList(isRefresh: false);
    }
  }

  /// 清空搜索
  void clearSearch() {
    state.searchKeyword = "";
    state.searchText = "";
    getBranchList(isRefresh: true);
  }
}
