import 'dart:async';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/provider/event/validate_sync_event.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/date_Utils.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:fuduoduo/utils/upgrade_utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'state.dart';

import 'package:fuduoduo/store/index.dart';
import 'dart:math';
import 'package:fuduoduo/utils/storage_common.dart';
import 'package:fuduoduo/domain/b2b_user_profile_bean.dart';
class B2bPage_viewLogic extends GetxController {
  final B2bPage_viewState state = B2bPage_viewState();
  RefreshController controller = RefreshController();

  StreamSubscription? _validateSyncSubscription;

  final publicState = Get.find<Public>().state;
  var _pow;

  @override
  void onInit() {
    super.onInit();
    // 获取版本更新信息
    getUpgradeInfo();
    // getDealerNo();
    getLocalUserInfo();
    // getUserStatusInfo();
    // getValidateFlagInfo(false);
    getKingKongList();
    // 获取个人绩效统计数据
    getPersonalPerformanceStatistics();
    // getHomePageTotalData("", "", "");
    // getTeamData();
    print('首页个人信息===========${SpUtil.getObject('userInfo')}');

    _validateSyncSubscription =
        validateSyncEventBus.on<ValidateSyncEvent>().listen((event) {
      getValidateFlagInfo(false);
    });
  }

  /* 获取个人绩效统计列表(b2b)
  * timeRange 时间范围，需为 yesterday（昨日）、week（本周）、month（本月）之一
  */
  void getPersonalPerformanceStatistics() {
    String timeRange = 'day'; // 默认今日
    // 根据 dateType 转换时间范围参数
    switch (state.dateType.value) {
      case '0': // 昨日
        timeRange = 'yesterday';
        break;
      case '1': // 本周
        timeRange = 'week';
        break;
      case '2': // 本月
        timeRange = 'month';
        break;
    }

    var token = SpUtil.getString('access_token');
    if (token == null) return;

    MyDio.get(Apis.getPersonalPerformanceStatistics,
        queryParameters: {"timeRange": timeRange},
        successCallBack: (value) {
      debugPrint("个人绩效统计数据: $value");
      var response = DioResultBean.fromJson(value);
      if (response.code == '0' || response.code == 0) {
        dynamic data = response.data ?? {};
        state.personalPerformanceData.value = data;
        // 更新个人业绩列表
        setNewIndividualList();

        // 更新今日订单金额
        if (state.dateType.value == "0") {
          state.todayOrderMount.value = (data["totalOrderAmount"] ?? 0.0).toStringAsFixed(2);
        }
      } else if (response.code == "401") {
        MyCommonUtils.showToast('登录失效，请重新登录');
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.msg ?? '获取数据失败');
      }
    }, failCallBack: (value) {
      debugPrint("获取个人绩效统计数据失败: $value");
    }, showErrMsg: false);
  }

  /* 首页汇总数据
  * employeeNo 业务员编号
  * startTime 开始时间(年月日)
  * endTime 结束时间(年月日)
  * sheetSource 订单来源
  */
  void getHomePageTotalData(
      String startTime, String endTime, String sheetSource) {
    startTime = calendarManager(int.parse(state.dateType.value ?? "0"));
    endTime = calendarManager(0);
    // print('=============${state.dateType.value}====$startTime===$endTime');
    sheetSource = '';
    var token = SpUtil.getString('access_token');
    if (token == null) return;
    MyDio.get(Apis.getHomePageTotalData, queryParameters: {
      "employeeNo": '',
      "startTime": startTime,
      "endTime": endTime,
      "sheetSource": sheetSource
    }, successCallBack: (value) {
      debugPrint("cjn--->>>> 首页数据 ${value}  state.dateType.value ${state.dateType.value}");
      // debugPrint("cjn--->>>> 首页数据 ${json.encode(value)}");
      controller.refreshCompleted();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic data = response.data ?? {};


        // 测试
        // data['xdMoney'] = 1;
        // data['fhDataCount'] = 1;


        state.personalDataInfo.value = data;
        setIndividualList(state.dateType.value);
        if (state.dateType.value == "0") {

          // state.todayOrderMount.value = (data["ywDataCount"] ?? 0.00) +
          //     (data["dzDataCount"] ?? 0.00) +
          //     (data["htDataCount"] ?? 0.00) -
          //     (data["thDataCount"] ?? 0.00);
           var _todayOrderMount = (double.parse(data["ywDataCountStr"] ?? 0.00)) +
              (double.parse(data["dzDataCountStr"] ?? 0.00)) + (double.parse(data["htDataCountStr"] ?? 0.00));
           // print('=====1======== ${_todayOrderMount}');
          state.todayOrderMount.value = doubleNotlossAuth(double.parse((_todayOrderMount ?? 0.0).toString()), decimal: _pow) ?? "0.00";
          // print('=====1======== ${state.todayOrderMount.value}');
        } // MyCommonUtils.showToast("数据请求成功，刷新页面");
      } else if (response.code == "401") {
        MyCommonUtils.showToast('登录失效，请重新登录');
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

//团队业绩
  getTeamData() {
    // if (refresh) {
    //   state.pageNo = 1;
    // } else {
    //   state.pageNo += 1;
    // }
    String startTime = calendarManager(int.parse(state.dateType.value ?? "0"));
    String endTime = calendarManager(0);
    String sheetSource = "";
    String searchSource = "";
    var token = SpUtil.getString('access_token');
    if (token == null) return;
    MyDio.get(Apis.getHomePageTotalTeamData, queryParameters: {
      "employeeNo": "",
      "startTime": startTime,
      "endTime": endTime,
      "sheetSource": sheetSource,
      "searchSource": searchSource,
      "pageNo": state.pageNo,
      "pageSize": state.pageSize
    }, successCallBack: (value) {
      controller.refreshCompleted();
      var response = DioResultBean.fromJson(value);
      if (response.code == "200") {
        state.teamTotal.value = response.data?['total'] ?? 0;
        dynamic data = response.data?["items"] ?? [];
        state.teamDataInfo.value = data;
        state.teamDataInfo.insert(0, {});
        // if (refresh) {
        //   state.teamDataInfo = data;
        //   refreshController.refreshCompleted(resetFooterState: true);
        //   if (state.teamDataInfo.length >= state.teamTotal) {
        //     refreshController.loadNoData();
        //   }
        // } else {
        //   state.teamDataInfo.addall(data);
        //   refreshController.loadComplete();
        //   if (state.teamDataInfo.length >= state.teamTotal) {
        //     refreshController.loadNoData();
        //   }
        // }
        if (state.teamDataInfo.length >= state.teamTotal.value + 1) {
          controller.loadNoData();
        }
        print('===============绘制i======${state.teamTotal.value}');
        print('===============绘制h======${state.teamDataInfo.length}');
      } else if (response.code == "401") {
        MyCommonUtils.showToast('登录失效，请重新登录');
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        Get.offAllNamed(PageName.LOGIN);
      } else {
        MyCommonUtils.showToast(response.msg!);
        // if (refresh) {
        //   refreshController.refreshCompleted(resetFooterState: true);
        // } else {
        //   state.pageNo -= 1;
        //   refreshController.loadFailed();
        // }
      }
    });
  }

  // 获取经销商编号
  void getDealerNo() {
    MyDio.get(
      Apis.searchDealerNo,
      successCallBack: (value) {
        Map result = Map.from(value);
        if (result['code'].toString() == '200') {
          String dealerNo = result['data']['branchNo'].toString();
          SpUtil.putString('dealerNo', dealerNo);
        }
      },
    );
  }

  void checkShowShopScan() {
    dynamic userInfo = SpUtil.getObject('userInfo') ?? {};
    if (userInfo == {}) {
      getLocalUserInfo();
      return;
    }
    dynamic employee = userInfo['employee'] ?? {};
    MyDio.get(Apis.checkShowShopScan,
        queryParameters: {'employeeNo': employee['employeeNo']},
        successCallBack: (value) {
      DioResultBean response = DioResultBean.fromJson(value);
      if (response.code.toString() == '200') {
        String data = response.data.toString();
        SpUtil.putString('showShopScan', data);
        getKingKongList();
      }
    }, showErrMsg: false);
  }

  /**
   * @时间 2024/3/7 18:29
   * @作者 YuanYanKai
   * @方法名 getKingKongList
   * @说明  获取金刚区功能
   */
  void getKingKongList() {
    List tempList = [];
    if (UserAppModuleUtils().moduleCanShow('custom')) {
      tempList.add({
        "name": "客户",
        "path": PageName.B2bCustomerListPage,
        "icon": 'assets/images/kehu.png'
      });
    }
    if (UserAppModuleUtils().moduleCanShow('replenishment')) {
      tempList.add({
        "name": "补货",
        "path": PageName.ReplenishmentPage,
        "icon": 'assets/images/buhuo.png'
      });
    }
    if (UserAppModuleUtils().moduleCanShow('arrears')) {
      tempList.add({
        "name": "欠款",
        "path": PageName.DeptCustomerListPage,
        "icon": 'assets/images/qiankuan.png'
      });
    }
    if (UserAppModuleUtils().moduleCanShow('expand_shop')) {
      tempList.add({
        "name": "拓店",
        "path": PageName.B2bStoreInfoPage,
        "icon": 'assets/images/tuodian.png'
      });
    }
    if (UserAppModuleUtils().moduleCanShow('return_warehouse')) {
      tempList.add({
        "name": "返仓",
        "path": PageName.ReplenishmentPage,
        "icon": 'assets/images/fancang.png'
      });
    }
    if (UserAppModuleUtils().moduleCanShow('inventory')) {
      tempList.add({
        "name": "盘点",
        "path": PageName.ReplenishmentPage,
        "icon": 'assets/images/pandian.png'
      });
    }
    // if (UserAppModuleUtils().moduleCanShow('unload')) {
    //   tempList.add({
    //     "name": "卸货",
    //     "path": PageName.CloudCommerceUnloadPage,
    //     "icon": 'assets/images/xiehuo.png'
    //   });
    // }
    if (SpUtil.getString('showShopScan', defValue: '0') != '0') {
      tempList.add({
        "name": "店铺预览",
        "path": PageName.SelectShopPage,
        "icon": 'assets/images/shop_preview_icon.png'
      });
    }

    if (UserAppModuleUtils().moduleCanShow('place_order')) {
      tempList.add({
        "name": "开单",
        "path": PageName.PlaceOrderPage,
        "icon": 'assets/images/kaidan.png'
      });
    }

    // if ((SpUtil.getObject('userInfo') != null) &&
    //     (SpUtil.getObject('userInfo')?['user'] != null) &&
    //     (SpUtil.getObject('userInfo')?['user']["whetherBoss"] != null)) {
    //   print(
    //       "whetherBoss=>${SpUtil.getObject('userInfo')?['user']["whetherBoss"]}");
    //   if (SpUtil.getObject('userInfo')?['user']["whetherBoss"] == '1') {
    //     tempList.add({
    //       "name": "提现",
    //       "path": PageName.InAppWebViewPage,
    //       "icon": 'assets/images/tixian.png'
    //     });
    //   }
    // }

    state.tempList.value = tempList;
  }

  //查询经销商信息
  getCloudShopInfo() {
    MyDio.get(Apis.getCloudShopInfo, queryParameters: {},
        successCallBack: (value) {
      if (value["code"] == 200) {
        String dealerId = value["data"]["branchNo"] ?? "";
        String dealerName = value["data"]["ysBranchName"] ?? "";
        print("dealerId=>${dealerId}");
        print("dealerName=>${dealerName}");
        //https://tf-shop.tongfuyouxuan.com/frontend/tf-business-h5/index.html#/subPages/taskCenter/cashReward?dealerId=xxx&dealerName=xxx
        String withdrawUrl = SetInfo.instance.missionCenterUrl +
            "frontend/tf-business-h5/index.html#/subPages/taskCenter/cashReward" +
            "?dealerId=${dealerId}&dealerName=${dealerName}&channelSource=fst";
        print("withdrawUrl=>${withdrawUrl}");

        Get.toNamed(PageName.InAppWebViewPage, arguments: {'url': withdrawUrl});
      } else {
        if (value["msg"] != null) {
          MyCommonUtils.showToast(value["msg"]);
        }
      }
    });
  }

  //查询用户信息
  getLocalUserInfo() async {
    var userType = await SecureStorageCommon.save('userType').get();
    print('xxxx=== 2 $userType');
    Map<String, dynamic>? queryParameters = {};

    if (userType == 'b2bSystem') {
      MyDio.get(Apis.b2bUserinfo, queryParameters: queryParameters,
          successCallBack: (value) {
            B2bUserProfileBean userProfile = B2bUserProfileBean.fromJson(value);
            publicState.userInfo = userProfile.data;
            SpUtil.putObject('userInfo', value['data']);
            state.userInfo.value = value['data'];
            update();
          }
      );
    }

  }

  //查询用户是否启用
  getUserStatusInfo() {
    MyDio.get(Apis.getUserStatusInfo, queryParameters: {},
        successCallBack: (value) {
      if (value["code"] == 200) {
        // 0 停用 1 启用
        if (value["data"] == 1) {
          SpUtil.putBool('StartFlag', true);
        } else {
          SpUtil.putBool('StartFlag', false);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("StartFlag=>${SpUtil.getBool('StartFlag')}");
      update();
    }, showErrMsg: false);
  }

  // 查询手机号是否验证 0未验证 1已验证
  getValidateFlagInfo(bool needJump) {
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getValidateFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if ((value["data"] != null) &&
            (value["data"]["phoneStatus"] != null) &&
            (value["data"]["phoneStatus"] == "1")) {
          state.validateFlag.value = true;
          SpUtil.putBool("validateFlag", true);
        } else {
          state.validateFlag.value = false;
          SpUtil.putBool("validateFlag", false);
        }

        if (needJump == true) {
          getCloudFlagInfo(true);
        } else {
          getCloudFlagInfo(false);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("validateFlag=>${state.validateFlag.value}");
    }, showErrMsg: false);
  }

  // 查询云商数据同步开关是否开启 0关闭 1开启
  getCloudFlagInfo(bool needJump) {
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getCloudFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if (value["data"] == 1) {
          state.cloudFlag.value = true;
          SpUtil.putBool("CloudFlag", true);
        } else {
          state.cloudFlag.value = false;
          SpUtil.putBool("CloudFlag", false);
        }

        if ((((SpUtil.getObject('userInfo'))?['employee']["duty"] == "4") ||
                ((SpUtil.getObject('userInfo'))?['employee']["duty"] == "2")) &&
            (SpUtil.getBool("CloudFlag") == true)) {
          state.showActivityView.value = true;
        } else {
          state.showActivityView.value = false;
        }

        if (needJump == true) {
          if (state.cloudFlag.value == false) {
            MyCommonUtils.showToast("当前经销商未开通云商，请联系管理员");
          } else {
            getMissionCenterUrl();
          }
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("CloudFlag=>${SpUtil.getBool('CloudFlag')}");
      update();
    }, showErrMsg: needJump);
  }

  //获取任务中心H5地址
  getMissionCenterUrl() {
    String employeeNo = "";
    String employeeName = "";
    String duty = "";
    Map<String, dynamic>? queryParameters = {};
    SmartDialog.showLoading(msg: "加载中...");
    MyDio.get(Apis.getMissionCenterUrl, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      if (value["code"] == 200) {
        String token = value["data"]["token"] ?? "";
        String branchNo = value["data"]["branchNo"] ?? "";
        String memberId = value["data"]["memberId"] ?? "";
        String tenantId = "10001";
        String phone = value["data"]["phone"] ?? "";

        if (state.userInfo["employee"] != null) {
          employeeNo = state.userInfo["employee"]["employeeNo"] ?? "";
          employeeName = state.userInfo["employee"]["employeeName"] ?? "";
          // SpUtil.getObject('userInfo')?["employee"]["duty"]: 1 库管员 2 配送员 3 采购员 4 业务员 5 系统管理员
          // duty: 1 车销业务员 2 访销业务员 3 配送员
          if ((state.userInfo["employee"]["duty"] ?? "") == "2") {
            duty = "3";
          } else if ((state.userInfo["employee"]["duty"] ?? "") == "4") {
            if ((SpUtil.getString('branchNo') ?? '') == "001") {
              duty = "2";
            } else {
              duty = "1";
            }
          }
        }

        print("token=>${token}");
        print("branchNo=>${branchNo}");
        print("memberId=>${memberId}");
        print("tenantId=>${tenantId}");
        print("phone=>${phone}");
        print("employeeNo=>${employeeNo}");
        print("employeeName=>${employeeName}");
        print("duty=>${duty}");

        String centerUrl = SetInfo.instance.missionCenterUrl +
            "frontend/tf-business-h5/index.html#/subPages/taskCenter/index" +
            "?token=${token}&branchNo=${branchNo}&memberId=${memberId}&tenantId=${tenantId}&phone=${phone}&employeeNo=${employeeNo}&employeeName=${employeeName}&duty=${duty}&channelSource=fst";
        print("centerUrl=>${centerUrl}");

        // Get.toNamed(PageName.CommonWebViewPage,
        //     arguments: {'title': '任务中心', 'url': centerUrl});

        Get.toNamed(PageName.InAppWebViewPage, arguments: {'url': centerUrl});

        // Map fstShopInfo = {
        //   'token': token,
        //   'branchNo': branchNo,
        //   'memberId': memberId,
        //   'tenantId': tenantId,
        //   'phone': phone,
        //   'employeeNo': employeeNo,
        //   'employeeName': employeeName,
        //   'duty': duty,
        // };
        // Get.toNamed(PageName.MissionCenterPage, arguments: fstShopInfo);
      } else {
        if (value["msg"] != null) {
          MyCommonUtils.showToast(value["msg"]);
        }
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
      // MyCommonUtils.showToast("网络连接失败");
    });
  }

  // 设置新的个人业绩数据（基于新接口）
  setNewIndividualList() {
    state.individualList.clear();
    var data = state.personalPerformanceData.value;

    state.individualList.add({
      'type': '',
      'title': '总订单金额',
      'num': (data["totalOrderAmount"] ?? 0.0).toStringAsFixed(2)
    });

    state.individualList.add({
      'type': '',
      'title': '总订单数量',
      'num': (data["totalOrderCount"] ?? 0).toString()
    });

    state.individualList.add({
      'type': '',
      'title': '店主订单金额',
      'num': (data["storeOrderAmount"] ?? 0.0).toStringAsFixed(2)
    });

    state.individualList.add({
      'type': '',
      'title': '店主订单数量',
      'num': (data["storeOrderCount"] ?? 0).toString()
    });

    state.individualList.add({
      'type': '',
      'title': '代客订单金额',
      'num': (data["proxyOrderAmount"] ?? 0.0).toStringAsFixed(2)
    });

    state.individualList.add({
      'type': '',
      'title': '代客订单数量',
      'num': (data["proxyOrderCount"] ?? 0).toString()
    });

    state.individualList.add({
      'type': '',
      'title': '退货总金额',
      'num': (data["refundTotal"] ?? 0.0).toStringAsFixed(2)
    });

    state.individualList.add({
      'type': '',
      'title': '拜访门店数',
      'num': (data["visitStoreCount"] ?? 0).toString()
    });

    state.individualList.add({
      'type': '',
      'title': '动销门店数',
      'num': (data["operateStoreCount"] ?? 0).toString()
    });

    state.individualList.add({
      'type': '',
      'title': '拓店数量',
      'num': (data["addBranchCount"] ?? 0).toString()
    });
  }

  // 设置个人业绩数据
  setIndividualList(type) {
    state.individualList.clear();
    var userOrgSettings = publicState.userInfo?['userOrgSettings'];
    int _amount = int.parse(userOrgSettings['detailed_amount_decimal']);
    _pow = int.parse(pow(10, _amount).toString());

    // print('_amount_amount_amount $_amount $_pow');

    // print('doubleNotlossAuth(_totalInAmt, decimal: _pow) $_pow ${doubleNotlossAuth(double.parse(state.personalDataInfo["htDataCountStr"].toString()), decimal: _pow)}');
    state.individualList.add({
      'type': 'OT',
      'title': '下单金额',
      'num': doubleNotlossAuth(double.parse((state.personalDataInfo["xdMoney"] ?? 0.0).toString()), decimal: _pow) ?? "0.00"
    });

    state.individualList.add({
      'type': '',
      'title': '下单数量',
      'num': state.personalDataInfo["xdDataCount"]?.toString() ?? "0"
    });

    state.individualList.add({
      'type': '',
      // 'type': 'YW', // 临时隐藏详情
      'title': '业务员下单',
      'num': doubleNotlossAuth(double.parse((state.personalDataInfo["ywDataCountStr"] ?? 0.0).toString()), decimal: _pow) ?? "0.00"
    });
    state.individualList.add({
      'type': '',
      // 'type': 'DZ', // 临时隐藏详情
      'title': '店主下单',
      'num': state.personalDataInfo["dzDataCountStr"] ?? "0.00"
    });
    state.individualList.add({
      'type': '',
      // 'type': 'HT', // 临时隐藏详情
      'title': '后台下单',
      'num': state.personalDataInfo["htDataCountStr"] ?? "0.00"
    });

    state.individualList.add({
      'type': 'SA',
      'title': '发货金额',
      'num': doubleNotlossAuth(double.parse((state.personalDataInfo["fhDataCount"] ?? 0.0).toString()), decimal: _pow) ?? "0.00"
    });

    state.individualList.add({
      'type': 'TH',
      'title': '退货金额',
      'num': doubleNotlossAuth(double.parse(state.personalDataInfo["thDataCountStr"].toString()), decimal: _pow) ?? "0.00"
    });


    state.individualList.add({
      'type': 'BF',
      'title': '拜访门店',
      'num': state.personalDataInfo["bfDataCount"]?.toString() ?? "0"
    });
    state.individualList.add({
      'type': 'TD',
      'title': '拓店数量',
      'num': state.personalDataInfo["tdDataCount"]?.toString() ?? "0"
    });
    state.individualList.add({
      'type': 'DX',
      'title': '动销门店',
      'num': state.personalDataInfo["dxDataCount"]?.toString() ?? "0"
    });
    state.individualList.add({
      'type': 'XD',
      'title': '未下单客户数',
      'num': state.personalDataInfo["wxdDataCount"]?.toString() ?? "0"
    });
    state.individualList.add({
      'type': 'SKU',
      'title': '动销SKU数',
      'num': state.personalDataInfo["dxSKUDataCount"]?.toString() ?? "0"
    });
    String inName = '今日收款', outName = '今日欠款';
    switch (type) {
      case '0':
        inName = '今日收款';
        outName = '今日欠款';
        break;
      case '1':
        inName = '本周收款';
        outName = '本周欠款';
        break;
      case '2':
        inName = '本月收款';
        outName = '本月欠款';
        break;
    }
    // state.individualList.add({
    //   'type': 'SK',
    //   'title': inName,
    //   'num': state.personalDataInfo["skDataCountStr"] ?? "0.00"
    // });
    // state.individualList.add({
    //   'type': '',
    //   'title': outName,
    //   'num': state.personalDataInfo["qkDataCountStr"] ?? "0.00"
    // });
  }

  //个人业绩子view跳转 类型：YW/DZ/TH/HT/BF/TD/DX/XD/SKU
  itemJump(type) {
    String startTime = calendarManager(int.parse(state.dateType.value ?? "0"));
    String endTime = calendarManager(0);
    switch (type) {
      case 'SA':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'SA',
          'startTime': startTime,
          'endTime': endTime
        });
        break;

      case 'OT':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'OT',
          'startTime': startTime,
          'endTime': endTime
        });
        break;

      case 'YW':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'YW',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'DZ':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'DZ',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'TH':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'TH',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'HT':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'HT',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'BF':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'BF',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'TD':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'TD',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'DX':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'DX',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'XD':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'XD',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'SKU':
        Get.toNamed(PageName.HOMESecondQuery, arguments: {
          'searchSource': 'SKU',
          'startTime': startTime,
          'endTime': endTime
        });
        break;
      case 'SK':
        Get.toNamed(PageName.todayCollectionPage,
            arguments: {'startTime': startTime, 'endTime': endTime});
        break;
    }
  }

  kingKongJump(item) {
    if (item['name'].toString().contains('补货')) {
      Get.toNamed("${item['path']}", arguments: {'tabIndex': 0});
    } else if (item['name'].toString().contains('返仓')) {
      Get.toNamed("${item['path']}", arguments: {'tabIndex': 1});
    } else if (item['name'].toString().contains('盘点')) {
      Get.toNamed("${item['path']}", arguments: {'tabIndex': 2});
    } else if (item['name'].toString().contains('店铺预览')) {
      // 如果只绑定了一个经销商直接跳转网页
      String shopId = SpUtil.getString('showShopScan', defValue: '0')!;
      Map fstShopInfo = {'externalCode': shopId};
      Get.toNamed(PageName.ShopPreviewPage, arguments: fstShopInfo);
    }
    // else if (item['name'].toString().contains('提现')) {
    //   getCloudShopInfo();
    // }
    else {
      Get.toNamed("${item['path']}");
    }
  }

  @override
  void dispose() {
    if (_validateSyncSubscription != null) {
      _validateSyncSubscription!.cancel();
    }
    super.dispose();
  }
}
