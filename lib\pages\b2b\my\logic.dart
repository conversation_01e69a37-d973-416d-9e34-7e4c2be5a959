/*
 * @Date: 2024-03-07 14:52:46
 * @LastEditors: 潘腾龙
 * @LastEditTime: 2024-03-30 16:49:35
 * @FilePath: /tongfu-salesman-app/lib/pages/my/logic.dart
 */
/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月12日09:29:57
 * 修改时间：2023年09月12日09:29:57
 */

import 'dart:io';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';

import 'package:fuduoduo/domain/common_tentant_bean.dart';
import 'package:fuduoduo/domain/common_other_LoginInfo.dart';

import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:fuduoduo/widget/MyDialog.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/domain/common_login_code.dart';

import '../../../base/base_logic.dart';
import '../../../utils/set_info.dart';
import '../../../widget/my_service_phone_dialog.dart';
import '../../../widget/loading_dialog.dart';

import 'state.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:fuduoduo/resource/string_resource.dart';
import 'package:fuduoduo/utils/storage.dart';
// import 'package:fuduoduo/common/config.dart';
import 'package:fuduoduo/utils/string_utils.dart';

import 'dart:convert';
import 'package:fuduoduo/utils/storage_common.dart';

class B2bMyLogic extends BaseLogic {
  final B2bMyState state = B2bMyState();
  var dialogContext;

  @override
  void onReady() async {
    super.onReady();
    getAppVersion();
    if (UserAppModuleUtils().moduleCanShow('account_for') ||
        UserAppModuleUtils().moduleCanShow('logistics_distribution')) {
      // getData();
    }
    getLocalUserInfo();
    getCloudFlagInfo(false);

    // 增加删除账户假功能，苹果审核用
    String currentAccount = SpUtil.getString('currentAccount', defValue: '')!;
    if (Platform.isIOS) {
      state.showDeleteAccount = (currentAccount == 'el(测试账号)');
    }

    // 查询租户信息
    getTentantData();


    //  添加b2b 逻辑

    String _appListsString = await SecureStorageCommon.save<String>('appLists').get();
    var _appLists = json.decode(_appListsString);

    // print('_appLists $_appLists');
    if (_appLists != null ) {
      state.AppNum = _appLists.length;
      state.appLists = _appLists;
      // if (state.AppNum > 1) {
        state.currentApp = await SecureStorageCommon.save<String>('userInfo').get();

        state.userType = await SecureStorageCommon.save<String>('userType').get();
        // print('11_appLists  ee ${state.currentApp}   ${state.userType}');
      // }

    }

    // print('11_appLists  $_appLists');
  }

  toggleDrawer() {
    // state.drawerOpen.value = !state.drawerOpen.value;
  }

  reLogin(BuildContext context, String code) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["tenantCode"] = code;
    queryParameters["applicationCode"] = StringUtil.getApplicationCode();
    // queryParameters["applicationCode"] = AppConfig.applicationCode;
    showLoadingDialog(context, '加载中...');

    MyDio.post(
        '${Apis.switchTenant2Saas}?tenantCode=${code}',
        queryParameters: queryParameters,
        successCallBack: (value) async {
          var response = LoginCodeInfoBean.fromJson(value);

          String? accessToken = response.data?[0].accessToken;

          /// 删除存储的令牌。
          /// 
          /// 此操作会清除安全存储中的令牌信息。       
          await SecureStorage.token().delete?.call();

          SpUtil.putString("access_token", accessToken!);

          /// 如果访问令牌不为空，则将其存储在安全存储中。
          if (!accessToken.isEmpty) await SecureStorage.token<String>().set(accessToken!);

          MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {

            var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

            // 租户ID
            String tenant_id = '${responseOther.tenantId}';
            SpUtil.putString('tenant_id', tenant_id);

            List moduleArray = responseOther.appModules ?? [];
            if (moduleArray.isEmpty) {
              MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
            }
            List<String> moduleList = [];
            for (var element in moduleArray) {
              moduleList.add(element);
            }
            UserAppModuleUtils().saveUserAppModuls(moduleArray);

            SpUtil.putBool("validateFlag", true);

            Get.offAllNamed( PageName.TAB)?.then((value) {
              dismissDialog();
            });
          });

        });
  }



  reB2bLogin(BuildContext context, String code) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["tenantCode"] = code;
    queryParameters["applicationCode"] = StringUtil.getApplicationCode();
    // queryParameters["applicationCode"] = AppConfig.applicationCode;
    showLoadingDialog(context, '加载中...');

    MyDio.post(
        '${Apis.switchTenant2Saas}?tenantCode=${code}',
        queryParameters: queryParameters,
        successCallBack: (value) async {
          var response = LoginCodeInfoBean.fromJson(value);

          String? accessToken = response.data?[0].accessToken;

          /// 删除存储的令牌。
          ///
          /// 此操作会清除安全存储中的令牌信息。
          await SecureStorage.token().delete?.call();

          SpUtil.putString("access_token", accessToken!);

          /// 如果访问令牌不为空，则将其存储在安全存储中。
          if (!accessToken.isEmpty) await SecureStorage.token<String>().set(accessToken!);

          MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {

            var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

            // 租户ID
            String tenant_id = '${responseOther.tenantId}';
            SpUtil.putString('tenant_id', tenant_id);

            List moduleArray = responseOther.appModules ?? [];
            if (moduleArray.isEmpty) {
              MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
            }
            List<String> moduleList = [];
            for (var element in moduleArray) {
              moduleList.add(element);
            }
            UserAppModuleUtils().saveUserAppModuls(moduleArray);

            SpUtil.putBool("validateFlag", true);

            Get.offAllNamed( PageName.B2BTAB)?.then((value) {
              dismissDialog();
            });
          });

        });
  }





  getTentantData() {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["applicationCode"] = StringUtil.getApplicationCode();
    // queryParameters["applicationCode"] = AppConfig.applicationCode;
    MyDio.get(
      Apis.getTenant,
      queryParameters: queryParameters,
      successCallBack: (value) {

        var response = CommonTentantBean.fromJson(value);

        if (value['code'] == '0') {
          state.currentTenant = value['data']['currentTenant'];
          update([StringResource.PAGE_ME_ID]);
        }
      },
      failCallBack: (value) {
        if (value != null) {
          MyCommonUtils.showToast(value['msg']);
        }
      },
    );
  }

  getData() {
    return;
    MyDio.get(
      Apis.getEmployeeSheetAmtSum,
      successCallBack: (value) {
        var response = DioResultBean.fromJson(value);
        if (response.code.toString() == "200") {
          double employeeSheetAmtSum = double.parse(response.data.toString());
          state.EmployeeSheetAmtSum =
              '${employeeSheetAmtSum.toStringAsFixed(2)}';
          update();
        }
      },
      failCallBack: (value) {
        state.EmployeeSheetAmtSum = '';
        if (value != null) {
          MyCommonUtils.showToast(value['msg']);
        }
      },
    );
  }

  getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    state.version = packageInfo.version;
    update(["mySetUp"]);
    // Get.forceAppUpdate();
  }

  // 跳转去交账界面
  void toSubmitAccountPage() {
    Get.toNamed(PageName.SubmitAccountPage);
  }

  // 跳转切换账号
  void toChangeAccountListPage() {
    Get.toNamed(PageName.ChangeAccountListPage);
  }

  // 跳转更换手机号
  void toChangePhoneNumnerPage() {
    Get.toNamed(PageName.ChangePhoneNumnerPage)?.then((value) {
      getLocalUserInfo();
      getValidateFlagInfo(false);
    });
  }

  // 跳转验证手机号
  void toBindPhoneNumberPage() {
    Get.toNamed(PageName.BindPhoneNumberPage,
        parameters: {"fromPage": "MyPage"})?.then((value) {
      if (value != null && value == true) {
        getLocalUserInfo();
        getValidateFlagInfo(false);
      }
    });
  }

  //查询用户信息
  getLocalUserInfo() {

    return;
    MyDio.get(Apis.getAppUserInfo, queryParameters: {},
        successCallBack: (value) {
      if (value["code"] == 200) {
        dynamic data = value["data"] ?? {};
        SpUtil.putObject('userInfo', data);
      }
      // print("userInfo=>${SpUtil.getObject('userInfo')}");
      update();
    }, showErrMsg: false);
  }

  // 查询手机号是否验证 0未验证 1已验证
  getValidateFlagInfo(bool needJump) {
    return;
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getValidateFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if ((value["data"] != null) &&
            (value["data"]["phoneStatus"] != null) &&
            (value["data"]["phoneStatus"] == "1")) {
          state.validateFlag = true;
          SpUtil.putBool("validateFlag", true);
        } else {
          state.validateFlag = false;
          SpUtil.putBool("validateFlag", false);
        }

        if (needJump == true) {
          getCloudFlagInfo(true);
        } else {
          getCloudFlagInfo(false);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("validateFlag=>${state.validateFlag}");
      update(["mySetUp"]);
    }, showErrMsg: false);
  }

  // 查询云商数据同步开关是否开启 0关闭 1开启
  getCloudFlagInfo(bool needJump) {
    return;
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getCloudFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if (value["data"] == 1) {
          state.cloudFlag = true;
          SpUtil.putBool("CloudFlag", true);
        } else {
          state.cloudFlag = false;
          SpUtil.putBool("CloudFlag", false);
        }

        if (needJump == true) {
          if (state.cloudFlag == false) {
            MyCommonUtils.showToast("当前经销商未开通云商，请联系管理员");
          } else {
            getMissionCenterUrl();
          }
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("CloudFlag=>${SpUtil.getBool('CloudFlag')}");
      update();
    }, showErrMsg: false);
  }

  //获取任务中心H5地址
  getMissionCenterUrl() {
    String employeeNo = "";
    String employeeName = "";
    String duty = "";
    Map<String, dynamic>? queryParameters = {};
    SmartDialog.showLoading(msg: "加载中...");
    MyDio.get(Apis.getMissionCenterUrl, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      if (value["code"] == 200) {
        String token = value["data"]["token"] ?? "";
        String branchNo = value["data"]["branchNo"] ?? "";
        String memberId = value["data"]["memberId"] ?? "";
        String tenantId = "10001";
        String phone = value["data"]["phone"] ?? "";

        if (SpUtil.getObject('userInfo') != null &&
            SpUtil.getObject('userInfo')?["employee"] != null) {
          employeeNo =
              SpUtil.getObject('userInfo')?["employee"]["employeeNo"] ?? "";
          employeeName =
              SpUtil.getObject('userInfo')?["employee"]["employeeName"] ?? "";
          // SpUtil.getObject('userInfo')?["employee"]["duty"]: 1 库管员 2 配送员 3 采购员 4 业务员 5 系统管理员
          // duty: 1 车销业务员 2 访销业务员 3 配送员
          if ((SpUtil.getObject('userInfo')?["employee"]["duty"] ?? "") ==
              "2") {
            duty = "3";
          } else if ((SpUtil.getObject('userInfo')?["employee"]["duty"] ??
                  "") ==
              "4") {
            if ((SpUtil.getString('branchNo') ?? '') == "001") {
              duty = "2";
            } else {
              duty = "1";
            }
          }
        }

        print("token=>${token}");
        print("branchNo=>${branchNo}");
        print("memberId=>${memberId}");
        print("tenantId=>${tenantId}");
        print("phone=>${phone}");
        print("employeeNo=>${employeeNo}");
        print("employeeName=>${employeeName}");
        print("duty=>${duty}");

        String centerUrl = SetInfo.instance.missionCenterUrl +
            "frontend/tf-business-h5/index.html#/subPages/taskCenter/index" +
            "?token=${token}&branchNo=${branchNo}&memberId=${memberId}&tenantId=${tenantId}&phone=${phone}&employeeNo=${employeeNo}&employeeName=${employeeName}&duty=${duty}&channelSource=fst";
        print("centerUrl=>${centerUrl}");
        Get.toNamed(PageName.InAppWebViewPage, arguments: {'url': centerUrl});
      } else {
        if (value["msg"] != null) {
          MyCommonUtils.showToast(value["msg"]);
        }
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
      // MyCommonUtils.showToast("网络连接失败");
    });
  }

  // 跳转库存查询
  void toInventoryInquiryPage() {
    Get.toNamed(PageName.InventoryInquiryPage);
  }

  // 跳转账号管理页面
  void showServicePhoneDialog(BuildContext context) {
    showDialog(
        context: context,
        // barrierDismissible: false,
        builder: (_context) {
          return MyServicePhoneDialog(title: "联系客服");
        });
  }

  //弹出loading弹出框
  void showLoadingDialog(BuildContext context, String text,
      {bool useRootNavigator = true}) {
    showDialog(
        context: context,
        useRootNavigator: useRootNavigator,
        barrierDismissible: false,
        builder: (buildContext) {
          dialogContext = buildContext;
          return LoadingDialog(
            outsideDismiss: false,
            loadingText: text,
          );
        });
  }

  //关闭弹出框
  void dismissDialog() {
    if (dialogContext != null) {
      Navigator.pop(dialogContext);
    }
  }
}
