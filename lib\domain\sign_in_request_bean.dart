class SignInRequestBean {
  String? colonelId;
  String? latitude;
  String? longitude;
  String? consumerNo;
  String? signInAddress;
  String? signInImgUrls;
  String? signInLatitude;
  String? signInLongitude;
  String? signInType;
  String? signInDistance;
  String? branchNo;

  SignInRequestBean({
    this.colonelId,
    this.latitude,
    this.longitude,
    this.consumerNo,
    this.signInAddress,
    this.signInImgUrls,
    this.signInLatitude,
    this.signInLongitude,
    this.signInType,
    this.signInDistance,
    this.branchNo,
  });

  factory SignInRequestBean.fromJson(Map<String, dynamic> json) {
    return SignInRequestBean(
      colonelId: json['colonelId']?.toString(),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      consumerNo: json['consumerNo']?.toString(),
      signInAddress: json['signInAddress']?.toString(),
      signInImgUrls: json['signInImgUrls']?.toString(),
      signInLatitude: json['signInLatitude']?.toString(),
      signInLongitude: json['signInLongitude']?.toString(),
      signInType: json['signInType']?.toString(),
      signInDistance: json['signInDistance']?.toString(),
      branchNo: json['branchNo']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'colonelId': colonelId,
      'latitude': latitude,
      'longitude': longitude,
      'consumerNo': consumerNo,
      'signInAddress': signInAddress,
      'signInImgUrls': signInImgUrls,
      'signInLatitude': signInLatitude,
      'signInLongitude': signInLongitude,
      'signInType': signInType,
      'signInDistance': signInDistance,
      'branchNo': branchNo,
    };
  }
}

class SignInResponseBean {
  String? code;
  String? msg;
  dynamic data;

  SignInResponseBean({
    this.code,
    this.msg,
    this.data,
  });

  factory SignInResponseBean.fromJson(Map<String, dynamic> json) {
    return SignInResponseBean(
      code: json['code']?.toString(),
      msg: json['msg']?.toString(),
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'data': data,
    };
  }
}
