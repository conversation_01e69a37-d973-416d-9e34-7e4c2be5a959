import 'package:collection/collection.dart';

import 'branch_coupon_data.dart';

class B2bAppBranchCouponList {
  int? code;
  BranchCouponData? data;
  String? msg;

  B2bAppBranchCouponList({this.code, this.data, this.msg});

  factory B2bAppBranchCouponList.fromJson(Map<String, dynamic> json) {
    return B2bAppBranchCouponList(
      code: json['code'] as int?,
      data: json['data'] == null
          ? null
          : BranchCouponData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'BranchCouponData': data?.toJson(),
        'msg': msg,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! B2bAppBranchCouponList) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => code.hashCode ^ data.hashCode ^ msg.hashCode;
}
