@echo off
echo ========================================
echo 清理Flutter项目并重新构建
echo ========================================

echo 1. 清理Flutter缓存...
flutter clean

echo 2. 删除build目录...
if exist "build" rmdir /s /q "build"
if exist "android\build" rmdir /s /q "android\build"
if exist "android\app\build" rmdir /s /q "android\app\build"

echo 3. 清理Gradle缓存...
if exist "%USERPROFILE%\.gradle\caches" rmdir /s /q "%USERPROFILE%\.gradle\caches"

echo 4. 重新获取依赖...
flutter pub get

echo 5. 开始构建APK...
flutter build apk --debug

echo ========================================
echo 构建完成！
echo ========================================
pause
