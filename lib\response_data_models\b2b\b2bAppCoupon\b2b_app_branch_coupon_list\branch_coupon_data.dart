import 'package:collection/collection.dart';

import 'branch_coupon_list.dart';

class BranchCouponData {
  List<BranchCouponList>? list;
  int? total;
  dynamic rest;

  BranchCouponData({this.list, this.total, this.rest});

  factory BranchCouponData.fromJson(Map<String, dynamic> json) {
    return BranchCouponData(
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => BranchCouponList.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int?,
      rest: json['rest'] as dynamic,
    );
  }

  Map<String, dynamic> toJson() => {
        'BranchCouponList': list?.map((e) => e.toJson()).toList(),
        'total': total,
        'rest': rest,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! BranchCouponData) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => list.hashCode ^ total.hashCode ^ rest.hashCode;
}
