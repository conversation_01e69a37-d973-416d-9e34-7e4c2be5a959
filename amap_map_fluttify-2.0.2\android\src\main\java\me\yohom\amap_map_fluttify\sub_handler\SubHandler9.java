//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler9 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.model.Text::getAlignX_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getAlignX();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::getAlignY_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getAlignY();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::setObject_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.lang.Object var1 = (java.lang.Object) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setObject(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::getObject_batch", (__argsBatch__, __methodResult__) -> {
                List<java.lang.Object> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.lang.Object __result__ = null;
                    try {
                        __result__ = __this__.getObject();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::setRotate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRotate(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::getRotate_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRotate();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Text::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Text __this__ = (com.amap.api.maps.model.Text) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds.Builder::include_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds.Builder __this__ = (com.amap.api.maps.model.LatLngBounds.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds.Builder __result__ = null;
                    try {
                        __result__ = __this__.include(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds.Builder::build_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds.Builder __this__ = (com.amap.api.maps.model.LatLngBounds.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds __result__ = null;
                    try {
                        __result__ = __this__.build();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getHeatMapItem_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.HeatMapItem> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.HeatMapItem __result__ = null;
                    try {
                        __result__ = __this__.getHeatMapItem(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::getOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.HeatMapLayerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.HeatMapLayerOptions __result__ = null;
                    try {
                        __result__ = __this__.getOptions();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapLayer::setOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.HeatMapLayerOptions var1 = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapLayer __this__ = (com.amap.api.maps.model.HeatMapLayer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setOptions(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::add__com_amap_api_maps_model_LatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.add(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::addAll_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.addAll(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::width_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.width(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::topColor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.topColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::sideColor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.sideColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::zIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.zIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::visible_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.visible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::set3DModel_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.set3DModel(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.LatLng>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                    try {
                        __result__ = __this__.getPoints();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getTopColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTopColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getSideColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getSideColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::is3DModel_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.is3DModel();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::setPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoints(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrowOptions::clone_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.NavigateArrowOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrowOptions __this__ = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.NavigateArrowOptions __result__ = null;
                    try {
                        __result__ = __this__.clone();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.ColorLatLng::getColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.ColorLatLng __this__ = (com.amap.api.maps.model.ColorLatLng) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.ColorLatLng::getLatLngs_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.LatLng>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.ColorLatLng __this__ = (com.amap.api.maps.model.ColorLatLng) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                    try {
                        __result__ = __this__.getLatLngs();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromResource_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromResource(var0.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromView_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.view.View var0 = (android.view.View) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromView(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromPath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromPath(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromAsset_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromAsset(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromFile_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromFile(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.defaultMarker();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::defaultMarker__double_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.defaultMarker(var0.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::fromBitmap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.graphics.Bitmap var0 = (android.graphics.Bitmap) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.fromBitmap(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BitmapDescriptorFactory::getContext_batch", (__argsBatch__, __methodResult__) -> {
                List<android.content.Context> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    android.content.Context __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.BitmapDescriptorFactory.getContext();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getUrl_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getUrl();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getKey_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getKey();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileProvider::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MVTTileProvider __this__ = (com.amap.api.maps.model.MVTTileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineJoinType::getTypeValue_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapPara.LineJoinType __this__ = (com.amap.api.maps.model.AMapPara.LineJoinType) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTypeValue();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapPara.LineJoinType::valueOf_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.AMapPara.LineJoinType> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.AMapPara.LineJoinType __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.AMapPara.LineJoinType.valueOf(var0.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::anchor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MultiPointOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getAnchorU_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorU();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getAnchorV_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorV();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::icon_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MultiPointOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.icon(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getIcon_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getIcon();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::setMultiPointItems_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.MultiPointItem> var1 = (java.util.List<com.amap.api.maps.model.MultiPointItem>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMultiPointItems(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::getMultiPointItems_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.MultiPointItem>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.MultiPointItem> __result__ = null;
                    try {
                        __result__ = __this__.getMultiPointItems();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::setEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlayOptions::clone_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MultiPointOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MultiPointOverlayOptions __this__ = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MultiPointOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.clone();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PoiCreator::newArray_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Poi[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PoiCreator __this__ = (com.amap.api.maps.model.PoiCreator) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.Poi[] __result__ = null;
                    try {
                        __result__ = __this__.newArray(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setUseTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setUseTexture(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setCustomTexture(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getCustomTexture();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTextureList_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.List<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setCustomTextureList(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTextureList_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.BitmapDescriptor>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.BitmapDescriptor> __result__ = null;
                    try {
                        __result__ = __this__.getCustomTextureList();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setCustomTextureIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setCustomTextureIndex(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getCustomTextureIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<Integer>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<Integer> __result__ = null;
                    try {
                        __result__ = __this__.getCustomTextureIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::colorValues_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.colorValues(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getColorValues_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<Integer>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<Integer> __result__ = null;
                    try {
                        __result__ = __this__.getColorValues();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::useGradient_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.useGradient(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isUseGradient_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isUseGradient();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isUseTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isUseTexture();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isGeodesic_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isGeodesic();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::add__com_amap_api_maps_model_LatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.add(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::addAll_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.addAll(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::width_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.width(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::color_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.color(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::zIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.zIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::visible_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.visible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::geodesic_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.geodesic(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setDottedLine_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setDottedLine(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isDottedLine_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isDottedLine();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setDottedLineType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setDottedLineType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::lineCapType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // enum arg
                    com.amap.api.maps.model.PolylineOptions.LineCapType var1 = com.amap.api.maps.model.PolylineOptions.LineCapType.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.lineCapType(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::lineJoinType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // enum arg
                    com.amap.api.maps.model.PolylineOptions.LineJoinType var1 = com.amap.api.maps.model.PolylineOptions.LineJoinType.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.lineJoinType(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getLineCapType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions.LineCapType> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions.LineCapType __result__ = null;
                    try {
                        __result__ = __this__.getLineCapType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getLineJoinType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions.LineJoinType> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions.LineJoinType __result__ = null;
                    try {
                        __result__ = __this__.getLineJoinType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getDottedLineType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getDottedLineType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.LatLng>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                    try {
                        __result__ = __this__.getPoints();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::transparency_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.transparency(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getTransparency_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getTransparency();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::aboveMaskLayer_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.aboveMaskLayer(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isAboveMaskLayer_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isAboveMaskLayer();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoints(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRatio_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getShownRatio();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setShownRatio_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setShownRatio(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setShownRange_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setShownRange(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRangeBegin_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getShownRangeBegin();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getShownRangeEnd_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getShownRangeEnd();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::showPolylineRangeEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.showPolylineRangeEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::isShowPolylineRangeEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isShowPolylineRangeEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setPolylineShowRange_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setPolylineShowRange(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPolylineShownRangeBegin_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getPolylineShownRangeBegin();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getPolylineShownRangeEnd_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getPolylineShownRangeEnd();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setFootPrintTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setFootPrintTexture(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getFootPrintTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getFootPrintTexture();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setFootPrintGap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setFootPrintGap(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getFootPrintGap_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getFootPrintGap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setEraseTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var2 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setEraseTexture(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseTexture_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getEraseTexture();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getEraseVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::setEraseColor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions __result__ = null;
                    try {
                        __result__ = __this__.setEraseColor(var1, var2.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions::getEraseColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions __this__ = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getEraseColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Tile::obtain_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Tile> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    byte[] var2 = (byte[]) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.Tile __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.Tile.obtain(var0.intValue(), var1.intValue(), var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAngle(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::getAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAngle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setModelFixedLength_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setModelFixedLength(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModel::setZoomLimit_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModel __this__ = (com.amap.api.maps.model.GL3DModel) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZoomLimit(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Gradient::getColors_batch", (__argsBatch__, __methodResult__) -> {
                List<int[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Gradient __this__ = (com.amap.api.maps.model.Gradient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    int[] __result__ = null;
                    try {
                        __result__ = __this__.getColors();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Gradient::getStartPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<float[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Gradient __this__ = (com.amap.api.maps.model.Gradient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    float[] __result__ = null;
                    try {
                        __result__ = __this__.getStartPoints();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTile_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Tile> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.Tile __result__ = null;
                    try {
                        __result__ = __this__.getTile(var1.intValue(), var2.intValue(), var3.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTileWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTileWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileProvider::getTileHeight_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileProvider __this__ = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTileHeight();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getCenter();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCenter(var1.doubleValue(), var3.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getIntensity_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getIntensity();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setIntensity_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setIntensity(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::getIndexes_batch", (__argsBatch__, __methodResult__) -> {
                List<int[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    int[] __result__ = null;
                    try {
                        __result__ = __this__.getIndexes();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapItem::setIndexes_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    int[] var1 = (int[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.HeatMapItem __this__ = (com.amap.api.maps.model.HeatMapItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setIndexes(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::setTargetPoint_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTargetPoint(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::setNaviStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setNaviStyle(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::getTargetPoint_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getTargetPoint();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NaviPara::getNaviStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NaviPara __this__ = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getNaviStyle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::image_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.image(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::anchor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::position__com_amap_api_maps_model_LatLng__double_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.position(var1, var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::position__com_amap_api_maps_model_LatLng__double__double_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.position(var1, var2.floatValue(), var3.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::positionFromBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.positionFromBounds(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::bearing_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.bearing(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::zIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.zIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::visible_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.visible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::transparency_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.transparency(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getImage_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getImage();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getLocation_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getLocation();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getHeight_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getHeight();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds __result__ = null;
                    try {
                        __result__ = __this__.getBounds();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getBearing();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getTransparency_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getTransparency();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getAnchorU_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorU();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::getAnchorV_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorV();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptions::clone_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GroundOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GroundOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.clone();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::textureDrawable_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.textureDrawable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::vertexData__String_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.vertexData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::vertexData__List_double___List_double__batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<Float> var1 = (java.util.List<Float>) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    java.util.List<Float> var2 = (java.util.List<Float>) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.vertexData(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::position_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.position(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::angle_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.angle(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getVertext_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<Float>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<Float> __result__ = null;
                    try {
                        __result__ = __this__.getVertext();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getTextrue_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<Float>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<Float> __result__ = null;
                    try {
                        __result__ = __this__.getTextrue();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAngle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getLatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getLatLng();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getBitmapDescriptor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getBitmapDescriptor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::setModelFixedLength_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.setModelFixedLength(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getModelFixedLength_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getModelFixedLength();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::title_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.title(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::snippet_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.GL3DModelOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.GL3DModelOptions __result__ = null;
                    try {
                        __result__ = __this__.snippet(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getTitle_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTitle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GL3DModelOptions::getSnippet_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GL3DModelOptions __this__ = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getSnippet();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPosition(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setDimensions__double_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDimensions(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setImage_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setImage(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setDimensions__double__double_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDimensions(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getHeight_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getHeight();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setPositionFromBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPositionFromBounds(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds __result__ = null;
                    try {
                        __result__ = __this__.getBounds();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setBearing(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getBearing();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::setTransparency_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTransparency(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::getTransparency_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getTransparency();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlay::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.GroundOverlay __this__ = (com.amap.api.maps.model.GroundOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
        }};
    }
}
