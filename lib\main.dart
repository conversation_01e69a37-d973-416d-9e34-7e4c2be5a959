import 'dart:io' show Platform;
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/pages/homePages/map_help/mapFluttify.dart';
import 'package:fuduoduo/pages/unknown/unknown_page.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'common/dio_manager.dart';
import 'package:fuduoduo/store/index.dart';
import 'utils/network_manager.dart';
import 'pages/homePages/ChineseCupertinoLocalizations.dart';
import 'package:fuduoduo/utils/storage.dart';

import 'package:fuduoduo/utils/upgrade_utils.dart';
import 'package:fuduoduo/utils/storage_common.dart';

Future<void> main() async {
  FlutterBugly.postCatchedException(() async {
    // 如果需要 ensureInitialized，请在这里运行。
    // WidgetsFlutterBinding.ensureInitialized();
    WidgetsFlutterBinding.ensureInitialized();

    if (Platform.isAndroid) {
      await InAppWebViewController.setWebContentsDebuggingEnabled(true);
    }

    // 初始化-存储
    await SpUtil.getInstance();
    // 初始化-网络
    await DioManager.init();

    // 初始化网络状态管理器
    Get.put(NetworkManager());

    //登陆页提示token失效问题解决
    // // 获取版本更新信息
    // getUpgradeInfo();
    // 获取用户信息
    final publicLogic = Get.put(Public());

    // 如果有token, 就不需要登录
    var _token = await SecureStorage.token().get();
    var _isB2bApp = await SecureStorageCommon.save('userType').get();

    // print('xx  $_isB2bApp _token $_token ${SpUtil.getString('access_token')}');

    if (_token != null && _token!.isNotEmpty) {
      // print('xx isNotEmpty');
      SpUtil.putString('access_token', _token);
      SpUtil.putBool("validateFlag", true);
      SpUtil.putBool("appType", _isB2bApp == 'b2bSystem' ? true : false);

      // 获取APP的账号信息
      publicLogic.getLoginInfo();
      await Future.delayed(Duration(seconds: 1));

      //获取个人信息
      publicLogic.getUserInfo();
      // 获取单位换算信息
      SetInfo.instance.initLoad();
    }
    //高德地图授权
    AmapService.instance.updatePrivacyAgree(true);
    AmapService.instance.updatePrivacyShow(true);
    AmapService.instance.init(androidKey: mapAndroidKey, iosKey: mapIOSKey);
    runApp(const MyApp());
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    //除去安卓半透明栏
    if (Theme.of(context).platform == TargetPlatform.android) {
      SystemUiOverlayStyle _style =
          const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(_style);
    }

    // // 获取版本更新信息
    getUpgradeInfo();
    
    return ScreenUtilInit(
      designSize: const Size(750, 1624),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (BuildContext context, widget) {
        return KeyboardDismissOnTap(
            child: MediaQuery(
                data: MediaQuery.of(context)
                    .copyWith(textScaler: TextScaler.noScaling),
                child: GetMaterialApp(
                  title: '客户通',
                  localizationsDelegates: [
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    DefaultCupertinoLocalizations.delegate,
                    ChineseCupertinoLocalizations.delegate,
                    RefreshLocalizations.delegate,
                  ],
                  supportedLocales: [
                    const Locale('zh', 'CH'),
                    const Locale('en', 'US'),
                  ],
                  locale: const Locale('zh'),
                  debugShowCheckedModeBanner: false,
                  enableLog: true,
                  initialRoute: PageName.HOME,
                  //根路由
                  getPages: PageRoutes.routes,
                  // 配置路由
                  defaultTransition: Platform.isIOS
                      ? Transition.native
                      : Transition.rightToLeft,
                  // 转场动画
                  unknownRoute: GetPage(
                      name: PageName.UnknownPage,
                      page: () {
                        return UnknownPage();
                      }),
                  routingCallback: (routing) {
                    // 设置路由监听，当路由事件触发时，调用routingCallback回调
                    // 在routingCallback回调中，进行业务逻辑的处理
                    // if(routing.current == '/xxxx'){
                    // }
                  },
                  // 初始化SmartDialog
                  navigatorObservers: [FlutterSmartDialog.observer],
                  // here
                  builder: FlutterSmartDialog.init(
                    builder: (BuildContext context, child) => Scaffold(
                      body: GestureDetector(
                        child: child,
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          //全局
                          //点击背景收起键盘
                          hideKeyboard(context);
                        },
                      ),
                    ),
                  ),
                )));
      },
    );
  }

  //点击背景收起键盘
  void hideKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}
