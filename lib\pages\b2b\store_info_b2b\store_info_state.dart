// import 'package:fuduoduo/resource/address_resource.dart';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:fuduoduo/resource/address_no_resource.dart';

class StroeInfoState {
  bool fillFlag = false;
  StroeInfoState() {
    ///Initialize variables
  }
  List<Map> TDData = AREA_NO_STREET_RESOURCE;
  String? TDInitData;
  String TDSelected = '';

  String provinceName = ''; // 省份
  String cityName = ''; // 城市
  String districtName = ''; // 区县
  String streetName = ''; // 街道
  String provinceCode = ''; // 省份编码
  String cityCode = ''; // 城市编码
  String districtCode = ''; // 区县编码
  String streetCode = ''; // 街道编码

  // 等级价格是否必填
  bool level_price_required = false;
  int isSyncB2b = 1;
  bool isEdit = false;

  bool isAddUserAccount = true;
  bool isShowAccount = true;
  bool CashOnDelivery = false;
  bool B2bOnlinePayment = false;

  List AreaLists = [];
  String? TDAreaInitData;
  String TDAreaSelected = '';

  List<Poi> pois = [];
}