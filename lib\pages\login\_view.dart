import 'dart:io';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/domain/login_info_bean.dart';

import 'package:fuduoduo/domain/common_login_code.dart';
import 'package:fuduoduo/domain/common_other_LoginInfo.dart';
import 'dart:convert';
// import './cryptor.dart';

import 'package:fuduoduo/pages/login/logic.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/store/state.dart';
import 'package:fuduoduo/utils/color_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:fuduoduo/utils/time_utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/utils/storage.dart';
import 'package:fuduoduo/utils/storage_common.dart';

// import 'package:fuduoduo/common/config.dart';
import 'package:fuduoduo/utils/string_utils.dart';

class LoginPage extends StatelessWidget {
  LoginPage({super.key});

  final LoginLogic logic = Get.put(LoginLogic());
  final MainState state = Get.find<Public>().state;
  final state1 = Get.find<LoginLogic>().state;
  DateTime? lastPopTime;

  @override
  Widget build(BuildContext context) {
    GlobalKey _formKey = new GlobalKey<FormState>();
    GlobalKey _accountPositionkey = GlobalKey();

    /// 切换环境
    var times = 0;
    var _lastPressedAt = DateTime.now();
    String isFirstInto = SpUtil.getString('first') ?? '';
    return buildLoginView(_formKey, _accountPositionkey, _lastPressedAt, times,
        isFirstInto, context);
  }

  buildLoginView(
      GlobalKey<State<StatefulWidget>> _formKey,
      GlobalKey _accountPositionkey,
      DateTime _lastPressedAt,
      int times,
      String isFirst,
      BuildContext context) {
    bool showExplanIcon = true;
    var historyAccountInfo = SpUtil.getObject('accountInfo');
    print('=============accountInfo  ${historyAccountInfo}');
    if (historyAccountInfo == null || historyAccountInfo.isEmpty) {
      showExplanIcon = false;
      historyAccountInfo = {};
    }
    List<String> allKeys = historyAccountInfo.keys.cast<String>().toList();
    if (allKeys.length < 2) {
      showExplanIcon = false;
    }

    // if (state1.loginType.value != 1) {
    //
    //   String? _phoneNumber = SpUtil.getString("phoneNumber") ?? '';
    //   print('**************** $_phoneNumber');
    //   state1.phoneNumber = _phoneNumber;
    // }

    return Container(
      color: ColorResource.WHITE_COMMON_COLOR,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Stack(
          children: [
            AppBar(
              foregroundColor: Colors.white,
              toolbarHeight: 0,
              centerTitle: true,
              backgroundColor: Colors.white,
              elevation: 0,
            ),
            Form(
              key: _formKey,
              onChanged: () {},
              child: Container(
                color: ColorResource.WHITE_COMMON_COLOR,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                        onTap: () {
                          if (DateTime.now().difference(_lastPressedAt) >
                              const Duration(seconds: 1)) {
                            //两次点击间隔超过1秒则重新计时
                            _lastPressedAt = DateTime.now();
                            times = 0;
                          } else {
                            times++;
                            if (times >= 4) {
                              _lastPressedAt = DateTime.now();
                              SetInfo.instance.changeHostUrl();
                            }
                          }
                        },
                        child: Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(top: 300.w, bottom: 150.w),
                          child: Container(
                            decoration: const BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                      color: Color(0xFFFDA7C8),
                                      offset: Offset(0, 2),
                                      blurRadius: 5,
                                      spreadRadius: -4)
                                ],
                                borderRadius:
                                    BorderRadius.all(Radius.circular(10))),
                            child: Image.asset(
                              gaplessPlayback: true,
                              "assets/images/fdd_login_logo.png",
                              width: 144.w,
                              height: 144.w,
                            ),
                          ),
                        )),
                    Obx(() {
                      return Container(
                        margin: EdgeInsets.only(left: 60.w, right: 60.w),
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(left: 10.w),
                              width: 1.sw,
                              child: Text(
                                  state1.loginType.value == 1 ? "账号" : "手机号",
                                  style: TextStyle(
                                      color: ColorResource.BLACK_COMMON_COLOR,
                                      fontSize: 24.sp)),
                            ),
                            SizedBox(height: 20.w),
                            Container(
                              key: _accountPositionkey,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: ColorUtil.fromHex('#F8F8F8'),
                                borderRadius: BorderRadius.circular(20.w),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: GetInputItem(
                                      controller: state1.accountTextController,
                                      type: 1,
                                      label: "",
                                      hintText: state1.loginType.value == 1
                                          ? '请输入账号'
                                          : '请输入手机号',
                                      onChanged: (value) {
                                        if (state1.loginType.value == 1) {
                                          state1.usernamePass =
                                              value.toString();
                                        } else {
                                          SpUtil.putString("phoneNumber", value.toString() == '' ? '' : value.toString());
                                          state1.phoneNumber = value.toString();
                                        }
                                      },
                                    ),
                                  ),
                                  Visibility(
                                    visible: showExplanIcon &&
                                        state1.loginType.value == 1,
                                    child: InkWell(
                                      onTap: () {
                                        state1.arowDown.value = true;
                                        clickExplandMore(
                                            context,
                                            _accountPositionkey,
                                            (historyAccountInfo as Map));
                                      },
                                      child: Obx(() {
                                        return Icon(
                                          state1.arowDown.value == false
                                              ? Icons.expand_more
                                              : Icons.expand_less,
                                          color: ColorResource
                                              .ORANGE_RED_COMMON_COLOR,
                                          size: 40.w,
                                        );
                                      }),
                                    ),
                                  ),
                                  SizedBox(width: 24.w),
                                ],
                              ),
                            ),
                            SizedBox(height: 40.w),
                            Container(
                              margin: EdgeInsets.only(left: 10.w),
                              width: 1.sw,
                              child: Text(
                                  state1.loginType.value == 1 ? "密码" : "验证码",
                                  style: TextStyle(
                                      color: ColorResource.BLACK_COMMON_COLOR,
                                      fontSize: 24.sp)),
                            ),
                            SizedBox(height: 20.w),
                            state1.loginType.value == 1
                                ? GetInputItem(
                                    controller: state1.passwordTextController,
                                    type: 2,
                                    label: "",
                                    obscureText: true,
                                    hintText: '请输入密码',
                                    keyType: 'password',
                                    onChanged: (value) {
                                      state1.password = value.toString();
                                    },
                                  )
                                : Container(
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: ColorUtil.fromHex('#F8F8F8'),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: GetInputItem(
                                            controller:
                                                state1.passwordTextController,
                                            keyboardType: TextInputType.number,
                                            type: 2,
                                            label: "",
                                            obscureText: false,
                                            hintText: '请输入验证码',
                                            onChanged: (value) {
                                              state1.msgCode = value.toString();
                                            },
                                          ),
                                        ),
                                        InkWell(
                                          onTap: () {
                                            String? _phoneNumber = SpUtil.getString("phoneNumber") ?? '';
                                            state1.phoneNumber = _phoneNumber;

                                            logic.getMsgCode();
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.only(
                                                left: 14.w,
                                                right: 14.w,
                                                bottom: 12.w,
                                                top: 12.w),
                                            decoration: BoxDecoration(
                                              color: ColorResource
                                                  .WHITE_COMMON_COLOR,
                                              gradient: LinearGradient(
                                                  colors: ColorResource
                                                      .LINEAR_GRADIENT_COMMON_COLOR,
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(20.w)),
                                            ),
                                            child: Obx(() {
                                              return Text(
                                                '${state1.msgCodeButtonInfo.value}',
                                                style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 28.sp),
                                              );
                                            }),
                                          ),
                                        ),
                                        SizedBox(width: 24.w),
                                      ],
                                    ),
                                  ),
                            SizedBox(height: 20.w),
                            Row(
                              children: [
                                Visibility(
                                    visible: state1.loginType.value == 1,
                                    child: Container(
                                      margin: EdgeInsets.only(left: 10.w),
                                      child: InkWell(
                                        onTap: () {
                                          Get.toNamed(
                                              PageName.ForgetPasswordPage);
                                        },
                                        child: Text(
                                          '忘记密码',
                                          style: TextStyle(
                                            color:
                                                ColorResource.GRAY_EDIT_COLOR,
                                            fontSize: 24.sp,
                                          ),
                                        ),
                                      ),
                                    )),
                                Spacer(),
                                Container(
                                  margin: EdgeInsets.only(right: 10.w),
                                  child: InkWell(
                                    onTap: () {
                                      if (state1.loginType.value == 1) {
                                        String? _phoneNumber = SpUtil.getString("phoneNumber") ?? '';

                                        state1.loginType.value = 2;
                                        state1.accountTextController.text =  _phoneNumber;
                                        state1.passwordTextController.text =
                                            state1.msgCode;
                                      } else if (state1.loginType.value == 2) {
                                        state1.loginType.value = 1;
                                        state1.accountTextController.text =
                                            state1.usernamePass;
                                        state1.passwordTextController.text =
                                            state1.password;
                                      }
                                    },
                                    child: Text(
                                      state1.loginType.value == 1
                                          ? '手机验证码登录'
                                          : '用户名登录',
                                      style: TextStyle(
                                        color: ColorResource.RED_TITLE_COLOR,
                                        fontSize: 24.sp,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 90.w),
                            Row(
                              children: [
                                Expanded(
                                  child: InkWell(
                                    onTap: () {
                                      print('phoneNumber InkWell,,,,,${state1.phoneNumber}');
                                      if (!logic.isSelect.value &&
                                          Platform.isAndroid) {
                                        MyCommonUtils.showToast(
                                            '请仔细阅读并同意《用户协议》 《隐私协议》');
                                        return;
                                      }



                                      if (state1.loginType.value == 1) {
                                        if (state1.usernamePass == 'zl' &&
                                            state1.password == '123456') {
                                          Get.offAndToNamed(PageName.TAB);
                                          return;
                                        }
                                        var _state =
                                            _formKey.currentState as FormState;
                                        if (!_state.validate())
                                          return print("验证失败");
                                        _state.save();
                                        print(state1.usernamePass);
                                        print(state1.password);

                                        // 防止重复点击
                                        if (lastPopTime == null || DateTime.now().difference(lastPopTime!) > Duration(seconds: 3)) {
                                          lastPopTime = DateTime.now();
                                          loginByUserPass(state1.usernamePass,
                                              state1.password, context);
                                        } else {
                                          lastPopTime = DateTime.now();
                                          MyCommonUtils.showToast("请勿重复点击！");
                                        }


                                      } else {
                                        var _state =
                                            _formKey.currentState as FormState;
                                        if (!_state.validate())
                                          return print("验证失败");
                                        _state.save();
                                        print('phoneNumber,,,,,${state1.phoneNumber}');
                                        print(state1.msgCode);
                                        if (!state1.phoneNumber.isPhoneNumber) {
                                          MyCommonUtils.showToast('请输入正确手机号');
                                          return;
                                        }
                                        if (state1.msgCode.isEmpty) {
                                          MyCommonUtils.showToast('请输入验证码');
                                          return;
                                        }
                                        if (!state1.msgCode.isNum) {
                                          MyCommonUtils.showToast('请输入正确验证码');
                                          return;
                                        }
                                        if (state1.msgCode.length != 6) {
                                          MyCommonUtils.showToast('请输入6位验证码');
                                          return;
                                        }

                                        // getDealerInfoList(context);


                                        // 防止重复点击
                                        if (lastPopTime == null || DateTime.now().difference(lastPopTime!) > Duration(seconds: 3)) {
                                          lastPopTime = DateTime.now();
                                          loginByUserCode2Saas(context);
                                        } else {
                                          lastPopTime = DateTime.now();
                                          MyCommonUtils.showToast("请勿重复点击！");
                                        }
                                      }
                                    },
                                    child: Container(
                                      height: 100.w,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(25),
                                          color: ColorUtil.fromHex('#FF4127')),
                                      child: Text('登录',
                                          style: TextStyle(
                                              fontSize: 20,
                                              color: Colors.white)),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // Visibility(
                            //   visible: Platform.isAndroid ? true : false,
                            //   child: InkWell(
                            //       onTap: () {
                            //         if (!logic.isSelect.value &&
                            //             Platform.isAndroid) {
                            //           MyCommonUtils.showToast(
                            //               '请仔细阅读并同意《用户协议》 《隐私协议》');
                            //           return;
                            //         }
                            //         loginByUserPass(
                            //             'fddtyyh', 'csty@2023', context);
                            //       },
                            //       child: Container(
                            //         child: Text(
                            //           '快速体验',
                            //           style: TextStyle(
                            //               fontSize: 26.sp,
                            //               color: Colors.red,
                            //               decoration: TextDecoration.underline),
                            //         ),
                            //         margin: EdgeInsets.only(top: 30.w),
                            //       )),
                            // ),
                            Visibility(
                              visible: Platform.isAndroid ? true : false,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Obx(() {
                                    return Transform.scale(
                                      scale: 0.8,
                                      child: Checkbox(
                                          value: logic.isSelect.value,
                                          shape: CircleBorder(),
                                          onChanged: (value) {
                                            logic.isSelect.value =
                                                !logic.isSelect.value;
                                            SpUtil.putBool('checkBoxIsSelected',
                                                logic.isSelect.value);
                                          }),
                                    );
                                  }),
                                  Row(
                                    children: [
                                      Text('我已阅读并同意',
                                          style: TextStyle(fontSize: 24.sp)),
                                      InkWell(
                                        child: Text(
                                          '《用户协议》',
                                          style: TextStyle(
                                              color: Colors.blue,
                                              fontSize: 24.sp),
                                        ),
                                        onTap: () async {
                                          String url = Apis.userServe;
                                          Get.toNamed(PageName.AgreementPage,
                                              arguments: {
                                                'title': '用户协议',
                                                'url': url
                                              });
                                        },
                                      ),
                                      InkWell(
                                        child: Text(
                                          '《隐私政策》',
                                          style: TextStyle(
                                              color: Colors.blue,
                                              fontSize: 24.sp),
                                        ),
                                        onTap: () async {
                                          String url = Apis.privacyServe;
                                          Get.toNamed(PageName.AgreementPage,
                                              arguments: {
                                                'title': '隐私协议',
                                                'url': url
                                              });
                                        },
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 点击展开更多
  void clickExplandMore(
      BuildContext context, GlobalKey _accountPositionkey, Map accountInfo) {
    final RenderBox renderBoxRed =
        _accountPositionkey.currentContext!.findRenderObject() as RenderBox;
    final positionsAccount = renderBoxRed.localToGlobal(Offset(0, 0));
    List<String> allKeys = accountInfo.keys.cast<String>().toList();
    print("accountInfo=>${accountInfo}");
    print("allKeys=>${allKeys}");

    showMenu(
        context: context,
        shadowColor: Colors.transparent,
        color: ColorUtil.fromHex('#F8F8F8'),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(20.w),
            bottomRight: Radius.circular(20.w),
          ),
        ),
        position: RelativeRect.fromLTRB(
            60.w, positionsAccount.dy + 102.w, 60.w, 100.w),
        constraints: BoxConstraints(maxWidth: 1.sw - 120.w, maxHeight: 240.w),
        items: List.generate(allKeys.length, (index) {
          String account = allKeys[index];
          String passwordAndUserName = accountInfo[account];
          List tempList = passwordAndUserName.split('-');
          String password = tempList.first;

          ///====追加租户名并判断是否超长=====///
          String accountName = account.split('(')[0];
          String accountCenter = account.split('(')[1].replaceAll(')', '');
          String accountRight = ')';
          double width1 =
              MyCommonUtils.getTextSize(accountName, TextStyle(), context)
                  .width;
          double width2 = MyCommonUtils.getTextSize(
                  "(${accountCenter}", TextStyle(), context)
              .width;
          double width3 =
              MyCommonUtils.getTextSize(accountRight, TextStyle(), context)
                  .width;
          bool isLong =
              (width1 + width2 + width3) > (1.sw - 120.w - 120.w - 40.w);
          print("realwidth=>${width1 + width2 + width3}");
          print("leftwidth=>${1.sw - 120.w - 120.w - 70.w}");

          ///=====追加租户名并判断是否超长=====///
          return PopupMenuItem(
              onTap: () {
                if (account.contains('(')) {
                  account = account.split('(')[0];
                }
                state1.accountTextController.text = account;
                state1.usernamePass = state1.accountTextController.text;
                state1.passwordTextController.text = password;
                state1.password = password;
                Get.forceAppUpdate();
              },
              height: 80.w,
              child: Container(
                alignment: Alignment.centerLeft,
                width: 1.sw - 120.w,
                height: 80.w,
                child: account.contains("(")
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            '${accountName}',
                          ),
                          isLong
                              ? Expanded(
                                  child: Text(
                                  "(${accountCenter}",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ))
                              : Text(
                                  "(${accountCenter}",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                          Text('${accountRight}'),
                        ],
                      )
                    : Text('$account'),
              ));
        })).then((value) {
      state1.arowDown.value = !state1.arowDown.value;
    });
  }


  // saas 改造的公共登录
  void loginByUserCode2Saas(BuildContext context) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["mobile"] = state1.phoneNumber;
    queryParameters["verificationCode"] = state1.msgCode;
    queryParameters["userType"] = 2;

    // logic.showLoadingDialog(context, '登录中...');
    MyDio.get(Apis.loginByCode2Saas, queryParameters: queryParameters,
        successCallBack: (value) {
          

          // // 登录完成请求个人信息
          // _getUserInfo('', '');
          // // 校验是否是灰度用户
          // _checkIsGray();

          /// 登录成功后保存token、用户权限信息等、
          debugPrint("/// 登录成功后保存token、用户权限信息等---lq");
          var response = LoginCodeInfoBean.fromJson(value);
          String? _accessToken = response.data?[0].accessToken;
          SpUtil.putString("access_token", _accessToken!);

          String? userCode = response.data?[0].userCode;
          Map<String, dynamic>? queryRefreshTokenParameters = {};

          queryRefreshTokenParameters["token"] = _accessToken;
          queryRefreshTokenParameters["userCode"] = userCode;
          queryRefreshTokenParameters["applicationCode"] = StringUtil.getApplicationCode();
          queryRefreshTokenParameters["tokenType"] = 1;

          MyDio.post(
            Apis.loginByrefreshToken,
            queryParameters: queryRefreshTokenParameters,
            successCallBack: (value) {
              if (value['code'] == '0') {
                String? accessToken = value['data']['accessToken'];

                SpUtil.putString("access_token", accessToken!);

                /// 如果访问令牌不为空，则将其存储在安全存储中。
                if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);

                // print('accessToken=>${ response.data?[0].userCode}');

                // 登录完成请求个人信息
                // _getUserInfo('', '');
                // 校验是否是灰度用户
                _checkIsGray();

                // 从另外的接口
                MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {
                  // logic.dismissDialog();

                  var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

                  // 租户ID
                  String tenant_id = '${responseOther.tenantId}';
                  SpUtil.putString('tenant_id', tenant_id);

                  List moduleArray = responseOther.appModules ?? [];
                  if (moduleArray.isEmpty) {
                    MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
                  }
                  debugPrint("moduleList 1 ---- ${moduleArray}");
                  List<String> moduleList = [];
                  for (var element in moduleArray) {
                    moduleList.add(element);
                  }
                  debugPrint("moduleList ---- ${moduleList}");
                  // SpUtil.putStringList("appModules", moduleList);
                  state.appModules = responseOther.appModules!;
                  UserAppModuleUtils().saveUserAppModuls(moduleArray);

                  // 登录完成请求个人信息
                  _getUserInfo('', '');
                });

              }

            }
          );








        }, failCallBack: (value) {
          var historyAccountInfo = SpUtil.getObject('accountInfo');
          if (historyAccountInfo == null) {
            historyAccountInfo = {};
          }
          //登录失败并且存储中包含这个key则移除此账号
          historyAccountInfo.removeWhere((key, value) {
            String key1 = key.toString();
            if (key1.contains('(')) {
              key1 = key1.split('(')[0];
            }
            return key1 == state1.phoneNumber;
          });
          SpUtil.putObject('accountInfo', historyAccountInfo);
          logic.dismissDialog();
        });


  }


  //用户名密码登录
  void loginByUserPass(String username, String password, BuildContext context) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["mobile"] = username;

    // queryParameters["loginType"] = 1;

    var aesScrectKey = '======annto=====';

    // EncryptUtils.initAes(aesScrectKey).then((value) async {
    //   String encryptPwd = EncryptUtils.encryptAes(password);
    //   String ivAes = EncryptUtils.getIvAes();
    //   print('encryptPwd $encryptPwd');

    queryParameters["password"] = password;
      // queryParameters["ivAes"] = ivAes;

    // logic.showLoadingDialog(context, '登录中...');
    MyDio.post(Apis.loginPwd2Anyunda, queryParameters: queryParameters,
        successCallBack: (value) {


          // // 登录完成请求个人信息
          // _getUserInfo(username, password);
          // // 校验是否是灰度用户
          // _checkIsGray();

          /// 登录成功后保存token、用户权限信息等、

          var response = LoginCodeInfoBean.fromJson(value);
          String? _accessToken = response.data?[0].accessToken;
          SpUtil.putString("access_token", _accessToken!);


          String? userCode = response.data?[0].userCode;
          Map<String, dynamic>? queryRefreshTokenParameters = {};

          queryRefreshTokenParameters["token"] = _accessToken;
          queryRefreshTokenParameters["userCode"] = userCode;
          queryRefreshTokenParameters["applicationCode"] = StringUtil.getApplicationCode();
          queryRefreshTokenParameters["tokenType"] = 1;

          MyDio.post(
            Apis.loginByrefreshToken,
            queryParameters: queryRefreshTokenParameters,
            successCallBack: (value) {
              if (value['code'] == '0') {
                String accessToken = value['data']['accessToken'];
                SpUtil.putString("access_token", accessToken!);




                /// 如果访问令牌不为空，则将其存储在安全存储中。
                if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);


                // 登录完成请求个人信息
                // _getUserInfo(username, password);
                // 校验是否是灰度用户
                _checkIsGray();

                // 从另外的接口
                MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {
                  // logic.dismissDialog();
                  var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

                  // 租户ID
                  String tenant_id = '${responseOther.tenantId}';
                  SpUtil.putString('tenant_id', tenant_id);

                  List moduleArray = responseOther.appModules ?? [];
                  if (moduleArray.isEmpty) {
                    MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
                  }
                  debugPrint("moduleList 2 ---- ${moduleArray}");
                  List<String> moduleList = [];
                  for (var element in moduleArray) {
                    moduleList.add(element);
                  }
                  debugPrint("moduleList ---- ${moduleList}");
                  // SpUtil.putStringList("appModules", moduleList);
                  state.appModules = responseOther.appModules!;
                  UserAppModuleUtils().saveUserAppModuls(moduleArray);

                  _getUserInfo(username, password);
                });



              }

            }
          );








        }, failCallBack: (value) {
          var historyAccountInfo = SpUtil.getObject('accountInfo');
          if (historyAccountInfo == null) {
            historyAccountInfo = {};
          }
          //登录失败并且存储中包含这个key则移除此账号
          historyAccountInfo.removeWhere((key, value) {
            String key1 = key.toString();
            if (key1.contains('(')) {
              key1 = key1.split('(')[0];
            }
            return key1 == username;
          });
          SpUtil.putObject('accountInfo', historyAccountInfo);
          logic.dismissDialog();
        });
    // });

    // String encryptPwd = EncryptUtils.encryptAes(password);




  }

  /// 获取经销商列表
  getDealerInfoList(BuildContext context) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["phone"] = state1.phoneNumber;
    queryParameters["code"] = state1.msgCode;
    MyDio.post(Apis.getDealerInfoList, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        state1.dealerInfoList = value['data'];
        if (state1.dealerInfoList.length > 1) {
          state1.shouldBack = true;
          chooseDealerInfo(context);
        } else if (state1.dealerInfoList.length == 1) {
          state1.dealerNo = state1.dealerInfoList[0]["id"] ?? 0;
          state1.dealerName = state1.dealerInfoList[0]["systemName"] ?? "";
          state1.strategyId = state1.dealerInfoList[0]["strategyId"] ?? 0;
          getDealerUserList(context);
        } else {
          MyCommonUtils.showToast("无对应经销商信息，请联系管理员！");
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
    });
  }

  /// 选择经销商
  chooseDealerInfo(BuildContext context) {
    state1.dealerNo = state1.dealerInfoList[0]["id"] ?? 0;
    state1.dealerName = state1.dealerInfoList[0]["systemName"] ?? "";
    state1.strategyId = state1.dealerInfoList[0]["strategyId"] ?? 0;
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setDiaLogState) {
            return SafeArea(
              child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(30.w))),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20.h),
                    Row(
                      children: [
                        SizedBox(width: 20.w),
                        InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Text(
                            "取消",
                            style: TextStyle(
                                color: ColorResource.RED_TITLE_COLOR,
                                fontSize: 36.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        Spacer(),
                        Container(
                          alignment: Alignment.center,
                          child: Text(
                            "选择经销商",
                            style: TextStyle(
                                color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                                fontSize: 36.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            getDealerUserList(context);
                          },
                          child: Text(
                            "确定",
                            style: TextStyle(
                                color: ColorResource.RED_TITLE_COLOR,
                                fontSize: 36.sp,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        SizedBox(width: 20.w),
                      ],
                    ),
                    SizedBox(height: 20.h),
                    Container(
                        // constraints: BoxConstraints(maxHeight: state1.dealerInfoList.length > 5 ? 5.5 * 80.h : state1.dealerInfoList.length * 80.h),
                        constraints: BoxConstraints(maxHeight: 3 * 80.h),
                        child: MediaQuery.removePadding(
                          context: context,
                          removeTop: true,
                          removeBottom: true,
                          child: ListView.builder(
                            shrinkWrap: false,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemCount: state1.dealerInfoList.length,
                            itemBuilder: (context, index) {
                              var dealerInfo = state1.dealerInfoList[index];
                              return InkWell(
                                onTap: () {
                                  setDiaLogState(() {
                                    state1.dealerNo = dealerInfo["id"] ?? 0;
                                    state1.dealerName =
                                        dealerInfo["systemName"];
                                    state1.strategyId =
                                        dealerInfo["strategyId"] ?? 0;
                                  });
                                },
                                child: Container(
                                  margin: EdgeInsets.only(
                                      top: 10.h, left: 10.w, right: 10.w),
                                  alignment: Alignment.center,
                                  height: 70.h,
                                  decoration: BoxDecoration(
                                    color: ColorResource.WHITE_COMMON_COLOR,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(10.r)),
                                    border: Border.all(
                                        color: dealerInfo["systemName"] ==
                                                state1.dealerName
                                            ? ColorResource.RED_COMMON_COLOR
                                            : ColorResource.GRAY_COMMON_COLOR,
                                        width: 1),
                                  ),
                                  child: Text(
                                    dealerInfo["systemName"] ?? "",
                                    style: TextStyle(
                                      color: dealerInfo["systemName"] ==
                                              state1.dealerName
                                          ? ColorResource.RED_COMMON_COLOR
                                          : ColorResource.GRAY_COMMON_COLOR,
                                      fontSize: 32.sp,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                          // Scrollbar(
                          //   thumbVisibility: true,
                          //   child:
                          // ),
                        )),
                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            );
          });
        });

    // Pickers.showSinglePicker(
    //   context,
    //   data: datas,
    //   selectData: datas[0],
    //   pickerStyle: PickerStyle(
    //     title: Container(
    //       alignment: Alignment.center,
    //       child: Text(
    //         '选择经销商',
    //         style: TextStyle(
    //             color: ColorResource.BLACK_BOLD_TITLE_COLOR,
    //             fontSize: 36.sp,
    //             fontWeight: FontWeight.w600
    //         ),
    //       ),
    //     ),
    //     cancelButton: Container(
    //       margin: EdgeInsets.only(left: 20.w),
    //       child: Text(
    //           "取消",
    //           style: TextStyle(
    //               color: ColorResource.RED_TITLE_COLOR,
    //               fontSize: 32.sp,
    //               fontWeight: FontWeight.w600
    //           )
    //       ),
    //     ),
    //     commitButton: Container(
    //       margin: EdgeInsets.only(right: 20.w),
    //       child: Text(
    //           "确定",
    //           style: TextStyle(
    //               color: ColorResource.RED_TITLE_COLOR,
    //               fontSize: 32.sp,
    //               fontWeight: FontWeight.w600
    //           )
    //       ),
    //     )
    //   ),
    //   onConfirm: (p, position) {
    //     state.dealerNo = dealers[position]["dealerNo"];
    //     MyCommonUtils.showToast("选择了${p},编号为${state.dealerNo}");
    //   }
    // );
  }

  /// 获取用户列表
  getDealerUserList(BuildContext context) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["phone"] = state1.phoneNumber;
    queryParameters["code"] = state1.msgCode;
    queryParameters["tenantId"] = state1.dealerNo;
    queryParameters["strategyId"] = state1.strategyId;
    MyDio.post(Apis.getDealerUserList, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        state1.dealerUserList = value['data'];
        if (state1.dealerUserList.length > 1) {
          Get.toNamed(PageName.ChooseUserPage, arguments: {
            "phoneNumber": state1.phoneNumber,
            "msgCode": state1.msgCode,
            "dealerNo": state1.dealerNo,
            "dealerName": state1.dealerName,
            "strategyId": state1.strategyId,
            // "dealerUserList": state1.dealerUserList
          })?.then((value) {
            if (value != null) {
              state1.userNameCode = value["userName"];
              loginByPhoneCode(context, state1.userNameCode, state1.msgCode);
            }
          });
        } else if (state1.dealerUserList.length == 1) {
          state1.userNameCode = state1.dealerUserList[0]["userName"];
          loginByPhoneCode(context, state1.userNameCode, state1.msgCode);
        } else {
          MyCommonUtils.showToast("无对应用户信息，请联系管理员！");
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
    });
  }

  //手机号验证码登录
  void loginByPhoneCode(
      BuildContext context, String userName, String verifyCode) {
    FocusScopeNode currentFocus = FocusScope.of(Get.context!);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }

    Map<String, dynamic> queryParameters = {
      'userName': userName,
      'code': verifyCode
    };
    logic.showLoadingDialog(context, '登录中...');
    MyDio.post(Apis.loginByPhoneNumber, queryParameters: queryParameters,
        successCallBack: (value) {
      logic.dismissDialog();

      state1.canTap = true;
      state1.msgCodeTime = 120;
      SpUtil.putString("loginPhoneNumber", state1.phoneNumber);
      submitPhoneNumberVerified();
      // 登录完成请求个人信息
      _getUserInfo("", "");
      // 校验是否是灰度用户
      _checkIsGray();

      /// 登录成功后保存token、用户权限信息等、
      debugPrint("/// 登录成功后保存token、用户权限信息等---lq");
      var response = LoginInfoBean.fromJson(value);
      SpUtil.putString("access_token", response.data!.accessToken!);

      // 租户ID
      String tenant_id = '${response.data!.tenantId}';
      SpUtil.putString('tenant_id', tenant_id);

      List moduleArray = response.data?.appModules ?? [];
      if (moduleArray.isEmpty) {
        MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
      }
      debugPrint("moduleList ---- ${moduleArray}");
      List<String> moduleList = [];
      for (var element in moduleArray) {
        moduleList.add(element);
      }
      debugPrint("moduleList ---- ${moduleList}");
      // SpUtil.putStringList("appModules", moduleList);
      state.appModules = response.data!.appModules!;
      UserAppModuleUtils().saveUserAppModuls(moduleArray);
    }, failCallBack: (value) {
      var historyAccountInfo = SpUtil.getObject('accountInfo');
      if (historyAccountInfo == null) {
        historyAccountInfo = {};
      }
      //登录失败并且存储中包含这个key则移除此账号
      // historyAccountInfo.removeWhere((key, value) {
      //   String key1 = key.toString();
      //   if (key1.contains('(')) {
      //     key1 = key1.split('(')[0];
      //   }
      //   return key1 == username;
      // });
      // SpUtil.putObject('accountInfo', historyAccountInfo);
      logic.dismissDialog();
    });
  }

  // 提交手机号已验证
  void submitPhoneNumberVerified() {
    MyDio.get(Apis.submitPhoneNumberVerified,
        successCallBack: (value) {},
        failCallBack: (value) {},
        showErrMsg: false);
  }

  // 获取个人信息
  void _getUserInfo(String username, String password) {
    MyDio.get(Apis.getAppUserInfo, queryParameters: {},
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic data = response.data ?? {};
        SpUtil.putObject('userInfo', data);
        // print("userInfo=>login${SpUtil.getObject('userInfo')}");

        if ((SpUtil.getObject('userInfo') == null) ||
            (SpUtil.getObject('userInfo') == {})) {
          _getUserInfo(username, password);
          return;
        }

        SetInfo.instance.initLoad();

        getDealerNo();
        checkShowShopScan();
        getValidateFlagInfo(username, password);

        if (username == 'fddtyyh') return;

        if (state1.loginType.value == 1) {
          var accountInfo = SpUtil.getObject('accountInfo');
          if (accountInfo == null) {
            accountInfo = {};
          }
          String tenantName = '';
          if (null != data['enterprise']) {
            tenantName = data['enterprise']['systemName'];
          }
          username = username + '($tenantName)';

          accountInfo.removeWhere((key, value) {
            String key1 = key.toString();
            if (key1.contains('(')) {
              key1 = key1.split('(')[0];
            }
            return key1 == username.split('(')[0];
          });
          accountInfo[username] = password +
              '-' +
              '${TimeUtils.getCurrentDateAndFormat([
                    "yyyy",
                    "/",
                    "mm",
                    "/",
                    "dd",
                    " ",
                    "HH",
                    ":",
                    "nn",
                    ":",
                    "ss"
                  ])}' +
              '-' +
              '${(data["employee"]?["employeeName"] ?? "")}';

          var sortedList = accountInfo.entries.toList()
            ..sort((b, a) {
              String aValue = a.value;
              List aValueList = aValue.split('-');
              String bValue = b.value;
              List bValueList = bValue.split('-');
              String date1 = aValueList[1];
              String date2 = bValueList[1];
              return (date1).compareTo(date2);
            });
          print('================拼接后的数据==========${sortedList.toString()}');
          Map newAccountInfo = {};
          newAccountInfo.addEntries(sortedList);
          SpUtil.putObject('accountInfo', newAccountInfo);
          SpUtil.putString('currentAccount', username);
          SpUtil.putString('employeeNo', data['employee']['employeeNo']);
        }
      }
    });
  }

  // 用户判断
  _chooseUser() async {
    // 加入b2b 用户
    var userType = await SecureStorageCommon.save('userType').get();
    print('userType  $userType');


    Future.delayed(Duration(seconds: 1), () async {   // 模拟接口
      var data = {
        "code": 200,
        "message": "操作成功",
        "data": {
          "erpSystem": {
            "appCode": "ERP001",
            "appName": "金蝶云星空",
            "tenantCode": "T10086"
          },
          "b2bSystem": {
            "appCode": "B2B2024",
            "appName": "数商云平台",
            "tenantCode": "BT2025"
          }
        }
      };

      // List _data = [
      //   {
      //     'type': 'erp',
      //     'tenantCode': 'annto',
      //     'applicationCode': 'APP5312624649523200',
      //   },
      //   {
      //     'type': 'b2b',
      //     'tenantCode': 'annto',
      //     'applicationCode': 'APP5312624649523200',
      //   }
      // ];
      // 将 Map 转换为 List 格式，以便在选择页面中使用
      Map<String, dynamic> dataMap = data['data']! as Map<String, dynamic>;
      List<Map<String, dynamic>> appList = [];

      // 转换 erpSystem
      if (dataMap.containsKey('erpSystem')) {
        Map<String, dynamic> erpSystem = dataMap['erpSystem'] as Map<String, dynamic>;
        var _item = {
          'id': 'erpSystem',
          'type': 'ERP业务员',
          'appCode': erpSystem['appCode'],
          'appName': erpSystem['appName'],
          'tenantCode': erpSystem['tenantCode'],
        };
        appList.add(_item);
        state1.erpSystemItem = _item;
      }

      // 转换 b2bSystem
      if (dataMap.containsKey('b2bSystem')) {
        Map<String, dynamic> b2bSystem = dataMap['b2bSystem'] as Map<String, dynamic>;
        var _item = {
          'id': 'b2bSystem',
          'type': '运营商业务员',
          'appCode': b2bSystem['appCode'],
          'appName': b2bSystem['appName'],
          'tenantCode': b2bSystem['tenantCode'],
        };
        appList.add(_item);
        state1.b2bSystemItem = _item;
      }

      // 临时将 appList 转换为 Map 以保持兼容性，但实际应该修改 state 中的类型定义
      state1.AppLists = appList;

      String jsonString = json.encode(appList);
      await SecureStorageCommon.save<String>('appLists').set(jsonString);

      if (userType == null) {
        // 如果为null 就调用接口 查询用户租户的绑定的应用
        if (appList.length > 1) {// 如果有 b2b 和  erp 调整到新页面选择
          // print('xxxxxxxxxxxx');
          Get.offAndToNamed(PageName.B2bChooseTypePage);
        } else {
          // print('xxxxxxxxxxxx  ${state1.erpSystemItem} ${state1.erpSystemItem != null}  ${state1.b2bSystemItem}' );
          if (state1.erpSystemItem != null) {
            await SecureStorageCommon.save<String>('userType').set('erpSystem');
            Get.offAndToNamed(PageName.TAB);
          } else if (state1.b2bSystemItem != null) {
            await SecureStorageCommon.save<String>('userType').set('b2bSystem');
            Get.offAndToNamed(PageName.B2BTAB);
          }
        }
      } else {

        if (userType == 'erpSystem') {
          Get.offAndToNamed(PageName.TAB);
        }
      }





    });

  }


  // 查询手机号是否验证 0未验证 1已验证
  getValidateFlagInfo(String username, String password) async {
    _chooseUser();

    // Get.offAndToNamed(PageName.TAB);


    return;
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getValidateFlagInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      if (value["code"] == 200) {
        if ((value["data"] != null) &&
            (value["data"]["phoneStatus"] != null) &&
            (value["data"]["phoneStatus"] == "1")) {
          state1.validateFlag.value = true;
          SpUtil.putBool("validateFlag", true);
        } else {
          state1.validateFlag.value = false;
          SpUtil.putBool("validateFlag", false);
        }

        if (state1.validateFlag.value == false) {
          Get.toNamed(PageName.BindPhoneNumberPage,
              parameters: {"fromPage": "LoginPage"})?.then((value) {
            if (value != null) {
              if (value == true) {
                SpUtil.putBool("validateFlag", true);
                _getUserInfo(username, password);
              } else {
                SpUtil.putBool("validateFlag", false);
                Get.offAndToNamed(PageName.TAB);
              }
            }
          });
        } else {
          if (state1.shouldBack) {
            Get.back();
          }
          Get.offAndToNamed(PageName.TAB);
        }
      } else {
        MyCommonUtils.showToast(value["msg"]);
      }
      print("validateFlag=>${state1.validateFlag.value}");
    }, showErrMsg: false);
  }
}

// 校验是否是灰度用户
void _checkIsGray() {
  // MyDio.get(
  //   Apis.checkGray,
  //   successCallBack: (value) {
  //     print('============灰度：$value');
  //   },
  //   failCallBack: () {},
  // );
}

// 获取经销商编号
void getDealerNo() {
  MyDio.get(
    Apis.searchDealerNo,
    successCallBack: (value) {
      Map result = Map.from(value);
      if (result['code'].toString() == '200') {
        String dealerNo = result['data']['branchNo'].toString();
        SpUtil.putString('dealerNo', dealerNo);
      }
    },
  );
}

void checkShowShopScan() {
  dynamic userInfo = SpUtil.getObject('userInfo');
  dynamic employee = userInfo['employee'] ?? {};
  MyDio.get(Apis.checkShowShopScan,
      queryParameters: {'employeeNo': employee['employeeNo']},
      successCallBack: (value) {
    DioResultBean response = DioResultBean.fromJson(value);
    if (response.code.toString() == '200') {
      String data = response.data.toString();
      SpUtil.putString('showShopScan', data);
    }
  }, showErrMsg: false);
}

class GetInputItem extends StatefulWidget {

  GetInputItem({
    super.key,
    required this.label,
    this.hintText,
    this.keyType,
    this.onSaved,
    this.obscureText,
    this.type,
    this.keyboardType,
    this.controller,
    this.onChanged,
  });

  final label;
  final hintText;
  final obscureText;
  final onSaved;
  final onChanged;
  final type;
  final keyType;
  TextInputType? keyboardType;
  TextEditingController? controller;

  @override
  _GetInputItemState createState() => _GetInputItemState();
}
// State类
class _GetInputItemState extends State<GetInputItem> {

  final FocusNode _focusNode = FocusNode();

  // 控制密码显示/隐藏
  bool _isObscure = true;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (event) {
        // bug, 部分机器, 切换textfield软键盘类型时, 键盘会收起然后再弹出, 会导致焦点丢失, 这里做延迟重新获取焦点
        Future.delayed(Duration(milliseconds: 800))
            .then((value) => _focusNode.requestFocus());
      },
      child: Container(
        height: 112.w,
        margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
        padding: EdgeInsets.fromLTRB(15, 0, 20, 0),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: ColorUtil.fromHex('#F8F8F8'),
          borderRadius: BorderRadius.circular(10),
        ),
        child: TextFormField(
          controller: widget.controller ?? TextEditingController(),
          focusNode: _focusNode,
          textAlignVertical: TextAlignVertical.center,
          obscureText: widget.keyType == 'password' ? _isObscure : false,
          keyboardType: widget.keyboardType,
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: widget.hintText,
            hintStyle: TextStyle(
                color: ColorResource.GRAY_EDIT_COLOR, fontSize: 26.sp),
            hintTextDirection: TextDirection.ltr,
            // 兼容 密码框的小眼睛
            suffixIcon: widget.keyType == 'password' ? IconButton(
              icon: Icon(
                _isObscure ? Icons.visibility_off : Icons.visibility,  // 切换眼睛图标
              ),
              onPressed: () {
                setState(() {
                  _isObscure = !_isObscure;
                });
              },
            ) : null
          ),
          onSaved: widget.onSaved ?? null,
          onChanged: widget.onChanged ?? null,
          inputFormatters: <TextInputFormatter>[
            LengthLimitingTextInputFormatter(widget.type == 1 ? 25 : 20) //限制长度
          ],
        ),
      ),
    );
  }
}
