//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler10 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSmoothColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getSmoothColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSmoothColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSmoothColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSlowColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getSlowColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSlowColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSlowColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getCongestedColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getCongestedColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setCongestedColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCongestedColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getSeriousCongestedColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getSeriousCongestedColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setSeriousCongestedColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSeriousCongestedColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getRatio_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRatio();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setRatio_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRatio(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::getTrafficRoadBackgroundColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTrafficRoadBackgroundColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MyTrafficStyle::setTrafficRoadBackgroundColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MyTrafficStyle __this__ = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTrafficRoadBackgroundColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::fromLatLngZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.CameraPosition.fromLatLngZoom(var0, var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::builder_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.CameraPosition.builder();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition::builder__com_amap_api_maps_model_CameraPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.CameraPosition var0 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.CameraPosition.builder(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TextOptionsCreator::newArray_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.TextOptions[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.TextOptionsCreator __this__ = (com.amap.api.maps.model.TextOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.TextOptions[] __result__ = null;
                    try {
                        __result__ = __this__.newArray(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::getCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getCenter();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::setCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCenter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::getKeywords_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getKeywords();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PoiPara::setKeywords_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PoiPara __this__ = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setKeywords(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::icons_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.icons(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::rotatingIcons_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.rotatingIcons(var1, var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAngleOffset_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAngleOffset();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isRotatingMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isRotatingMode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getIcons_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> __result__ = null;
                    try {
                        __result__ = __this__.getIcons();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::period_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.period(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getPeriod_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getPeriod();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isPerspective_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isPerspective();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::perspective_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.perspective(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::position_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.position(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setFlat_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.setFlat(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::icon_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.icon(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::anchor_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.anchor(var1.floatValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setInfoWindowOffset_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.setInfoWindowOffset(var1.intValue(), var2.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::title_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.title(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::snippet_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.snippet(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::draggable_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.draggable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::visible_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.visible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::setGps_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.setGps(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getTitle_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTitle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getSnippet_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getSnippet();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getIcon_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BitmapDescriptor> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                    try {
                        __result__ = __this__.getIcon();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAnchorU_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorU();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getInfoWindowOffsetX_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowOffsetX();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getInfoWindowOffsetY_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowOffsetY();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAnchorV_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAnchorV();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isDraggable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isDraggable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isGps_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isGps();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isFlat_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isFlat();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::zIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.zIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::alpha_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.alpha(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getAlpha_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAlpha();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::autoOverturnInfoWindow_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.autoOverturnInfoWindow(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isInfoWindowAutoOverturn_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isInfoWindowAutoOverturn();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::displayLevel_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.displayLevel(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getDisplayLevel_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getDisplayLevel();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::rotateAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.rotateAngle(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::getRotateAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRotateAngle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::infoWindowEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.infoWindowEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isInfoWindowEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isInfoWindowEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::belowMaskLayer_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.belowMaskLayer(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::isBelowMaskLayer_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isBelowMaskLayer();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.MarkerOptions::clone_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MarkerOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.MarkerOptions __this__ = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MarkerOptions __result__ = null;
                    try {
                        __result__ = __this__.clone();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoints(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.LatLng>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                    try {
                        __result__ = __this__.getPoints();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setWidth(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setTopColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTopColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getTopColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTopColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setSideColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSideColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getSideColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getSideColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::set3DModel_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.set3DModel(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.NavigateArrow::is3DModel_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.NavigateArrow __this__ = (com.amap.api.maps.model.NavigateArrow) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.is3DModel();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.AnimationSet::addAnimation_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.animation.AnimationSet __this__ = (com.amap.api.maps.model.animation.AnimationSet) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addAnimation(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.AnimationSet::cleanAnimation_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.animation.AnimationSet __this__ = (com.amap.api.maps.model.animation.AnimationSet) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.cleanAnimation();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setDuration_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDuration(var1.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setFillMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setFillMode(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getFillMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getFillMode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setRepeatCount_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRepeatCount(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::setRepeatMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRepeatMode(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getRepeatMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getRepeatMode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::getRepeatCount_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getRepeatCount();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.animation.Animation::resetUpdateFlags_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.animation.Animation __this__ = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.resetUpdateFlags();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::builder_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds.Builder __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.LatLngBounds.builder();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::contains__com_amap_api_maps_model_LatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.contains(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::contains__com_amap_api_maps_model_LatLngBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.contains(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::intersects_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.intersects(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLngBounds::including_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLngBounds __result__ = null;
                    try {
                        __result__ = __this__.including(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleDataPath_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleDataPath();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleDataPath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleDataPath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleTexturePath_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleTexturePath();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleTexturePath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleTexturePath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleData_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    byte[] __result__ = null;
                    try {
                        __result__ = __this__.getStyleData();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleData_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleTextureData_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    byte[] __result__ = null;
                    try {
                        __result__ = __this__.getStyleTextureData();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleTextureData_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleTextureData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleId_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleId(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::isEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleExtraData_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    byte[] __result__ = null;
                    try {
                        __result__ = __this__.getStyleExtraData();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleExtraData_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleExtraData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleExtraPath_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleExtraPath();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleExtraPath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleExtraPath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleDataOverseaPath_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleDataOverseaPath();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleDataOverseaPath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleDataOverseaPath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleDataOversea_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    byte[] __result__ = null;
                    try {
                        __result__ = __this__.getStyleDataOversea();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleDataOversea_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleDataOversea(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleResDataPath_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getStyleResDataPath();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleResDataPath_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleResDataPath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::getStyleResData_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    byte[] __result__ = null;
                    try {
                        __result__ = __this__.getStyleResData();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CustomMapStyleOptions::setStyleResData_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CustomMapStyleOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CustomMapStyleOptions __this__ = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CustomMapStyleOptions __result__ = null;
                    try {
                        __result__ = __this__.setStyleResData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::getType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::getMinZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getMinZoom();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::setMinZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMinZoom(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::getMaxZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getMaxZoom();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::setMaxZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMaxZoom(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::getUrl_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getUrl();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::setCacheEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCacheEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlaySource::isCacheEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isCacheEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlayOptions::setRes_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CrossOverlayOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.graphics.Bitmap var1 = (android.graphics.Bitmap) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CrossOverlayOptions __this__ = (com.amap.api.maps.model.CrossOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CrossOverlayOptions __result__ = null;
                    try {
                        __result__ = __this__.setRes(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlayOptions::getRes_batch", (__argsBatch__, __methodResult__) -> {
                List<android.graphics.Bitmap> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CrossOverlayOptions __this__ = (com.amap.api.maps.model.CrossOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.graphics.Bitmap __result__ = null;
                    try {
                        __result__ = __this__.getRes();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.VisibleRegionCreator::newArray_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.VisibleRegion[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.VisibleRegionCreator __this__ = (com.amap.api.maps.model.VisibleRegionCreator) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.VisibleRegion[] __result__ = null;
                    try {
                        __result__ = __this__.newArray(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.LatLng::clone_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.LatLng __this__ = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.clone();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition.Builder::target_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CameraPosition.Builder __this__ = (com.amap.api.maps.model.CameraPosition.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = __this__.target(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition.Builder::zoom_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CameraPosition.Builder __this__ = (com.amap.api.maps.model.CameraPosition.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = __this__.zoom(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition.Builder::tilt_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CameraPosition.Builder __this__ = (com.amap.api.maps.model.CameraPosition.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = __this__.tilt(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition.Builder::bearing_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition.Builder> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CameraPosition.Builder __this__ = (com.amap.api.maps.model.CameraPosition.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition.Builder __result__ = null;
                    try {
                        __result__ = __this__.bearing(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CameraPosition.Builder::build_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.CameraPosition.Builder __this__ = (com.amap.api.maps.model.CameraPosition.Builder) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition __result__ = null;
                    try {
                        __result__ = __this__.build();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions.LineCapType::valueOf_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolylineOptions.LineCapType> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.model.PolylineOptions.LineCapType __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.model.PolylineOptions.LineCapType.valueOf(var0.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptions.LineCapType::getTypeValue_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.PolylineOptions.LineCapType __this__ = (com.amap.api.maps.model.PolylineOptions.LineCapType) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTypeValue();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPosition(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setTitle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTitle(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getTitle_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTitle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getSnippet_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getSnippet();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setSnippet_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSnippet(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setObject_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.lang.Object var1 = (java.lang.Object) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setObject(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getObject_batch", (__argsBatch__, __methodResult__) -> {
                List<java.lang.Object> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.lang.Object __result__ = null;
                    try {
                        __result__ = __this__.getObject();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setRotateAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRotateAngle(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::getRotateAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRotateAngle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::setAnimation_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAnimation(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::startAnimation_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.startAnimation();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::showInfoWindow_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.showInfoWindow();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.BasePointOverlay::isInfoWindowEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.BasePointOverlay __this__ = (com.amap.api.maps.model.BasePointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isInfoWindowEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.CircleOptionsCreator::newArray_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CircleOptions[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.CircleOptionsCreator __this__ = (com.amap.api.maps.model.CircleOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CircleOptions[] __result__ = null;
                    try {
                        __result__ = __this__.newArray(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::setStrokeWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStrokeWidth(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::getStrokeWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getStrokeWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::setStrokeColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStrokeColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::getStrokeColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getStrokeColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Arc::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Arc __this__ = (com.amap.api.maps.model.Arc) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getFov_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getFov();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getAspectRatio_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getAspectRatio();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getRotate_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRotate();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getX_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getX();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getY_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getY();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.AMapCameraInfo::getZ_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.AMapCameraInfo __this__ = (com.amap.api.maps.model.AMapCameraInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZ();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCenter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getCenter();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setRadius_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRadius(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getRadius_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getRadius();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setStrokeWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStrokeWidth(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getStrokeWidth_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getStrokeWidth();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setStrokeColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStrokeColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getStrokeColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getStrokeColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setFillColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setFillColor(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getFillColor_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getFillColor();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZIndex(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::isVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isVisible();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::contains_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.contains(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setHoleOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.BaseHoleOptions> var1 = (java.util.List<com.amap.api.maps.model.BaseHoleOptions>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setHoleOptions(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getHoleOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.maps.model.BaseHoleOptions>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.maps.model.BaseHoleOptions> __result__ = null;
                    try {
                        __result__ = __this__.getHoleOptions();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::setStrokeDottedLineType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStrokeDottedLineType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.Circle::getStrokeDottedLineType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.model.Circle __this__ = (com.amap.api.maps.model.Circle) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getStrokeDottedLineType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolygonOptions::add__com_amap_api_maps_model_LatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolygonOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolygonOptions __this__ = (com.amap.api.maps.model.PolygonOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolygonOptions __result__ = null;
                    try {
                        __result__ = __this__.add(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.model.PolygonOptions::addAll_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.PolygonOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.lang.Iterable<com.amap.api.maps.model.LatLng> var1 = (java.lang.Iterable<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.model.PolygonOptions __this__ = (com.amap.api.maps.model.PolygonOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.PolygonOptions __result__ = null;
                    try {
                        __result__ = __this__.addAll(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
        }};
    }
}
