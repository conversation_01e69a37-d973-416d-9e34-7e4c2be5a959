//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler6 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.model.Polyline::setOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.PolylineOptions var1 = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setOptions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOptions(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getOptions", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getOptions(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions __result__ = null;
                try {
                    __result__ = __this__.getOptions();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setCustemTextureIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setCustemTextureIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustemTextureIndex(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setCustomTextureIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<Integer> var1 = (java.util.List<Integer>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setCustomTextureIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomTextureIndex(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setShownRatio", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setShownRatio(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setShownRatio(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setShownRange", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setShownRange(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setShownRange(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getShownRatio", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getShownRatio(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getShownRatio();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::showPolylineRangeEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::showPolylineRangeEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.showPolylineRangeEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::isShowPolylineRangeEnable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::isShowPolylineRangeEnable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isShowPolylineRangeEnable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setPolylineShowRange", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setPolylineShowRange(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPolylineShowRange(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getPolylineShownRangeBegin", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getPolylineShownRangeBegin(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getPolylineShownRangeBegin();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getPolylineShownRangeEnd", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getPolylineShownRangeEnd(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getPolylineShownRangeEnd();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setFootPrintTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setFootPrintTexture(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFootPrintTexture(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getFootPrintTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getFootPrintTexture(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getFootPrintTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setFootPrintGap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setFootPrintGap(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFootPrintGap(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getFootPrintGap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getFootPrintGap(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getFootPrintGap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setEraseTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var2 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setEraseTexture(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setEraseTexture(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getEraseTexture", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getEraseTexture(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getEraseTexture();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getEraseVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getEraseVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.getEraseVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setEraseColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setEraseColor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setEraseColor(var1, var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getEraseColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getEraseColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getEraseColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setCustomTextureList", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.List<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setCustomTextureList(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomTextureList(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::getMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::getMap(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMap __result__ = null;
                try {
                    __result__ = __this__.getMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onCreate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onCreate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onCreate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onResume", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onResume(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onResume();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onPause", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onPause(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onPause();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onDestroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onDestroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onLowMemory", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onLowMemory(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onLowMemory();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::onSaveInstanceState", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::onSaveInstanceState(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onSaveInstanceState(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapView::setVisibility", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapView __this__ = (com.amap.api.maps.TextureMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapView@" + __this__ + "::setVisibility(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisibility(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::calculateLineDistance", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::calculateLineDistance(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = com.amap.api.maps.AMapUtils.calculateLineDistance(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::calculateArea__com_amap_api_maps_model_LatLng__com_amap_api_maps_model_LatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::calculateArea(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = com.amap.api.maps.AMapUtils.calculateArea(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::calculateArea__List_com_amap_api_maps_model_LatLng_", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var0 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::calculateArea(" + var0 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = com.amap.api.maps.AMapUtils.calculateArea(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::getLatestAMapApp", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.content.Context var0 = (android.content.Context) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::getLatestAMapApp(" + var0 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.getLatestAMapApp(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::openAMapNavi", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.NaviPara var0 = (com.amap.api.maps.model.NaviPara) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::openAMapNavi(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.openAMapNavi(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::openAMapPoiNearbySearch", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.PoiPara var0 = (com.amap.api.maps.model.PoiPara) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::openAMapPoiNearbySearch(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.openAMapPoiNearbySearch(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::openAMapDrivingRoute", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.RoutePara var0 = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::openAMapDrivingRoute(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.openAMapDrivingRoute(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::openAMapTransitRoute", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.RoutePara var0 = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::openAMapTransitRoute(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.openAMapTransitRoute(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapUtils::openAMapWalkingRoute", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.RoutePara var0 = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapUtils::openAMapWalkingRoute(" + var0 + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    com.amap.api.maps.AMapUtils.openAMapWalkingRoute(var0, var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::newInstance", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment::newInstance(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.TextureMapFragment __result__ = null;
                try {
                    __result__ = com.amap.api.maps.TextureMapFragment.newInstance();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::newInstance__com_amap_api_maps_AMapOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMapOptions var0 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment::newInstance(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.TextureMapFragment __result__ = null;
                try {
                    __result__ = com.amap.api.maps.TextureMapFragment.newInstance(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::getMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::getMap(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMap __result__ = null;
                try {
                    __result__ = __this__.getMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onAttach", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.app.Activity var1 = (android.app.Activity) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onAttach(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onAttach(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onCreate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onCreate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onCreate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onResume", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onResume(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onResume();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onPause", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onPause(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onPause();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onDestroyView", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onDestroyView(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroyView();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onDestroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onDestroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onLowMemory", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onLowMemory(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onLowMemory();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::onSaveInstanceState", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::onSaveInstanceState(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onSaveInstanceState(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::setArguments", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::setArguments(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setArguments(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.TextureMapFragment::setUserVisibleHint", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.TextureMapFragment __this__ = (com.amap.api.maps.TextureMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.TextureMapFragment@" + __this__ + "::setUserVisibleHint(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setUserVisibleHint(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowAnimation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.animation.Animation.AnimationListener var2 = (com.amap.api.maps.model.animation.Animation.AnimationListener) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowAnimation(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowAnimation(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowAppearAnimation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowAppearAnimation(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowAppearAnimation(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowBackColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowBackColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowBackColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowBackEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowBackEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowBackEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowBackScale", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowBackScale(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowBackScale(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowDisappearAnimation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowDisappearAnimation(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowDisappearAnimation(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::setInfoWindowMovingAnimation", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation var1 = (com.amap.api.maps.model.animation.Animation) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::setInfoWindowMovingAnimation(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowMovingAnimation(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowAnimationManager::startAnimation", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.InfoWindowAnimationManager __this__ = (com.amap.api.maps.InfoWindowAnimationManager) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowAnimationManager@" + __this__ + "::startAnimation(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.startAnimation();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::getMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::getMap(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMap __result__ = null;
                try {
                    __result__ = __this__.getMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onCreate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onCreate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onCreate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onResume", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onResume(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onResume();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onPause", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onPause(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onPause();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onDestroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onDestroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onLowMemory", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onLowMemory(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onLowMemory();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onSaveInstanceState", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onSaveInstanceState(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onSaveInstanceState(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::setVisibility", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::setVisibility(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisibility(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::setOnDismissCallbackListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.WearMapView.OnDismissCallback var1 = (com.amap.api.maps.WearMapView.OnDismissCallback) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::setOnDismissCallbackListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnDismissCallbackListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onDismiss", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onDismiss(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDismiss();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onEnterAmbient", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onEnterAmbient(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onEnterAmbient(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.WearMapView::onExitAmbient", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.WearMapView __this__ = (com.amap.api.maps.WearMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.WearMapView@" + __this__ + "::onExitAmbient(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onExitAmbient();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.BaseMapView::loadWorldVectorMap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.BaseMapView __this__ = (com.amap.api.maps.BaseMapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.BaseMapView@" + __this__ + "::loadWorldVectorMap(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.loadWorldVectorMap(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getCameraPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getCameraPosition(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CameraPosition __result__ = null;
                try {
                    __result__ = __this__.getCameraPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMaxZoomLevel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMaxZoomLevel(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getMaxZoomLevel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMinZoomLevel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMinZoomLevel(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getMinZoomLevel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::moveCamera", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.CameraUpdate var1 = (com.amap.api.maps.CameraUpdate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::moveCamera(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.moveCamera(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::animateCamera__com_amap_api_maps_CameraUpdate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.CameraUpdate var1 = (com.amap.api.maps.CameraUpdate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::animateCamera(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.animateCamera(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::animateCamera__com_amap_api_maps_CameraUpdate__com_amap_api_maps_AMap_CancelableCallback", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.CameraUpdate var1 = (com.amap.api.maps.CameraUpdate) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.AMap.CancelableCallback var2 = (com.amap.api.maps.AMap.CancelableCallback) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::animateCamera(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.animateCamera(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::animateCamera__com_amap_api_maps_CameraUpdate__int__com_amap_api_maps_AMap_CancelableCallback", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.CameraUpdate var1 = (com.amap.api.maps.CameraUpdate) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                com.amap.api.maps.AMap.CancelableCallback var4 = (com.amap.api.maps.AMap.CancelableCallback) ((Map<String, Object>) __args__).get("var4");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::animateCamera(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.animateCamera(var1, var2.longValue(), var4);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::stopAnimation", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::stopAnimation(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.stopAnimation();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addNavigateArrow", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.NavigateArrowOptions var1 = (com.amap.api.maps.model.NavigateArrowOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addNavigateArrow(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.NavigateArrow __result__ = null;
                try {
                    __result__ = __this__.addNavigateArrow(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addPolyline", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.PolylineOptions var1 = (com.amap.api.maps.model.PolylineOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addPolyline(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Polyline __result__ = null;
                try {
                    __result__ = __this__.addPolyline(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addCircle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CircleOptions var1 = (com.amap.api.maps.model.CircleOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addCircle(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Circle __result__ = null;
                try {
                    __result__ = __this__.addCircle(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addArc", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.ArcOptions var1 = (com.amap.api.maps.model.ArcOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addArc(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Arc __result__ = null;
                try {
                    __result__ = __this__.addArc(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addPolygon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.PolygonOptions var1 = (com.amap.api.maps.model.PolygonOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addPolygon(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Polygon __result__ = null;
                try {
                    __result__ = __this__.addPolygon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addGroundOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.GroundOverlayOptions var1 = (com.amap.api.maps.model.GroundOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addGroundOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlay __result__ = null;
                try {
                    __result__ = __this__.addGroundOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addMarker", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MarkerOptions var1 = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addMarker(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Marker __result__ = null;
                try {
                    __result__ = __this__.addMarker(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addGL3DModel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.GL3DModelOptions var1 = (com.amap.api.maps.model.GL3DModelOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addGL3DModel(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GL3DModel __result__ = null;
                try {
                    __result__ = __this__.addGL3DModel(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addText", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.TextOptions var1 = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addText(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.Text __result__ = null;
                try {
                    __result__ = __this__.addText(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addMarkers", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.ArrayList<com.amap.api.maps.model.MarkerOptions> var1 = (java.util.ArrayList<com.amap.api.maps.model.MarkerOptions>) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                boolean var2 = (boolean) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addMarkers(" + var1 + var2 + ")");
                }
            
                // invoke native method
                java.util.ArrayList<com.amap.api.maps.model.Marker> __result__ = null;
                try {
                    __result__ = __this__.addMarkers(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapScreenMarkers", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapScreenMarkers(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.Marker> __result__ = null;
                try {
                    __result__ = __this__.getMapScreenMarkers();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addTileOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.TileOverlayOptions var1 = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addTileOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlay __result__ = null;
                try {
                    __result__ = __this__.addTileOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addMVTTileOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MVTTileOverlayOptions var1 = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addMVTTileOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MVTTileOverlay __result__ = null;
                try {
                    __result__ = __this__.addMVTTileOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addHeatMapLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.HeatMapLayerOptions var1 = (com.amap.api.maps.model.HeatMapLayerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addHeatMapLayer(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapLayer __result__ = null;
                try {
                    __result__ = __this__.addHeatMapLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addHeatMapGridLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.HeatMapGridLayerOptions var1 = (com.amap.api.maps.model.HeatMapGridLayerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addHeatMapGridLayer(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapGridLayer __result__ = null;
                try {
                    __result__ = __this__.addHeatMapGridLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addMultiPointOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MultiPointOverlayOptions var1 = (com.amap.api.maps.model.MultiPointOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addMultiPointOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MultiPointOverlay __result__ = null;
                try {
                    __result__ = __this__.addMultiPointOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addParticleOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleOverlayOptions var1 = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addParticleOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlay __result__ = null;
                try {
                    __result__ = __this__.addParticleOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::clear", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::clear(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.clear();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::clear__bool", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::clear(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.clear(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapType(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getMapType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMapType(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMapType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::isTrafficEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::isTrafficEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isTrafficEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setTrafficEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setTrafficEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTrafficEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::showMapText", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::showMapText(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.showMapText(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::showIndoorMap", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::showIndoorMap(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.showIndoorMap(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::showBuildings", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::showBuildings(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.showBuildings(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyTrafficStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MyTrafficStyle var1 = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMyTrafficStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyTrafficStyle(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyTrafficStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMyTrafficStyle(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyTrafficStyle __result__ = null;
                try {
                    __result__ = __this__.getMyTrafficStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setTrafficStyleWithTextureData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setTrafficStyleWithTextureData(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTrafficStyleWithTextureData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::isMyLocationEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::isMyLocationEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isMyLocationEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMyLocationEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyLocationEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyLocation", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMyLocation(" + "" + ")");
                }
            
                // invoke native method
                android.location.Location __result__ = null;
                try {
                    __result__ = __this__.getMyLocation();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setLocationSource", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.LocationSource var1 = (com.amap.api.maps.LocationSource) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setLocationSource(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLocationSource(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MyLocationStyle var1 = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMyLocationStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyLocationStyle(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyLocationStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMyLocationStyle(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MyLocationStyle __result__ = null;
                try {
                    __result__ = __this__.getMyLocationStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMyLocationType(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyLocationType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationRotateAngle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMyLocationRotateAngle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyLocationRotateAngle(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getUiSettings", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getUiSettings(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.UiSettings __result__ = null;
                try {
                    __result__ = __this__.getUiSettings();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getProjection", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getProjection(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.Projection __result__ = null;
                try {
                    __result__ = __this__.getProjection();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnCameraChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnCameraChangeListener var1 = (com.amap.api.maps.AMap.OnCameraChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnCameraChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnCameraChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMapClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapClickListener var1 = (com.amap.api.maps.AMap.OnMapClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMapClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMapClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMapTouchListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapTouchListener var1 = (com.amap.api.maps.AMap.OnMapTouchListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMapTouchListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMapTouchListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnPOIClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPOIClickListener var1 = (com.amap.api.maps.AMap.OnPOIClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnPOIClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnPOIClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMyLocationChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMyLocationChangeListener var1 = (com.amap.api.maps.AMap.OnMyLocationChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMyLocationChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMyLocationChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMapLongClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLongClickListener var1 = (com.amap.api.maps.AMap.OnMapLongClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMapLongClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMapLongClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMarkerClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerClickListener var1 = (com.amap.api.maps.AMap.OnMarkerClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMarkerClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMarkerClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnPolylineClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPolylineClickListener var1 = (com.amap.api.maps.AMap.OnPolylineClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnPolylineClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnPolylineClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMarkerDragListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerDragListener var1 = (com.amap.api.maps.AMap.OnMarkerDragListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMarkerDragListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMarkerDragListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnInfoWindowClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnInfoWindowClickListener var1 = (com.amap.api.maps.AMap.OnInfoWindowClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnInfoWindowClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnInfoWindowClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setInfoWindowAdapter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.InfoWindowAdapter var1 = (com.amap.api.maps.AMap.InfoWindowAdapter) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setInfoWindowAdapter(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowAdapter(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCommonInfoWindowAdapter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.CommonInfoWindowAdapter var1 = (com.amap.api.maps.AMap.CommonInfoWindowAdapter) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCommonInfoWindowAdapter(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCommonInfoWindowAdapter(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMapLoadedListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLoadedListener var1 = (com.amap.api.maps.AMap.OnMapLoadedListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMapLoadedListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMapLoadedListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnIndoorBuildingActiveListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnIndoorBuildingActiveListener var1 = (com.amap.api.maps.AMap.OnIndoorBuildingActiveListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnIndoorBuildingActiveListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnIndoorBuildingActiveListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setOnMultiPointClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMultiPointClickListener var1 = (com.amap.api.maps.AMap.OnMultiPointClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setOnMultiPointClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnMultiPointClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapPrintScreen", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.onMapPrintScreenListener var1 = (com.amap.api.maps.AMap.onMapPrintScreenListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapPrintScreen(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.getMapPrintScreen(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapScreenShot", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapScreenShotListener var1 = (com.amap.api.maps.AMap.OnMapScreenShotListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapScreenShot(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.getMapScreenShot(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getScalePerPixel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getScalePerPixel(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getScalePerPixel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::runOnDrawFrame", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::runOnDrawFrame(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.runOnDrawFrame();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removecache", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removecache(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removecache();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removecache__com_amap_api_maps_AMap_OnCacheRemoveListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnCacheRemoveListener var1 = (com.amap.api.maps.AMap.OnCacheRemoveListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removecache(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removecache(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomRenderer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.CustomRenderer var1 = (com.amap.api.maps.CustomRenderer) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCustomRenderer(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomRenderer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setPointToCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setPointToCenter(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPointToCenter(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapTextZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMapTextZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMapTextZIndex(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setLoadOfflineData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setLoadOfflineData(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLoadOfflineData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapTextZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapTextZIndex(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getMapTextZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getVersion", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap::getVersion(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = com.amap.api.maps.AMap.getVersion();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::reloadMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::reloadMap(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.reloadMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setRenderFps", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setRenderFps(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRenderFps(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setIndoorBuildingInfo", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.IndoorBuildingInfo var1 = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setIndoorBuildingInfo(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIndoorBuildingInfo(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setAMapGestureListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.AMapGestureListener var1 = (com.amap.api.maps.model.AMapGestureListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setAMapGestureListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAMapGestureListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getZoomToSpanLevel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                com.amap.api.maps.model.LatLng var2 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getZoomToSpanLevel(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZoomToSpanLevel(var1, var2);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getInfoWindowAnimationManager", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getInfoWindowAnimationManager(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.InfoWindowAnimationManager __result__ = null;
                try {
                    __result__ = __this__.getInfoWindowAnimationManager();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMaskLayerParams", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                // ref arg
                Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                // ref arg
                Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMaskLayerParams(" + var1 + var2 + var3 + var4 + var5 + var6 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMaskLayerParams(var1.intValue(), var2.intValue(), var3.intValue(), var4.intValue(), var5.intValue(), var6.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMaxZoomLevel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMaxZoomLevel(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMaxZoomLevel(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMinZoomLevel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMinZoomLevel(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMinZoomLevel(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::resetMinMaxZoomPreference", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::resetMinMaxZoomPreference(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.resetMinMaxZoomPreference();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapStatusLimits", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMapStatusLimits(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMapStatusLimits(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addCrossOverlay", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CrossOverlayOptions var1 = (com.amap.api.maps.model.CrossOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addCrossOverlay(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CrossOverlay __result__ = null;
                try {
                    __result__ = __this__.addCrossOverlay(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addRouteOverlay", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addRouteOverlay(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.RouteOverlay __result__ = null;
                try {
                    __result__ = __this__.addRouteOverlay();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getViewMatrix", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getViewMatrix(" + "" + ")");
                }
            
                // invoke native method
                float[] __result__ = null;
                try {
                    __result__ = __this__.getViewMatrix();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getProjectionMatrix", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getProjectionMatrix(" + "" + ")");
                }
            
                // invoke native method
                float[] __result__ = null;
                try {
                    __result__ = __this__.getProjectionMatrix();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapCustomEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMapCustomEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMapCustomEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStylePath", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCustomMapStylePath(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomMapStylePath(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CustomMapStyleOptions var1 = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCustomMapStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomMapStyle(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStyleID", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCustomMapStyleID(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomMapStyleID(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomTextureResourcePath", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setCustomTextureResourcePath(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomTextureResourcePath(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setRenderMode", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setRenderMode(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRenderMode(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapContentApprovalNumber", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getMapContentApprovalNumber(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getMapContentApprovalNumber();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getSatelliteImageApprovalNumber", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getSatelliteImageApprovalNumber(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getSatelliteImageApprovalNumber();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getTerrainApprovalNumber", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getTerrainApprovalNumber(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getTerrainApprovalNumber();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapLanguage", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setMapLanguage(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMapLanguage(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setRoadArrowEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setRoadArrowEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRoadArrowEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setNaviLabelEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setNaviLabelEnable(" + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setNaviLabelEnable(var1, var2.intValue(), var3.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setTouchPoiEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setTouchPoiEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTouchPoiEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::isTouchPoiEnable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::isTouchPoiEnable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isTouchPoiEnable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getNativeMapController", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getNativeMapController(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getNativeMapController();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getNativeMapEngineID", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getNativeMapEngineID(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getNativeMapEngineID();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnCameraChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnCameraChangeListener var1 = (com.amap.api.maps.AMap.OnCameraChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnCameraChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnCameraChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMapClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapClickListener var1 = (com.amap.api.maps.AMap.OnMapClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMapClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMapClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMarkerDragListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerDragListener var1 = (com.amap.api.maps.AMap.OnMarkerDragListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMarkerDragListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMarkerDragListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMapLoadedListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLoadedListener var1 = (com.amap.api.maps.AMap.OnMapLoadedListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMapLoadedListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMapLoadedListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMapTouchListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapTouchListener var1 = (com.amap.api.maps.AMap.OnMapTouchListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMapTouchListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMapTouchListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMarkerClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerClickListener var1 = (com.amap.api.maps.AMap.OnMarkerClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMarkerClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMarkerClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnPolylineClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPolylineClickListener var1 = (com.amap.api.maps.AMap.OnPolylineClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnPolylineClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnPolylineClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnPOIClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPOIClickListener var1 = (com.amap.api.maps.AMap.OnPOIClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnPOIClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnPOIClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMapLongClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLongClickListener var1 = (com.amap.api.maps.AMap.OnMapLongClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMapLongClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMapLongClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnInfoWindowClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnInfoWindowClickListener var1 = (com.amap.api.maps.AMap.OnInfoWindowClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnInfoWindowClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnInfoWindowClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnIndoorBuildingActiveListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnIndoorBuildingActiveListener var1 = (com.amap.api.maps.AMap.OnIndoorBuildingActiveListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnIndoorBuildingActiveListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnIndoorBuildingActiveListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::addOnMyLocationChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMyLocationChangeListener var1 = (com.amap.api.maps.AMap.OnMyLocationChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::addOnMyLocationChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.addOnMyLocationChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnCameraChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnCameraChangeListener var1 = (com.amap.api.maps.AMap.OnCameraChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnCameraChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnCameraChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMapClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapClickListener var1 = (com.amap.api.maps.AMap.OnMapClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMapClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMapClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMarkerDragListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerDragListener var1 = (com.amap.api.maps.AMap.OnMarkerDragListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMarkerDragListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMarkerDragListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMapLoadedListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLoadedListener var1 = (com.amap.api.maps.AMap.OnMapLoadedListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMapLoadedListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMapLoadedListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMapTouchListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapTouchListener var1 = (com.amap.api.maps.AMap.OnMapTouchListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMapTouchListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMapTouchListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMarkerClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMarkerClickListener var1 = (com.amap.api.maps.AMap.OnMarkerClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMarkerClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMarkerClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnPolylineClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPolylineClickListener var1 = (com.amap.api.maps.AMap.OnPolylineClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnPolylineClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnPolylineClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnPOIClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnPOIClickListener var1 = (com.amap.api.maps.AMap.OnPOIClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnPOIClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnPOIClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMapLongClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMapLongClickListener var1 = (com.amap.api.maps.AMap.OnMapLongClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMapLongClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMapLongClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnInfoWindowClickListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnInfoWindowClickListener var1 = (com.amap.api.maps.AMap.OnInfoWindowClickListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnInfoWindowClickListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnInfoWindowClickListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
        }};
    }
}
