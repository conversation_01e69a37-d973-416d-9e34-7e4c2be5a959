import 'package:collection/collection.dart';

import 'available_coupons_data.dart';

class B2bAppAvailableCoupons {
  int? code;
  AvailableCouponsData? data;
  String? msg;

  B2bAppAvailableCoupons({this.code, this.data, this.msg});

  factory B2bAppAvailableCoupons.fromJson(Map<String, dynamic> json) {
    return B2bAppAvailableCoupons(
      code: json['code'] as int?,
      data: json['data'] == null
          ? null
          : AvailableCouponsData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
        'msg': msg,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! B2bAppAvailableCoupons) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => code.hashCode ^ data.hashCode ^ msg.hashCode;
}
