/*
 * @Author: 潘腾龙
 * @Date: 2023-09-17 15:01:04
 * @LastEditTime: 2024-04-03 21:33:09
 */

import 'dart:io';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/pages/boss_reportforms/home/<USER>';
import 'package:fuduoduo/pages/logistics_distribution/logistics_home/view.dart';
import 'package:fuduoduo/pages/my/view.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/bt_print_utils.dart';
import 'package:fuduoduo/utils/upgrade_utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:get/get.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import '../../common/apis.dart';
import '../../common/dio_utils.dart';
import '../../domain/dio_default_result_bean.dart';
import '../homePages/map_help/mapFluttify.dart';
import '../inventory/view.dart';
import 'home/page_view/view.dart';
import 'home/view.dart' as Home;

import 'package:fuduoduo/domain/common_tentant_bean.dart';
// import 'package:fuduoduo/common/config.dart';
import 'package:fuduoduo/utils/string_utils.dart';

class TabPage extends StatefulWidget {
  @override
  _TabPageState createState() => _TabPageState();
}

class _TabPageState extends State<TabPage> {
  bool isLastestApp = true;
  int _selectedIndex = 0;
  final publicLogic = Get.put(Public());
// 公共
  //登陆页提示token失效问题解决

  @override
  void initState() {
    super.initState();
    debugPrint('测试一下退出登录后，再次登录（不杀掉程序），会不会执行到这里');
    print('======================$_selectedIndex=============');
    publicLogic.getUserInfo();
    //高德地图授权
    AmapService.instance.updatePrivacyAgree(true);
    AmapService.instance.updatePrivacyShow(true);
    AmapService.instance.init(androidKey: mapAndroidKey, iosKey: mapIOSKey);

    // 初始化Bugly
    if (Apis.baseUrl == 'https://fdd.tongfuyouxuan.com/') {
      String appChannel =
          String.fromEnvironment('APP_CHANNEL', defaultValue: 'Pgyer');
      // 初始化友盟
      UmengCommonSdk.initCommon(
          '6572897795b14f599df9df02', '657287ba95b14f599df9dcff', appChannel);
      UmengCommonSdk.setPageCollectionModeManual();

      FlutterBugly.init(
        androidAppId: "5193229451",
        iOSAppId: "ba8d31d8b4",
        // customUpgrade: false, // 调用 Android 原生升级方式
      ).then((_result) {
        print('================bugly初始化结果正式环境${_result.isSuccess}');
      });
    } else {
      FlutterBugly.init(
        androidAppId: "90fd7287eb",
        iOSAppId: "e87694d97d",
        // customUpgrade: false, // 调用 Android 原生升级方式
      ).then((_result) {
        print('================bugly初始化结果测试环境${_result.isSuccess}');
      });
    }

    /// 连接蓝牙
    if (Platform.isAndroid) {
      String btName = SpUtil.getString("bt_name") ?? "";
      String btAddress = SpUtil.getString("bt_address") ?? "";
      if (btName.isNotEmpty && btAddress.isNotEmpty) {
        BluetoothInfo info = BluetoothInfo(name: btName, macAdress: btAddress);
        BtPrintUtils.instance.connectPrinter(info);
      }
    }
    _getUserInfo();

    getTentantData();

    // ColorResource().setB2BColor();

    // 延迟 1 秒后执行
    Future.delayed(Duration(seconds: 1), () {
      _getAppVersion();
    });

    ColorResource().setErpColor();
  }

  // 获取应用版本信息
  Future<void> _getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String lastest_version = SpUtil.getString('lastest_version') ?? '';
    String _version = packageInfo.version;
    setState(() {
      isLastestApp = lastest_version == _version;
    });
  }

  getTentantData() {
    Map<String, dynamic>? queryParameters = {};
    queryParameters["applicationCode"] = StringUtil.getApplicationCode();
    // queryParameters["applicationCode"] = AppConfig.applicationCode;

    MyDio.get(
      Apis.getTenant,
      queryParameters: queryParameters,
      successCallBack: (value) {
        var response = CommonTentantBean.fromJson(value);
        if (value['code'] == '0') {
          SpUtil.putObject('currentTenant', value['data']['currentTenant']);
          var tenants = value['data'];
          SpUtil.putObject('tenants', tenants);
        }
      },
      failCallBack: (value) {
        if (value != null) {
        }
      },
    );
  }




  _getUserInfo() {
    MyDio.get(Apis.getAppUserInfo, queryParameters: {},
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic data = response.data ?? {};
        print('获取业务员编号');
        if (data is Map && data.isNotEmpty) {
          SpUtil.putString('employeeNo', data['employee']['employeeNo']);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 创建tabbarItem
    Widget _buildTabBarItem(String title, IconData iconData, int index) {
      return Expanded(
          child: InkWell(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        onTap: () {
          setState(() {
            _selectedIndex = index;
            if (index == 0) {
              getUpgradeInfo();
            }
          });
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Visibility(
                    visible: title == '我的' && isLastestApp != true,
                    child: Positioned(
                      left: 25,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: ColorResource.RED_COMMON_COLOR,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    )
                ),
                Container(
                  width: 35,
                  height: 24,
                  child: Icon(
                    iconData,
                    color: _selectedIndex == index
                        ? ColorResource.RED_COMMON_COLOR
                        : ColorResource.GRAY_COMMON_COLOR,
                  ),
                )
              ],
            ),

            Text(
              '$title',
              style: TextStyle(
                fontSize: 24.sp,
                color: _selectedIndex == index
                    ? ColorResource.RED_COMMON_COLOR
                    : ColorResource.GRAY_COMMON_COLOR,
              ),
            ),
          ],
        ),
      ));
    }

    // Widget tabView1 = Home.Page();
    Widget tabView1 = Page_viewPage(); //重构后的首页
    Widget tabView2 = BossReportFormPage();
    Widget tabView3 = InventoryPage();
    Widget tabView4 = MyPage();
    Widget tabView5 = LogisticsHomePage();

    // 页面列表
    List<Widget> tabbarItems = [];
    List<Widget> pageItems = [];
    if (UserAppModuleUtils().moduleCanShow('homepage')) {
      Widget tabItem1 = _buildTabBarItem('首页', Icons.home, 0);
      tabbarItems.add(tabItem1);
      pageItems.add(tabView1);
    }
    if (UserAppModuleUtils().moduleCanShow('boss_report')) {
      Widget tabItem2 =
          _buildTabBarItem('老板报表', Icons.dynamic_form, tabbarItems.length);
      tabbarItems.add(tabItem2);
      pageItems.add(tabView2);
    }
    if (UserAppModuleUtils().moduleCanShow('warehouse')) {
      Widget tabItem3 = _buildTabBarItem(
          '库存', Icons.account_balance_outlined, tabbarItems.length);
      tabbarItems.add(tabItem3);
      pageItems.add(tabView3);
    }
    if (UserAppModuleUtils().moduleCanShow('logistics_distribution')) {
      Widget tabItem5 =
          _buildTabBarItem('物流配送', Icons.car_rental, tabbarItems.length);
      tabbarItems.add(tabItem5);
      pageItems.add(tabView5);
    }

    Widget tabItem4 = _buildTabBarItem('我的', Icons.person, tabbarItems.length);
    tabbarItems.add(tabItem4);
    pageItems.add(tabView4);

    return Scaffold(
      backgroundColor: Colors.white,
      body: (pageItems.length) > _selectedIndex
          ? pageItems[_selectedIndex]
          : pageItems[0],
      bottomNavigationBar: BottomAppBar(
        surfaceTintColor: Colors.white,
        color: Colors.white,
        height: 135.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: tabbarItems,
        ),
      ),
    );
  }
}
