import 'package:collection/collection.dart';

class AvailableCouponsList {
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? couponTemplateId;
  String? sysCode;
  String? couponId;
  dynamic supplierId;
  String? couponName;
  String? state;
  num? funcScope;
  num? status;
  num? activityStatus;
  String? templateStartDate;
  String? templateEndDate;
  num? spuScope;
  num? discounttype;
  dynamic spuScopeApplyIds;
  num? receiveScope;
  dynamic receiveScopeApplyIds;
  num? discountType;
  num? triggerAmt;
  num? discountAmt;
  dynamic discountPercent;
  dynamic discountLimitAmt;
  num? expirationType;
  String? expirationDateStart;
  String? expirationDateEnd;
  dynamic disableDays;
  dynamic expireDays;
  num? receiveType;
  num? costFlag;
  num? couponQty;
  num? limit;
  dynamic maxLimit;
  dynamic receiveCount;
  dynamic useCount;
  String? excludable;
  String? repeatFlag;
  dynamic repeatPid;
  dynamic repeatSeq;
  dynamic repeatRule;
  dynamic supplierName;

  AvailableCouponsList({
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.couponTemplateId,
    this.sysCode,
    this.couponId,
    this.supplierId,
    this.couponName,
    this.state,
    this.funcScope,
    this.status,
    this.activityStatus,
    this.templateStartDate,
    this.templateEndDate,
    this.spuScope,
    this.discounttype,
    this.spuScopeApplyIds,
    this.receiveScope,
    this.receiveScopeApplyIds,
    this.discountType,
    this.triggerAmt,
    this.discountAmt,
    this.discountPercent,
    this.discountLimitAmt,
    this.expirationType,
    this.expirationDateStart,
    this.expirationDateEnd,
    this.disableDays,
    this.expireDays,
    this.receiveType,
    this.costFlag,
    this.couponQty,
    this.limit,
    this.maxLimit,
    this.receiveCount,
    this.useCount,
    this.excludable,
    this.repeatFlag,
    this.repeatPid,
    this.repeatSeq,
    this.repeatRule,
    this.supplierName,
  });

  factory AvailableCouponsList.fromJson(Map<String, dynamic> json) {
    return AvailableCouponsList(
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      couponTemplateId: json['couponTemplateId'] as String?,
      sysCode: json['sysCode'] as String?,
      couponId: json['couponId'] as String?,
      supplierId: json['supplierId'] as dynamic,
      couponName: json['couponName'] as String?,
      state: json['state'] as String?,
      funcScope: json['funcScope'] as num?,
      status: json['status'] as num?,
      activityStatus: json['activityStatus'] as num?,
      templateStartDate: json['templateStartDate'] as String?,
      templateEndDate: json['templateEndDate'] as String?,
      spuScope: json['spuScope'] as num?,
      discounttype: json['discounttype'] as num?,
      spuScopeApplyIds: json['spuScopeApplyIds'] as dynamic,
      receiveScope: json['receiveScope'] as num?,
      receiveScopeApplyIds: json['receiveScopeApplyIds'] as dynamic,
      discountType: json['discountType'] as num?,
      triggerAmt: json['triggerAmt'] as num?,
      discountAmt: json['discountAmt'] as num?,
      discountPercent: json['discountPercent'] as dynamic,
      discountLimitAmt: json['discountLimitAmt'] as dynamic,
      expirationType: json['expirationType'] as num?,
      expirationDateStart: json['expirationDateStart'] as String?,
      expirationDateEnd: json['expirationDateEnd'] as String?,
      disableDays: json['disableDays'] as dynamic,
      expireDays: json['expireDays'] as dynamic,
      receiveType: json['receiveType'] as num?,
      costFlag: json['costFlag'] as num?,
      couponQty: json['couponQty'] as num?,
      limit: json['limit'] as num?,
      maxLimit: json['maxLimit'] as dynamic,
      receiveCount: json['receiveCount'] as dynamic,
      useCount: json['useCount'] as dynamic,
      excludable: json['excludable'] as String?,
      repeatFlag: json['repeatFlag'] as String?,
      repeatPid: json['repeatPid'] as dynamic,
      repeatSeq: json['repeatSeq'] as dynamic,
      repeatRule: json['repeatRule'] as dynamic,
      supplierName: json['supplierName'] as dynamic,
    );
  }

  Map<String, dynamic> toJson() => {
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'couponTemplateId': couponTemplateId,
        'sysCode': sysCode,
        'couponId': couponId,
        'supplierId': supplierId,
        'couponName': couponName,
        'state': state,
        'funcScope': funcScope,
        'status': status,
        'activityStatus': activityStatus,
        'templateStartDate': templateStartDate,
        'templateEndDate': templateEndDate,
        'spuScope': spuScope,
        'discounttype': discounttype,
        'spuScopeApplyIds': spuScopeApplyIds,
        'receiveScope': receiveScope,
        'receiveScopeApplyIds': receiveScopeApplyIds,
        'discountType': discountType,
        'triggerAmt': triggerAmt,
        'discountAmt': discountAmt,
        'discountPercent': discountPercent,
        'discountLimitAmt': discountLimitAmt,
        'expirationType': expirationType,
        'expirationDateStart': expirationDateStart,
        'expirationDateEnd': expirationDateEnd,
        'disableDays': disableDays,
        'expireDays': expireDays,
        'receiveType': receiveType,
        'costFlag': costFlag,
        'couponQty': couponQty,
        'limit': limit,
        'maxLimit': maxLimit,
        'receiveCount': receiveCount,
        'useCount': useCount,
        'excludable': excludable,
        'repeatFlag': repeatFlag,
        'repeatPid': repeatPid,
        'repeatSeq': repeatSeq,
        'repeatRule': repeatRule,
        'supplierName': supplierName,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AvailableCouponsList) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      createBy.hashCode ^
      createTime.hashCode ^
      updateBy.hashCode ^
      updateTime.hashCode ^
      couponTemplateId.hashCode ^
      sysCode.hashCode ^
      couponId.hashCode ^
      supplierId.hashCode ^
      couponName.hashCode ^
      state.hashCode ^
      funcScope.hashCode ^
      status.hashCode ^
      activityStatus.hashCode ^
      templateStartDate.hashCode ^
      templateEndDate.hashCode ^
      spuScope.hashCode ^
      discounttype.hashCode ^
      spuScopeApplyIds.hashCode ^
      receiveScope.hashCode ^
      receiveScopeApplyIds.hashCode ^
      discountType.hashCode ^
      triggerAmt.hashCode ^
      discountAmt.hashCode ^
      discountPercent.hashCode ^
      discountLimitAmt.hashCode ^
      expirationType.hashCode ^
      expirationDateStart.hashCode ^
      expirationDateEnd.hashCode ^
      disableDays.hashCode ^
      expireDays.hashCode ^
      receiveType.hashCode ^
      costFlag.hashCode ^
      couponQty.hashCode ^
      limit.hashCode ^
      maxLimit.hashCode ^
      receiveCount.hashCode ^
      useCount.hashCode ^
      excludable.hashCode ^
      repeatFlag.hashCode ^
      repeatPid.hashCode ^
      repeatSeq.hashCode ^
      repeatRule.hashCode ^
      supplierName.hashCode;
}
