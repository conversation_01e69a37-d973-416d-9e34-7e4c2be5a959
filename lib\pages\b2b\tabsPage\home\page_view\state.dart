import 'package:flustars/flustars.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/domain/b2b_user_profile_bean.dart';
class B2bPage_viewState {
  ///仓库选择下标
  RxInt branchSelectIndex = 0.obs;
  ///切换仓库
  RxString selectBranch = ''.obs;

  RxBool showShopScan = false.obs;

  RxBool showActivityView = false.obs;

  RxBool validateFlag = false.obs;
  RxBool cloudFlag = false.obs;

  RxList tempList = [].obs;
  ///首页汇总数据（个人业绩）
  RxMap personalDataInfo = {}.obs;
  ///首页汇总数据（团队业绩）
  RxList teamDataInfo = [].obs;
  /// 今日订单金额（元）
  RxString todayOrderMount = '0.00'.obs;

  /// 任务中心入口显示标志
  RxBool missionFlag = true.obs;
  ///活动列表
  int missionCount = 2;

  ///日期类型（0 个人业绩 1 团队业绩）
  RxString yeJiType = '0'.obs;
  /// 用户信息
  RxMap userInfo = {}.obs;
  ///日期类型（0今日，1，本周，2，本月）
  RxString dateType = '0'.obs;

  ///团队业绩的请求参数pageNo
  int pageNo = 0;
  ///团队业绩的请求参数pageSize
  int pageSize = 10;
  RxInt teamTotal = 0.obs;

  ///个人业绩集合
  RxList individualList = [].obs;

  ///个人绩效统计数据
  RxMap personalPerformanceData = {}.obs;

  List tableTitle = [
    {"title": "排名", "width": 100.w},
    {"title": "姓名"},
    {"title": "销售", "width": 160.w},
    {"title": "拜访", "width": 140.w},
    {"title": "动销", "width": 140.w},
  ];
  B2bPage_viewState() {
    ///Initialize variables
  }
}
