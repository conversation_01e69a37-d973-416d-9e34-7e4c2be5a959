/// 所在地区列表数据模型
class AreaCityBean {
  int? code;
  AreaCityData? data;
  String? msg;

  AreaCityBean({this.code, this.data, this.msg});

  AreaCityBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? AreaCityData.fromJson(json['data']) : null;
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['msg'] = msg;
    return data;
  }
}

class AreaCityData {
  List<AreaCityItem>? list;
  int? total;
  String? rest;

  AreaCityData({this.list, this.total, this.rest});

  AreaCityData.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <AreaCityItem>[];
      json['list'].forEach((v) {
        list!.add(AreaCityItem.fromJson(v));
      });
    }
    total = json['total'];
    rest = json['rest'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    data['rest'] = rest;
    return data;
  }
}

class AreaCityItem {
  String? areaCityId;
  String? pid;
  int? deep;
  String? extId;
  String? name;

  AreaCityItem({
    this.areaCityId,
    this.pid,
    this.deep,
    this.extId,
    this.name,
  });

  AreaCityItem.fromJson(Map<String, dynamic> json) {
    areaCityId = json['areaCityId'];
    pid = json['pid'];
    deep = json['deep'];
    extId = json['extId'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['areaCityId'] = areaCityId;
    data['pid'] = pid;
    data['deep'] = deep;
    data['extId'] = extId;
    data['name'] = name;
    return data;
  }
}
