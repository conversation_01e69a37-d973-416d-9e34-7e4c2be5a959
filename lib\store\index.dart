import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import '../pages/homePages/map_help/mapFluttify.dart';
import 'state.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:flustars/flustars.dart';

import 'dart:io';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/common_other_LoginInfo.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:fuduoduo/utils/storage_common.dart';
import 'package:fuduoduo/domain/b2b_user_profile_bean.dart';
class Public extends GetxController {
  // 数据
  final state = MainState();

  @override
  void onInit() {
    ///初始化应用信息
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  // 切换仓库
  void changebranchInfo(dynamic info) {
    SpUtil.putString("branchNo", info['branchNo']);
    SpUtil.putString("branchType", info['branchType']);
    SpUtil.putString("branchName", info['branchName']);
    state.branchInfo = info;
    state.branchName = info['branchName'] ?? "";
    state.selectedindex = 0;
    update();
  }

  // 获取
  void getLoginInfo () async {
    var userType = await SecureStorageCommon.save('userType').get();

    if (userType == 'b2bSystem') {
      return;
    }

    MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {

      var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

      // 租户ID
      String tenant_id = '${responseOther.tenantId}';
      SpUtil.putString('tenant_id', tenant_id);

      List moduleArray = responseOther.appModules ?? [];
      if (moduleArray.isEmpty) {
        MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
      }
      List<String> moduleList = [];
      for (var element in moduleArray) {
        moduleList.add(element);
      }
      UserAppModuleUtils().saveUserAppModuls(moduleArray);

      SpUtil.putBool("validateFlag", true);
    });
  }

  // 仓库编号
  String get branchNo {
    dynamic branchInfo = state.branchInfo;
    return branchInfo['branchNo'] ?? "";
  }

  // 获取用户信息
  void getUserInfo() async {
    var userType = await SecureStorageCommon.save('userType').get();
    Map<String, dynamic>? queryParameters = {};

    if (userType == 'b2bSystem') {
      // queryParameters["system"] = 'b2bmart';
      MyDio.get(Apis.b2bUserinfo, queryParameters: queryParameters,
          successCallBack: (value) {
            B2bUserProfileBean userProfile = B2bUserProfileBean.fromJson(value);
            state.userInfo = userProfile.data;
            SpUtil.putObject('userInfo', value['data']);
          }
        );


      return;
    }
    MyDio.get(Apis.getAppUserInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      print('======getAppInfo======================$value');
      _checkIsGray();
      getDealerNo();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic data = response.data ?? {};
        state.userInfo = data;
        SpUtil.putObject('userInfo', data);
        // 是否显示但商铺预览
        checkShowShopScan();

        // is_unified_annto_wms 为0 是 美的付在线收款
        String _key = 'is_unified_annto_wms:${data["user"]['employeeOrgCode']}';
        if (data['userOrgSettings']?[_key] != null && !data['userOrgSettings']?[_key].isEmpty) {
          SpUtil.putString('is_unified_annto_wms', data['userOrgSettings'][_key]);
        } else {
          SpUtil.putString('is_unified_annto_wms', '');
        }


        // is_unified_annto_wms 是否对接b2b 1 对接 0 不对接
        String _b2bkey = 'is_unified_b2b:${data["user"]['employeeOrgCode']}';
        if (data['userOrgSettings']?[_b2bkey] != null && !data['userOrgSettings']?[_b2bkey].isEmpty) {
          SpUtil.putString('is_unified_b2b', data['userOrgSettings'][_b2bkey]);
        } else {
          SpUtil.putString('is_unified_b2b', '');
        }


        // 1，允许负库存，0不允许
        // data['userOrgSettings']['is_app_order_check_stock'] = '0';
        // print('====    ${data['userOrgSettings']['is_app_order_check_stock']}   ${data['userOrgSettings']['is_app_order_check_stock'] == '1'}');
        if (data['userOrgSettings']['is_app_order_check_stock'] == '1') {
          SpUtil.putString('is_app_order_check_stock', data['userOrgSettings']['is_app_order_check_stock']);
        } else {
          SpUtil.putString('is_app_order_check_stock', '0');
        }

        // 是否含税开单
        
        // data['userOrgSettings']['is_tax_included'] = '0';

        if (data['userOrgSettings']['is_tax_included'] == '1') {
          SpUtil.putString('is_tax_included', data['userOrgSettings']['is_tax_included']);
        } else {
          SpUtil.putString('is_tax_included', '0');
        }

        // 是否启用商品税
        // data['userOrgSettings']['item_tax_rate_show'] = '0';
        if (data['userOrgSettings']['item_tax_rate_show'] == '1') {
          SpUtil.putString('item_tax_rate_show', data['userOrgSettings']['item_tax_rate_show']);
        } else {
          SpUtil.putString('item_tax_rate_show', '0');
        }
        
        // 默认税率
        if (data['userOrgSettings']['default_tax_rate'].isNotEmpty) {
          SpUtil.putString('default_tax_rate', data['userOrgSettings']['default_tax_rate']);
        } else {
          SpUtil.putString('default_tax_rate', '0');
        }

        
        // 关闭拆单
        // state.userInfo['userOrgSettings']['is_unified_warehouse_allocation'] = '0';
        //是否隐藏批次号
        if (null != data['userOrgSettings']) {
          String is_date_manager =
              data['userOrgSettings']?['is_producte_date_manager'] ?? '';
          SpUtil.putString('visibleDate', is_date_manager);
          // 租户ID
          String tenant_id = data['enterprise']?['id'] ?? '';
          SpUtil.putString('tenant_id', tenant_id);
        }
        List branches = data['branches'] ?? [];
        // branches.removeAt(1);
        // branches.removeAt(1);
        // branches.removeAt(0);
        if (branches.length > 1) {
          state.strSwitch = "切换仓库";
        } else {
          state.strSwitch = "";
        }
        //  多仓库 并且没有缓存仓库信息
        print('branchNo==============  $branchNo');
        if (branches.length > 0 && branchNo == '') {
          changebranchInfo(branches[0]);
        } else {
          // SpUtil.putString("branchName", "");
          // state.strSwitch = "";
          // SpUtil.putString("branchNo", "");
        }
        update();
      } else {
        state.userInfo = {};
        // MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  // 获取经销商编号
  void getDealerNo() {
    MyDio.get(
      Apis.searchDealerNo,
      successCallBack: (value) {
        Map result = Map.from(value);
        if (result['code'].toString() == '200') {
          String dealerNo = result['data']['branchNo'].toString();
          SpUtil.putString('dealerNo', dealerNo);
        }
      },
    );
  }

  void checkShowShopScan() {
    dynamic userInfo = SpUtil.getObject('userInfo');
    dynamic employee = userInfo['employee'] ?? {};
    MyDio.get(Apis.checkShowShopScan,
        queryParameters: {'employeeNo': employee['employeeNo']},
        successCallBack: (value) {
      DioResultBean response = DioResultBean.fromJson(value);
      if (response.code.toString() == '200') {
        String data = response.data.toString();
        SpUtil.putString('showShopScan', data);
      }
    },showErrMsg: false);
  }

// 校验是否是灰度用户
  void _checkIsGray() {
    // MyDio.get(
    //   Apis.checkGray,
    //   successCallBack: (value) {
    //     print('============灰度：$value');
    //   },
    //   failCallBack: () {},
    // );
  }

  void getPoint() async {
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    await AmapLocation.instance.init(iosKey: mapIOSKey);
    dynamic _location =
        await AmapLocation.instance.fetchLocation(mode: LocationAccuracy.High);
    print(_location);
    // await Future.delayed(const Duration(milliseconds: 200));
    // await _controller?.setCenterCoordinate(_location.latLng!);
    // if (_location.latLng != null) {
    //   _latLng = _location.latLng!;
    //   _mapAddMarker(_latLng!);
    //   if (mounted) setState(() {});
    // }
  }

  void requestPermission() async {
    // 申请权限
    if (Platform.isAndroid) {
      if (await Permission.location.request().isGranted) {
        await Future.delayed(const Duration(milliseconds: 1000));
        getPoint();
      } else {
        print("定位权限申请不通过");
      }
    } else {
      if (await Permission.location.request().isGranted &&
          await Permission.locationAlways.request().isGranted &&
          await Permission.locationWhenInUse.request().isGranted) {
        getPoint();
      } else {
        print("定位权限申请不通过");
      }
    }
  }

}
