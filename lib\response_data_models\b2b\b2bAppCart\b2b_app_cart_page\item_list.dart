import 'package:collection/collection.dart';

import 'car_id.dart';

class ItemList {
  CarId? carId;
  num? markPrice;
  num? stockQty;
  num? maxQty;
  num? productNum;
  num? recommendNum;
  bool? selected;
  num? recommendFlag;
  String? spuName;
  String? skuThumb;
  String? specName;
  num? skuUnit;
  num? minOq;
  num? jumpOq;
  num? shelfStatus;
  num? payAmt;
  List<dynamic>? activityList;
  List<dynamic>? spuActivityLabelList;
  String? unit;
  num? unitSize;
  String? unitName;
  num? stockConvertRate;
  num? itemType;
  num? minUnit;
  String? addTime;
  bool? spuCombineProduct;

  ItemList({
    this.carId,
    this.markPrice,
    this.stockQty,
    this.maxQty,
    this.productNum,
    this.recommendNum,
    this.selected,
    this.recommendFlag,
    this.spuName,
    this.skuThumb,
    this.specName,
    this.skuUnit,
    this.minOq,
    this.jumpOq,
    this.shelfStatus,
    this.payAmt,
    this.activityList,
    this.spuActivityLabelList,
    this.unit,
    this.unitSize,
    this.unitName,
    this.stockConvertRate,
    this.itemType,
    this.minUnit,
    this.addTime,
    this.spuCombineProduct,
  });

  factory ItemList.fromJson(Map<String, dynamic> json) => ItemList(
        carId: json['carId'] == null
            ? null
            : CarId.fromJson(json['carId'] as Map<String, dynamic>),
        markPrice: json['markPrice'] as num?,
        stockQty: json['stockQty'] as num?,
        maxQty: json['maxQty'] as num?,
        productNum: json['productNum'] as num?,
        recommendNum: json['recommendNum'] as num?,
        selected: json['selected'] as bool?,
        recommendFlag: json['recommendFlag'] as num?,
        spuName: json['spuName'] as String?,
        skuThumb: json['skuThumb'] as String?,
        specName: json['specName'] as String?,
        skuUnit: json['skuUnit'] as num?,
        minOq: json['minOq'] as num?,
        jumpOq: json['jumpOq'] as num?,
        shelfStatus: json['shelfStatus'] as num?,
        payAmt: json['payAmt'] as num?,
        activityList: json['activityList'] as List<dynamic>?,
        spuActivityLabelList: json['spuActivityLabelList'] as List<dynamic>?,
        unit: json['unit'] as String?,
        unitSize: json['unitSize'] as num?,
        unitName: json['unitName'] as String?,
        stockConvertRate: json['stockConvertRate'] as num?,
        itemType: json['itemType'] as num?,
        minUnit: json['minUnit'] as num?,
        addTime: json['addTime'] as String?,
        spuCombineProduct: json['spuCombineProduct'] as bool?,
      );

  Map<String, dynamic> toJson() => {
        'carId': carId?.toJson(),
        'markPrice': markPrice,
        'stockQty': stockQty,
        'maxQty': maxQty,
        'productNum': productNum,
        'recommendNum': recommendNum,
        'selected': selected,
        'recommendFlag': recommendFlag,
        'spuName': spuName,
        'skuThumb': skuThumb,
        'specName': specName,
        'skuUnit': skuUnit,
        'minOq': minOq,
        'jumpOq': jumpOq,
        'shelfStatus': shelfStatus,
        'payAmt': payAmt,
        'activityList': activityList,
        'spuActivityLabelList': spuActivityLabelList,
        'unit': unit,
        'unitSize': unitSize,
        'unitName': unitName,
        'stockConvertRate': stockConvertRate,
        'itemType': itemType,
        'minUnit': minUnit,
        'addTime': addTime,
        'spuCombineProduct': spuCombineProduct,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! ItemList) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      carId.hashCode ^
      markPrice.hashCode ^
      stockQty.hashCode ^
      maxQty.hashCode ^
      productNum.hashCode ^
      recommendNum.hashCode ^
      selected.hashCode ^
      recommendFlag.hashCode ^
      spuName.hashCode ^
      skuThumb.hashCode ^
      specName.hashCode ^
      skuUnit.hashCode ^
      minOq.hashCode ^
      jumpOq.hashCode ^
      shelfStatus.hashCode ^
      payAmt.hashCode ^
      activityList.hashCode ^
      spuActivityLabelList.hashCode ^
      unit.hashCode ^
      unitSize.hashCode ^
      unitName.hashCode ^
      stockConvertRate.hashCode ^
      itemType.hashCode ^
      minUnit.hashCode ^
      addTime.hashCode ^
      spuCombineProduct.hashCode;
}
