//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler1.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler1)
- (NSDictionary<NSString*, Handler>*) getSubHandler1 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAHeatMapGradient::initWithColor_andWithStartPoints_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapGradient::initWithColor_andWithStartPoints(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<UIColor*>* colors = (NSArray<UIColor*>*) args[@"colors"];
                // jsonable arg
                NSArray<NSNumber*>* startPoints = (NSArray<NSNumber*>*) args[@"startPoints"];
        
                // ref
                MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAHeatMapGradient* result = [ref initWithColor: colors andWithStartPoints: startPoints];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapStatus::statusWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapStatus::statusWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // jsonable arg
                CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
                // jsonable arg
                CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
                // struct arg
                NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
                CGPoint screenAnchor;
                if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
                  [screenAnchorValue getValue:&screenAnchor];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"screenAnchor不能为null"]);
                  return;
                }
        
        
                // ref
        
        
                // invoke native method
                MAMapStatus* result = [MAMapStatus statusWithCenterCoordinate: coordinate zoomLevel: zoomLevel rotationDegree: rotationDegree cameraDegree: cameraDegree screenAnchor: screenAnchor];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapStatus::initWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapStatus::initWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // jsonable arg
                CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
                // jsonable arg
                CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
                // struct arg
                NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
                CGPoint screenAnchor;
                if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
                  [screenAnchorValue getValue:&screenAnchor];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"screenAnchor不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSObject* result = [ref initWithCenterCoordinate: coordinate zoomLevel: zoomLevel rotationDegree: rotationDegree cameraDegree: cameraDegree screenAnchor: screenAnchor];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACircle::circleWithCenterCoordinate_radius_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::circleWithCenterCoordinate_radius(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordValue = (NSValue*) args[@"coord"];
                CLLocationCoordinate2D coord;
                if (coordValue != nil && (NSNull*) coordValue != [NSNull null]) {
                  [coordValue getValue:&coord];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coord不能为null"]);
                  return;
                }
        
                // jsonable arg
                CLLocationDistance radius = [args[@"radius"] doubleValue];
        
                // ref
        
        
                // invoke native method
                MACircle* result = [MACircle circleWithCenterCoordinate: coord radius: radius];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACircle::circleWithMapRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::circleWithMapRect(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
        
                // ref
        
        
                // invoke native method
                MACircle* result = [MACircle circleWithMapRect: mapRect];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACircle::setCircleWithCenterCoordinate_radius_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::setCircleWithCenterCoordinate_radius(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordValue = (NSValue*) args[@"coord"];
                CLLocationCoordinate2D coord;
                if (coordValue != nil && (NSNull*) coordValue != [NSNull null]) {
                  [coordValue getValue:&coord];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coord不能为null"]);
                  return;
                }
        
                // jsonable arg
                CLLocationDistance radius = [args[@"radius"] doubleValue];
        
                // ref
                MACircle* ref = (MACircle*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setCircleWithCenterCoordinate: coord radius: radius];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAArcRenderer::initWithArc_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAArcRenderer::initWithArc(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAArc* arc = (MAArc*) (args[@"arc"] == [NSNull null] ? nil : args[@"arc"]);
        
                // ref
                MAArcRenderer* ref = (MAArcRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAArcRenderer* result = [ref initWithArc: arc];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotation::setCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotation::setCoordinate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* newCoordinateValue = (NSValue*) args[@"newCoordinate"];
                CLLocationCoordinate2D newCoordinate;
                if (newCoordinateValue != nil && (NSNull*) newCoordinateValue != [NSNull null]) {
                  [newCoordinateValue getValue:&newCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"newCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setCoordinate : newCoordinate];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatableAnnotation::step_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::step(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat timeDelta = [args[@"timeDelta"] floatValue];
        
                // ref
                id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref step : timeDelta];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatableAnnotation::isAnimationFinished_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::isAnimationFinished(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref isAnimationFinished];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatableAnnotation::shouldAnimationStart_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::shouldAnimationStart(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref shouldAnimationStart];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatableAnnotation::rotateDegree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::rotateDegree(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CLLocationDirection result = [ref rotateDegree];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMapViewController::sharedInstance_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMapViewController::sharedInstance(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
        
        
                // invoke native method
                MAOfflineMapViewController* result = [MAOfflineMapViewController sharedInstance];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolygon::polygonWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::polygonWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
        
        
                // invoke native method
                MAPolygon* result = [MAPolygon polygonWithCoordinates: coords count: count];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolygon::polygonWithPoints_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::polygonWithPoints_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
                MAMapPoint points[pointsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                    NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                    MAMapPoint pointsItem;
                    [pointsValue getValue:&pointsItem];
                    points[__i__] = pointsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
        
        
                // invoke native method
                MAPolygon* result = [MAPolygon polygonWithPoints: points count: count];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolygon::setPolygonWithPoints_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::setPolygonWithPoints_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
                MAMapPoint points[pointsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                    NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                    MAMapPoint pointsItem;
                    [pointsValue getValue:&pointsItem];
                    points[__i__] = pointsItem;
                }
                // jsonable arg
                NSInteger count = [args[@"count"] longValue];
        
                // ref
                MAPolygon* ref = (MAPolygon*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setPolygonWithPoints: points count: count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolygon::setPolygonWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::setPolygonWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSInteger count = [args[@"count"] longValue];
        
                // ref
                MAPolygon* ref = (MAPolygon*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setPolygonWithCoordinates: coords count: count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverlay::particleOverlayWithOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlay::particleOverlayWithOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAParticleOverlayOptions* option = (MAParticleOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
        
        
                // invoke native method
                MAParticleOverlay* result = [MAParticleOverlay particleOverlayWithOption: option];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverlay::updateOverlayOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlay::updateOverlayOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAParticleOverlayOptions* overlayOption = (MAParticleOverlayOptions*) (args[@"overlayOption"] == [NSNull null] ? nil : args[@"overlayOption"]);
        
                // ref
                MAParticleOverlay* ref = (MAParticleOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref updateOverlayOption : overlayOption];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolyline::polylineWithPoints_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::polylineWithPoints_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
                MAMapPoint points[pointsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                    NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                    MAMapPoint pointsItem;
                    [pointsValue getValue:&pointsItem];
                    points[__i__] = pointsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
        
        
                // invoke native method
                MAPolyline* result = [MAPolyline polylineWithPoints: points count: count];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolyline::polylineWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::polylineWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
        
        
                // invoke native method
                MAPolyline* result = [MAPolyline polylineWithCoordinates: coords count: count];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolyline::setPolylineWithPoints_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::setPolylineWithPoints_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
                MAMapPoint points[pointsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                    NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                    MAMapPoint pointsItem;
                    [pointsValue getValue:&pointsItem];
                    points[__i__] = pointsItem;
                }
                // jsonable arg
                NSInteger count = [args[@"count"] longValue];
        
                // ref
                MAPolyline* ref = (MAPolyline*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setPolylineWithPoints: points count: count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolyline::setPolylineWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::setPolylineWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSInteger count = [args[@"count"] longValue];
        
                // ref
                MAPolyline* ref = (MAPolyline*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setPolylineWithCoordinates: coords count: count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMultiColoredPolylineRenderer::initWithMultiPolyline_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiColoredPolylineRenderer::initWithMultiPolyline(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMultiPolyline* multiPolyline = (MAMultiPolyline*) (args[@"multiPolyline"] == [NSNull null] ? nil : args[@"multiPolyline"]);
        
                // ref
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMultiColoredPolylineRenderer* result = [ref initWithMultiPolyline: multiPolyline];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatedAnnotation::allMoveAnimations_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::allMoveAnimations(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSArray<MAAnnotationMoveAnimation*>* result = [ref allMoveAnimations];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnimatedAnnotation::setNeedsStart_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::setNeedsStart(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setNeedsStart ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlay::coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlay::coordinate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAOverlay> ref = (id<MAOverlay>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CLLocationCoordinate2D result = [ref coordinate];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlay::boundingMapRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlay::boundingMapRect(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAOverlay> ref = (id<MAOverlay>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMapRect result = [ref boundingMapRect];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMultiTexturePolylineRenderer::initWithMultiPolyline_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiTexturePolylineRenderer::initWithMultiPolyline(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMultiPolyline* multiPolyline = (MAMultiPolyline*) (args[@"multiPolyline"] == [NSNull null] ? nil : args[@"multiPolyline"]);
        
                // ref
                MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMultiTexturePolylineRenderer* result = [ref initWithMultiPolyline: multiPolyline];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAHeatMapVectorGridOverlay::heatMapOverlayWithOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorGridOverlay::heatMapOverlayWithOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorGridOverlayOptions* option = (MAHeatMapVectorGridOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
        
        
                // invoke native method
                MAHeatMapVectorGridOverlay* result = [MAHeatMapVectorGridOverlay heatMapOverlayWithOption: option];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAHeatMapVectorOverlayRender::initWithHeatOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlayRender::initWithHeatOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorOverlay* heatOverlay = (MAHeatMapVectorOverlay*) (args[@"heatOverlay"] == [NSNull null] ? nil : args[@"heatOverlay"]);
        
                // ref
                MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAHeatMapVectorOverlayRender* result = [ref initWithHeatOverlay: heatOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAHeatMapVectorOverlayRender::getHeatMapItem_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlayRender::getHeatMapItem(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAHeatMapVectorItem* result = [ref getHeatMapItem: coordinate];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATileOverlayRenderer::initWithTileOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlayRenderer::initWithTileOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MATileOverlay* tileOverlay = (MATileOverlay*) (args[@"tileOverlay"] == [NSNull null] ? nil : args[@"tileOverlay"]);
        
                // ref
                MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MATileOverlayRenderer* result = [ref initWithTileOverlay: tileOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATileOverlayRenderer::reloadData_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlayRenderer::reloadData(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref reloadData ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAHeatMapVectorGridOverlayRenderer::initWithHeatOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorGridOverlayRenderer::initWithHeatOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorGridOverlay* heatOverlay = (MAHeatMapVectorGridOverlay*) (args[@"heatOverlay"] == [NSNull null] ? nil : args[@"heatOverlay"]);
        
                // ref
                MAHeatMapVectorGridOverlayRenderer* ref = (MAHeatMapVectorGridOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAHeatMapVectorGridOverlayRenderer* result = [ref initWithHeatOverlay: heatOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATraceManager::sharedInstance_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::sharedInstance(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
        
        
                // invoke native method
                MATraceManager* result = [MATraceManager sharedInstance];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATraceManager::start_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::start(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MATraceManager* ref = (MATraceManager*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref start ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATraceManager::stop_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::stop(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MATraceManager* ref = (MATraceManager*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref stop ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMultiPointOverlayRenderer::initWithMultiPointOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiPointOverlayRenderer::initWithMultiPointOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMultiPointOverlay* multiPointOverlay = (MAMultiPointOverlay*) (args[@"multiPointOverlay"] == [NSNull null] ? nil : args[@"multiPointOverlay"]);
        
                // ref
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMultiPointOverlayRenderer* result = [ref initWithMultiPointOverlay: multiPointOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolylineRenderer::initWithPolyline_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolylineRenderer::initWithPolyline(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAPolyline* polyline = (MAPolyline*) (args[@"polyline"] == [NSNull null] ? nil : args[@"polyline"]);
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAPolylineRenderer* result = [ref initWithPolyline: polyline];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::name_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::name(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSString* result = [ref name];
        
                // result
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::coordinates_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::coordinates(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CLLocationCoordinate2D* result = [ref coordinates];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D*)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSUInteger result = [ref count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::duration(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref duration];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::elapsedTime_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::elapsedTime(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref elapsedTime];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::cancel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::cancel(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref cancel ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::isCancelled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::isCancelled(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref isCancelled];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationMoveAnimation::passedPointCount_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::passedPointCount(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSInteger result = [ref passedPointCount];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationView::setSelected_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::setSelected_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL selected = [args[@"selected"] boolValue];
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setSelected : selected animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationView::initWithAnnotation_reuseIdentifier_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::initWithAnnotation_reuseIdentifier(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
                // jsonable arg
                NSString* reuseIdentifier = (NSString*) args[@"reuseIdentifier"];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSObject* result = [ref initWithAnnotation: annotation reuseIdentifier: reuseIdentifier];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationView::prepareForReuse_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::prepareForReuse(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref prepareForReuse ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAAnnotationView::setDragState_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::setDragState_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAAnnotationViewDragState newDragState = (MAAnnotationViewDragState) [args[@"newDragState"] integerValue];
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setDragState : newDragState animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MATileOverlay::initWithURLTemplate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlay::initWithURLTemplate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* URLTemplate = (NSString*) args[@"URLTemplate"];
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSObject* result = [ref initWithURLTemplate: URLTemplate];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomCalloutView::initWithCustomView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomCalloutView::initWithCustomView(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIView* customView = (UIView*) (args[@"customView"] == [NSNull null] ? nil : args[@"customView"]);
        
                // ref
                MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSObject* result = [ref initWithCustomView: customView];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::sharedOfflineMap_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::sharedOfflineMap(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
        
        
                // invoke native method
                MAOfflineMap* result = [MAOfflineMap sharedOfflineMap];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::isDownloadingForItem_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::isDownloadingForItem(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
                // ref
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref isDownloadingForItem: item];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::pauseItem_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::pauseItem(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
                // ref
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref pauseItem: item];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::deleteItem_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::deleteItem(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
                // ref
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref deleteItem : item];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::cancelAll_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::cancelAll(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref cancelAll ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOfflineMap::clearDisk_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::clearDisk(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref clearDisk ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACircleRenderer::initWithCircle_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircleRenderer::initWithCircle(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MACircle* circle = (MACircle*) (args[@"circle"] == [NSNull null] ? nil : args[@"circle"]);
        
                // ref
                MACircleRenderer* ref = (MACircleRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MACircleRenderer* result = [ref initWithCircle: circle];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverlayRenderer::initWithParticleOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlayRenderer::initWithParticleOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAParticleOverlay* particleOverlay = (MAParticleOverlay*) (args[@"particleOverlay"] == [NSNull null] ? nil : args[@"particleOverlay"]);
        
                // ref
                MAParticleOverlayRenderer* ref = (MAParticleOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleOverlayRenderer* result = [ref initWithParticleOverlay: particleOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleVelocityGenerate::getX_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getX(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref getX];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleVelocityGenerate::getY_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getY(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref getY];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleVelocityGenerate::getZ_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getZ(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref getZ];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleRandomVelocityGenerate::initWithBoundaryValueX1_Y1_Z1_X2_Y2_Z2_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRandomVelocityGenerate::initWithBoundaryValueX1_Y1_Z1_X2_Y2_Z2(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float x1 = [args[@"x1"] floatValue];
                // jsonable arg
                float y1 = [args[@"y1"] floatValue];
                // jsonable arg
                float z1 = [args[@"z1"] floatValue];
                // jsonable arg
                float x2 = [args[@"x2"] floatValue];
                // jsonable arg
                float y2 = [args[@"y2"] floatValue];
                // jsonable arg
                float z2 = [args[@"z2"] floatValue];
        
                // ref
                MAParticleRandomVelocityGenerate* ref = (MAParticleRandomVelocityGenerate*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleRandomVelocityGenerate* result = [ref initWithBoundaryValueX1: x1 Y1: y1 Z1: z1 X2: x2 Y2: y2 Z2: z2];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleColorGenerate::getColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleColorGenerate::getColor(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleColorGenerate> ref = (id<MAParticleColorGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float* result = [ref getColor];
        
                // result
                // return a (value)*
                NSValue* __result__ = [NSValue valueWithPointer:result];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleRandomColorGenerate::initWithBoundaryColorR1_G1_B1_A1_R2_G2_B2_A2_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRandomColorGenerate::initWithBoundaryColorR1_G1_B1_A1_R2_G2_B2_A2(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float r1 = [args[@"r1"] floatValue];
                // jsonable arg
                float g1 = [args[@"g1"] floatValue];
                // jsonable arg
                float b1 = [args[@"b1"] floatValue];
                // jsonable arg
                float a1 = [args[@"a1"] floatValue];
                // jsonable arg
                float r2 = [args[@"r2"] floatValue];
                // jsonable arg
                float g2 = [args[@"g2"] floatValue];
                // jsonable arg
                float b2 = [args[@"b2"] floatValue];
                // jsonable arg
                float a2 = [args[@"a2"] floatValue];
        
                // ref
                MAParticleRandomColorGenerate* ref = (MAParticleRandomColorGenerate*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleRandomColorGenerate* result = [ref initWithBoundaryColorR1: r1 G1: g1 B1: b1 A1: a1 R2: r2 G2: g2 B2: b2 A2: a2];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleRotationGenerate::getRotate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRotationGenerate::getRotate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleRotationGenerate> ref = (id<MAParticleRotationGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float result = [ref getRotate];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleConstantRotationGenerate::initWithRotate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleConstantRotationGenerate::initWithRotate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float rotate = [args[@"rotate"] floatValue];
        
                // ref
                MAParticleConstantRotationGenerate* ref = (MAParticleConstantRotationGenerate*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleConstantRotationGenerate* result = [ref initWithRotate: rotate];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleSizeGenerate::getSizeX_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeX(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float timeFrame = [args[@"timeFrame"] floatValue];
        
                // ref
                id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float result = [ref getSizeX: timeFrame];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleSizeGenerate::getSizeY_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeY(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float timeFrame = [args[@"timeFrame"] floatValue];
        
                // ref
                id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float result = [ref getSizeY: timeFrame];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleSizeGenerate::getSizeZ_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeZ(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float timeFrame = [args[@"timeFrame"] floatValue];
        
                // ref
                id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float result = [ref getSizeZ: timeFrame];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleCurveSizeGenerate::initWithCurveX_Y_Z_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleCurveSizeGenerate::initWithCurveX_Y_Z(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float x = [args[@"x"] floatValue];
                // jsonable arg
                float y = [args[@"y"] floatValue];
                // jsonable arg
                float z = [args[@"z"] floatValue];
        
                // ref
                MAParticleCurveSizeGenerate* ref = (MAParticleCurveSizeGenerate*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleCurveSizeGenerate* result = [ref initWithCurveX: x Y: y Z: z];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleEmissionModuleOC::initWithEmissionRate_rateTime_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleEmissionModuleOC::initWithEmissionRate_rateTime(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                int rate = [args[@"rate"] intValue];
                // jsonable arg
                int rateTime = [args[@"rateTime"] intValue];
        
                // ref
                MAParticleEmissionModuleOC* ref = (MAParticleEmissionModuleOC*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleEmissionModuleOC* result = [ref initWithEmissionRate: rate rateTime: rateTime];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleShapeModule::getPoint_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleShapeModule::getPoint(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleShapeModule> ref = (id<MAParticleShapeModule>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float* result = [ref getPoint];
        
                // result
                // return a (value)*
                NSValue* __result__ = [NSValue valueWithPointer:result];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleShapeModule::isRatioEnable_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleShapeModule::isRatioEnable(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                id<MAParticleShapeModule> ref = (id<MAParticleShapeModule>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref isRatioEnable];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleSinglePointShapeModule::initWithShapeX_Y_Z_useRatio_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSinglePointShapeModule::initWithShapeX_Y_Z_useRatio(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float x = [args[@"x"] floatValue];
                // jsonable arg
                float y = [args[@"y"] floatValue];
                // jsonable arg
                float z = [args[@"z"] floatValue];
                // jsonable arg
                BOOL isUseRatio = [args[@"isUseRatio"] boolValue];
        
                // ref
                MAParticleSinglePointShapeModule* ref = (MAParticleSinglePointShapeModule*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleSinglePointShapeModule* result = [ref initWithShapeX: x Y: y Z: z useRatio: isUseRatio];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleRectShapeModule::initWithLeft_top_right_bottom_useRatio_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRectShapeModule::initWithLeft_top_right_bottom_useRatio(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float left = [args[@"left"] floatValue];
                // jsonable arg
                float top = [args[@"top"] floatValue];
                // jsonable arg
                float right = [args[@"right"] floatValue];
                // jsonable arg
                float bottom = [args[@"bottom"] floatValue];
                // jsonable arg
                BOOL isUseRatio = [args[@"isUseRatio"] boolValue];
        
                // ref
                MAParticleRectShapeModule* ref = (MAParticleRectShapeModule*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAParticleRectShapeModule* result = [ref initWithLeft: left top: top right: right bottom: bottom useRatio: isUseRatio];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverLifeModuleOC::setVelocityOverLife_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setVelocityOverLife(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleVelocityGenerate> velocity = (id<MAParticleVelocityGenerate>) (args[@"velocity"] == [NSNull null] ? nil : args[@"velocity"]);
        
                // ref
                MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setVelocityOverLife : velocity];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverLifeModuleOC::setRotationOverLife_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setRotationOverLife(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleRotationGenerate> rotation = (id<MAParticleRotationGenerate>) (args[@"rotation"] == [NSNull null] ? nil : args[@"rotation"]);
        
                // ref
                MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setRotationOverLife : rotation];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverLifeModuleOC::setSizeOverLife_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setSizeOverLife(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleSizeGenerate> size = (id<MAParticleSizeGenerate>) (args[@"size"] == [NSNull null] ? nil : args[@"size"]);
        
                // ref
                MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setSizeOverLife : size];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverLifeModuleOC::setColorOverLife_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setColorOverLife(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleColorGenerate> color = (id<MAParticleColorGenerate>) (args[@"color"] == [NSNull null] ? nil : args[@"color"]);
        
                // ref
                MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setColorOverLife : color];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAParticleOverlayOptionsFactory::particleOverlayOptionsWithType_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlayOptionsFactory::particleOverlayOptionsWithType(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAParticleOverlayType particleType = (MAParticleOverlayType) [args[@"particleType"] integerValue];
        
                // ref
        
        
                // invoke native method
                NSArray<MAParticleOverlayOptions*>* result = [MAParticleOverlayOptionsFactory particleOverlayOptionsWithType: particleType];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMVTTileOverlay::mvtTileOverlayWithOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMVTTileOverlay::mvtTileOverlayWithOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMVTTileOverlayOptions* option = (MAMVTTileOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
        
        
                // invoke native method
                MAMVTTileOverlay* result = [MAMVTTileOverlay mvtTileOverlayWithOption: option];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::initWithOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::initWithOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAOverlayRenderer* result = [ref initWithOverlay: overlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::getViewMatrix_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getViewMatrix(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float* result = [ref getViewMatrix];
        
                // result
                // return a (value)*
                NSValue* __result__ = [NSValue valueWithPointer:result];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::getProjectionMatrix_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getProjectionMatrix(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                float* result = [ref getProjectionMatrix];
        
                // result
                // return a (value)*
                NSValue* __result__ = [NSValue valueWithPointer:result];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::getOffsetPoint_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getOffsetPoint(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMapPoint result = [ref getOffsetPoint];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::getMapZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getMapZoomLevel(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref getMapZoomLevel];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::glPointForMapPoint_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glPointForMapPoint(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapPointValue = (NSValue*) args[@"mapPoint"];
                MAMapPoint mapPoint;
                if (mapPointValue != nil && (NSNull*) mapPointValue != [NSNull null]) {
                  [mapPointValue getValue:&mapPoint];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapPoint不能为null"]);
                  return;
                }
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGPoint result = [ref glPointForMapPoint: mapPoint];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::glPointsForMapPoints_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glPointsForMapPoints_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* mapPointsValueList = (NSArray<NSValue*>*) args[@"mapPoints"];
                MAMapPoint mapPoints[mapPointsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < mapPointsValueList.count; __i__++) {
                    NSValue* mapPointsValue = (NSValue*) [mapPointsValueList objectAtIndex:__i__];
                    MAMapPoint mapPointsItem;
                    [mapPointsValue getValue:&mapPointsItem];
                    mapPoints[__i__] = mapPointsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGPoint* result = [ref glPointsForMapPoints: mapPoints count: count];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint*)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::glWidthForWindowWidth_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glWidthForWindowWidth(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat windowWidth = [args[@"windowWidth"] floatValue];
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGFloat result = [ref glWidthForWindowWidth: windowWidth];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::glRender_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glRender(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref glRender ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::loadTexture_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::loadTexture(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* textureImage = (UIImage*) (args[@"textureImage"] == [NSNull null] ? nil : args[@"textureImage"]);
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                GLuint result = [ref loadTexture: textureImage];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::deleteTexture_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::deleteTexture(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                GLuint textureId = [args[@"textureId"] unsignedIntegerValue];
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref deleteTexture : textureId];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAOverlayRenderer::setNeedsUpdate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::setNeedsUpdate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setNeedsUpdate ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAHeatMapVectorOverlay::heatMapOverlayWithOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlay::heatMapOverlayWithOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorOverlayOptions* option = (MAHeatMapVectorOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
        
        
                // invoke native method
                MAHeatMapVectorOverlay* result = [MAHeatMapVectorOverlay heatMapOverlayWithOption: option];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMultiPointOverlay::initWithMultiPointItems_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiPointOverlay::initWithMultiPointItems(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<MAMultiPointItem*>* items = (NSArray<MAMultiPointItem*>*) args[@"items"];
        
                // ref
                MAMultiPointOverlay* ref = (MAMultiPointOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMultiPointOverlay* result = [ref initWithMultiPointItems: items];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomBuildingOverlayOption::optionWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayOption::optionWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
        
        
                // invoke native method
                MACustomBuildingOverlayOption* result = [MACustomBuildingOverlayOption optionWithCoordinates: coords count: count];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomBuildingOverlayOption::setOptionWithCoordinates_count_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayOption::setOptionWithCoordinates_count(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg struct
                NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
                CLLocationCoordinate2D coords[coordsValueList.count];
                for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                    NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                    CLLocationCoordinate2D coordsItem;
                    [coordsValue getValue:&coordsItem];
                    coords[__i__] = coordsItem;
                }
                // jsonable arg
                NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setOptionWithCoordinates: coords count: count];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomBuildingOverlay::addCustomOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlay::addCustomOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MACustomBuildingOverlayOption* option = (MACustomBuildingOverlayOption*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
                MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref addCustomOption : option];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomBuildingOverlay::removeCustomOption_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlay::removeCustomOption(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MACustomBuildingOverlayOption* option = (MACustomBuildingOverlayOption*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
                MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref removeCustomOption : option];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAArc::arcWithStartCoordinate_passedCoordinate_endCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAArc::arcWithStartCoordinate_passedCoordinate_endCoordinate(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* startCoordinateValue = (NSValue*) args[@"startCoordinate"];
                CLLocationCoordinate2D startCoordinate;
                if (startCoordinateValue != nil && (NSNull*) startCoordinateValue != [NSNull null]) {
                  [startCoordinateValue getValue:&startCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"startCoordinate不能为null"]);
                  return;
                }
        
                // struct arg
                NSValue* passedCoordinateValue = (NSValue*) args[@"passedCoordinate"];
                CLLocationCoordinate2D passedCoordinate;
                if (passedCoordinateValue != nil && (NSNull*) passedCoordinateValue != [NSNull null]) {
                  [passedCoordinateValue getValue:&passedCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"passedCoordinate不能为null"]);
                  return;
                }
        
                // struct arg
                NSValue* endCoordinateValue = (NSValue*) args[@"endCoordinate"];
                CLLocationCoordinate2D endCoordinate;
                if (endCoordinateValue != nil && (NSNull*) endCoordinateValue != [NSNull null]) {
                  [endCoordinateValue getValue:&endCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"endCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
        
        
                // invoke native method
                MAArc* result = [MAArc arcWithStartCoordinate: startCoordinate passedCoordinate: passedCoordinate endCoordinate: endCoordinate];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setRegion_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setRegion_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* regionValue = (NSValue*) args[@"region"];
                MACoordinateRegion region;
                if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
                  [regionValue getValue:&region];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"region不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setRegion : region animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::regionThatFits_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::regionThatFits(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* regionValue = (NSValue*) args[@"region"];
                MACoordinateRegion region;
                if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
                  [regionValue getValue:&region];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"region不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MACoordinateRegion result = [ref regionThatFits: region];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setVisibleMapRect_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setVisibleMapRect : mapRect animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::mapRectThatFits_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapRectThatFits(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMapRect result = [ref mapRectThatFits: mapRect];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::mapRectThatFits_edgePadding_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapRectThatFits_edgePadding(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
                // struct arg
                NSValue* insetsValue = (NSValue*) args[@"insets"];
                UIEdgeInsets insets;
                if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
                  [insetsValue getValue:&insets];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"insets不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMapRect result = [ref mapRectThatFits: mapRect edgePadding: insets];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setVisibleMapRect_edgePadding_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_edgePadding_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
                // struct arg
                NSValue* insetsValue = (NSValue*) args[@"insets"];
                UIEdgeInsets insets;
                if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
                  [insetsValue getValue:&insets];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"insets不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setVisibleMapRect : mapRect edgePadding: insets animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setVisibleMapRect_edgePadding_animated_duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_edgePadding_animated_duration(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
                MAMapRect mapRect;
                if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
                  [mapRectValue getValue:&mapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"mapRect不能为null"]);
                  return;
                }
        
                // struct arg
                NSValue* insetsValue = (NSValue*) args[@"insets"];
                UIEdgeInsets insets;
                if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
                  [insetsValue getValue:&insets];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"insets不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
                // jsonable arg
                CFTimeInterval duration = [args[@"duration"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setVisibleMapRect : mapRect edgePadding: insets animated: animated duration: duration];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setCenterCoordinate_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCenterCoordinate_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setCenterCoordinate : coordinate animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setZoomLevel_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setZoomLevel_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setZoomLevel : zoomLevel animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setZoomLevel_atPivot_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setZoomLevel_atPivot_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // struct arg
                NSValue* pivotValue = (NSValue*) args[@"pivot"];
                CGPoint pivot;
                if (pivotValue != nil && (NSNull*) pivotValue != [NSNull null]) {
                  [pivotValue getValue:&pivot];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"pivot不能为null"]);
                  return;
                }
        
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setZoomLevel : zoomLevel atPivot: pivot animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setRotationDegree_animated_duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setRotationDegree_animated_duration(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
                // jsonable arg
                CFTimeInterval duration = [args[@"duration"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setRotationDegree : rotationDegree animated: animated duration: duration];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setCameraDegree_animated_duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCameraDegree_animated_duration(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
                // jsonable arg
                CFTimeInterval duration = [args[@"duration"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setCameraDegree : cameraDegree animated: animated duration: duration];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::getMapStatus_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::getMapStatus(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAMapStatus* result = [ref getMapStatus];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setMapStatus_animated_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setMapStatus_animated(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMapStatus* status = (MAMapStatus*) (args[@"status"] == [NSNull null] ? nil : args[@"status"]);
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setMapStatus : status animated: animated];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setMapStatus_animated_duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setMapStatus_animated_duration(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAMapStatus* status = (MAMapStatus*) (args[@"status"] == [NSNull null] ? nil : args[@"status"]);
                // jsonable arg
                BOOL animated = [args[@"animated"] boolValue];
                // jsonable arg
                CFTimeInterval duration = [args[@"duration"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setMapStatus : status animated: animated duration: duration];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setCompassImage_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCompassImage(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setCompassImage : image];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::takeSnapshotInRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::takeSnapshotInRect(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* rectValue = (NSValue*) args[@"rect"];
                CGRect rect;
                if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
                  [rectValue getValue:&rect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"rect不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                UIImage* result = [ref takeSnapshotInRect: rect];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::metersPerPointForZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::metersPerPointForZoomLevel(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                double result = [ref metersPerPointForZoomLevel: zoomLevel];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::convertCoordinate_toPointToView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertCoordinate_toPointToView(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // ref arg
                UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGPoint result = [ref convertCoordinate: coordinate toPointToView: view];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::convertPoint_toCoordinateFromView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertPoint_toCoordinateFromView(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* pointValue = (NSValue*) args[@"point"];
                CGPoint point;
                if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
                  [pointValue getValue:&point];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"point不能为null"]);
                  return;
                }
        
                // ref arg
                UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CLLocationCoordinate2D result = [ref convertPoint: point toCoordinateFromView: view];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::convertRegion_toRectToView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertRegion_toRectToView(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* regionValue = (NSValue*) args[@"region"];
                MACoordinateRegion region;
                if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
                  [regionValue getValue:&region];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"region不能为null"]);
                  return;
                }
        
                // ref arg
                UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                CGRect result = [ref convertRegion: region toRectToView: view];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGRect)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::convertRect_toRegionFromView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertRect_toRegionFromView(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* rectValue = (NSValue*) args[@"rect"];
                CGRect rect;
                if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
                  [rectValue getValue:&rect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"rect不能为null"]);
                  return;
                }
        
                // ref arg
                UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MACoordinateRegion result = [ref convertRect: rect toRegionFromView: view];
        
                // result
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::reloadMap_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::reloadMap(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref reloadMap ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::clearDisk_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::clearDisk(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref clearDisk ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::reloadInternalTexture_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::reloadInternalTexture(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref reloadInternalTexture ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::mapContentApprovalNumber_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapContentApprovalNumber(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSString* result = [ref mapContentApprovalNumber];
        
                // result
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::satelliteImageApprovalNumber_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::satelliteImageApprovalNumber(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSString* result = [ref satelliteImageApprovalNumber];
        
                // result
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::terrainApprovalNumber_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::terrainApprovalNumber(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                NSString* result = [ref terrainApprovalNumber];
        
                // result
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::forceRefresh_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::forceRefresh(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref forceRefresh ];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::setConstructingRoadEnable_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setConstructingRoadEnable(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL enabled = [args[@"enabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                [ref setConstructingRoadEnable : enabled];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::updatePrivacyShow_privacyInfo_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::updatePrivacyShow_privacyInfo(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                AMapPrivacyShowStatus showStatus = (AMapPrivacyShowStatus) [args[@"showStatus"] integerValue];
                // enum arg
                AMapPrivacyInfoStatus containStatus = (AMapPrivacyInfoStatus) [args[@"containStatus"] integerValue];
        
                // ref
        
        
                // invoke native method
                [MAMapView updatePrivacyShow: showStatus privacyInfo: containStatus];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAMapView::updatePrivacyAgree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::updatePrivacyAgree(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                AMapPrivacyAgreeStatus agreeStatus = (AMapPrivacyAgreeStatus) [args[@"agreeStatus"] integerValue];
        
                // ref
        
        
                // invoke native method
                [MAMapView updatePrivacyAgree: agreeStatus];
        
                // result
                // 无返回值
                NSString* __result__ = @"success";
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAGroundOverlayRenderer::initWithGroundOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlayRenderer::initWithGroundOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAGroundOverlay* groundOverlay = (MAGroundOverlay*) (args[@"groundOverlay"] == [NSNull null] ? nil : args[@"groundOverlay"]);
        
                // ref
                MAGroundOverlayRenderer* ref = (MAGroundOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAGroundOverlayRenderer* result = [ref initWithGroundOverlay: groundOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MACustomBuildingOverlayRenderer::initWithCustomBuildingOverlay_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayRenderer::initWithCustomBuildingOverlay(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MACustomBuildingOverlay* customBuildingOverlay = (MACustomBuildingOverlay*) (args[@"customBuildingOverlay"] == [NSNull null] ? nil : args[@"customBuildingOverlay"]);
        
                // ref
                MACustomBuildingOverlayRenderer* ref = (MACustomBuildingOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MACustomBuildingOverlayRenderer* result = [ref initWithCustomBuildingOverlay: customBuildingOverlay];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        // top constant
        @"getMAOfflineMapErrorDomain": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __MAOfflineMapErrorDomain__ = MAOfflineMapErrorDomain;
        
            methodResult(__MAOfflineMapErrorDomain__);
        },
        // top constant
        @"getMAOfflineMapDownloadReceivedSizeKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __MAOfflineMapDownloadReceivedSizeKey__ = MAOfflineMapDownloadReceivedSizeKey;
        
            methodResult(__MAOfflineMapDownloadReceivedSizeKey__);
        },
        // top constant
        @"getMAOfflineMapDownloadExpectedSizeKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __MAOfflineMapDownloadExpectedSizeKey__ = MAOfflineMapDownloadExpectedSizeKey;
        
            methodResult(__MAOfflineMapDownloadExpectedSizeKey__);
        },
        // top constant
        @"getkMAMapLayerCenterMapPointKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __kMAMapLayerCenterMapPointKey__ = kMAMapLayerCenterMapPointKey;
        
            methodResult(__kMAMapLayerCenterMapPointKey__);
        },
        // top constant
        @"getkMAMapLayerZoomLevelKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __kMAMapLayerZoomLevelKey__ = kMAMapLayerZoomLevelKey;
        
            methodResult(__kMAMapLayerZoomLevelKey__);
        },
        // top constant
        @"getkMAMapLayerRotationDegreeKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __kMAMapLayerRotationDegreeKey__ = kMAMapLayerRotationDegreeKey;
        
            methodResult(__kMAMapLayerRotationDegreeKey__);
        },
        // top constant
        @"getkMAMapLayerCameraDegreeKey": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // result
            // 返回值: jsonable
            id __kMAMapLayerCameraDegreeKey__ = kMAMapLayerCameraDegreeKey;
        
            methodResult(__kMAMapLayerCameraDegreeKey__);
        },
        @"MAOfflineCity::get_cityCode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineCity::get_cityCode");
            }
        
            // ref object
            MAOfflineCity* ref = (MAOfflineCity*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.cityCode;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiPoint::get_points": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPoint::get_points");
            }
        
            // ref object
            MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapPoint* result = ref.points;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint*)];
        
            methodResult(__result__);
        },
        
        @"MAMultiPoint::get_pointCount": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPoint::get_pointCount");
            }
        
            // ref object
            MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSUInteger result = ref.pointCount;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMultiPoint::get_cross180Longitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPoint::get_cross180Longitude");
            }
        
            // ref object
            MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.cross180Longitude;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAGroundOverlay::get_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlay::get_icon");
            }
        
            // ref object
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = ref.icon;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAGroundOverlay::get_alpha": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlay::get_alpha");
            }
        
            // ref object
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.alpha;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAGroundOverlay::get_zoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlay::get_zoomLevel");
            }
        
            // ref object
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.zoomLevel;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAGroundOverlay::get_bounds": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlay::get_bounds");
            }
        
            // ref object
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACoordinateBounds result = ref.bounds;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateBounds)];
        
            methodResult(__result__);
        },
        
        @"MAPolygonRenderer::get_polygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPolygonRenderer::get_polygon");
            }
        
            // ref object
            MAPolygonRenderer* ref = (MAPolygonRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPolygon* result = ref.polygon;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAPinAnnotationView::get_pinColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPinAnnotationView::get_pinColor");
            }
        
            // ref object
            MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPinAnnotationColor result = ref.pinColor;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPinAnnotationView::get_animatesDrop": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPinAnnotationView::get_animatesDrop");
            }
        
            // ref object
            MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.animatesDrop;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapNode::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapNode::get_coordinate");
            }
        
            // ref object
            MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAHeatMapNode::get_intensity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapNode::get_intensity");
            }
        
            // ref object
            MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = ref.intensity;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapGradient::get_colors": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapGradient::get_colors");
            }
        
            // ref object
            MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<UIColor*>* result = ref.colors;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapGradient::get_startPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapGradient::get_startPoints");
            }
        
            // ref object
            MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<NSNumber*>* result = ref.startPoints;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapTileOverlay::get_data": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::get_data");
            }
        
            // ref object
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAHeatMapNode*>* result = ref.data;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapTileOverlay::get_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::get_radius");
            }
        
            // ref object
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = ref.radius;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapTileOverlay::get_opacity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::get_opacity");
            }
        
            // ref object
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.opacity;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapTileOverlay::get_gradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::get_gradient");
            }
        
            // ref object
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapGradient* result = ref.gradient;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapTileOverlay::get_allowRetinaAdapting": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapTileOverlay::get_allowRetinaAdapting");
            }
        
            // ref object
            MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.allowRetinaAdapting;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapStatus::get_centerCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::get_centerCoordinate");
            }
        
            // ref object
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.centerCoordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAMapStatus::get_zoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::get_zoomLevel");
            }
        
            // ref object
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.zoomLevel;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapStatus::get_rotationDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::get_rotationDegree");
            }
        
            // ref object
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.rotationDegree;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapStatus::get_cameraDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::get_cameraDegree");
            }
        
            // ref object
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.cameraDegree;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapStatus::get_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapStatus::get_screenAnchor");
            }
        
            // ref object
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.screenAnchor;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAPointAnnotation::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::get_coordinate");
            }
        
            // ref object
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAPointAnnotation::get_isLockedToScreen": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::get_isLockedToScreen");
            }
        
            // ref object
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.lockedToScreen;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAPointAnnotation::get_lockedScreenPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAPointAnnotation::get_lockedScreenPoint");
            }
        
            // ref object
            MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.lockedScreenPoint;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MACircle::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACircle::get_coordinate");
            }
        
            // ref object
            MACircle* ref = (MACircle*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MACircle::get_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACircle::get_radius");
            }
        
            // ref object
            MACircle* ref = (MACircle*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDistance result = ref.radius;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAArcRenderer::get_arc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArcRenderer::get_arc");
            }
        
            // ref object
            MAArcRenderer* ref = (MAArcRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAArc* result = ref.arc;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotation::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotation::get_coordinate");
            }
        
            // ref object
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAAnnotation::get_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotation::get_title");
            }
        
            // ref object
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.title;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAAnnotation::get_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnnotation::get_subtitle");
            }
        
            // ref object
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.subtitle;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineMapViewController::get_offlineMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineMapViewController::get_offlineMap");
            }
        
            // ref object
            MAOfflineMapViewController* ref = (MAOfflineMapViewController*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOfflineMap* result = ref.offlineMap;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapCustomStyleOptions::get_styleData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::get_styleData");
            }
        
            // ref object
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSData* result = ref.styleData;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapCustomStyleOptions::get_styleDataOverseaPath": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::get_styleDataOverseaPath");
            }
        
            // ref object
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.styleDataOverseaPath;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapCustomStyleOptions::get_styleId": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::get_styleId");
            }
        
            // ref object
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = ref.styleId;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapCustomStyleOptions::get_styleTextureData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::get_styleTextureData");
            }
        
            // ref object
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSData* result = ref.styleTextureData;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapCustomStyleOptions::get_styleExtraData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapCustomStyleOptions::get_styleExtraData");
            }
        
            // ref object
            MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSData* result = ref.styleExtraData;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAParticleOverlay::get_overlayOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlay::get_overlayOption");
            }
        
            // ref object
            MAParticleOverlay* ref = (MAParticleOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleOverlayOptions* result = ref.overlayOption;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiColoredPolylineRenderer::get_multiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiColoredPolylineRenderer::get_multiPolyline");
            }
        
            // ref object
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiPolyline* result = ref.multiPolyline;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiColoredPolylineRenderer::get_strokeColors": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiColoredPolylineRenderer::get_strokeColors");
            }
        
            // ref object
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<UIColor*>* result = ref.strokeColors;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiColoredPolylineRenderer::get_isGradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiColoredPolylineRenderer::get_isGradient");
            }
        
            // ref object
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.gradient;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAAnimatedAnnotation::get_movingDirection": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAAnimatedAnnotation::get_movingDirection");
            }
        
            // ref object
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDirection result = ref.movingDirection;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMultiTexturePolylineRenderer::get_multiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiTexturePolylineRenderer::get_multiPolyline");
            }
        
            // ref object
            MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiPolyline* result = ref.multiPolyline;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMultiTexturePolylineRenderer::get_strokeTextureImages": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiTexturePolylineRenderer::get_strokeTextureImages");
            }
        
            // ref object
            MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<UIImage*>* result = ref.strokeTextureImages;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridNode::get_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridNode::get_coordinate");
            }
        
            // ref object
            MAHeatMapVectorGridNode* ref = (MAHeatMapVectorGridNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = ref.coordinate;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGrid::get_inputNodes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGrid::get_inputNodes");
            }
        
            // ref object
            MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAHeatMapVectorGridNode*>* result = ref.inputNodes;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGrid::get_color": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGrid::get_color");
            }
        
            // ref object
            MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.color;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_type": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::get_type");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapType result = ref.type;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::get_visible");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.visible;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_inputGrids": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::get_inputGrids");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAHeatMapVectorGrid*>* result = ref.inputGrids;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_minZoom": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::get_minZoom");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.minZoom;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_maxZoom": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorGridOverlayOptions::get_maxZoom");
            }
        
            // ref object
            MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.maxZoom;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
    };
}

@end
