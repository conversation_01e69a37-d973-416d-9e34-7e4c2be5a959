import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/branch_list_bean.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';

class B2bCustomerSearchPageState {
  // 客户数据（兼容旧版本）
  List<dynamic> CustomerSearch = [];
  // 门店数据列表
  List<BranchInfo> branchList = [];

  String searchText = ''; // 搜索内容
  String searchKeyword = ''; // 搜索关键词

  // 分页相关
  int currentPage = 1;
  int pageSize = 10;
  int totalCount = 0;
  bool isLoading = false;
  bool hasMore = true;
  bool isRefreshing = false;

  B2bCustomerSearchPageState() {
    ///Initialize variables
  }

  // 重置分页状态
  void resetPagination() {
    currentPage = 1;
    totalCount = 0;
    hasMore = true;
    branchList.clear();
  }

  // 更新分页状态
  void updatePagination(int total, int currentDataLength) {
    print('搜索分页更新: total=$total currentDataLength=$currentDataLength branchListLength=${branchList.length}');
    totalCount = total;
    hasMore = branchList.length < total;
    if (currentDataLength < pageSize) {
      hasMore = false;
    }
  }
}
