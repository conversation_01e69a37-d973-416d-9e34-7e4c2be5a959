import 'package:extended_image/extended_image.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:fuduoduo/resource/image_resource.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/date_Utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
// import '../logic.dart';
import 'package:fuduoduo/domain/b2b_user_profile_bean.dart';
import 'package:fuduoduo/store/index.dart';
import 'logic.dart';

class B2bPage_viewPage extends StatelessWidget {
  B2bPage_viewPage({Key? key}) : super(key: key);

  final logic = Get.put(B2bPage_viewLogic());
  final state = Get.find<B2bPage_viewLogic>().state;
  final publicState = Get.find<Public>().state;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButton: GetBuilder<B2bPage_viewLogic>(builder: (controller) {
      return Visibility(
          //1、当前用户角色为业务员 2、当前用户开通账号 3、该经销商开通同福云商
          visible: false,
              // ((SpUtil.getObject('userInfo'))?['employee']["duty"] == "4") &&
              //     (SpUtil.getBool("StartFlag") == true) &&
              //     (SpUtil.getBool("CloudFlag") == true),
          child: InkWell(
            onTap: () {
              // MyCommonUtils.showToast("跳转云商专属邀请码页面");
              Get.toNamed(PageName.InvitationCodePage)?.then((value) => {
                    logic.getLocalUserInfo(),
                    logic.getUserStatusInfo(),
                    logic.getCloudFlagInfo(false)
                  });
            },
            child: Opacity(
              opacity: 1,
              child: Container(
                width: 210.w,
                height: 96.h,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                ),
                child: Image.asset(
                  ImageResource.ICON_INVITATION,
                  width: 210.w,
                  height: 96.h,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ));
    }), body: Obx(() {
      return SmartRefresher(
        controller: logic.controller,
        onLoading: () {
          if (state.yeJiType.value == '0') {
            state.pageNo++;
            logic.getHomePageTotalData("", "", "");
          } else {
            return;
          }
        },
        onRefresh: () {
          if (state.yeJiType.value == '0') {
            logic.getHomePageTotalData("", "", "");
          } else {
            logic.getTeamData();
          }
        },
        enablePullUp: state.yeJiType.value == '0' ? false : true,
        enablePullDown: true,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: SizedBox(
            child: Stack(
              children: [
                _headerView(context),
                Positioned(child: _contentView(context)),
              ],
            ),
          ),
        ),
      );
    }));
  }

  //头部区
  _headerView(context) {
    // 安全地获取用户信息
    dynamic userInfoData = publicState.userInfo;
    B2bUserData? userInfo;

    if (userInfoData != null) {
      try {
        if (userInfoData is Map<String, dynamic>) {
          userInfo = B2bUserData.fromJson(userInfoData);
        } else if (userInfoData is B2bUserData) {
          userInfo = userInfoData;
        }
      } catch (e) {
        print('用户信息解析失败: $e');
        userInfo = null;
      }
    }
    List<Color> _color = [
      Color.fromRGBO(255, 117, 26, 1),
      Color.fromRGBO(255, 83, 47, 1),
      Color.fromRGBO(255, 33, 26, 1),
    ];
    print('=======仓库======${state.selectBranch.value}');
    return Obx(() {
      return Container(
        // decoration: BoxDecoration(gradient: LinearGradient(colors: _color)),
        decoration: BoxDecoration(gradient: LinearGradient(colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR)),
        padding: EdgeInsets.symmetric(
          horizontal: 24.w,
        ),
        height: 0.4.sh,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              // padding: EdgeInsets.only(top: 44.w),
              child: AppBar(
                foregroundColor: Colors.white,
                title: Text(
                    state.selectBranch.isEmpty
                        ? '${SpUtil.getString('branchName') ?? ''}'
                        : state.selectBranch.value,
                    style: TextStyle(fontSize: 34.sp, color: Colors.white)),
                backgroundColor: Colors.transparent,
                elevation: 0,
                centerTitle: true,
                actions: [
                  InkWell(
                    onTap: () {
                      MyCommonUtils.showSelectBranchDialog(
                          context,
                          state.userInfo['branches'],
                          state.branchSelectIndex.value,
                          sureCallBack: (data, index) {
                        state.branchSelectIndex.value = index;
                        state.selectBranch.value = data['branchName'];
                        SpUtil.putString("branchNo", data['branchNo']);
                        SpUtil.putString("branchType", data['branchType']);
                        SpUtil.putString("branchName", data['branchName']);
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(right: 32.w),
                      child: Text(
                        // '切换仓库',
                        (state.userInfo['branches'] ?? []).length > 1
                            ? '切换仓库'
                            : '',
                        style: TextStyle(fontSize: 28.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 24.w),
              child: Text(
                // '岳麓区吴彦祖',
                '${state.userInfo['nickName'] ?? ''}',
                style: TextStyle(
                    fontSize: 36.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 16.w),
            Text(
              // '999.00',
              "${state.todayOrderMount.value}",
              style: TextStyle(
                  fontSize: 60.w,
                  color: Colors.white,
                  fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.w),
            Text(
              '今日订单金额（元）',
              style: TextStyle(
                  fontSize: 28.w, color: Color.fromARGB(255, 243, 222, 222)),
            )
          ],
        ),
      );
    });
  }

  //底部内容区
  _contentView(context) {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 0.32.sh, 0, 0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _kingKongView(),
          Visibility(
              // visible: (((SpUtil.getObject('userInfo'))?['employee']["duty"] == "4") || ((SpUtil.getObject('userInfo'))?['employee']["duty"] == "2")) && (SpUtil.getBool("CloudFlag") == true),
              visible: state.showActivityView.value,
              child: _rebateActivityView(context)),
          _gradesView(context)
        ],
      ),
    );
  }

  //金刚区
  _kingKongView() {
    return Container(
      height: 224.w,
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.w),
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(24, 24, 24, .2),
              blurRadius: 10.w,
              spreadRadius: 0,
              offset: Offset(0, 0),
            ),
          ]),
      child: ListView.builder(
          physics: const ScrollPhysics(),
          scrollDirection: Axis.horizontal,
          itemCount: state.tempList.length,
          itemBuilder: (context, index) {
            double width = (1.sw - 48.w) / 4.5;
            var item = state.tempList[index];
            return InkWell(
              onTap: () {
                logic.kingKongJump(item);
              },
              child: Container(
                width: width,
                height: width,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        "${item["icon"]}",
                        width: 1.sw,
                        fit: BoxFit.fill,
                      ),
                      Container(
                        transform: Matrix4.translationValues(0, -10, 0),
                        child: Text(
                          "${item["name"]}",
                          style: TextStyle(
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      )
                    ]),
              ),
            );
          }),
    );
  }

  //任务返利活动区
  _rebateActivityView(context) {
    // List colorList = [Colors.red, Colors.green, Colors.blue];
    // 1、角色为车销业务员、访销业务员、配送员 2、该经销商开通同福云商
    return InkWell(
      onTap: () {
        if (state.validateFlag.value == false) {
          Get.toNamed(PageName.BindPhoneNumberPage,
              parameters: {"fromPage": "Page_viewPage"})?.then((value) {
            if (value != null && value == true) {
              logic.getLocalUserInfo();
              logic.getValidateFlagInfo(true);
            }
          });
        } else {
          logic.getCloudFlagInfo(true);
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 20.h),
        height: 160.h,
        width: 1.sw - 48.w,
        child:
            Image.asset(ImageResource.ICON_ACTIVITY_BANNER, fit: BoxFit.fill),
      ),
    );
    // return Visibility(
    //   child: Container(
    //     margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 20.h),
    //     height: 160.h,
    //     child: Swiper(
    //       scrollDirection: Axis.horizontal,
    //       loop: true,
    //       autoplay: true,
    //       autoplayDelay: 3000,
    //       autoplayDisableOnInteraction: true,
    //       itemCount: colorList.length,
    //       viewportFraction: 1,
    //       pagination: SwiperPagination(
    //           builder: DotSwiperPaginationBuilder(
    //               color: Colors.white,
    //               activeColor: Colors.yellow
    //           )
    //       ),
    //       itemBuilder: (BuildContext context, int index) {
    //         return Container(
    //           decoration: BoxDecoration(
    //             color: colorList[index],
    //             borderRadius: BorderRadius.circular(20.r),
    //           ),
    //         );
    //       },
    //       onTap: (int index) {
    //         switch(index) {
    //           case 0:
    //             return MyCommonUtils.showToast("点击了红色");
    //           case 1:
    //             return MyCommonUtils.showToast("点击了绿色");
    //           case 2:
    //             return MyCommonUtils.showToast("点击了蓝色");
    //         }
    //       },
    //     ),
    //   )
    // );
    // return Visibility(
    //     visible: state.missionCount > 0,
    //     child: Container(
    //       margin: EdgeInsets.only(left: 20.w, right: 20.w),
    //       padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
    //       decoration: BoxDecoration(
    //         color: ColorResource.WHITE_COMMON_COLOR,
    //         borderRadius: BorderRadius.circular(20.r),
    //       ),
    //       child: Column(
    //         children: [
    //           Row(
    //             children: [
    //               Text(
    //                   "任务返利活动",
    //                   style: TextStyle(color: ColorResource.BLACK_BOLD_TITLE_COLOR, fontSize: 30.sp, fontWeight: FontWeight.w600)
    //               ),
    //               Spacer(),
    //               InkWell(
    //                 onTap: () {
    //                   // MyCommonUtils.showToast("跳转任务列表页面");
    //                   Get.toNamed(PageName.MissionSystemListPage)?.then((value) =>
    //                   {
    //                   });
    //                 },
    //                 child: Row(
    //                   children: [
    //                     Text(
    //                         "更多任务",
    //                         style: TextStyle(color: ColorResource.BLACK_NORMAL_TITLE_COLOR, fontSize: 24.sp)
    //                     ),
    //                     Container(
    //                       width: 30.w,
    //                       height: 30.h,
    //                       child: ClipRRect(
    //                         borderRadius: BorderRadius.circular(20.r),
    //                         child: ExtendedImage.asset(ImageResource.ARROW_RIGHT, color: ColorResource.BLACK_COMMON_COLOR),
    //                       ),
    //                     )
    //                   ],
    //                 ),
    //               )
    //             ],
    //           ),
    //           Visibility(
    //               visible: state.missionCount > 0,
    //               child: SizedBox(height: 20.h)
    //           ),
    //           Visibility(
    //               visible: state.missionCount > 0,
    //               child: Container(
    //                 padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
    //                 decoration: BoxDecoration(
    //                   color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
    //                   borderRadius: BorderRadius.circular(20.r),
    //                 ),
    //                 child: Column(
    //                   crossAxisAlignment: CrossAxisAlignment.start,
    //                   children: [
    //                     Row(
    //                       children: [
    //                         Text(
    //                             "云商代客下单",
    //                             style: TextStyle(color: ColorResource.BLACK_NORMAL_TITLE_COLOR, fontSize: 30.sp)
    //                         ),
    //                         Spacer(),
    //                         Container(
    //                           alignment: Alignment.center,
    //                           width: 150.w,
    //                           height: 50.h,
    //                           decoration: BoxDecoration(
    //                             color: ColorResource.ORANGE_RED_COMMON_COLOR,
    //                             borderRadius:
    //                             BorderRadius.all(Radius.circular(25.r)),
    //                           ),
    //                           child: InkWell(
    //                             onTap: () {
    //                               Get.toNamed(PageName.CustomerListPage)?.then((value) =>
    //                               {
    //                               });
    //                             },
    //                             child: Text(
    //                               "去下单",
    //                               style: TextStyle(
    //                                   color: ColorResource.WHITE_COMMON_COLOR,
    //                                   fontSize: 24.sp,
    //                                   fontWeight: FontWeight.w600),
    //                             ),
    //                           ),
    //                         )
    //                       ],
    //                     ),
    //                     SizedBox(height: 20.h),
    //                     Text(
    //                         "订单完成配送，实付金额每满10元领1福分",
    //                         style: TextStyle(color: ColorResource.GRAY_TITLE_COLOR, fontSize: 24.sp)
    //                     ),
    //                   ],
    //                 ),
    //               )
    //           ),
    //           Visibility(
    //               visible: state.missionCount > 1,
    //               child: SizedBox(height: 20.h)
    //           ),
    //           Visibility(
    //               visible: state.missionCount > 1,
    //               child: Container(
    //                 padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
    //                 decoration: BoxDecoration(
    //                   color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
    //                   borderRadius: BorderRadius.circular(20.r),
    //                 ),
    //                 child: Column(
    //                   crossAxisAlignment: CrossAxisAlignment.start,
    //                   children: [
    //                     Row(
    //                       children: [
    //                         Text(
    //                             "销售订货",
    //                             style: TextStyle(color: ColorResource.BLACK_NORMAL_TITLE_COLOR, fontSize: 30.sp)
    //                         ),
    //                         Spacer(),
    //                         Container(
    //                           alignment: Alignment.center,
    //                           width: 150.w,
    //                           height: 50.h,
    //                           decoration: BoxDecoration(
    //                             color: ColorResource.ORANGE_RED_COMMON_COLOR,
    //                             borderRadius:
    //                             BorderRadius.all(Radius.circular(25.r)),
    //                           ),
    //                           child: InkWell(
    //                             onTap: () {
    //                               Get.toNamed(PageName.CustomerListPage)?.then((value) =>
    //                               {
    //                               });
    //                             },
    //                             child: Text(
    //                               "去下单",
    //                               style: TextStyle(
    //                                   color: ColorResource.WHITE_COMMON_COLOR,
    //                                   fontSize: 24.sp,
    //                                   fontWeight: FontWeight.w600),
    //                             ),
    //                           ),
    //                         )
    //                       ],
    //                     ),
    //                     SizedBox(height: 20.h),
    //                     Text(
    //                         "订单完成配送，实付金额每满10元领1福分",
    //                         style: TextStyle(color: ColorResource.GRAY_TITLE_COLOR, fontSize: 24.sp)
    //                     ),
    //                   ],
    //                 ),
    //               )
    //           ),
    //         ],
    //       ),
    //     )
    // );
  }

  //业绩区title
  _gradesView(context) {
    return Obx(() {
      return Container(
        margin: EdgeInsets.only(top: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Row(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      InkWell(
                          onTap: () {
                            state.yeJiType.value = '0';
                            // logic.getHomePageTotalData("", "", "");
                          },
                          child: Text(
                            '个人业绩',
                            style: TextStyle(
                                fontSize:
                                    state.yeJiType.value == '0' ? 32.sp : 28.sp,
                                color: state.yeJiType.value == '0'
                                    ? ColorResource.BLACK_NORMAL_TITLE_COLOR
                                    : ColorResource.GRAY_TITLE_COLOR),
                          )),
                      SizedBox(
                        width: 40.w,
                      ),
                      // InkWell(
                      //   onTap: () {
                      //     state.yeJiType.value = '1';
                      //     // logic.getTeamData();
                      //   },
                      //   child: Text('团队业绩',
                      //       style: TextStyle(
                      //           fontSize:
                      //               state.yeJiType.value != '0' ? 32.sp : 28.sp,
                      //           color: state.yeJiType.value != '0'
                      //               ? ColorResource.BLACK_NORMAL_TITLE_COLOR
                      //               : ColorResource.GRAY_TITLE_COLOR)),
                      // ),
                    ],
                  ),
                  Spacer(),
                  Container(
                    margin: EdgeInsets.fromLTRB(0, 0, 20.w, 0),
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40.w),
                        color: Color.fromRGBO(247, 224, 224, 1)),
                    child: Row(
                      children: _initDateList(),
                    ),
                  )
                ],
              ),
            ),
            Container(
              child: state.yeJiType.value == '0' ? _Individual() : _Team(),
            )
          ],
        ),
      );
    });
  }

  ///今日、本周、本月
  _initDateList() {
    var tempList = [
      {
        "name": "今日",
        "type": "0",
      },
      {
        "name": "本周",
        "type": "1",
      },
      {
        "name": "本月",
        "type": "2",
      }
    ].map((e) {
      final selected = e['type'];
      return Obx(() {
        return GestureDetector(
            onTap: () {
              // dateType = selected;
              if (state.dateType.value == selected) return;
              state.dateType.value = selected!;

              // if (state.yeJiType.value == "0") {
              //   logic.getHomePageTotalData("", "", "");
              // } else {
              //   logic.getTeamData();
              // }
              print(
                  "state.yeJiType-----${state.yeJiType}======selected======$selected");
            },
            child: Container(
              // margin: EdgeInsets.fromLTRB(0, 0, 20, 0),
              alignment: Alignment.center,
              height: 50.w,
              width: 100.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25.w),
                  color: state.dateType.value == selected
                      ? Color.fromRGBO(250, 35, 33, 1)
                      : Color.fromRGBO(247, 224, 224, 1)),
              child: Text(
                "${e['name']}",
                style: TextStyle(
                    color: state.dateType.value == selected
                        ? Colors.white
                        : Color.fromRGBO(250, 35, 33, 1),
                    fontSize: 26.w),
              ),
            ));
      });
    });
    return tempList.toList();
  }

  //个人业绩
  _Individual() {
    return Obx(() {
      return Container(
        padding: EdgeInsets.only(top: 24.w),
        margin: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 24.w),
        child: GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            scrollDirection: Axis.vertical,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              //设置列数
              crossAxisCount: 2,
              //设置横向间距
              crossAxisSpacing: 26.w,
              //设置主轴间距
              mainAxisSpacing: 24.w,
              childAspectRatio: 169 / 93,
            ),
            itemCount: state.individualList.length,
            itemBuilder: (context, index) {
              return _IndividualItem(state.individualList[index]);
            }),
      );
    });
  }

  //个人业绩子view
  _IndividualItem(info) {
    return InkWell(
      onTap: () {
        logic.itemJump(info['type']);
      },
      child: Container(
        width: 340.w,
        height: 170.w,
        padding: EdgeInsets.symmetric(vertical: 32.w, horizontal: 24.w),
        decoration: BoxDecoration(boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(24, 24, 24, .1),
            blurRadius: 5.w,
            spreadRadius: 0,
          ),
        ], borderRadius: BorderRadius.circular(15.w), color: Colors.white),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              info['num'],
              style: TextStyle(
                  fontSize: 40.sp,
                  color: ColorResource.BLACK_COMMON_COLOR,
                  fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  info['title'],
                  style: TextStyle(
                      fontSize: 24.sp, color: ColorResource.GRAY_EDIT_COLOR),
                ),
                info['type'].toString().isEmpty
                    ? Spacer()
                    : Icon(
                        Icons.chevron_right,
                        size: 40.w,
                        color: Colors.grey[400],
                      )
              ],
            )
          ],
        ),
      ),
    );
  }

  //团队业绩view
  _Team() {
    print('===============绘制前======${state.teamDataInfo.length}');
    return Obx(() {
      return Container(
          decoration: BoxDecoration(color: Colors.white),
          margin: EdgeInsets.only(top: 24.w),
          child: ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: state.teamDataInfo.length,
            padding: EdgeInsets.zero,
            scrollDirection: Axis.vertical,
            itemBuilder: (context, index) {
              var e = state.teamDataInfo[index];
              String indexStr = (index).toString();
              List imgPath = [
                'assets/images/rank_one.png',
                'assets/images/rank_two.png',
                'assets/images/rank_three.png'
              ];
              if (index == 0) {
                return Row(
                  children: _getTableTitle(),
                );
              } else {
                return _teamItemView(index - 1, indexStr, imgPath, e);
              }
            },
          ));
    });
  }

  _teamItemView(int index, String indexStr, List<dynamic> imgPath, e) {
    return Container(
      child: Row(
        children: [
          Container(
            width: 100.w,
            height: 80.w,
            alignment: Alignment.center,
            child: index > 2
                ? Text(indexStr, style: TextStyle(fontSize: 24.w))
                : Image.asset(imgPath[index],
                    width: 50.w,
                    height: 50.w,
                    alignment: Alignment.center,
                    fit: BoxFit.cover),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child:
                Text(e['employeeName'] ?? "", style: TextStyle(fontSize: 24.w)),
          )),
          Container(
            width: 160.w,
            alignment: Alignment.center,
            child: Text((e['xsAmt'] ?? 0).toString(),
                style: TextStyle(fontSize: 24.w)),
          ),
          Container(
            width: 140.w,
            alignment: Alignment.center,
            child: Text((e['bfConsumerNum'] ?? 0).toString(),
                style: TextStyle(fontSize: 24.w)),
          ),
          Container(
            width: 140.w,
            alignment: Alignment.center,
            child: Text((e['dxConsumerNum'] ?? 0).toString(),
                style: TextStyle(fontSize: 24.w)),
          )
        ],
      ),
    );
  }

  ///团队业绩标头
  _getTableTitle() {
    List tableTitle = [
      {"title": "排名", "width": 100.w},
      {"title": "姓名"},
      {"title": "销售", "width": 160.w},
      {"title": "拜访", "width": 140.w},
      {"title": "动销", "width": 140.w},
    ];
    var tempList = tableTitle.map((e) {
      double doubleWidth = (e['width'] ?? 0).toDouble();
      return doubleWidth == 0
          ? Expanded(
              child: Container(
              height: 100.w,
              alignment: Alignment.center,
              child: Text(
                "${e['title']}",
                style: TextStyle(fontSize: 24.w),
              ),
            ))
          : Container(
              width: doubleWidth,
              height: 100.w,
              alignment: Alignment.center,
              child: Text("${e['title']}", style: TextStyle(fontSize: 24.w)),
            );
    });
    return tempList.toList();
  }
}
