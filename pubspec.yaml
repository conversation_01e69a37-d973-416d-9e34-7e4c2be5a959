name: fuduoduo
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.2.59+18

environment:
  sdk: ">=3.0.6 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # cupertino_icons: ^1.0.2

  # 状态管理
  get: ^4.6.5
  # 键值储存
  #  get_storage: ^2.0.2
  # shared_preferences: ^2.2.1
  # sp_util: ^2.0.3
  # 数据库
  sqflite: ^2.3.0
  # 事件总线（发送-接收）
  event_bus: ^2.0.0
  # 异步处理
  synchronized: ^3.1.0
  # 响应式编程
  # rxdart: ^0.27.7

  # 加密
  flutter_secure_storage: ^9.0.0
  pointycastle: ^3.7.4
  # encrypt: ^5.0.3

  # 检验网络连接
  connectivity_plus: ^4.0.2
  # 网络请求
  dio: ^5.3.2
  # 网络Cookie管理
  # CookieJar改为PersistCookieJar
  cookie_jar: ^4.0.8
  dio_cookie_manager: ^3.1.0+1
  # 网络请求缓存
  # dio_cache_interceptor: ^3.2.6

  # Dart-常用工具类库
  # common_utils:  ^3.0.0
  # Flutter-常用工具类库
  flustars: ^2.0.1
  # 屏幕适配
  flutter_screenutil: ^5.9.0
  # 获取应用信息
  package_info_plus: ^8.0.3
  # 获取设备信息
  device_info_plus: ^11.0.0
  # 日志打印
  logger: ^2.0.1
  # Toast提示框
  fluttertoast: ^8.2.2
  # 进度指示器
  # flutter_spinkit: ^5.2.0
  # 自定义分段控制
  custom_sliding_segmented_control: ^1.7.5
  # 可滑动+自动滚动折线图
  syncfusion_flutter_charts: ^27.1.51
  # 弹窗
  flutter_smart_dialog: ^4.9.4
  # 下拉弹窗
  #  gzx_dropdown_menu: ^3.1.0
  # 刷新组件
  pull_to_refresh: ^2.0.0
  # 可以滑动到指定index的listview
  # scrollable_positioned_list: ^0.3.8

  # 图片加载
  extended_image: ^8.1.0
  # 图片占位
  transparent_image: ^2.0.1
  # 图片缓存
  cached_network_image: ^3.3.0
  # 图片选择
  image_picker: ^1.0.3
  # 图片压缩
  # flutter_image_compress: ^2.0.4
  flutter_image_compress: ^1.1.1
  # 图片上传
  wechat_camera_picker: ^4.2.1
  wechat_assets_picker: ^9.3.2
  # 图片预览
  photo_view: ^0.15.0
  # 图片保存
  image_gallery_saver: ^2.0.3

  #  another_telephony: ^0.3.0 # 或者使用最新版本

  # 二维码扫描
  # qrscan: ^0.3.3
  # qr_code_scanner: ^1.0.1
  # flutter_barcode_scanner: ^2.0.0
  scan: ^1.5.0
  # 生成二维码
  qr_flutter: ^4.1.0
  # 文件打开
  open_file: ^3.3.2
  # 文件下载
  # flutter_downloader: ^1.11.1
  flutter_download_manager: ^0.5.5
  # 文件处理
  path_provider: ^2.0.10
  # 权限管理
  permission_handler: ^11.3.1
  # 生物识别
  # local_auth: ^2.1.7
  # 日期格式转化
  date_format: ^2.0.6
  # 字符串格式化
  sprintf: "^7.0.0"
  # 选择器库(日期及时间选择器（可设置范围）、单项选择器（可用于性别、民族、学历、星座、年龄、身高、体重、温度等）、城市地址选择器（分省级、地级及县级）、多项选择器等)
  flutter_pickers: ^2.1.9
  # 打开外部APP
  url_launcher: ^6.1.12
  # 友盟统计
  # umeng_common_sdk: ^1.2.6
  # 异常统计
  # sentry_flutter: ^7.9.0
  # 多渠道打包
  # package_by_walle: ^1.0.2
  # 键盘操作
  # keyboard_actions: ^4.2.0
  # 隐藏键盘
  flutter_keyboard_visibility: ^6.0.0
  # 国际化
  flutter_localization: ^0.2.2
  # bugly
  flutter_bugly: ^1.0.2
  #友盟统计
  umeng_common_sdk: ^1.2.3

  # 进步器
  timeline_tile: ^2.0.0

  # QQ开放平台（登录｜分享）
  # tencent_kit: ^6.0.1
  # 微信开放平台（登录｜分享｜支付）
  fluwx: ^4.4.9

  # 支付宝支付
  # tobias: ^3.2.0

  # 启动页
  # intro_slider: ^4.2.1
  # 轮播图（flutter_swiper）
  # flutter_swiper: ^1.1.6
  flutter_swiper_null_safety: ^1.0.2

  # Webview加载
  webview_flutter: ^4.4.2
  flutter_inappwebview: ^6.0.0
  # flutter_webview_plugin: ^0.4.0

  # 悬浮头部
  # sticky_headers: ^0.3.0+2
  # 小红点
  # badges: ^3.1.2
  # 桌面角标
  # flutter_app_badger: ^1.5.0
  # 登录注册
  # flutter_login: ^4.2.1
  # 安装
  install_plugin: ^2.1.0
  # install_plugin_v2: ^1.0.0 # 仅支持Android, 不干扰IOS

  # 蓝牙打印
  #  bluetooth_print: ^4.3.0

  # 高德地图
  amap_map_fluttify:
    path: amap_map_fluttify-2.0.2/
  amap_location_fluttify: 0.22.0
  amap_search_fluttify: 0.18.0

  print_bluetooth_thermal:
    path: print_bluetooth_thermal-main/
  network_logger: ^1.0.4
  tdesign_flutter: ^0.1.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # assets:

  # aliases:

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
