import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../response_data_models/b2b/b2bAppCustomer/b2b_app_customer_page/b2b_app_customer_list.dart';

class CustomerUserState {
  // 用户列表
  RxList<B2bAppCustomerList> userList = <B2bAppCustomerList>[].obs;

  // 搜索关键词
  RxString searchKeyword = ''.obs;

  // 加载状态
  RxBool isLoadingMore = false.obs;

  var total = 0.obs;

  // 编辑用户表单数据
  Rx<B2bAppCustomerList?> editingUser = Rx<B2bAppCustomerList?>(null);

  // 表单控制器
  // RxString userNumber = '13202025555'.obs;
  var userNumber = TextEditingController(text: '13202025555').obs;
  // RxString userName = '前端测试-用户'.obs;
  var userName = TextEditingController(text: '前端测试-用户').obs;
  RxList<String> selectedStores = <String>[].obs; // 改为多选门店列表
  // RxString address = '测试备注'.obs;
  var address = TextEditingController(text: '测试备注').obs;
  // RxString password = 'a12345678'.obs;
  var password = TextEditingController(text: 'a12345678').obs;
  RxString confirmPassword = 'a12345678'.obs;

  // 分页参数
  RxInt currentPage = 1.obs;
  RxInt pageSize = 10.obs;
  RxBool hasMore = true.obs;

  // 可选门店列表
  RxList<String> availableStores = <String>[
    '小林的店',
    '门店8566',
    '军哥专营店',
    '测试',
    '测试111',
    '6d店',
    '新华书店',
    '便利店',
    '超市',
    '专卖店'
  ].obs;

  // 表单验证错误
  RxString userNumberError = ''.obs;
  RxString userNameError = ''.obs;
  RxString selectedStoresError = ''.obs; // 门店选择错误信息
  RxString passwordError = ''.obs;
  RxString confirmPasswordError = ''.obs;

  // 是否显示密码
  RxBool showPassword = false.obs;
  RxBool showConfirmPassword = false.obs;

  // 门店下拉框状态
  RxBool isStoreDropdownExpanded = false.obs;

  // 创建一个定时器
  Timer? throttleTimer = null.obs.value;

  // 节流时间间隔（毫秒）
  var throttleDuration = 500.obs;
}

class CustomerUser {
  final String id;
  final String userNumber;
  final String userName;
  final String managerName;
  final String address;
  final String createTime;
  final bool isActive;

  CustomerUser({
    required this.id,
    required this.userNumber,
    required this.userName,
    required this.managerName,
    required this.address,
    required this.createTime,
    required this.isActive,
  });

  factory CustomerUser.fromJson(Map<String, dynamic> json) {
    return CustomerUser(
      id: json['id'] ?? '',
      userNumber: json['userNumber'] ?? '',
      userName: json['userName'] ?? '',
      managerName: json['managerName'] ?? '',
      address: json['address'] ?? '',
      createTime: json['createTime'] ?? '',
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userNumber': userNumber,
      'userName': userName,
      'managerName': managerName,
      'address': address,
      'createTime': createTime,
      'isActive': isActive,
    };
  }

  CustomerUser copyWith({
    String? id,
    String? userNumber,
    String? userName,
    String? managerName,
    String? address,
    String? createTime,
    bool? isActive,
  }) {
    return CustomerUser(
      id: id ?? this.id,
      userNumber: userNumber ?? this.userNumber,
      userName: userName ?? this.userName,
      managerName: managerName ?? this.managerName,
      address: address ?? this.address,
      createTime: createTime ?? this.createTime,
      isActive: isActive ?? this.isActive,
    );
  }
}
