class SignOutRequestBean {
  String? colonelId;
  String? latitude;
  String? longitude;
  String? consumerNo;
  String? signOutAddress;
  String? signOutLatitude;
  String? signOutLongitude;
  String? signOutDistance;
  String? remark;
  String? branchNo;

  SignOutRequestBean({
    this.colonelId,
    this.latitude,
    this.longitude,
    this.consumerNo,
    this.signOutAddress,
    this.signOutLatitude,
    this.signOutLongitude,
    this.signOutDistance,
    this.remark,
    this.branchNo,
  });

  factory SignOutRequestBean.fromJson(Map<String, dynamic> json) {
    return SignOutRequestBean(
      colonelId: json['colonelId']?.toString(),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      consumerNo: json['consumerNo']?.toString(),
      signOutAddress: json['signOutAddress']?.toString(),
      signOutLatitude: json['signOutLatitude']?.toString(),
      signOutLongitude: json['signOutLongitude']?.toString(),
      signOutDistance: json['signOutDistance']?.toString(),
      remark: json['remark']?.toString(),
      branchNo: json['branchNo']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'colonelId': colonelId,
      'latitude': latitude,
      'longitude': longitude,
      'consumerNo': consumerNo,
      'signOutAddress': signOutAddress,
      'signOutLatitude': signOutLatitude,
      'signOutLongitude': signOutLongitude,
      'signOutDistance': signOutDistance,
      'remark': remark,
      'branchNo': branchNo,
    };
  }
}

class SignOutResponseBean {
  String? code;
  String? msg;
  String? timestamp;
  dynamic data;

  SignOutResponseBean({
    this.code,
    this.msg,
    this.timestamp,
    this.data,
  });

  factory SignOutResponseBean.fromJson(Map<String, dynamic> json) {
    return SignOutResponseBean(
      code: json['code']?.toString(),
      msg: json['msg']?.toString(),
      timestamp: json['timestamp']?.toString(),
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'timestamp': timestamp,
      'data': data,
    };
  }
}
