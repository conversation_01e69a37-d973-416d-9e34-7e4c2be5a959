import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/widget/loading_dialog.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/domain/common_other_LoginInfo.dart';
import 'state.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:flustars/flustars.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/store/state.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:fuduoduo/pages/login/logic.dart';
import '../../../route/index.dart';
import 'package:fuduoduo/utils/time_utils.dart';

class B2bChooseTypeLogic extends GetxController {
  final B2bChooseTypeState state = B2bChooseTypeState();
  final MainState publicState = Get.find<Public>().state;
  final state1 = Get.find<LoginLogic>().state;
  var dialogContext;

  @override
  void onInit() {
    super.onInit();

    // if (Get.parameters["fromPage"] != null) {
    //   state.itemLists = Get.parameters["itemLists"]!;
    // }
  }

  //弹出loading弹出框
  void showLoadingDialog(BuildContext context, String text,
      {bool useRootNavigator = true}) {
    showDialog(
        context: context,
        useRootNavigator: useRootNavigator,
        barrierDismissible: false,
        builder: (buildContext) {
          dialogContext = buildContext;
          return LoadingDialog(
            outsideDismiss: false,
            loadingText: text,
          );
        });
  }

  // erp 登录完成后  获取app 的信息
  getLoginInfo() {
    MyDio.get(Apis.getLoginInfo, queryParameters: {}, successCallBack: (loginVerificationValue) {
      // logic.dismissDialog();

      var responseOther = CommonOtherLoginInfoBean.fromJson(loginVerificationValue);

      // 租户ID
      String tenant_id = '${responseOther.tenantId}';
      SpUtil.putString('tenant_id', tenant_id);

      List moduleArray = responseOther.appModules ?? [];
      if (moduleArray.isEmpty) {
        MyCommonUtils.showToast("没有任何功能权限,请联系管理员进行权限配置");
      }
      debugPrint("moduleList 1 ---- ${moduleArray}");
      List<String> moduleList = [];
      for (var element in moduleArray) {
        moduleList.add(element);
      }
      debugPrint("moduleList ---- ${moduleList}");
      // SpUtil.putStringList("appModules", moduleList);
      publicState.appModules = responseOther.appModules!;
      UserAppModuleUtils().saveUserAppModuls(moduleArray);

      // 登录完成请求个人信息
      _getUserInfo('', '');
    });
  }

  // 获取个人信息
  void _getUserInfo(String username, String password) {
    MyDio.get(Apis.getAppUserInfo, queryParameters: {},
        successCallBack: (value) {
          var response = DioResultBean.fromJson(value);
          if (response.code == '200') {
            dynamic data = response.data ?? {};
            SpUtil.putObject('userInfo', data);
            // print("userInfo=>login${SpUtil.getObject('userInfo')}");

            if ((SpUtil.getObject('userInfo') == null) ||
                (SpUtil.getObject('userInfo') == {})) {
              _getUserInfo(username, password);
              return;
            }

            SetInfo.instance.initLoad();

            getDealerNo();
            checkShowShopScan();
            Get.offAndToNamed(PageName.TAB);

            if (username == 'fddtyyh') return;

            if (state1.loginType.value == 1) {
              var accountInfo = SpUtil.getObject('accountInfo');
              if (accountInfo == null) {
                accountInfo = {};
              }
              String tenantName = '';
              if (null != data['enterprise']) {
                tenantName = data['enterprise']['systemName'];
              }
              username = username + '($tenantName)';

              accountInfo.removeWhere((key, value) {
                String key1 = key.toString();
                if (key1.contains('(')) {
                  key1 = key1.split('(')[0];
                }
                return key1 == username.split('(')[0];
              });
              accountInfo[username] = password +
                  '-' +
                  '${TimeUtils.getCurrentDateAndFormat([
                    "yyyy",
                    "/",
                    "mm",
                    "/",
                    "dd",
                    " ",
                    "HH",
                    ":",
                    "nn",
                    ":",
                    "ss"
                  ])}' +
                  '-' +
                  '${(data["employee"]?["employeeName"] ?? "")}';

              var sortedList = accountInfo.entries.toList()
                ..sort((b, a) {
                  String aValue = a.value;
                  List aValueList = aValue.split('-');
                  String bValue = b.value;
                  List bValueList = bValue.split('-');
                  String date1 = aValueList[1];
                  String date2 = bValueList[1];
                  return (date1).compareTo(date2);
                });
              print('================拼接后的数据==========${sortedList.toString()}');
              Map newAccountInfo = {};
              newAccountInfo.addEntries(sortedList);
              SpUtil.putObject('accountInfo', newAccountInfo);
              SpUtil.putString('currentAccount', username);
              SpUtil.putString('employeeNo', data['employee']['employeeNo']);
            }
          }
        });
  }
  // 获取经销商编号
  void getDealerNo() {
    MyDio.get(
      Apis.searchDealerNo,
      successCallBack: (value) {
        Map result = Map.from(value);
        if (result['code'].toString() == '200') {
          String dealerNo = result['data']['branchNo'].toString();
          SpUtil.putString('dealerNo', dealerNo);
        }
      },
    );
  }
  void checkShowShopScan() {
    dynamic userInfo = SpUtil.getObject('userInfo');
    dynamic employee = userInfo['employee'] ?? {};
    MyDio.get(Apis.checkShowShopScan,
        queryParameters: {'employeeNo': employee['employeeNo']},
        successCallBack: (value) {
          DioResultBean response = DioResultBean.fromJson(value);
          if (response.code.toString() == '200') {
            String data = response.data.toString();
            SpUtil.putString('showShopScan', data);
          }
        }, showErrMsg: false);
  }

  //关闭弹出框
  void dismissDialog() {
    if (dialogContext != null) {
      Navigator.pop(dialogContext);
    }
  }
}
