import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/MoneyFormartUtils.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:fuduoduo/widget/ImageLoad.dart';
import 'package:fuduoduo/widget/dropdown/drop_down_widget.dart';
import 'package:fuduoduo/widget/dropdown/dropdown_header.dart';
import 'package:fuduoduo/widget/dropdown/dropdown_menu.dart';
import 'package:fuduoduo/widget/dropdown/dropdown_menu_controller.dart';
import 'package:fuduoduo/x_utils/app_color.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../common/apis.dart';
import '../../../common/dio_utils.dart';
import '../../../domain/dio_default_result_bean.dart';
import '../../customer/info/logic.dart';
import 'logic.dart';
import 'package:fuduoduo/utils/scan_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/domain/order_meeting_list_bean.dart';
import '../../../resource/string_resource.dart';
import '../carts/logic.dart';

import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:fuduoduo/utils/product_filter_utils.dart';

class VehicleSalePage extends StatefulWidget {
  const VehicleSalePage({super.key});

  @override
  State<VehicleSalePage> createState() => _VehicleSalePageState();
}

class _VehicleSalePageState extends State<VehicleSalePage> {
  final logic = Get.find<VehicleSaleLogic>();
  final state = Get.find<VehicleSaleLogic>().state;
  final shopState = Get.find<CustomerInfoPageLogic>().state;
  final publicState = Get.find<Public>().state;

  final cartLogic = Get.put(VehicleCartsPageLogic());

  dynamic userSettings = {};

  double _leftWidth = 180.w; // 左边分类统一宽度
  String currentItemNo = '';
  int selectRadio = 0; // 所选规格下标
  dynamic currentUnit = null; // 所选单位

  double clsItemHeight = 80.w; // 分类组件高度;
  String inputEditType = 'num'; // num 数量,price 价格
  int inputEditIndex = 0; //修改下标

  bool isPoint= false; // 是否点击了点

  String updateFieldType = '';  // 1 修改单据单价 2 修改未税单价 3 修改金额

  var _isOpen = false;

  GlobalKey _stackKey = GlobalKey();

  GlobalKey _searchKey = GlobalKey();

  DropdownMenuController _dropdownMenuController = DropdownMenuController();
  Widget addIcon = new Image.asset('assets/images/add.png',
      width: 40.w,
      height: 40.w,
      alignment: Alignment.center,
      fit: BoxFit.cover);
  Widget addGreyIcon = new Image.asset('assets/images/add_grey.png',
      width: 40.w,
      height: 40.w,
      alignment: Alignment.center,
      fit: BoxFit.cover);

  @override
  void initState() {
    super.initState();
    dynamic userInfo = publicState.userInfo ?? {};
    userSettings = userInfo['userSettings'] ?? {};
    state.isTCTP = (publicState.userInfo?['userOrgSettings']
                ?['is_unified_warehouse_allocation'] ??
            '') ==
        '1';

    // state.is_app_org_order = (publicState.userInfo?['userOrgSettings']
    // ?['app_org_submit_order'] ??
    //     '') ==
    //     '1';
    state.is_app_org_order = (publicState.userInfo?['enterprise']
    ?['allowedOrgCount'] ??
        0) > 1;


    state.isFX = SpUtil.getString('branchNo') == '001';
    logic.getCartsInfo();
  }

  @override
  void dispose() {
    // 重置条件筛选
    ProductFilterUtils.resetData();
    state.stockQtyFlag = '';
    state.filter_branchNo = '';
    state.filter_brandNo = '';
    state.isSetStockQtyFlag = false;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<VehicleSaleLogic>(
        // id: StringResource.ORDER_SALE_BODY_ID,
        builder: (_) => Container(
                child: Column(
              children: [
                state.is_app_org_order ? _searchBox(context) : Offstage(offstage: true, child: Text('')),
                Expanded(child: _ContentBox(context)),
                _BottomBtn(context)
              ],
            )));
  }

  /// 底部弹窗添加数量
  void _showBottomSheet(List latestPriceList, goods, pCartGoods, cartTrans) {
    TextStyle _TextStyle = TextStyle(fontSize: 40.w);
    BoxDecoration _DivStyle = BoxDecoration(
        color: Colors.white, borderRadius: BorderRadius.circular(10.w));
    double _ItemWidth = 170.w;
    double _ItemHeight = 70.w;
    int itemRealStock = (goods['itemRealStock'] ?? 0).toInt();

    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    if (isActivity) {
      var activityId = state.activityClsList[state.selectedTwoClsIndex]['id'];
      if (activityId == null) return MyCommonUtils.showToast('订货会余额不足，请充值');
    }

    print("2=>cartData=>${goods}");
    // 重置选择
    if (currentItemNo != goods["itemNo"]) {
      setState(() {
        selectRadio = 0;
        currentUnit = null;
        inputEditType = 'num';
        inputEditIndex = 0;
        updateFieldType = '';
        isPoint = false;
      });
    }

    if ( !state.isAllowNoStocks && itemRealStock <= 0) {
      return MyCommonUtils.showToast('库存不足');
    }

    List _numList = [
      {"key": "1", "value": "1"},
      {"key": "2", "value": "2"},
      {"key": "3", "value": "3"},
      {"key": "4", "value": "4"},
      {"key": "5", "value": "5"},
      {"key": "6", "value": "6"},
      {"key": "7", "value": "7"},
      {"key": "8", "value": "8"},
      {"key": "9", "value": "9"},
      {"key": "0", "value": "0", "two": true},
      {"key": "·", "value": "."}
    ];

    //包装类型数组
    List packageList = [];
    var cartGoods = jsonDecode(jsonEncode(pCartGoods));
    List cartPackageList = cartGoods?['packageList'] ?? [];

    printLong("goods=>${goods}");
    printLong("cartGoods=>${cartGoods}");
    printLong("cartPackageList=>${cartPackageList}");
    printLong("currentItemNo=>${currentItemNo}");

    double suggestPrice = (goods['suggestPrice'] ?? 0).toDouble();
    double suggestPriceCentre = (goods['suggestPriceCentre'] ?? 0).toDouble();
    double suggestPriceMax = (goods['suggestPriceMax'] ?? 0).toDouble();

    // 大包装是否用于配送 0 否 1 是    控制是否显示
    if (goods['largeDistribution'] != "0" &&
        goods['itemLargeBoxUnit'] != null) {
      var package =
          cartPackageList.firstWhereOrNull((e) => e['packageType'] == '2');
      int itemLargeBoxNum = (goods['itemLargeBoxNum'] ?? 1).toInt();
      double realityPriceMax = (goods['realityPriceMax'] ?? 0).toDouble();
      double lowestPriceMax = (goods['lowestSellingPriceMax'] ?? 0).toDouble();




      if (package != null) {
        package['conversionQty'] = itemLargeBoxNum;
        package['originalPrice'] = realityPriceMax;
        package['lowestPrice'] = lowestPriceMax;

        package['suggestPrice'] = suggestPriceMax;
        // package['suggestPriceCentre'] = suggestPriceCentre;
        // package['suggestPriceMax'] = suggestPriceMax;

        package['itemPrice'] = realityPriceMax;

        packageList.add(package);
      } else {
        packageList.add({
          "packageType": "2",
          "conversionQty": itemLargeBoxNum,
          "itemPrice": realityPriceMax,
          "originalPrice": realityPriceMax,
          "lowestPrice": lowestPriceMax,

          "suggestPrice": suggestPriceMax,
          // "suggestPriceCentre": suggestPriceCentre,
          // "suggestPriceMax": suggestPriceMax,


          "itemUnit": goods['itemLargeBoxUnit'],
          "itemBarcode": goods['itemLargeBarcode'],
          "detailQty": 0, // 订购数量

          "title":
              "1${getItemUnitStr(goods['itemLargeBoxUnit'])}=${itemLargeBoxNum}${getItemUnitStr(goods['itemUnit'])}",
          "minDetailQty": 0,
          "detailPrice": 0.0,
        });
      }
    }

    // 中包装是否用于配送 0 否 1 是    控制是否显示
    if (goods['mediumDistribution'] != "0" &&
        goods['itemMediumBoxUnit'] != null) {
      var package =
          cartPackageList.firstWhereOrNull((e) => e['packageType'] == '1');
      int itemMediumBoxNum = (goods['itemMediumBoxNum'] ?? 1).toInt();
      double realityPriceCentre = (goods['realityPriceCentre'] ?? 0).toDouble();
      double lowestPriceCentre =
          (goods['lowestSellingPriceCentre'] ?? 0).toDouble();
      if (package != null) {
        package['conversionQty'] = itemMediumBoxNum;
        package['originalPrice'] = realityPriceCentre;
        package['lowestPrice'] = lowestPriceCentre;

        package['suggestPrice'] = suggestPriceCentre;
        // package['suggestPriceCentre'] = suggestPriceCentre;
        // package['suggestPriceMax'] = suggestPriceMax;

        package['itemPrice'] = realityPriceCentre;

        packageList.add(package);
      } else {
        packageList.add({
          "packageType": "1",
          "conversionQty": itemMediumBoxNum,
          "itemPrice": realityPriceCentre,
          "originalPrice": realityPriceCentre,
          "lowestPrice": lowestPriceCentre,
          "itemUnit": goods['itemMediumBoxUnit'],
          "itemBarcode": goods['itemMediumBarcode'],
          "detailQty": 0, // 订购数量

          "suggestPrice": suggestPriceCentre,
          // "suggestPriceCentre": suggestPriceCentre,
          // "suggestPriceMax": suggestPriceMax,

          "title":
              "1${getItemUnitStr(goods['itemUnit'])}=${itemMediumBoxNum}${getItemUnitStr(goods['itemUnit'])}",
          "minDetailQty": 0,
          "detailPrice": 0.0,
        });
      }
    }

    // 小包装是否用于配送 0 否 1 是    控制是否显示
    if (goods['isDistribution'] != "0") {
      var package =
          cartPackageList.firstWhereOrNull((e) => e['packageType'] == '0');
      double realityPrice = (goods['realityPrice'] ?? 0).toDouble();
      double lowestPrice = (goods['lowestSellingPrice'] ?? 0).toDouble();
      if (package != null) {
        package['originalPrice'] = realityPrice;
        package['lowestPrice'] = lowestPrice;

        package['suggestPrice'] = suggestPrice;
        // package['suggestPriceCentre'] = suggestPriceCentre;
        // package['suggestPriceMax'] = suggestPriceMax;
        package['itemPrice'] = realityPrice;


        packageList.add(package);
      } else {
        packageList.add({
          "packageType": "0",
          "itemBarcode": goods['itemBarcode'],
          "itemPrice": realityPrice,
          "itemUnit": goods['itemUnit'],
          "originalPrice": realityPrice,
          "lowestPrice": lowestPrice,
          "conversionQty": 1,
          "detailQty": 0, // 订购数量

          "suggestPrice": suggestPrice,
          // "suggestPriceCentre": suggestPriceCentre,
          // "suggestPriceMax": suggestPriceMax,

          "title": "最小单位",
          "minDetailQty": 0,
          "detailPrice": 0.0,
        });
      }
    }
    // 是否启动开单含税开单
    String? is_tax_included = SpUtil.getString('is_tax_included');
    // 是否启用商品税
    String? item_tax_rate_show = SpUtil.getString('item_tax_rate_show');
    // 默认税率
    String taxRate = goods['taxRate'].toString();


    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setDiaLogState) {
            return Container(
              // height: 300.h + 100.h * packageList.length + 100.h * (latestPriceList.length + 1) + 150.h * 1,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30.w),
                      topRight: Radius.circular(30.w))),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(width: 100.w),
                      Expanded(
                        child: Center(
                          child: Text(
                            '${goods["itemName"]}',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 34.sp,
                                color: Color(0xff333333),
                                fontWeight: FontWeight.w600),
                          )
                        )

                      ),

                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          height: 90.h,
                          width: 100.w,
                          alignment: Alignment.center,
                          child: Text(
                            '关闭',
                            style:
                                TextStyle(color: Colors.red, fontSize: 26.sp),
                          ),
                        ),
                      )
                    ],
                  ),
                  Visibility(
                    visible: latestPriceList.length > 0,
                    child: Container(
                      alignment: Alignment.centerLeft,
                      margin: EdgeInsets.only(left: 20.w),
                      child: Text(
                        '历史价格参考（近三次）:',
                        style: TextStyle(
                            fontSize: 28.sp, color: Color(0xff333333)),
                      ),
                    ),
                  ),
                  SizedBox(height: latestPriceList.length > 0 ? 16.h : 0.h),
                  Visibility(
                    visible: latestPriceList.length > 0,
                    child: Container(
                      margin: EdgeInsets.only(left: 20.w, right: 20.w),
                      color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                      child: Row(
                        children: [
                          Expanded(
                              flex: 2,
                              child: Container(
                                margin:
                                    EdgeInsets.only(top: 24.h, bottom: 24.h),
                                alignment: Alignment.center,
                                child: Text(
                                  '日期',
                                  style: TextStyle(
                                      fontSize: 28.sp,
                                      color: Color(0xff333333)),
                                ),
                              )),

                          item_tax_rate_show != '1' ?
                            Offstage(
                              offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                              child: Text(""),
                            )
                            : Expanded(
                              flex: 1,
                              child: Container(
                                margin:
                                EdgeInsets.only(top: 24.h, bottom: 24.h),
                                alignment: Alignment.center,
                                child: Text(
                                  '含税单价',
                                  style: TextStyle(
                                      fontSize: 28.sp,
                                      color: Color(0xff333333)),
                                ),
                            )),

                          item_tax_rate_show != '1' ?
                            Offstage(
                              offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                              child: Text(""),
                            )
                            : Expanded(
                              flex: 1,
                              child: Container(
                                margin:
                                EdgeInsets.only(top: 24.h, bottom: 24.h),
                                alignment: Alignment.center,
                                child: Text(
                                  '未税单价',
                                  style: TextStyle(
                                      fontSize: 28.sp,
                                      color: Color(0xff333333)),
                                ),
                            )),

                          item_tax_rate_show == '1' ?
                              Offstage(
                                offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                                child: Text(""),
                              )
                            : Expanded(
                              flex: 1,
                              child: Container(
                                margin:
                                    EdgeInsets.only(top: 24.h, bottom: 24.h),
                                alignment: Alignment.center,
                                child: Text(
                                  '单价',
                                  style: TextStyle(
                                      fontSize: 28.sp,
                                      color: Color(0xff333333)),
                                ),
                              )),
                          Expanded(
                              flex: 1,
                              child: Container(
                                margin:
                                    EdgeInsets.only(top: 24.h, bottom: 24.h),
                                alignment: Alignment.center,
                                child: Text(
                                  '单位',
                                  style: TextStyle(
                                      fontSize: 28.sp,
                                      color: Color(0xff333333)),
                                ),
                              )),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    child: Column(
                      children: List.generate(latestPriceList.length, (index) {
                        dynamic item = latestPriceList[index];

                        return Container(
                          margin: EdgeInsets.only(left: 20.w, right: 20.w),
                          decoration: BoxDecoration(
                            color: ColorResource.WHITE_COMMON_COLOR,
                            border: Border.all(
                                width: 1.w,
                                color: ColorResource
                                    .LIGHT_GRAY_LAYER_BACKGROUND_COLOR),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                  flex: 2,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: 24.h, bottom: 24.h),
                                    alignment: Alignment.center,
                                    child: Text(
                                      // '2023/10/10',
                                      (item["orderTime"] ?? "")
                                                  .toString()
                                                  .length >=
                                              10
                                          ? (item["orderTime"] ?? "")
                                              .toString()
                                              .substring(0, 10)
                                          : (item["orderTime"] ?? ""),
                                      style: TextStyle(
                                          fontSize: 28.sp,
                                          color: Color(0xff333333)),
                                    ),
                                  )),


                              item_tax_rate_show == '1' ?
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: 24.h, bottom: 24.h),
                                    alignment: Alignment.center,
                                    child: Text(
                                      // '¥604.12',
                                      "¥" +
                                          // (is_tax_included == '1' ? item["itemPrice"] : item["itemPrice"] * (1 + int.parse(taxRate) / 100)).toStringAsFixed(2),
                                          // 最近历史记录  不用计算 直接用接口字段
                                          item["itemPrice"].toStringAsFixed(2),
                                      style: TextStyle(
                                          fontSize: 28.sp,
                                          color: ColorResource.RED_TITLE_COLOR),
                                    ),
                                  )) :
                                Offstage(
                                  offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                                  child: Text(""),
                                ),

                              item_tax_rate_show == '1' ?
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: 24.h, bottom: 24.h),
                                    alignment: Alignment.center,
                                    child: Text(
                                      // '¥604.12',
                                      "¥" +
                                          // (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toStringAsFixed(2),
                                          // 最近历史记录  不用计算 直接用接口字段
                                          (item["unitPriceWithoutVat"] != null ? item["unitPriceWithoutVat"].toStringAsFixed(2) : '0'),
                                      style: TextStyle(
                                          fontSize: 28.sp,
                                          color: ColorResource.RED_TITLE_COLOR),
                                    ),
                                  )) :
                                Offstage(
                                  offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                                  child: Text(""),
                                ),

                              item_tax_rate_show == '1' ?
                              Offstage(
                                offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                                child: Text(""),
                              ) :
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: 24.h, bottom: 24.h),
                                    alignment: Alignment.center,
                                    child: Text(
                                      // '¥604.12',
                                      "¥" +
                                          item["itemPrice"].toStringAsFixed(2),
                                      style: TextStyle(
                                          fontSize: 28.sp,
                                          color: ColorResource.RED_TITLE_COLOR),
                                    ),
                                  )),
                              Expanded(
                                  flex: 1,
                                  child: Container(
                                    margin: EdgeInsets.only(
                                        top: 24.h, bottom: 24.h),
                                    alignment: Alignment.center,
                                    child: Text(
                                      // '捆',
                                      getItemUnitStr(item["itemUnit"]),
                                      style: TextStyle(
                                          fontSize: 28.sp,
                                          color: Color(0xff333333)),
                                    ),
                                  )),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Container(
                    // padding: EdgeInsets.only(right: 20.w, bottom: 20.w),
                    padding: EdgeInsets.only(right: 20.w),
                    child: Column(
                      children: List.generate(packageList.length, (index) {
                        dynamic item = packageList[index];

                        // print('xxxxxxxxxxxxxxxxxxxxxxx  $updateFieldType  ${item["itemPrice"]} ${updateFieldType != '2' || item["itemPrice"] == '0'}');
                        // 兼容修改金额时会更改的数据类型
                        if (item["itemPrice"] is String) {
                          item["itemPrice"] = double.parse(item["itemPrice"]);
                        } else if (item["itemPrice"] is num) {

                        }


                        // print('itemitemitem  === $item updateFieldType ${updateFieldType.isEmpty} ');
                        if (item_tax_rate_show == '1') {
                          item['taxRate'] = taxRate;


                          // updateFieldType.isEmpty 为true  就是没有进行改价操作   所以计算就按照原有的
                          if (updateFieldType.isEmpty) {
                            item['unitPriceVat'] = (is_tax_included == '1' ? item["itemPrice"] : item["itemPrice"] * (1 + int.parse(taxRate) / 100)).toString();
                            item['unitPriceWithoutVat'] = (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toString();
                            item['unitPriceNoVat'] = (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toStringAsFixed(2);
                          }





                          // 如果触发改价的时候
                          // 如果直接修改含税单价   含税单价不需要计算
                          // if (updateFieldType == '1') {
                          //   item['unitPriceVat'] = item["unitPriceVat"].toString();
                          // } else {
                          //   item['unitPriceVat'] = (is_tax_included == '1' ? item["itemPrice"] : item["itemPrice"] * (1 + int.parse(taxRate) / 100)).toString();
                          // }
                          //
                          // // item['unitPriceVat'] = (is_tax_included == '1' ? item["itemPrice"] : item["itemPrice"] * (1 + int.parse(taxRate) / 100)).toStringAsFixed(2);
                          // if (updateFieldType != '2' || item["itemPrice"] == '0') { // 如果修改的的是含税单价  需要重新计算
                          //   print('休息休息吧 $updateFieldType ${item["itemPrice"]}');
                          //   item['unitPriceWithoutVat'] = (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toString();
                          //   item['unitPriceNoVat'] = (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toStringAsFixed(2);
                          // }


                          // 保存改价前的价格
                          item['originalUnitPriceVat'] = (is_tax_included == '1' ? item["originalPrice"] : item["originalPrice"] * (1 + int.parse(taxRate) / 100)).toString();
                          item['originalUnitPriceWithoutVat'] = (is_tax_included == '1' ? item["originalPrice"]  / (1 + int.parse(taxRate) / 100) : item["originalPrice"]).toString();;
                        }

                        // 添加到购物车时   增加仓库字段
                        item['branchNo'] = goods['branchNo'] ?? '';
                        item['branchName'] = goods['branchName'] ?? '';

                        // print('itemitemitem2 $item');

                        return Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        setDiaLogState(() {
                                          selectRadio = index;
                                          inputEditIndex = index;
                                          inputEditType = 'num';
                                        });
                                        currentItemNo = goods["itemNo"];

                                        // goods['checkUnit'] = packageList[selectRadio]['itemUnit'];
                                        Navigator.pop(context);
                                        getLatestPriceList(
                                            0,
                                            packageList[selectRadio]['itemUnit'],
                                            goods,
                                            cartGoods,
                                            cartTrans);
                                      },
                                      child: Container(
                                        width: 320.w,
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: [
                                            // Radio(
                                            //     value: index,
                                            //     groupValue: selectRadio,
                                            //     onChanged: (value) {
                                            //       setDiaLogState(() {
                                            //         selectRadio = index;
                                            //         inputEditIndex = index;
                                            //         inputEditType = 'num';
                                            //         updateFieldType = '';
                                            //         // 保存当前的单位
                                            //         currentUnit = packageList[selectRadio]['itemUnit'];
                                            //       });
                                            //       currentItemNo = goods["itemNo"];
                                            //
                                            //       Navigator.pop(context);
                                            //       getLatestPriceList(
                                            //           0,
                                            //           packageList[selectRadio]
                                            //           ['itemUnit'],
                                            //           goods,
                                            //           cartGoods,
                                            //           cartTrans);
                                            //     }),

                                            Container(
                                              padding: EdgeInsets.only(top: 20.h, left: 20.h, right: 20.h, bottom: 20.h),
                                              child: InkWell(
                                                onTap: () {
                                                  setDiaLogState(() {
                                                    selectRadio = index;
                                                    inputEditIndex = index;
                                                    inputEditType = 'num';
                                                    updateFieldType = '';
                                                    // 保存当前的单位
                                                    currentUnit = packageList[selectRadio]['itemUnit'];
                                                  });
                                                  currentItemNo = goods["itemNo"];

                                                  Navigator.pop(context);
                                                  getLatestPriceList(
                                                      0,
                                                      packageList[selectRadio]
                                                      ['itemUnit'],
                                                      goods,
                                                      cartGoods,
                                                      cartTrans);
                                                },
                                                child: Icon(
                                                  selectRadio == index ? Icons.check_circle_rounded : Icons.radio_button_unchecked,
                                                  color: selectRadio == index ? ColorResource.RED_COMMON_COLOR : ColorResource.GRAY_COMMON_COLOR,
                                                ),
                                              ),
                                            ),


                                            Text(
                                              getItemUnitStr(item['itemUnit']),
                                              style: TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w500),
                                            ),
                                            SizedBox(
                                              width: 20.w,
                                            ),
                                            Text(
                                              logic.getPackageSizeUnit(goods, item),
                                              style: TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 26.sp),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),

                                  ],
                                ),

                                item_tax_rate_show != '1' ? Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Text('￥'),
                                    Column(
                                      children: [

                                        Row(
                                          crossAxisAlignment: CrossAxisAlignment.end,
                                          children: [
                                            Text('￥', style: TextStyle(
                                                fontSize: 26.sp,
                                                color: ColorResource.GRAY_EDIT_COLOR
                                            ),),
                                            InkWell(
                                              onTap: () {
                                                if (isActivity ||
                                                    goods['salesChangePrice'] !=
                                                        '1' ||
                                                    userSettings[
                                                    'edit_sales_price'] !=
                                                        '1') return;
                                                setDiaLogState(() {
                                                  inputEditType = 'price';
                                                  inputEditIndex = index;
                                                });
                                              },
                                              child: Container(
                                                margin: EdgeInsets.only(
                                                    left: 10.w, right: 10.w),
                                                alignment: Alignment.center,
                                                decoration: BoxDecoration(
                                                    color:
                                                    (inputEditType == 'price' &&
                                                        (inputEditIndex ==
                                                            index))
                                                        ? Colors.white  : ColorResource.COLOR_F2F3F5_COLOR,
                                                    border: Border.all(
                                                        width: 1.w,
                                                        color: (inputEditType ==
                                                            'price' &&
                                                            (inputEditIndex ==
                                                                index))
                                                            ? ColorResource.ORANGE_COMMON_COLOR
                                                            : Colors.white)),
                                                width: 135.w,
                                                height: 45.w,
                                                child: Text("${item['itemPrice']}"),
                                              ),
                                            ),
                                          ],
                                        ),



                                      ],
                                    ),

                                  ],
                                )  : Offstage(
                                  offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                                  child: Text(""),
                                ),


                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Offstage(
                                        // offstage: item['detailQty'] == 0 &&
                                        //     selectRadio != index,
                                        offstage: false,
                                        child: InkWell(
                                          onTap: () {
                                            if (item['detailQty'] == 0) return;
                                            setDiaLogState(() {
                                              packageList[index]['detailQty']--;
                                            });
                                          },
                                          child: Image.asset(
                                              'assets/images/reduce.png',
                                              width: 40.w,
                                              height: 40.w,
                                              alignment: Alignment.center,
                                              fit: BoxFit.cover),
                                        )),
                                    Visibility(
                                        visible: selectRadio == index,
                                        child: InkWell(
                                          onTap: () {
                                            setDiaLogState(() {
                                              inputEditType = 'num';
                                            });
                                          },



                                          child: Container(
                                            margin: EdgeInsets.only(
                                                left: 10.w, right: 10.w),
                                            alignment: Alignment.center,
                                            width: 110.w,
                                            height: 45.w,
                                            decoration: BoxDecoration(
                                                color: inputEditType == 'num' ? Colors.white  : ColorResource.COLOR_F2F3F5_COLOR,
                                                border: Border.all(
                                                    width: 1.w,
                                                    color: inputEditType == 'num'
                                                        ? ColorResource.ORANGE_COMMON_COLOR
                                                        : Colors.white)),
                                            child: Text("${item['detailQty']}"),
                                          ),

                                          // child: SizedBox(
                                          //     width: 100.w,
                                          //   child: TextField(
                                          //     maxLines: 1,
                                          //     minLines: 1,
                                          //     textAlign: TextAlign.center,
                                          //     keyboardType: TextInputType.text,
                                          //     textInputAction: TextInputAction.search,
                                          //     controller:
                                          //     TextEditingController(text: item['detailQty'].toString()),
                                          //     onChanged: (value) {
                                          //       // if (value  != '') {
                                          //       //   // callBack?.call(value: value);
                                          //       // }
                                          //       print('xxx $value');
                                          //     },
                                          //     onSubmitted: (value) {
                                          //       // callBack?.call(value: value);
                                          //       print('xxx onSubmitted $value');
                                          //     },
                                          //     decoration: InputDecoration(
                                          //         border: InputBorder.none,
                                          //         isCollapsed: true,
                                          //         hintText: '0',
                                          //         hintStyle: TextStyle(
                                          //             fontSize: 28.sp,
                                          //             color: ColorResource.GRAY_EDIT_COLOR
                                          //         )),
                                          //   ),
                                          // ),


                                        )),
                                    Visibility(
                                        // visible: selectRadio != index &&
                                        //     item['detailQty'] != 0,
                                        visible: selectRadio != index,
                                        child: Container(
                                          margin: EdgeInsets.only(
                                              left: 10.w, right: 10.w),
                                          alignment: Alignment.center,
                                          width: 110.w,
                                          height: 45.w,
                                          decoration: BoxDecoration(
                                              color: ColorResource.COLOR_F2F3F5_COLOR,
                                              border: Border.all(
                                                  width: 1.w,
                                                  color: Colors.white)),
                                          child: Text("${item['detailQty']}"),
                                        )),
                                    InkWell(
                                      onTap: () {
                                        setDiaLogState(() {
                                          packageList[index]['detailQty']++;
                                        });
                                      },
                                      child: Image.asset('assets/images/add.png',
                                          width: 40.w,
                                          height: 40.w,
                                          alignment: Alignment.center,
                                          fit: BoxFit.cover),
                                    ),
                                  ],
                                )
                              ],
                            ),

                            item_tax_rate_show != '1' && item['originalPrice'] != item['itemPrice'] ?
                            Row(
                              children: [
                                // SizedBox(
                                //   height: 5.h,
                                // ),
                                Row(
                                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Container(
                                      width: 360.w,
                                      height: 10.h,
                                      child: Text(''),
                                    ),
                                    SizedBox(
                                        height: 35.h,  // 设定子组件的高度
                                        child: Transform.translate(
                                            offset: Offset(0, -20.h),  // 向左平移 20 像素，模拟负的 margin
                                            child: Visibility(
                                                visible: item['originalPrice'] !=
                                                    item['itemPrice'],
                                                child: Text(
                                                    "原价: ${item['originalPrice']}", style: TextStyle(
                                                    fontSize: 26.sp
                                                )))
                                        ),
                                    ),

                                    Text(''),
                                  ],
                                )
                              ],
                            )
                                : Offstage(
                              offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                              child: Text(""),
                            ),

                            item_tax_rate_show == '1' ? Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Row(
                                  children: [
                                    SizedBox(width: 85.w),
                                    Text('含税单价 '),

                                    // 含税可以修改单价
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [

                                        Column(
                                          children: [
                                            Row(
                                              crossAxisAlignment: CrossAxisAlignment.end,
                                              children: [
                                                Text('￥', style: TextStyle(
                                                    fontSize: 26.sp,
                                                    color: ColorResource.GRAY_EDIT_COLOR
                                                ),),
                                                InkWell(
                                                  onTap: () {
                                                    if (isActivity ||
                                                        goods['salesChangePrice'] !=
                                                            '1' ||
                                                        userSettings[
                                                        'edit_sales_price'] !=
                                                            '1') return;
                                                    setDiaLogState(() {
                                                      inputEditType = 'price';
                                                      inputEditIndex = index;
                                                      updateFieldType = '1';
                                                    });
                                                    // 记录最后一次的点击的
                                                    item['updateFieldType'] = '1';
                                                  },
                                                  child: Container(
                                                    margin: EdgeInsets.only(
                                                        left: 5.w, right: 5.w),
                                                    alignment: Alignment.center,
                                                    decoration: BoxDecoration(
                                                        color:
                                                        (inputEditType == 'price' &&
                                                            (inputEditIndex ==
                                                                index) && (item['updateFieldType'] == '1'))
                                                            ? Colors.white  : ColorResource.COLOR_F2F3F5_COLOR,
                                                        border: Border.all(
                                                            width: 1.w,
                                                            color: (inputEditType ==
                                                                'price' &&
                                                                (inputEditIndex ==
                                                                    index) && (item['updateFieldType'] == '1'))
                                                                ? ColorResource.ORANGE_COMMON_COLOR
                                                                : Colors.white)),
                                                    width: 135.w,
                                                    height: 45.w,
                                                    child: Text("${double.parse(item['unitPriceVat']).toStringAsFixed(2)}"),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5.h,
                                            ),
                                            Visibility(
                                                visible: item['unitPriceVat'] !=
                                                    item['originalUnitPriceVat'],
                                                child: Text(
                                                    "原价: ${double.parse(item['originalUnitPriceVat']).toStringAsFixed(2)}", style: TextStyle(
                                                    fontSize: 26.sp
                                                )))
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),



                                // Text(
                                //   // '¥604.12',
                                //   "¥" +
                                //       (is_tax_included == '1' ? item["itemPrice"] : item["itemPrice"] * (1 + int.parse(taxRate) / 100)).toStringAsFixed(2),
                                //   style: TextStyle(
                                //       fontSize: 24.sp,
                                //       color: ColorResource.RED_TITLE_COLOR),
                                // ),

                                Row(
                                  children: [
                                    SizedBox(width: 10.w),
                                    Text('未税单价 '),

                                    // 未税可以修改单价
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [

                                        Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.end,
                                              children: [
                                                Text('￥', style: TextStyle(
                                                    fontSize: 26.sp,
                                                    color: ColorResource.GRAY_EDIT_COLOR
                                                )),
                                                InkWell(
                                                  onTap: () {
                                                    if (isActivity ||
                                                        goods['salesChangePrice'] !=
                                                            '1' ||
                                                        userSettings[
                                                        'edit_sales_price'] !=
                                                            '1') return;
                                                    setDiaLogState(() {
                                                      inputEditType = 'price';
                                                      inputEditIndex = index;
                                                      updateFieldType = '2';
                                                    });
                                                    // 记录最后一次的点击的
                                                    item['updateFieldType'] = '2';
                                                  },
                                                  child: Container(
                                                    margin: EdgeInsets.only(
                                                        left: 5.w, right: 5.w),
                                                    alignment: Alignment.center,
                                                    decoration: BoxDecoration(
                                                        color:
                                                        (inputEditType == 'price' &&
                                                            (inputEditIndex ==
                                                                index) && (item['updateFieldType'] == '2'))
                                                            ? Colors.white  : ColorResource.COLOR_F2F3F5_COLOR,
                                                        border: Border.all(
                                                            width: 1.w,
                                                            color: (inputEditType ==
                                                                'price' &&
                                                                (inputEditIndex ==
                                                                    index) && (item['updateFieldType'] == '2'))
                                                                ? ColorResource.ORANGE_COMMON_COLOR
                                                                : Colors.white)),
                                                    width: 135.w,
                                                    height: 45.w,
                                                    child: Text("${double.parse(item['unitPriceWithoutVat']).toStringAsFixed(2)}"),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5.h,
                                            ),
                                            Visibility(
                                                visible: item['unitPriceWithoutVat'] !=
                                                    item['originalUnitPriceWithoutVat'],
                                                child: Text(
                                                  "原价: ${double.parse(item['originalUnitPriceWithoutVat']).toStringAsFixed(2)}", style: TextStyle(
                                                    fontSize: 26.sp
                                                ),))
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                )


                                // Text(
                                //   // '¥604.12',
                                //   "¥" +
                                //       (is_tax_included == '1' ? item["itemPrice"]  / (1 + int.parse(taxRate) / 100) : item["itemPrice"]).toStringAsFixed(2),
                                //   style: TextStyle(
                                //       fontSize: 24.sp,
                                //       color: ColorResource.RED_TITLE_COLOR),
                                // )
                              ],
                            ) : Offstage(
                              offstage: true,  // 设置为 true，会将 widget 从布局树中移除
                              child: Text(""),
                            )


                          ],
                        );
                      }).toList(),
                    ),
                  ),

                  SizedBox(
                    height: 30.h,
                  ),

                  Container(
                    color: Colors.grey[200],
                    padding: EdgeInsets.all(18.w),
                    child: Row(
                      children: [
                        Expanded(
                            child: Wrap(
                          spacing: 10.w,
                          runSpacing: 10.w,
                          children: _numList.map((e) {
                            bool two = e['two'] ?? false;
                            return InkWell(
                              onTap: () {
                                if (inputEditType == 'num') {
                                  int buyNum =
                                      packageList[inputEditIndex]['detailQty'];
                                  // 如果是零直接替换 否者是累加
                                  String _num;
                                  if (buyNum == 0) {
                                    _num = e['value'];
                                  } else {
                                    _num = "${buyNum}${e['value']}";
                                  }
                                  setDiaLogState(() {
                                    packageList[inputEditIndex]['detailQty'] =
                                        int.parse(_num);
                                  });
                                } else if (inputEditType == 'price') {
                                  String price = '';

                                  // 插入含税逻辑
                                  if (item_tax_rate_show == '1') {
                                    if (updateFieldType == '1') {
                                      price = packageList[inputEditIndex]['unitPriceVat'];

                                    } else if(updateFieldType == '2') {
                                      price = packageList[inputEditIndex]['unitPriceWithoutVat'];
                                    }

                                  } else {
                                    price = packageList[inputEditIndex]['itemPrice'].toString();
                                  }


                                  // 会去掉小数部分，返回整数部分
                                  int _Price = double.parse(price).toInt();
                                  print('price  == $isPoint  ===  ${price} ${_Price}  ${price.contains('.') && ( _Price == '${_Price.toString()}.0' || _Price == '${_Price.toString()}.00' )}');
                                  if (price.contains('.') && ( price == '${_Price.toString()}.0' || price == '${_Price.toString()}.00' || double.parse(price).toStringAsFixed(2) == '${_Price.toString()}.00'  || double.parse(price).toStringAsFixed(3) == '${_Price.toString()}.000') ) {
                                    price = _Price.toString();
                                  }


                                  if (isPoint) {
                                    price = '$price.';
                                    setDiaLogState(() {
                                      isPoint= false;
                                    });
                                  }



                                  if (e['value'] == '.') {
                                    setDiaLogState(() {
                                      isPoint= true;
                                    });
                                  }

                                  print('xxxxx  ${state.pointNum} ---- ${price} ${(double.tryParse('2.35'))?.toInt()}  --- ${e['value']}');

                                  // print("e['value'] == '.' && price.contains('.')   ${e['value'] == '.' && price.contains('.')}");
                                  if (e['value'] == '.' && price.contains('.'))
                                    return;


                                  int dotCount = price.split('.').length - 1;
                                  if (dotCount > 1) return;


                                  // 如果是零直接替换 否者是累加
                                  String _num;
                                  if (price == "0" ||
                                      price == "0.00" ||
                                      price == "0.0" ||
                                      price == 0 ||
                                      price == '') {
                                    _num =
                                        e['value'] == '.' ? '0.' : e['value'];
                                  } else {
                                    _num = "${price}${e['value']}";
                                  }

                                  // print('_num  $_num');
                                  setDiaLogState(() {
                                    print('xxxxx $_num $updateFieldType $taxRate');

                                    // 含税逻辑
                                    if (item_tax_rate_show == '1') {
                                      String _p = '';
                                      if (updateFieldType == '1') {
                                        _p = _num;
                                        packageList[inputEditIndex]['unitPriceVat'] = _num;
                                        String _noTax = (double.parse(_num) / (1 + int.parse(taxRate) / 100)).toString();
                                        packageList[inputEditIndex]['unitPriceWithoutVat'] = _noTax ;
                                        packageList[inputEditIndex]['unitPriceNoVat'] = _noTax;
                                      } else if(updateFieldType == '2') {
                                        _p = (double.parse(_num) * (1 + int.parse(taxRate) / 100)).toString();
                                        packageList[inputEditIndex]['unitPriceWithoutVat'] = _num;
                                        packageList[inputEditIndex]['unitPriceNoVat'] = _num;
                                        packageList[inputEditIndex]['unitPriceVat'] = _p;
                                      }

                                      // 动态展示小数点



                                      // _p = double.parse(_p).toStringAsFixed(state.pointNum);
                                      // print('_p toStringAsFixed  $_p');
                                      packageList[inputEditIndex]['itemPrice'] = _p;

                                      // if (updateFieldType != '') {
                                      //   print('updateFieldType    ');
                                      //   packageList[inputEditIndex]['changePrice'] = true;
                                      // }


                                    } else {
                                      packageList[inputEditIndex]['itemPrice'] =
                                          _num;
                                    }

                                  });
                                }
                              },
                              child: Container(
                                  width: two ? 350.w : _ItemWidth,
                                  height: _ItemHeight,
                                  decoration: _DivStyle,
                                  alignment: Alignment.center,
                                  child: Text(
                                    "${e['key']}",
                                    style: _TextStyle,
                                  )),
                            );
                          }).toList(),
                        )),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: () {
                                setDiaLogState(() {
                                  if (inputEditType == 'num') {
                                    packageList[inputEditIndex]['detailQty'] =
                                        0;
                                  } else if (inputEditType == 'price') {
                                    packageList[inputEditIndex]['itemPrice'] =
                                        '0';

                                    if (item_tax_rate_show == '1') {
                                      packageList[inputEditIndex]['unitPriceWithoutVat'] ='0';
                                      packageList[inputEditIndex]['unitPriceNoVat'] = '0';
                                      packageList[inputEditIndex]['unitPriceVat'] = '0';
                                    }
                                  }
                                });
                              },
                              child: Container(
                                  width: _ItemWidth,
                                  height: _ItemHeight,
                                  margin: EdgeInsets.only(bottom: 10.w),
                                  decoration: _DivStyle,
                                  alignment: Alignment.center,
                                  child: Text(
                                    "清空",
                                    style: _TextStyle,
                                  )),
                            ),
                            InkWell(
                              onTap: () {
                                if (inputEditType == 'num') {
                                  String buyNum = packageList[inputEditIndex]
                                          ['detailQty']
                                      .toString();
                                  // 如果是零直接替换 否者是累加
                                  String newBuyNum =
                                      buyNum.substring(0, buyNum.length - 1);
                                  setDiaLogState(() {
                                    packageList[inputEditIndex]['detailQty'] =
                                        newBuyNum == ''
                                            ? 0
                                            : int.parse(newBuyNum);
                                  });
                                } else if (inputEditType == 'price') {
                                  String price = packageList[inputEditIndex]
                                          ['itemPrice']
                                      .toString();
                                  // 如果是零直接替换 否者是累加
                                  String newPrice =
                                      price.substring(0, price.length - 1);
                                  setDiaLogState(() {
                                    packageList[inputEditIndex]['itemPrice'] =
                                        newPrice == '' ? "0" : newPrice;
                                  });
                                }
                              },
                              child: Container(
                                  width: _ItemWidth,
                                  height: _ItemHeight,
                                  decoration: _DivStyle,
                                  margin: EdgeInsets.only(bottom: 10.w),
                                  alignment: Alignment.center,
                                  child: Text(
                                    "返回",
                                    style: _TextStyle,
                                  )),
                            ),
                            InkWell(
                              onTap: () {
                                if (cartGoods != null) {
                                  pCartGoods.addAll(cartGoods);
                                }
                                bool isNext = true;
                                for (var element in packageList) {
                                  var price = element['itemPrice'];
                                  if (price is String) {
                                    var _price = double.parse(price);
                                    element['itemPrice'] = _price;
                                  }
                                  if (element['itemPrice'] <
                                          element['lowestPrice'] &&
                                      element['detailQty'] > 0) {
                                    MyCommonUtils.showToast(
                                        '改价不能小于最低${element['lowestPrice']}');
                                    isNext = false;
                                    return;
                                  }
                                }

                                Map total =
                                    logic.getGoodsTotalInfo(packageList);
                                if (!state.isAllowNoStocks && itemRealStock < total['totalNum']) {
                                  return MyCommonUtils.showToast('购买数量超过库存数量');
                                }

                                if (isNext) {
                                  // print('xxx , ');
                                  goods['itemTotalQty'] = total['totalNum'];
                                  goods['itemTotalAmt'] = total['totalAmt'];
                                  goods['packageList'] = packageList;
                                  goods['branchNo'] = packageList[inputEditIndex]['branchNo'] ?? '';
                                  goods['branchName'] = packageList[inputEditIndex]['branchName'] ?? '';
                                  logic.updateCarts(goods, pCartGoods,
                                      cartTrans, shopState.cartData);

                                  cartLogic.selfCartSave(false);
                                  Navigator.pop(context);
                                }
                              },
                              child: Container(
                                  width: _ItemWidth,
                                  height: 150.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10.w),
                                      gradient: LinearGradient(colors: [
                                        Color.fromRGBO(255, 112, 29, 1),
                                        Color.fromRGBO(255, 41, 29, 1)
                                      ])),
                                  child: Text('完成',
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 30.w))),
                            )
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            );
          });
        });
  }

  // 底部按钮
  _BottomBtn(BuildContext context) {
    List<Color> _color = [
      Color.fromRGBO(255, 112, 29, 1),
      Color.fromRGBO(255, 41, 29, 1)
    ];
    return Container(
      decoration: BoxDecoration(color: Colors.white, boxShadow: [
        BoxShadow(
          color: Color.fromRGBO(24, 24, 24, .1),
          blurRadius: 5.w,
          spreadRadius: 0,
        ),
      ]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  SizedBox(
                    width: 20.w,
                  ),
                  Text(
                    '销售总金额:',
                    style: TextStyle(fontSize: 24.sp),
                  ),
                  Text('￥${(state.totalAmt).toStringAsFixed(2)}',
                      style: TextStyle(fontSize: 24.sp, color: Colors.red)),
                ],
              ),
              Row(
                children: [
                  SizedBox(
                    width: 20.w,
                  ),
                  Text(
                    '订货会总金额:',
                    style: TextStyle(fontSize: 24.sp),
                  ),
                  Text('￥${(state.activityTotalAmt).toStringAsFixed(2)}',
                      style: TextStyle(fontSize: 24.sp, color: Colors.red))
                ],
              ),
            ],
          ),
          InkWell(
            onTap: () {
              bool hasXS = shopState.cartData.any((e) => e['transNo'] == 'XS');
              bool hasHD = shopState.cartData.any((e) => e['transNo'] == 'HD');
              if (hasXS || hasHD) {
                Get.toNamed(PageName.VehicleCartsPage)?.then((value) {
                  logic.countTotal(shopState.cartData);
                  logic.countActivityTotal(shopState.cartData);
                });
              } else {
                MyCommonUtils.showToast("请选择购买的商品");
              }
            },
            child: Container(
              height: 90.w,
              width: 200.w,
              alignment: Alignment.center,
              decoration:
                  BoxDecoration(gradient: LinearGradient(colors: _color)),
              child: Text('去结算',
                  style: TextStyle(color: Colors.white, fontSize: 26.w)),
            ),
          )
        ],
      ),
    );
  }

  // 内容区域
  _ContentBox(BuildContext context) {
    return Row(
      children: [_Left(context), Expanded(child: _Right(context))],
    );
  }

  // 分类
  _Left(BuildContext context) {
    return Container(
      width: _leftWidth,
      child: ListView(
        padding: EdgeInsets.all(0),
        children: [
          _ActivityClsList(context),
          // 商品分类
          _oneClsList(context)
        ],
      ),
    );
  }

  // 一级分类
  _ActivityClsList(BuildContext context) {
    bool isSelect = state.selectedOneClsIndex == state.actIndex;
    // dynamic item = state.clsList[index];
    List<dynamic> children = state.activityClsList;

    return Offstage(
      offstage: children.length == 0,
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10.h),
        color: isSelect ? Colors.white : Colors.transparent,
        child: Column(
          children: [
            InkWell(
              onTap: () {
                logic.setSelectedOneClsIndex(state.actIndex);
              },
              child: Container(
                width: _leftWidth,
                height: 90.w,
                alignment: Alignment.centerLeft,
                child: Row(
                  children: [
                    Container(
                      margin: EdgeInsets.fromLTRB(0, 0, 10.w, 0),
                      width: 6.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: isSelect ? Colors.red : Colors.transparent,
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(3.w),
                            bottomRight: Radius.circular(3.w)),
                      ),
                    ),
                    Container(
                      width: 125.w,
                      child: Text(
                        "订货会",
                        style: TextStyle(
                            fontSize: 28.w,
                            overflow: TextOverflow.ellipsis,
                            color: isSelect ? Colors.black : Colors.grey[400]),
                      ),
                    ),
                    Icon(
                      Icons.expand_more,
                      size: 35.w,
                      color: isSelect
                          ? Colors.black
                          : const Color.fromARGB(0, 99, 95, 95),
                    )
                  ],
                ),
              ),
            ),
            _ActivityTwoClsList(context, children, isSelect)
          ],
        ),
      ),
    );
  }

  // 二级分类
  _ActivityTwoClsList(BuildContext context, List children, bool isSelect) {
    return Container(
      height: (isSelect && state.isOpenExpand)
          ? clsItemHeight * children.length
          : 0,
      child: Column(
        children: List.generate(children.length, (index) {
          bool isSelect = state.selectedTwoClsIndex == index;
          dynamic item = children[index];
          return InkWell(
            onTap: () {
              logic.setSelectedTowClsIndex(index);
            },
            child: Container(
              width: _leftWidth,
              height: clsItemHeight,
              padding: EdgeInsets.fromLTRB(15.w, 0, 0, 0),
              alignment: Alignment.centerLeft,
              child: Text(
                "${item["activityName"]}",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 24.w,
                    color: isSelect ? Colors.red[600] : Colors.black87),
              ),
            ),
          );
        }),
      ),
    );
  }

  // 一级分类
  _oneClsList(BuildContext context) {
    List<Widget> clsItemList = List.generate(
      state.clsList.length,
      (index) {
        bool isSelect = state.selectedOneClsIndex == index;
        dynamic item = state.clsList[index];
        dynamic children = item["children"] ?? [];
        return Container(
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10.h),
          color: isSelect ? Colors.white : Colors.transparent,
          child: Column(
            children: [
              InkWell(
                onTap: () {
                  logic.setSelectedOneClsIndex(index);
                },
                child: Container(
                  width: _leftWidth,
                  height: 90.w,
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      Container(
                        margin: EdgeInsets.fromLTRB(0, 0, 10.w, 0),
                        width: 6.w,
                        height: 20.h,
                        decoration: BoxDecoration(
                          color: isSelect ? Colors.red : Colors.transparent,
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(3.w),
                              bottomRight: Radius.circular(3.w)),
                        ),
                      ),
                      Container(
                        width: 125.w,
                        child: Text(
                          "${item['clsName']}",
                          style: TextStyle(
                              fontSize: 28.w,
                              overflow: TextOverflow.ellipsis,
                              color:
                                  isSelect ? Colors.black : Colors.grey[400]),
                        ),
                      ),
                      Icon(
                        Icons.expand_more,
                        size: 35.w,
                        color: isSelect ? Colors.black : Colors.transparent,
                      )
                    ],
                  ),
                ),
              ),
              _twoClsList(context, children, isSelect)
            ],
          ),
        );
      },
    );

    return Column(
      children: clsItemList,
    );
  }

  // 二级分类
  _twoClsList(BuildContext context, List children, bool isSelect) {
    return Container(
      height: (isSelect && state.isOpenExpand)
          ? clsItemHeight * children.length
          : 0,
      child: Column(
        children: List.generate(children.length, (index) {
          bool isSelect = state.selectedTwoClsIndex == index;
          dynamic item = children[index];
          return InkWell(
            onTap: () {
              logic.setSelectedTowClsIndex(index);
            },
            child: Container(
              width: _leftWidth,
              height: clsItemHeight,
              padding: EdgeInsets.fromLTRB(15.w, 0, 0, 0),
              alignment: Alignment.centerLeft,
              child: Text(
                "${item["clsName"]}",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 24.w,
                    color: isSelect ? Colors.red[600] : Colors.black87),
              ),
            ),
          );
        }),
      ),
    );
  }

  void _getWidgetHeight() {
    var _height;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final RenderBox? renderBox = _searchKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final offset = renderBox.localToGlobal(Offset.zero);
        final _height = (offset.dy ?? 0.0).abs() + 46;
        print('Widget 到顶部的高度: $_height');



        Navigator.of(context).push(TDSlidePopupRoute(
            modalTop: _height,
            modalBarrierColor: TDTheme.of(context).fontGyColor2,
            slideTransitionFrom: SlideTransitionFrom.top,
            open: () {
              // print('open');
            },
            opened: () {
              // print('opened');
              _isOpen = true;
              logic.filterUpdate();
            },
            close: () {
              if ( state.isSetStockQtyFlag || state.filter_branchNo.isNotEmpty || state.filter_brandNo.isNotEmpty) {
                _isOpen = true;
              } else {
                _isOpen = false;
              }

              logic.filterUpdate();
            },
            builder: (context) {

              return GetBuilder<VehicleSaleLogic>(
                  id: StringResource.ORDER_SALE_FILTTER_SEARCH,
                  builder: (controller) {
                    return Container(
                      // key: _searchKey,
                      color: Colors.white,
                      height: 500.h,
                      child: ProductFilterUtils.DropdownMenu(context,
                          confirmCb: ({Map? queryParameters }){
                            state.stockQtyFlag = queryParameters?['Inventory'];
                            state.filter_branchNo = queryParameters?['branchNo'];
                            state.filter_brandNo = queryParameters?['brandNo'];
                            if (state.searchText.isNotEmpty) state.searchText = '';
                            // _dropdownMenuController.hide();

                            if (state.stockQtyFlag != null ) state.isSetStockQtyFlag = true;

                            logic.getActivityClsTreeNode(false);
                            state.goodsList.clear();
                            logic.getItemByCls(type: 'TDSlidePopup', cb: () {
                              Navigator.maybePop(context);
                            });



                          },
                          cancelCb: (){
                            // print('xxxx 222');
                            state.stockQtyFlag = '';
                            state.filter_branchNo = '';
                            state.filter_brandNo = '';
                            if (state.searchText.isNotEmpty) state.searchText = '';

                            state.isSetStockQtyFlag = false;

                            logic.getActivityClsTreeNode(false);
                            state.goodsList.clear();
                            logic.getItemByCls(type: 'TDSlidePopup', cb: () {
                              Navigator.maybePop(context);
                            });

                            logic.filterUpdate();
                            // Navigator.maybePop(context);
                          }
                      ),
                    );
                  }
              );


            }));





      } else {
        // print('无法获取 RenderBox, key.currentContext 为 null');
         // 如果无法获取 RenderBox，返回默认值
      }
    });
    // return _height ?? 0.0; // 需要返回一个默认值，如果你有异步操作可以改成 Future<double>
  }

  _searchBox(BuildContext context) {
    // var _isOpen = false;
    return GetBuilder<VehicleSaleLogic>(
        id: StringResource.ORDER_SALE_SEARCH,
        builder: (controller) {
          return Container(
            color: Color(0xFFFFFFFF),  // 使用自定义颜色
            child: Row(
              children: [
                // Expanded(
                //   child: Container(
                //     alignment: Alignment.center,
                //     margin: EdgeInsets.all(10.w),
                //     height: 72.h,
                //     decoration: BoxDecoration(
                //         color: Colors.grey[200],
                //         borderRadius: BorderRadius.circular((40.0))),
                //     child: TextField(
                //       maxLines: 1,
                //       minLines: 1,
                //       textAlign: TextAlign.center,
                //       keyboardType: TextInputType.text,
                //       textInputAction: TextInputAction.search,
                //       controller:
                //           TextEditingController(text: state.searchText),
                //       onChanged: (value) {
                //         if (value == '' && state.searchText != '') {
                //           logic.confirmSearch(value);
                //         }
                //       },
                //       onSubmitted: (value) {
                //         logic.confirmSearch(value);
                //       },
                //       decoration: InputDecoration(
                //           border: InputBorder.none,
                //           isCollapsed: true,
                //           hintText: '请输入商品名称/编码/条码',
                //           hintStyle: TextStyle(fontSize: 28.sp)),
                //     ),
                //   ),
                // ),
                Expanded(
                  child: ScanUtils.SearchBar(context, searchText: state.searchText, callBack: ({String? value, bool isScan = false}){
                    // print('xxxxxxxxxxxxxx $value');
                    logic.confirmScanSearch(context, value!, isScan!, (goods, cartGoods, cartTrans){
                      // print('view ==goods, cartGoods, cartTrans  ${goods} ${cartGoods} ${cartTrans}');

                      String unit = goods["itemLargeBoxUnit"] != null
                          ? goods["itemLargeBoxUnit"]
                          : goods["itemMediumBoxUnit"] != null
                          ? goods["itemMediumBoxUnit"]
                          : goods["itemUnit"];
                      getLatestPriceList(
                          0, unit, goods, cartGoods, cartTrans);
                    });
                  }),
                ),

                InkWell(
                  onTap: () {
                    _getWidgetHeight();
                    // print('xxx $_height');
                    // Navigator.of(context).push(TDSlidePopupRoute(
                    //     modalTop: 340.h,
                    //     modalBarrierColor: TDTheme.of(context).fontGyColor2,
                    //     slideTransitionFrom: SlideTransitionFrom.top,
                    //     open: () {
                    //       // print('open');
                    //     },
                    //     opened: () {
                    //       // print('opened');
                    //       _isOpen = true;
                    //       logic.filterUpdate();
                    //     },
                    //     close: () {
                    //       if ( state.isSetStockQtyFlag || state.filter_branchNo.isNotEmpty || state.filter_brandNo.isNotEmpty) {
                    //         _isOpen = true;
                    //       } else {
                    //         _isOpen = false;
                    //       }
                    //
                    //       logic.filterUpdate();
                    //     },
                    //     builder: (context) {
                    //
                    //       return GetBuilder<VehicleSaleLogic>(
                    //         id: StringResource.ORDER_SALE_FILTTER_SEARCH,
                    //         builder: (controller) {
                    //           return Container(
                    //             key: _searchKey,
                    //             color: Colors.white,
                    //             height: 520.h,
                    //             child: ProductFilterUtils.DropdownMenu(context,
                    //                 confirmCb: ({Map? queryParameters }){
                    //                   state.stockQtyFlag = queryParameters?['Inventory'];
                    //                   state.filter_branchNo = queryParameters?['branchNo'];
                    //                   state.filter_brandNo = queryParameters?['brandNo'];
                    //                   if (state.searchText.isNotEmpty) state.searchText = '';
                    //                   // _dropdownMenuController.hide();
                    //
                    //                   if (state.stockQtyFlag != null ) state.isSetStockQtyFlag = true;
                    //
                    //                   logic.getActivityClsTreeNode(false);
                    //                   state.goodsList.clear();
                    //                   logic.getItemByCls(type: 'TDSlidePopup', cb: () {
                    //                     Navigator.maybePop(context);
                    //                   });
                    //
                    //
                    //
                    //                 },
                    //                 cancelCb: (){
                    //                   // print('xxxx 222');
                    //                   state.stockQtyFlag = '';
                    //                   state.filter_branchNo = '';
                    //                   state.filter_brandNo = '';
                    //                   if (state.searchText.isNotEmpty) state.searchText = '';
                    //
                    //                   state.isSetStockQtyFlag = false;
                    //
                    //                   logic.getActivityClsTreeNode(false);
                    //                   state.goodsList.clear();
                    //                   logic.getItemByCls(type: 'TDSlidePopup', cb: () {
                    //                     Navigator.maybePop(context);
                    //                   });
                    //
                    //                   logic.filterUpdate();
                    //                   // Navigator.maybePop(context);
                    //                 }
                    //             ),
                    //           );
                    //         }
                    //       );
                    //
                    //
                    //     }));
                  },
                  child: Container(
                    key: _searchKey,
                    height: 88.w,
                    padding: EdgeInsets.all(10.w),
                    child: Icon(
                      Icons.filter_alt,
                      size: 34.w,
                      color: _isOpen ? ColorResource.ORANGE_COMMON_COLOR : ColorResource.BLACK_COMMON_COLOR
                    ),
                  ),
                )
                  // Container(
                  //   height: 104.w,
                  //   width: 120.w,
                  //   child: DropDownHeader(
                  //     item: DropDownHeaderItem(
                  //       state.select_tv,
                  //       style: TextStyle(color: Colors.black),
                  //       iconData: Icons.segment,
                  //       iconDropDownData: Icons.segment,
                  //     ),
                  //     stackKey: _stackKey,
                  //     controller: _dropdownMenuController,
                  //     borderWidth: 0,
                  //     borderColor: Colors.white,
                  //     style: TextStyle(
                  //         color: Color(0xFF666666), fontSize: 26.sp),
                  //     dropDownStyle: TextStyle(
                  //       fontSize: 26.sp,
                  //       color: ColorResource.BLACK_COMMON_COLOR,
                  //     ),
                  //     iconDropDownColor: ColorResource.BLACK_COMMON_COLOR,
                  //   )
                  // )


                // Icon(TDIcons('filter_1_filled')),


              ],
            )
          );

    });
  }

  // 商品列表
  _Right(BuildContext context) {
    List<Color> _color = [
      Color.fromRGBO(255, 112, 29, 1),
      Color.fromRGBO(255, 41, 29, 1)
    ];
    List<dynamic> goodsListData = [];
    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    double activityMoney = 0;
    bool isActivityRun = false;
    List<dynamic> threeClsList = [];
    // 是订货会
    if (isActivity) {
      goodsListData = state.activityGoodsList;
      var activity = state.activityClsList[state.selectedTwoClsIndex];
      activityMoney = activity['usableAmount'] ?? 0;
      DateTime endDate = DateTime.parse(activity['endDate'] ?? '');
      isActivityRun = DateTime.now().isBefore(endDate);
    } else {
      goodsListData = state.goodsList ?? [];
      List<dynamic> clsList = state.clsList ?? [];
      int selectedOneClsIndex = state.selectedOneClsIndex ?? 0;
      if (clsList.length != 0) {
        dynamic oneItem = clsList[selectedOneClsIndex] ?? {};
        List twochildren = oneItem['children'] ?? [];
        dynamic twoItem = twochildren.length == 0
            ? {}
            : twochildren[state.selectedTwoClsIndex];
        threeClsList = twoItem['children'] ?? [];
      }
    }
    return Container(
      color: Colors.white,
      child: Stack(
        key: _stackKey,
        children: [
          Column(
            children: [
              Offstage(
                offstage: threeClsList.length == 0,
                child: Container(
                  margin: EdgeInsets.only(top: 10.w),
                  height: 50.w,
                  child: ListView.builder(
                      itemCount: threeClsList.length,
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (goods, index) {
                        bool isSelected = state.selectedThreeClsIndex == index;
                        dynamic item = threeClsList[index];
                        return InkWell(
                          onTap: () {
                            logic.setSelectedThreeClsIndex(index);
                          },
                          child: Container(
                            margin: EdgeInsets.only(left: 20.w),
                            padding: EdgeInsets.only(right: 20.w, left: 20.w),
                            height: 20.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(50.w),
                              color: isSelected
                                  ? Color.fromRGBO(253, 230, 233, 1)
                                  : Colors.grey[200],
                            ),
                            child: Text(
                              "${item['clsName']}",
                              style: TextStyle(
                                color: isSelected
                                    ? Color.fromRGBO(197, 89, 103, 1)
                                    : Colors.grey[800],
                              ),
                            ),
                          ),
                        );
                      }),
                ),
              ),


              !state.is_app_org_order ? Row(
                children: [
                  // Expanded(
                  //   child: Container(
                  //     alignment: Alignment.center,
                  //     margin: EdgeInsets.all(10.w),
                  //     height: 72.h,
                  //     decoration: BoxDecoration(
                  //         color: Colors.grey[200],
                  //         borderRadius: BorderRadius.circular((40.0))),
                  //     child: TextField(
                  //       maxLines: 1,
                  //       minLines: 1,
                  //       textAlign: TextAlign.center,
                  //       keyboardType: TextInputType.text,
                  //       textInputAction: TextInputAction.search,
                  //       controller:
                  //           TextEditingController(text: state.searchText),
                  //       onChanged: (value) {
                  //         if (value == '' && state.searchText != '') {
                  //           logic.confirmSearch(value);
                  //         }
                  //       },
                  //       onSubmitted: (value) {
                  //         logic.confirmSearch(value);
                  //       },
                  //       decoration: InputDecoration(
                  //           border: InputBorder.none,
                  //           isCollapsed: true,
                  //           hintText: '请输入商品名称/编码/条码',
                  //           hintStyle: TextStyle(fontSize: 28.sp)),
                  //     ),
                  //   ),
                  // ),
                  Expanded(
                    child: ScanUtils.SearchBar(context, searchText: state.searchText, callBack: ({String? value, bool isScan = false}){
                      // print('xxxxxxxxxxxxxx $value');
                      logic.confirmScanSearch(context, value!, isScan!, (goods, cartGoods, cartTrans){
                        // print('view ==goods, cartGoods, cartTrans  ${goods} ${cartGoods} ${cartTrans}');

                        String unit = goods["itemLargeBoxUnit"] != null
                            ? goods["itemLargeBoxUnit"]
                            : goods["itemMediumBoxUnit"] != null
                            ? goods["itemMediumBoxUnit"]
                            : goods["itemUnit"];
                        getLatestPriceList(
                            0, unit, goods, cartGoods, cartTrans);
                      });
                    }),
                  ),

                  Container(
                      height: 104.w,
                      width: 120.w,
                      child: DropDownHeader(
                        item: DropDownHeaderItem(
                          state.select_tv,
                          style: TextStyle(color: Colors.black),
                          iconData: Icons.segment,
                          iconDropDownData: Icons.segment,
                        ),
                        stackKey: _stackKey,
                        controller: _dropdownMenuController,
                        borderWidth: 0,
                        borderColor: Colors.white,
                        style: TextStyle(
                            color: Color(0xFF666666), fontSize: 26.sp),
                        dropDownStyle: TextStyle(
                          fontSize: 26.sp,
                          color: ColorResource.BLACK_COMMON_COLOR,
                        ),
                        iconDropDownColor: ColorResource.BLACK_COMMON_COLOR,
                      ))
                ],
              ) : Offstage(offstage: true, child: Text('')),

              Expanded(
                  child: Container(
                padding: EdgeInsets.only(
                  left: 20.w,
                  right: 20.w,
                ),
                child: Column(
                  children: [
                    Visibility(
                        visible: isActivity,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(children: [
                              Text('订货会余额：', style: TextStyle(fontSize: 28.w)),
                              Text(
                                "￥${activityMoney.toStringAsFixed(2)}",
                                style: TextStyle(
                                    color: Colors.red, fontSize: 28.w),
                              )
                            ]),
                            Visibility(
                                visible: isActivityRun,
                                child: InkWell(
                                  onTap: () {
                                    dynamic clsItem = state.activityClsList[
                                        state.selectedTwoClsIndex];
                                    if (clsItem['usableAmount'] == null)
                                      clsItem['usableAmount'] = 0;
                                    OrderMeetingBean item =
                                        OrderMeetingBean.fromJson(clsItem);
                                    logic.showOrderMeetingChargeDialog(
                                        context, item);
                                  },
                                  child: Container(
                                    height: 50.w,
                                    width: 140.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        gradient:
                                            LinearGradient(colors: _color),
                                        borderRadius:
                                            BorderRadius.circular(25.w)),
                                    child: Text('充值',
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 26.w)),
                                  ),
                                ))
                          ],
                        )),
                    SizedBox(
                      height: 20.w,
                    ),
                    Expanded(
                        child: SmartRefresher(
                      controller: logic.refreshController,
                      onLoading: () {
                        if (state.selectedOneClsIndex == state.actIndex) {
                          logic.getActivityItemList(isLoadMore: true);
                        } else {
                          logic.getItemByCls(isLoadMore: true);
                        }
                      },
                      enablePullUp: true,
                      enablePullDown: false,
                      child: ListView.builder(
                          controller: logic.scrollController,
                          padding: EdgeInsets.zero,
                          itemCount: goodsListData.length,
                          itemBuilder: (context, index) {
                            var item = goodsListData[index];
                            return _GoodsItem(context, item);
                          }),
                    ))
                  ],
                ),
              ))
            ],
          ),
          DropDownMenu(
            controller: _dropdownMenuController,
            animationMilliseconds: 300,
            menus: [
              DropdownMenuBuilder(
                  dropDownHeight: 40.0 * state.selectList.length,
                  dropDownWidget: DropDownWidget(
                    items: state.selectList,
                    callback: (value) {
                      state.select_tv = value.name;
                      state.stockQtyFlag = value.type;
                      _dropdownMenuController.hide();
                      logic.getActivityClsTreeNode(false);
                      state.goodsList.clear();
                      logic.getItemByCls();
                      print('======选择完成');
                      setState(() {});
                    },
                  )),
            ],
          ),
        ],
      ),
    );
  }

  // 商品列表
  Widget _GoodsItem(BuildContext context, dynamic goods) {
    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    String itemNo = goods['itemNo'] ?? '';
    String _branchNo = goods['branchNo'] ?? '';

    var cartTrans = null;
    if (isActivity) {
      String activityId =
          state.activityClsList[state.selectedTwoClsIndex]['id'] ?? '';
      cartTrans = shopState.cartData.firstWhereOrNull((e) =>
          e['transNo'] == 'HD' &&
          e['activityOrderId'] == activityId &&
          e['deliveryType'] == (goods['itemDeliveryType'] ?? 1));
    } else {
      cartTrans = shopState.cartData.firstWhereOrNull((e) =>
          e['transNo'] == 'XS' &&
          e['deliveryType'] == (goods['itemDeliveryType'] ?? 1));
    }
    var cartGoods = ((cartTrans?['detailList'] ?? []) as List).firstWhereOrNull(
        (goods) => goods['itemNo'] == itemNo && goods['branchNo'] == _branchNo && goods['itemType'] == '0');

    var promotionGoodInfo = state.promotionInfo[itemNo];
    // print('goodsgoods $goods ');
    // print(' === ${cartTrans?['detailList']}');
    String? item_tax_rate_show = SpUtil.getString('item_tax_rate_show');
    String? is_tax_included = SpUtil.getString('is_tax_included');
    if (item_tax_rate_show == '1') {
      double _realityPrice = is_tax_included == '1' ? goods["realityPrice"] : (goods["realityPrice"] * (1 + goods['taxRate'] / 100));
      goods['realityPriceNew'] = double.parse(_realityPrice.toStringAsFixed(2));
    } else {
      goods['realityPriceNew'] = goods["realityPrice"];
    }

    return Container(
      padding: EdgeInsets.only(bottom: 20.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              ImageLoad.loadWidget(goods['itemImg'] ?? "", size: 130),
              Positioned(
                  child: Visibility(
                visible: (promotionGoodInfo != null) && !isActivity,
                child: InkWell(
                  onTap: () {
                    _showPromotionInfo(itemNo, goods);
                  },
                  child: Container(
                    padding: EdgeInsets.fromLTRB(15.w, 5.w, 15.w, 5.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: Color.fromRGBO(15, 7, 8, 0.5),
                        borderRadius: BorderRadius.circular(30.r)),
                    child: Text(
                      (promotionGoodInfo?['info'][0]?['act']?["applicableType"] ??
                                  '') ==
                              1
                          ? '满赠'
                          : '每赠',
                      style: TextStyle(fontSize: 24.sp, color: Colors.white),
                    ),
                  ),
                ),
              ))
            ],
          ),
          SizedBox(
            width: 20.w,
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${goods['itemName']}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    fontSize: 26.sp,
                    color: Color(0xff333333),
                    fontWeight: FontWeight.w600),
              ),

              Row(
                  children: [
                    Expanded(
                        child: Text(
                          "${goods['itemBarcode']}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(color: Colors.grey[600], fontSize: 24.w),
                        )
                    ),
                  ]
              ),

              state.is_app_org_order && goods['branchName'] != null && !goods['branchName'].isEmpty ? Row(
                  children: [
                    Expanded(
                        child: Text(
                          "${goods['branchName']}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(color: Colors.grey[600], fontSize: 24.w),
                        )
                    ),
                  ]
              ) : Offstage(offstage: true, child: Text(''),),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      child: Text(
                    "${goods['itemSize']}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey[600], fontSize: 24.w),
                  )),
                  Row(
                    children: [
                      Text('库存：'),
                      Text(
                        logic.getPackageCount(
                            (goods['itemRealStock'] ?? 0).toInt(), goods),
                        style: state.isAllowNoStocks && (goods['itemRealStock'] ?? 0).toInt() <= 0 ? TextStyle(color: Colors.grey) :  TextStyle(color: Colors.red),
                      )
                    ],
                  )
                ],
              ),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MoneyUtils.getPriceWidget(goods['realityPriceNew'] ?? 0,
                  fontSize: 19, unit: getItemUnitStr(goods['itemUnit'])),

                  Expanded(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Offstage(
                        offstage: (cartGoods?['packageList'] ?? []).isEmpty,
                        child: InkWell(
                          onTap: () {
                            // _showBottomSheet([], goods, cartGoods, cartTrans);
                            String unit = goods["itemLargeBoxUnit"] != null
                                ? goods["itemLargeBoxUnit"]
                                : goods["itemMediumBoxUnit"] != null
                                    ? goods["itemMediumBoxUnit"]
                                    : goods["itemUnit"];
                            getLatestPriceList(
                                0, unit, goods, cartGoods, cartTrans);
                          },
                          child: Image.asset('assets/images/reduce.png',
                              width: 40.w,
                              height: 40.w,
                              alignment: Alignment.center,
                              fit: BoxFit.cover),
                        ),
                      ),
                      Offstage(
                          offstage: (cartGoods?['packageList'] ?? []).isEmpty,
                          child: Container(
                            margin: EdgeInsets.only(left: 10.w, right: 10.w),
                            alignment: Alignment.center,
                            child: Text(
                              logic.getCartNumStr(cartGoods ?? {}),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )),
                      InkWell(
                        onTap: () {
                          // if ((goods['itemRealStock'] ?? 0).toInt() > 0) {
                            // _showBottomSheet([], goods, cartGoods, cartTrans);
                            String unit = goods["itemLargeBoxUnit"] != null
                                ? goods["itemLargeBoxUnit"]
                                : goods["itemMediumBoxUnit"] != null
                                    ? goods["itemMediumBoxUnit"]
                                    : goods["itemUnit"];

                            if (currentUnit != null) {
                              unit = currentUnit;
                            }

                            getLatestPriceList(
                                0, unit, goods, cartGoods, cartTrans);
                          // }
                        },
                        // child: addIcon,
                        child: !state.isAllowNoStocks ? ((goods['itemRealStock'] ?? 0).toInt() > 0
                            ? addIcon
                            : addGreyIcon) : addIcon,
                      ),
                    ],
                  ))
                ],
              )
            ],
          ))
        ],
      ),
    );
  }

  // 获取商品最近销售价格
  getLatestPriceList(int type, String itemUnit, dynamic goods,
      dynamic cartGoods, dynamic cartTrans) {
    List latestPriceList = [];
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = logic.shopState.consumerNo;
    queryParameters['itemNo'] = goods["itemNo"];
    queryParameters['itemUnit'] = itemUnit;
    MyDio.post(Apis.getLatestPriceByGoods, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        if (list.isNotEmpty) {
          latestPriceList.addAll(list);
          if (type == 0) {
            _showBottomSheet(latestPriceList, goods, cartGoods, cartTrans);
          } else {
            if ((goods['itemRealStock'] ?? 0).toInt() > 0)
              _showBottomSheet(latestPriceList, goods, cartGoods, cartTrans);
          }
        } else {
          _showBottomSheet(latestPriceList, goods, cartGoods, cartTrans);
        }
      } else {
        _showBottomSheet(latestPriceList, goods, cartGoods, cartTrans);
        // MyCommonUtils.showToast(response.msg!);
      }
    }, failCallBack: (value) {
      _showBottomSheet(latestPriceList, goods, cartGoods, cartTrans);
    }, showErrMsg: false);
  }

  void _showPromotionInfo(String itemNo, listGoods) {
    var promotionGoodInfo = state.promotionInfo[itemNo] ?? {};
    printLong("promotionGoodInfo=>${promotionGoodInfo}");
    List promotionConditionList = promotionGoodInfo['info'] ?? [];
    double _fontSize = 26.w;

    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter setDiaLogState) {
            return SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 60.w),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30.w),
                        topRight: Radius.circular(30.w))),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 100.w,
                        ),
                        Text(
                          '活动规则',
                          style: TextStyle(fontSize: 26.w),
                        ),
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            height: 90.w,
                            width: 100.w,
                            alignment: Alignment.centerRight,
                            child: Text(
                              '取消',
                              style: TextStyle(
                                  color: Colors.red, fontSize: _fontSize),
                            ),
                          ),
                        )
                      ],
                    ),
                    Text('商品名称：', style: TextStyle(fontSize: 28.w)),
                    SizedBox(
                      height: 10.w,
                    ),
                    Container(
                      child: Text(
                        "${listGoods['itemName']} ${listGoods['itemSize']}",
                        style: TextStyle(
                            overflow: TextOverflow.fade,
                            fontSize: 25.w,
                            color: Colors.grey),
                      ),
                    ),
                    SizedBox(
                      height: 40.w,
                    ),
                    Text('活动规则：', style: TextStyle(fontSize: 28.w)),
                    Container(
                      alignment: Alignment.center,
                      height: 80.w,
                      color: Color.fromRGBO(244, 241, 242, 1),
                      child: Row(
                        children: [
                          Expanded(
                            flex:4,
                            child: Container(
                              alignment: Alignment.center,
                              child: Text('赠品方案'),
                            )
                          ),
                          Expanded(
                            flex:2,
                            child: Container(
                              alignment: Alignment.center,
                              child: Text('满足条件'),
                            )
                          ),
                          Expanded(
                              flex:5,
                              child: Container(
                            alignment: Alignment.center,
                            child: Text('赠品'),
                          ))
                        ],
                      ),
                    ),
                    Column(
                      children: promotionConditionList.map((promotionGoodCondition) {
                        var promotionCondition = promotionGoodCondition['goods'] ?? {};
                        int satisfyQty = promotionCondition['satisfyQty'] ?? 1;
                        List promotionGiftList = promotionCondition['subList'] ?? [];
                        return Column(
                          children: List.generate(promotionGiftList.length, (index) {
                            dynamic promotionGift = promotionGiftList[index];
                            return Container(
                                alignment: Alignment.center,
                                height: 80.w,
                                decoration: BoxDecoration(
                                    border: Border(
                                        top: BorderSide(
                                            color: Color.fromRGBO(
                                                244, 241, 242, 1),
                                            width: 2.w))),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex:4,
                                      child: Container(
                                        alignment: Alignment.center,
                                        child: Text("${promotionGoodInfo?['info'][0]?['act']?["promoteName"] ?? ''}", style: TextStyle(
                                            overflow: TextOverflow.ellipsis)),
                                      )
                                    ),
                                    Expanded(
                                      flex:2,
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            alignment: Alignment.center,
                                            child: Text(logic.getPackageCount(satisfyQty, listGoods)
                                            ),
                                          ),
                                          Container(
                                            alignment: Alignment.center,
                                            child: Text(
                                              "_${promotionGift['groupId']}",
                                              style: TextStyle(color: AppColor.red_ff211a, overflow: TextOverflow.ellipsis),
                                            ),
                                          ),
                                        ],
                                      )
                                    ),
                                    Expanded(
                                        flex:5,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              alignment: Alignment.center,
                                              child: Text(
                                                "${promotionGift['itemName']}",
                                                style: TextStyle(
                                                    overflow: TextOverflow.ellipsis),
                                              ),
                                            ),
                                            Container(
                                              alignment: Alignment.center,
                                              child: Text(
                                                " ${promotionGift['giveawayQty']}${getItemUnitStr(promotionGift['itemUnit'])}",
                                                style: TextStyle(color: AppColor.red_ff211a, overflow: TextOverflow.ellipsis),
                                              ),
                                            ),
                                          ],
                                        ))
                                  ],
                                ));
                          }).toList(),
                        );
                      }).toList(),
                    )
                  ],
                ),
              ),
            );
          });
        });
  }
}
