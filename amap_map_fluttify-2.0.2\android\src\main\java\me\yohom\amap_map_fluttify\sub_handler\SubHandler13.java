//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler13 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.AMap::setMapType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMapType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::isTrafficEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isTrafficEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setTrafficEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTrafficEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::showMapText_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.showMapText(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::showIndoorMap_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.showIndoorMap(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::showBuildings_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.showBuildings(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyTrafficStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.MyTrafficStyle var1 = (com.amap.api.maps.model.MyTrafficStyle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyTrafficStyle(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyTrafficStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MyTrafficStyle> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MyTrafficStyle __result__ = null;
                    try {
                        __result__ = __this__.getMyTrafficStyle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setTrafficStyleWithTextureData_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTrafficStyleWithTextureData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::isMyLocationEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isMyLocationEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyLocationEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyLocation_batch", (__argsBatch__, __methodResult__) -> {
                List<android.location.Location> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.location.Location __result__ = null;
                    try {
                        __result__ = __this__.getMyLocation();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.MyLocationStyle var1 = (com.amap.api.maps.model.MyLocationStyle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyLocationStyle(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getMyLocationStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.MyLocationStyle> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.MyLocationStyle __result__ = null;
                    try {
                        __result__ = __this__.getMyLocationStyle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyLocationType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMyLocationRotateAngle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyLocationRotateAngle(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getUiSettings_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.UiSettings> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.UiSettings __result__ = null;
                    try {
                        __result__ = __this__.getUiSettings();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getProjection_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.Projection> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.Projection __result__ = null;
                    try {
                        __result__ = __this__.getProjection();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setInfoWindowAdapter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.AMap.InfoWindowAdapter var1 = (com.amap.api.maps.AMap.InfoWindowAdapter) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInfoWindowAdapter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getScalePerPixel_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getScalePerPixel();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::runOnDrawFrame_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.runOnDrawFrame();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::removecache_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.removecache();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomRenderer_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.CustomRenderer var1 = (com.amap.api.maps.CustomRenderer) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomRenderer(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setPointToCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPointToCenter(var1.intValue(), var2.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapTextZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMapTextZIndex(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setLoadOfflineData_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLoadOfflineData(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapTextZIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getMapTextZIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getVersion_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.AMap.getVersion();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::reloadMap_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.reloadMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setRenderFps_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRenderFps(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setIndoorBuildingInfo_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.IndoorBuildingInfo var1 = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setIndoorBuildingInfo(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getZoomToSpanLevel_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    com.amap.api.maps.model.LatLng var2 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getZoomToSpanLevel(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getInfoWindowAnimationManager_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.InfoWindowAnimationManager> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.InfoWindowAnimationManager __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowAnimationManager();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMaskLayerParams_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                    // ref arg
                    Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                    // ref arg
                    Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                    // ref arg
                    Number var6 = (Number) ((Map<String, Object>) __args__).get("var6");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMaskLayerParams(var1.intValue(), var2.intValue(), var3.intValue(), var4.intValue(), var5.intValue(), var6.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMaxZoomLevel_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMaxZoomLevel(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMinZoomLevel_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMinZoomLevel(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::resetMinMaxZoomPreference_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.resetMinMaxZoomPreference();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapStatusLimits_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var1 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMapStatusLimits(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::addCrossOverlay_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CrossOverlay> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.CrossOverlayOptions var1 = (com.amap.api.maps.model.CrossOverlayOptions) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CrossOverlay __result__ = null;
                    try {
                        __result__ = __this__.addCrossOverlay(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::addRouteOverlay_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.RouteOverlay> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.RouteOverlay __result__ = null;
                    try {
                        __result__ = __this__.addRouteOverlay();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getViewMatrix_batch", (__argsBatch__, __methodResult__) -> {
                List<float[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    float[] __result__ = null;
                    try {
                        __result__ = __this__.getViewMatrix();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getProjectionMatrix_batch", (__argsBatch__, __methodResult__) -> {
                List<float[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    float[] __result__ = null;
                    try {
                        __result__ = __this__.getProjectionMatrix();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapCustomEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMapCustomEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStylePath_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomMapStylePath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.CustomMapStyleOptions var1 = (com.amap.api.maps.model.CustomMapStyleOptions) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomMapStyle(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomMapStyleID_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomMapStyleID(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setCustomTextureResourcePath_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomTextureResourcePath(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setRenderMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRenderMode(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getMapContentApprovalNumber_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getMapContentApprovalNumber();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getSatelliteImageApprovalNumber_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getSatelliteImageApprovalNumber();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getTerrainApprovalNumber_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTerrainApprovalNumber();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setMapLanguage_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMapLanguage(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setRoadArrowEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRoadArrowEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setNaviLabelEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setNaviLabelEnable(var1, var2.intValue(), var3.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setTouchPoiEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTouchPoiEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::isTouchPoiEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isTouchPoiEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getNativeMapController_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getNativeMapController();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getNativeMapEngineID_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getNativeMapEngineID();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setWorldVectorMapStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setWorldVectorMapStyle(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::getCurrentStyle_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getCurrentStyle();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::accelerateNetworkInChinese_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.accelerateNetworkInChinese(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap::setConstructingRoadEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setConstructingRoadEnable(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::getMap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMap> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMap __result__ = null;
                    try {
                        __result__ = __this__.getMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onCreate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onCreate(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onResume_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onResume();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onPause_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onPause();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onDestroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onLowMemory_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onLowMemory();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::onSaveInstanceState_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onSaveInstanceState(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapView::setVisibility_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisibility(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getLatitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getLatitude();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setLatitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLatitude(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getLongitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getLongitude();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setLongitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLongitude(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getSpeed_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getSpeed();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setSpeed_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setSpeed(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getBearing();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setBearing(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTime(var1.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::copy_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.trace.TraceLocation> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.trace.TraceLocation __result__ = null;
                    try {
                        __result__ = __this__.copy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::getInstance_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.trace.LBSTraceClient> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.content.Context var0 = (android.content.Context) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.trace.LBSTraceClient __result__ = null;
                    try {
                        __result__ = com.amap.api.trace.LBSTraceClient.getInstance(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::stopTrace_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.stopTrace();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::add_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.add(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::remove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.remove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setProperCamera_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setProperCamera(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::zoopToSpan_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.zoopToSpan();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getTraceStatus_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getTraceStatus();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setTraceStatus_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTraceStatus(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getDistance_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getDistance();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setDistance_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDistance(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getWaitTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getWaitTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setWaitTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setWaitTime(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.CommonInfoWindowAdapter::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.CommonInfoWindowAdapter __result__ = new com.amap.api.maps.AMap.CommonInfoWindowAdapter() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.CommonInfoWindowAdapter::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public com.amap.api.maps.InfoWindowParams getInfoWindowParams(com.amap.api.maps.model.BasePointOverlay var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: getInfoWindowParams(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "getInfoWindowParams",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
                        return null;
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnCameraChangeListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnCameraChangeListener __result__ = new com.amap.api.maps.AMap.OnCameraChangeListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnCameraChangeListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onCameraChange(com.amap.api.maps.model.CameraPosition var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onCameraChange(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onCameraChange",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onCameraChangeFinish(com.amap.api.maps.model.CameraPosition var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onCameraChangeFinish(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onCameraChangeFinish",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.LocationSource::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.LocationSource __result__ = new com.amap.api.maps.LocationSource() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.LocationSource::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void activate(com.amap.api.maps.LocationSource.OnLocationChangedListener var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: activate(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "activate",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void deactivate() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: deactivate(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "deactivate",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnCacheRemoveListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnCacheRemoveListener __result__ = new com.amap.api.maps.AMap.OnCacheRemoveListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnCacheRemoveListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onRemoveCacheFinish(boolean var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onRemoveCacheFinish(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onRemoveCacheFinish",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMultiPointClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMultiPointClickListener __result__ = new com.amap.api.maps.AMap.OnMultiPointClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMultiPointClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public boolean onPointClick(com.amap.api.maps.model.MultiPointItem var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onPointClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onPointClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
                        return true;
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.WearMapView.OnDismissCallback::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.WearMapView.OnDismissCallback __result__ = new com.amap.api.maps.WearMapView.OnDismissCallback() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.WearMapView.OnDismissCallback::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onDismiss() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onDismiss(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onDismiss",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onNotifySwipe() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onNotifySwipe(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onNotifySwipe",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.CancelableCallback::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.CancelableCallback __result__ = new com.amap.api.maps.AMap.CancelableCallback() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.CancelableCallback::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onFinish() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onFinish(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onFinish",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onCancel() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onCancel(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onCancel",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMapScreenShotListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMapScreenShotListener __result__ = new com.amap.api.maps.AMap.OnMapScreenShotListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMapScreenShotListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMapScreenShot(android.graphics.Bitmap var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapScreenShot(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapScreenShot",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onMapScreenShot(android.graphics.Bitmap var1, int var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapScreenShot(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapScreenShot_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMapLoadedListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMapLoadedListener __result__ = new com.amap.api.maps.AMap.OnMapLoadedListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMapLoadedListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMapLoaded() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapLoaded(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapLoaded",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMarkerClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMarkerClickListener __result__ = new com.amap.api.maps.AMap.OnMarkerClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMarkerClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public boolean onMarkerClick(com.amap.api.maps.model.Marker var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMarkerClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMarkerClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
                        return true;
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMyLocationChangeListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMyLocationChangeListener __result__ = new com.amap.api.maps.AMap.OnMyLocationChangeListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMyLocationChangeListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMyLocationChange(android.location.Location var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMyLocationChange(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMyLocationChange",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.onMapPrintScreenListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.onMapPrintScreenListener __result__ = new com.amap.api.maps.AMap.onMapPrintScreenListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.onMapPrintScreenListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMapPrint(android.graphics.drawable.Drawable var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapPrint(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapPrint",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMarkerDragListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMarkerDragListener __result__ = new com.amap.api.maps.AMap.OnMarkerDragListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMarkerDragListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMarkerDragStart(com.amap.api.maps.model.Marker var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMarkerDragStart(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMarkerDragStart",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onMarkerDrag(com.amap.api.maps.model.Marker var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMarkerDrag(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMarkerDrag",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onMarkerDragEnd(com.amap.api.maps.model.Marker var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMarkerDragEnd(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMarkerDragEnd",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker.MoveListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.utils.overlay.SmoothMoveMarker.MoveListener __result__ = new com.amap.api.maps.utils.overlay.SmoothMoveMarker.MoveListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.utils.overlay.SmoothMoveMarker.MoveListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void move(double var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: move(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "move",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener __result__ = new com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.utils.overlay.MovingPointOverlay.MoveListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void move(double var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: move(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "move",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.LocationSource.OnLocationChangedListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.LocationSource.OnLocationChangedListener __result__ = new com.amap.api.maps.LocationSource.OnLocationChangedListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.LocationSource.OnLocationChangedListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onLocationChanged(android.location.Location var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onLocationChanged(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onLocationChanged",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener __result__ = new com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.offlinemap.OfflineMapManager.OfflineMapDownloadListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onDownload(int var1, int var2, String var3) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onDownload(" + var1 + var2 + var3 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onDownload__",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onCheckUpdate(boolean var1, String var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onCheckUpdate(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onCheckUpdate_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onRemove(boolean var1, String var2, String var3) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onRemove(" + var1 + var2 + var3 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onRemove__",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener __result__ = new com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.offlinemap.OfflineMapManager.OfflineLoadedListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onVerifyComplete() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onVerifyComplete(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onVerifyComplete",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnIndoorBuildingActiveListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnIndoorBuildingActiveListener __result__ = new com.amap.api.maps.AMap.OnIndoorBuildingActiveListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnIndoorBuildingActiveListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void OnIndoorBuilding(com.amap.api.maps.model.IndoorBuildingInfo var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: OnIndoorBuilding(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "OnIndoorBuilding",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMapTouchListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMapTouchListener __result__ = new com.amap.api.maps.AMap.OnMapTouchListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMapTouchListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onTouch(android.view.MotionEvent var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onTouch(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onTouch",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener __result__ = new com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onGenerateComplete(android.graphics.Bitmap var1, int var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onGenerateComplete(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onGenerateComplete_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener __result__ = new com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onUpdate(int var1, com.amap.api.maps.model.CrossOverlay.UpdateItem var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onUpdate(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onUpdate_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.model.animation.Animation.AnimationListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.model.animation.Animation.AnimationListener __result__ = new com.amap.api.maps.model.animation.Animation.AnimationListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.model.animation.Animation.AnimationListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onAnimationStart() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onAnimationStart(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onAnimationStart",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onAnimationEnd() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onAnimationEnd(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onAnimationEnd",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.model.AMapGestureListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.model.AMapGestureListener __result__ = new com.amap.api.maps.model.AMapGestureListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.model.AMapGestureListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onDoubleTap(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onDoubleTap(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onDoubleTap_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onSingleTap(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onSingleTap(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onSingleTap_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onFling(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onFling(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onFling_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onScroll(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onScroll(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onScroll_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onLongPress(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onLongPress(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onLongPress_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onDown(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onDown(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onDown_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onUp(float var1, float var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onUp(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onUp_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onMapStable() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapStable(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapStable",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnPOIClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnPOIClickListener __result__ = new com.amap.api.maps.AMap.OnPOIClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnPOIClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onPOIClick(com.amap.api.maps.model.Poi var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onPOIClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onPOIClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnInfoWindowClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnInfoWindowClickListener __result__ = new com.amap.api.maps.AMap.OnInfoWindowClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnInfoWindowClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onInfoWindowClick(com.amap.api.maps.model.Marker var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onInfoWindowClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onInfoWindowClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMapClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMapClickListener __result__ = new com.amap.api.maps.AMap.OnMapClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMapClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMapClick(com.amap.api.maps.model.LatLng var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnPolylineClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnPolylineClickListener __result__ = new com.amap.api.maps.AMap.OnPolylineClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnPolylineClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onPolylineClick(com.amap.api.maps.model.Polyline var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onPolylineClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onPolylineClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.AMap.OnMapLongClickListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.AMap.OnMapLongClickListener __result__ = new com.amap.api.maps.AMap.OnMapLongClickListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.AMap.OnMapLongClickListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onMapLongClick(com.amap.api.maps.model.LatLng var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onMapLongClick(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onMapLongClick",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.maps.ExceptionLogger::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.maps.ExceptionLogger __result__ = new com.amap.api.maps.ExceptionLogger() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.maps.ExceptionLogger::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onException(java.lang.Throwable var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onException(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onException",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onDownloaderException(int var1, int var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onDownloaderException(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onDownloaderException_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.trace.LBSTraceBase::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.trace.LBSTraceBase __result__ = new com.amap.api.trace.LBSTraceBase() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.trace.LBSTraceBase::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void queryProcessedTrace(int var1, java.util.List<com.amap.api.trace.TraceLocation> var2, int var3, com.amap.api.trace.TraceListener var4) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: queryProcessedTrace(" + var1 + var2 + var3 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "queryProcessedTrace___",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                        put("var4", var4);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void setLocationInterval(long var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: setLocationInterval(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "setLocationInterval",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void setTraceStatusInterval(int var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: setTraceStatusInterval(" + var1 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "setTraceStatusInterval",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void startTrace(com.amap.api.trace.TraceStatusListener var1) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: startTrace(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "startTrace",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void stopTrace() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: stopTrace(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "stopTrace",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void destroy() {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: destroy(" + "" + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "destroy",
                                    new HashMap<String, Object>() {{
                    
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.trace.TraceStatusListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.trace.TraceStatusListener __result__ = new com.amap.api.trace.TraceStatusListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.trace.TraceStatusListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onTraceStatus(java.util.List<com.amap.api.trace.TraceLocation> var1, java.util.List<com.amap.api.maps.model.LatLng> var2, String var3) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onTraceStatus(" + var1 + var2 + var3 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onTraceStatus__",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            // anonymous class
            put("com.amap.api.trace.TraceListener::createAnonymous__", (__args__, __methodResult__) -> {
                // invoke native method
                com.amap.api.trace.TraceListener __result__ = new com.amap.api.trace.TraceListener() {
                    // method channel
                    MethodChannel callbackChannel = new MethodChannel(messenger, "com.amap.api.trace.TraceListener::Callback@" + getClass().getName() + ":" + System.identityHashCode(this), new StandardMethodCodec(new FluttifyMessageCodec()));
                    android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
            
                    // call dart method
                    @Override
                    public void onRequestFailed(int var1, String var2) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onRequestFailed(" + var1 + var2 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onRequestFailed_",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onTraceProcessing(int var1, int var2, java.util.List<com.amap.api.maps.model.LatLng> var3) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onTraceProcessing(" + var1 + var2 + var3 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onTraceProcessing__",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
                    @Override
                    public void onFinished(int var1, java.util.List<com.amap.api.maps.model.LatLng> var2, int var3, int var4) {
                        // print log
                        if (getEnableLog()) {
                            Log.d("java-callback", "fluttify-java-callback: onFinished(" + var1 + var2 + var3 + var4 + ")");
                        }
            
                        // call dart method
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                callbackChannel.invokeMethod(
                                    "onFinished___",
                                    new HashMap<String, Object>() {{
                                        put("var1", var1);
                                        put("var2", var2);
                                        put("var3", var3);
                                        put("var4", var4);
                                    }}
                                );
                            }
                        });
            
                        // method result
            
                    }
            
            };
            
                __methodResult__.success(__result__);
            });
            put("RefClass::isKindOfcom_amap_api_offlineservice_AMapPermissionActivity", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.offlineservice.AMapPermissionActivity);
            });
            put("RefClass::isKindOfcom_amap_api_maps_SupportMapFragment", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.SupportMapFragment);
            });
            put("RefClass::isKindOfcom_amap_api_maps_UiSettings", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.UiSettings);
            });
            put("RefClass::isKindOfcom_amap_api_maps_InfoWindowParams", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.InfoWindowParams);
            });
            put("RefClass::isKindOfcom_amap_api_maps_CameraUpdateFactory", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.CameraUpdateFactory);
            });
            put("RefClass::isKindOfcom_amap_api_maps_AMapException", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.AMapException);
            });
            put("RefClass::isKindOfcom_amap_api_maps_AMapOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.AMapOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_AMapOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.AMapOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_CoordinateConverter", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.CoordinateConverter);
            });
            put("RefClass::isKindOfcom_amap_api_maps_utils_SpatialRelationUtil", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.utils.SpatialRelationUtil);
            });
            put("RefClass::isKindOfcom_amap_api_maps_utils_overlay_MovingPointOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.utils.overlay.MovingPointOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_utils_overlay_SmoothMoveMarker", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.utils.overlay.SmoothMoveMarker);
            });
            put("RefClass::isKindOfcom_amap_api_maps_TextureSupportMapFragment", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.TextureSupportMapFragment);
            });
            put("RefClass::isKindOfcom_amap_api_maps_CameraUpdate", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.CameraUpdate);
            });
            put("RefClass::isKindOfcom_amap_api_maps_MapFragment", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.MapFragment);
            });
            put("RefClass::isKindOfcom_amap_api_maps_SwipeDismissView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.SwipeDismissView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_MapsInitializer", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.MapsInitializer);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_OfflineMapStatus", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.OfflineMapStatus);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_OfflineMapProvince", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.OfflineMapProvince);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_DownloadProgressView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.DownloadProgressView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_Province", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.Province);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_OfflineMapManager", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.OfflineMapManager);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_OfflineMapCity", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.OfflineMapCity);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_OfflineMapActivity", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.OfflineMapActivity);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_City", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.City);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_DownLoadListView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.DownLoadListView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_DownLoadExpandListView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.DownLoadExpandListView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_offlinemap_CityExpandView", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.offlinemap.CityExpandView);
            });
            put("RefClass::isKindOfcom_amap_api_maps_Projection", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.Projection);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Polygon", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Polygon);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatMapLayerOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatMapLayerOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BitmapDescriptor", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BitmapDescriptor);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PolygonHoleOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PolygonHoleOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TileOverlayOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TileOverlayOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Poi", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Poi);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MyLocationStyle", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MyLocationStyle);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_RouteOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.RouteOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_VisibleRegion", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.VisibleRegion);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_LatLngCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.LatLngCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CircleHoleOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CircleHoleOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Text", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Text);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_LatLngBounds_Builder", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.LatLngBounds.Builder);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatMapLayer", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatMapLayer);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_NavigateArrowOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.NavigateArrowOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_ColorLatLng", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.ColorLatLng);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_BitmapDescriptorFactory", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.BitmapDescriptorFactory);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_UrlTileProvider", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.UrlTileProvider);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MVTTileProvider", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MVTTileProvider);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MultiPointOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MultiPointOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PoiCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PoiCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PolylineOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PolylineOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Tile", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Tile);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_GL3DModel", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.GL3DModel);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_Gradient", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.Gradient);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatMapItem", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatMapItem);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_NaviPara", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.NaviPara);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_GroundOverlayOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.GroundOverlayOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_GL3DModelOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.GL3DModelOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_GroundOverlay", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.GroundOverlay);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MyTrafficStyle", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MyTrafficStyle);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_CameraPosition", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.CameraPosition);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_TextOptionsCreator", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.TextOptionsCreator);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_PoiPara", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.PoiPara);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_MarkerOptions", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.MarkerOptions);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_HeatmapTileProvider", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.HeatmapTileProvider);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_NavigateArrow", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.NavigateArrow);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_AnimationSet", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.AnimationSet);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_RotateAnimation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.RotateAnimation);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_TranslateAnimation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.TranslateAnimation);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_Animation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.Animation);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_EmergeAnimation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.EmergeAnimation);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_AlphaAnimation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.AlphaAnimation);
            });
            put("RefClass::isKindOfcom_amap_api_maps_model_animation_ScaleAnimation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.maps.model.animation.ScaleAnimation);
            });
        }};
    }
}
