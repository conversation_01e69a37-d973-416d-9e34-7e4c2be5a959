import 'package:collection/collection.dart';

import 'chart_page_data.dart';

class B2bAppCartPage {
  int? code;
  ChartPageData? data;
  String? msg;

  B2bAppCartPage({this.code, this.data, this.msg});

  factory B2bAppCartPage.fromJson(Map<String, dynamic> json) {
    return B2bAppCartPage(
      code: json['code'] as int?,
      data: json['data'] == null
          ? null
          : ChartPageData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
        'code': code,
        'data': data?.toJson(),
        'msg': msg,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! B2bAppCartPage) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => code.hashCode ^ data.hashCode ^ msg.hashCode;
}
