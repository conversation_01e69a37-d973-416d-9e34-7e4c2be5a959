import 'package:get/get.dart';

import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/chart_page_data.dart';

class StoreCartsState {
  // 当前选中的Tab索引 (0:购物车, 1:常购, 2:最近搜索)
  RxInt currentTabIndex = 0.obs;

  // 最近搜索的子Tab索引 (0:最近3天, 1:最近一周, 2:最近一个月)
  RxInt recentSearchTabIndex = 1.obs; // 默认选中"最近一周"

  // 配送方式Tab索引 (0:本地进货, 1:全国批包邮)
  RxInt deliveryTabIndex = 0.obs;

  // 加载状态
  RxBool isLoading = false.obs;
  RxBool isLoadingMore = false.obs;

  // 购物车数据
  Rx<ChartPageData> cartData = ChartPageData().obs;

  // 常购商品列表
  RxList<CartItem> frequentItems = <CartItem>[].obs;

  // 最近搜索列表
  RxList<SearchItem> recentSearches = <SearchItem>[].obs;

  // 分页参数
  RxInt currentPage = 1.obs;
  RxInt pageSize = 10.obs;
  var total = 0.obs;
  RxBool hasMore = true.obs;

  // 门店ID
  String storeId = '';
}

// 购物车响应数据模型
class CartResponse {
  final int total;
  final List<SupplierGroup> supplierGroupList;
  final String payPlatform;

  CartResponse({
    required this.total,
    required this.supplierGroupList,
    required this.payPlatform,
  });

  factory CartResponse.fromJson(Map<String, dynamic> json) {
    return CartResponse(
      total: json['total'] ?? 0,
      supplierGroupList: (json['supplierGroupList'] as List?)
              ?.map((item) => SupplierGroup.fromJson(item))
              .toList() ??
          [],
      payPlatform: json['payPlatform'] ?? '',
    );
  }
}

// 供应商分组模型
class SupplierGroup {
  final String supplierName;
  final String supplierId;
  final List<CartItem> itemList;

  SupplierGroup({
    required this.supplierName,
    required this.supplierId,
    required this.itemList,
  });

  factory SupplierGroup.fromJson(Map<String, dynamic> json) {
    return SupplierGroup(
      supplierName: json['supplierName'] ?? '',
      supplierId: json['supplierId'] ?? '',
      itemList: (json['itemList'] as List?)
              ?.map((item) => CartItem.fromJson(item))
              .toList() ??
          [],
    );
  }
}

// 购物车商品模型
class CartItem {
  final double markPrice;
  final int productNum;
  final String spuName;
  final String skuThumb;
  final String unitName;
  final String? addTime; // 加购时间，可能需要从其他地方获取

  CartItem({
    required this.markPrice,
    required this.productNum,
    required this.spuName,
    required this.skuThumb,
    required this.unitName,
    this.addTime,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      markPrice: (json['markPrice'] ?? 0).toDouble(),
      productNum: json['productNum'] ?? 0,
      spuName: json['spuName'] ?? '',
      skuThumb: json['skuThumb'] ?? '',
      unitName: json['unitName'] ?? '',
      addTime: json['addTime'],
    );
  }
}

// 搜索项模型
class SearchItem {
  final String id;
  final String keyword;
  final int resultCount;
  final String searchTime;

  SearchItem({
    required this.id,
    required this.keyword,
    required this.resultCount,
    required this.searchTime,
  });
}
