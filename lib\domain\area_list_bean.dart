/// 城市区域列表数据模型
class AreaListBean {
  int? code;
  List<AreaData>? data;
  String? msg;

  AreaListBean({this.code, this.data, this.msg});

  AreaListBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = <AreaData>[];
      json['data'].forEach((v) {
        data!.add(AreaData.fromJson(v));
      });
    }
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['msg'] = msg;
    return data;
  }
}

class AreaData {
  String? areaId;
  String? sysCode;
  String? pid;
  String? parentName;
  String? areaName;
  int? status;
  String? memo;
  String? dcId;
  String? localFlag;
  int? groupId;
  int? level;
  int? sortNum;
  String? firstAreaCityId;
  String? secondAreaCityId;
  String? threeAreaCityId;
  String? deleted;

  AreaData({
    this.areaId,
    this.sysCode,
    this.pid,
    this.parentName,
    this.areaName,
    this.status,
    this.memo,
    this.dcId,
    this.localFlag,
    this.groupId,
    this.level,
    this.sortNum,
    this.firstAreaCityId,
    this.secondAreaCityId,
    this.threeAreaCityId,
    this.deleted,
  });

  AreaData.fromJson(Map<String, dynamic> json) {
    areaId = json['areaId'];
    sysCode = json['sysCode'];
    pid = json['pid'];
    parentName = json['parentName'];
    areaName = json['areaName'];
    status = json['status'];
    memo = json['memo'];
    dcId = json['dcId'];
    localFlag = json['localFlag'];
    groupId = json['groupId'];
    level = json['level'];
    sortNum = json['sortNum'];
    firstAreaCityId = json['firstAreaCityId'];
    secondAreaCityId = json['secondAreaCityId'];
    threeAreaCityId = json['threeAreaCityId'];
    deleted = json['deleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['areaId'] = areaId;
    data['sysCode'] = sysCode;
    data['pid'] = pid;
    data['parentName'] = parentName;
    data['areaName'] = areaName;
    data['status'] = status;
    data['memo'] = memo;
    data['dcId'] = dcId;
    data['localFlag'] = localFlag;
    data['groupId'] = groupId;
    data['level'] = level;
    data['sortNum'] = sortNum;
    data['firstAreaCityId'] = firstAreaCityId;
    data['secondAreaCityId'] = secondAreaCityId;
    data['threeAreaCityId'] = threeAreaCityId;
    data['deleted'] = deleted;
    return data;
  }
}
