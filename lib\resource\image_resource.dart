/*
 * @Author: 潘腾龙
 * @Date: 2023-10-27 15:12:05
 * @LastEditTime: 2023-12-28 14:46:57
 */
/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月11日14:35:57
 * 修改时间：2023年09月11日14:35:57
 */

class ImageResource {
  static const SPLASH = "assets/images/splash.png";

  static const NOT_FOUND = "assets/images/not_found.png";
  static const NO_DATA = "assets/images/no_data.png";

  static const DEFAULT_AVATAR = "assets/images/default_avatar.png";
  static const DEFAULT_IMAGE = "assets/images/default_image.png";
  static const CLOSE_COMMON = "assets/images/close_common.png";
  static const CLOSE_FIX_COMMON = "assets/images/close.png";
  static const CLOSE_BLACK = "assets/images/close.png";
  static const ARROW_RIGHT = "assets/images/arrow_right.png";
  static const REFRESH = "assets/images/refresh.png";
  static const EXPLAIN = "assets/images/explain.png";
  static const ADD_BLACK = "assets/images/add_black.png";
  static const CLOSE_BTN = "assets/images/close_btn.png";
  static const ARROW_DOWN = "assets/images/arrow_down.png";
  static const ARROW_UP = "assets/images/arrow_up.png";
  static const SCAN_COMMON = "assets/images/scan_common.png";
  static const SEARCH_COMMON = "assets/images/search_common.png";
  static const IMAGES_UPLOAD = "assets/images/images_upload.png";

  static const ICON_UPGRADE_VERSION = "assets/images/icon_upgrade_version.png";
  static const ICON_UPGRADE_ROCKET = "assets/images/icon_upgrade_rocket.png";

  static const ICON_ACTIVITY_BANNER = "assets/images/icon_activity_banner.png";

  static const PAY_WX = "assets/images/pay_wx.png";
  static const PAY_ZFB = "assets/images/pay_zfb.png";
  static const PAY_CASH = "assets/images/pay_cash.png";

  static const RANK_ONE = "assets/images/rank_one.png";
  static const RANK_TWO = "assets/images/rank_two.png";
  static const RANK_THREE = "assets/images/rank_three.png";

  static const ICON_CAR_SALE = "assets/images/icon_car_sale.png";
  static const ICON_VISIT_SALE = "assets/images/icon_visit_sale.png";
  static const ICON_AFTERSALE_RETURN =
      "assets/images/icon_aftersale_return.png";
  static const ICON_AFTERSALE_EXCHANGE =
      "assets/images/icon_aftersale_exchange.png";
  static const ICON_ORDER_QUERY = "assets/images/icon_order_query.png";
  static const ICON_SHOP_PREVIEW = "assets/images/shop_preview_icon.png";
  static const ICON_ORDER_MEETING = "assets/images/icon_order_meeting.png";
  static const ICON_COST_MANAGEMENT = "assets/images/icon_cost_management.png";
  static const ICON_DISPALY_MANAGEMENT =
      "assets/images/icon_display_management.png";
  static const ICON_DEVICE_MANAGEMENT =
      "assets/images/icon_device_management.png";
  static const ICON_Exchange =
      "assets/images/icon_exchange.png";
  static const ICON_CLOUD_BINDING = "assets/images/icon_cloud_binding.png";
  static const ICON_TALLY_PHOTO = "assets/images/icon_tally_photo.png";
  static const ICON_ADVANCE_PAYMENT = "assets/images/icon_advance_payment.png";
  static const ICON_PLACE_ORDER = "assets/images/icon_place_order.png";
  static const ICON_DEAL_SALLE = "assets/images/dealandsale.png";

  static const ICON_INVITATION = "assets/images/icon_invitation.png";
  static const ICON_CUO = "assets/images/icon_cuo.png";
  static const ICON_DUI = "assets/images/icon_dui.png";

  static const ICON_DELAY_RECEIPT = "assets/images/icon_delay_receipt.png";
  static const ICON_ADVANCE_RECEIPT = "assets/images/icon_advance_receipt.png";

  static const MY_MISSION = "assets/images/my_mission.png";
  static const MY_CODE = "assets/images/my_code.png";
  static const MY_BLUETOOTH = "assets/images/my_bluetooth.png";
  static const MY_SERVICE = "assets/images/my_service.png";
  static const MY_ACCOUNT = "assets/images/my_account.png";

  static const ICON_DATE_CALENDAR = "assets/images/icon_date_calendar.png";
  //配送支付
  static const ICON_BANK_PAY = "assets/images/icon_bank_pay.png";
  static const ICON_ARREARS_PAY = "assets/images/icon_arrears_pay.png";
  static const ICON_CASH_PAY = "assets/images/icon_cash_pay.png";
  static const ICON_YS_PAY = "assets/images/icon_ys_pay.png";
  static const ICON_OTHER_PAY = "assets/images/icon_other_pay.png";
  static const ICON_FAN_CANG = "assets/images/fancang.png";
  static const ICON_WX_PAY = "assets/images/icon_wx_pay.png";

  /// 库存
  static const ICON_INVENTORY = "assets/images/icon_inventory.png";
  static const ICON_B2B_CART = "assets/images/b2b_cart.png";
  static const ICON_B2B_USERS = "assets/images/b2b_users.png";
  static const ICON_B2B_COUPON = "assets/images/b2b_coupon.png";
  static const ICON_INVENTORY_IN = "assets/images/icon_inventory_in.png";
  static const ICON_INVENTORY_CHECK = "assets/images/icon_inventory_check.png";
  static const ICON_INVENTORY_ALLOCATE =
      "assets/images/icon_inventory_allocate.png";
  static const ICON_INVENTORY_OUT = "assets/images/icon_inventory_out.png";
  static const ICON_INVENTORY_SCAN = "assets/images/icon_inventory_scan.png";
  static const ICON_SEARCH = "assets/images/icon_search.png";
  static const ICON_INVENTORY_DEL = "assets/images/icon_inventory_del.png";
  static const ICON_INVENTORY_DETAIL_IN =
      "assets/images/icon_inventory_detail_in.png";
  static const ICON_INVENTORY_DETAIL_OUT =
      "assets/images/icon_inventory_detail_out.png";
  static const ICON_INVENTORY_CHECK_DETAIL_ALREADY =
      "assets/images/icon_inventory_check_detail_already.png";
  static const ICON_REPLENISHMENT_FINISH =
      "assets/images/icon_replenishment_finish.png";
  static const ICON_CHECK = "assets/images/icon_check.png";
  static const ICON_CHECK_ACCOUNT = "assets/images/icon_check_account.png";

  static const SELECT_ON = "assets/images/select_on.png";
  static const SELECT_OFF = "assets/images/select_off.png";
}
