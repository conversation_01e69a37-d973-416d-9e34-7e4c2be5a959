import 'package:extended_image/extended_image.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/date_Utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:fuduoduo/resource/image_resource.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:get/get.dart';
import 'package:vector_math/vector_math_64.dart' as v;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/upgrade_utils.dart';
import 'logic.dart';

class Page extends StatelessWidget {
  final homeLogic = Get.put(HomePageLogic());
  final state = Get.find<HomePageLogic>().state;

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<Public>();
    logic.getUserInfo();
    homeLogic.getHomePageTotalData(state.employeeNo, "", "", "");
    homeLogic.getTeamData(true);
    homeLogic.getLocalUserInfo();
    homeLogic.getUserStatusInfo();
    homeLogic.getCloudFlagInfo();
    // dynamic employee = publicState.userInfo['employee'] ?? {};
    return Scaffold(
      body: GetBuilder<HomePageLogic>(
        builder: (_) => Column(
          // children: [HeaderBox(), ContentBox()],
          children: [
            _headerView(context),
            SizedBox(height: 20.h),
            Expanded(
                child: _contentView(
                    state.yeJiType ?? "0", state.dateType, homeLogic)),
          ],
        ),
      ),
      floatingActionButton: GetBuilder<HomePageLogic>(builder: (controller) {
        return Visibility(
            //1、当前用户角色为业务员 2、当前用户开通账号 3、该经销商开通同福云商
            visible:
                ((SpUtil.getObject('userInfo'))?['employee']["duty"] == "4") &&
                    (SpUtil.getBool("StartFlag") == true) &&
                    (SpUtil.getBool("CloudFlag") == true),
            // visible: ((SpUtil.getObject('userInfo'))?['employee']["duty"] == "4"),
            // visible: (SpUtil.getBool("StartFlag") == true),
            // visible: (SpUtil.getBool("CloudFlag") == true),
            child: InkWell(
              onTap: () {
                // MyCommonUtils.showToast("跳转云商专属邀请码页面");
                Get.toNamed(PageName.InvitationCodePage)?.then((value) => {
                      homeLogic.getLocalUserInfo(),
                      homeLogic.getUserStatusInfo(),
                      homeLogic.getCloudFlagInfo()
                    });
              },
              child: Opacity(
                opacity: 1,
                child: Container(
                  width: 210.w,
                  height: 96.h,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    // color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                    // borderRadius: BorderRadius.only(
                    //     topLeft: Radius.circular(50.r),
                    //     bottomLeft: Radius.circular(50.r)
                    // )
                  ),
                  child: Image.asset(
                    ImageResource.ICON_INVITATION,
                    width: 210.w,
                    height: 96.h,
                    fit: BoxFit.cover,
                  ),
                  // child: Row(
                  //   children: [
                  //     Column(
                  //       crossAxisAlignment: CrossAxisAlignment.start,
                  //       children: [
                  //         Expanded(
                  //             flex: 1,
                  //             child: Container(
                  //               alignment: Alignment.topCenter,
                  //               margin: EdgeInsets.only(top: 15.h),
                  //               child: Text(
                  //                 '出示云商',
                  //                 style: TextStyle(
                  //                     fontSize: 22.sp,
                  //                     color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                  //                     fontWeight: FontWeight.w600
                  //                 ),
                  //               ),
                  //             )
                  //         ),
                  //         Expanded(
                  //             flex: 1,
                  //             child: Container(
                  //               alignment: Alignment.bottomCenter,
                  //               margin: EdgeInsets.only(bottom: 15.h),
                  //               child: Text(
                  //                 '专属邀请码',
                  //                 style: TextStyle(
                  //                     fontSize: 22.sp,
                  //                     color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                  //                     fontWeight: FontWeight.w600
                  //                 ),
                  //               ),
                  //             )
                  //         )
                  //       ],
                  //     ),
                  //     Spacer(),
                  //     Column(
                  //       children: [
                  //         QrImageView(
                  //           padding: EdgeInsets.all(0),
                  //           data: 'This is a simple QR code',
                  //           version: QrVersions.auto,
                  //           size: 35,
                  //           gapless: false,
                  //         ),
                  //         // Container(
                  //         //   margin: EdgeInsets.only(left: 20.w),
                  //         //   width: 70.w,
                  //         //   height: 70.w,
                  //         //   child: ClipRRect(
                  //         //     // borderRadius: BorderRadius.circular(60.r),
                  //         //     child: Image.asset(
                  //         //       ImageResource.DEFAULT_IMAGE,
                  //         //       width: 70.w,
                  //         //       height: 70.w,
                  //         //       fit: BoxFit.cover,
                  //         //     ),
                  //         //   ),
                  //         // ),
                  //         Spacer(),
                  //       ],
                  //     ),
                  //   ],
                  // ),
                ),
              ),
            ));
      }),
    );
  }
}

_headerView(BuildContext context) {
  final homeState = Get.find<HomePageLogic>().state;
  List<Color> _color = [
    Color.fromRGBO(255, 117, 26, 1),
    Color.fromRGBO(255, 83, 47, 1),
    Color.fromRGBO(255, 33, 26, 1),
  ];
  return Container(
    height: homeState.missionCount > 1
        ? (558.h +
            250.h +
            20.h +
            (20.h + 50.h + 20.h + 150.h + 20.h + 150.h + 20.h))
        : homeState.missionCount > 0
            ? (558.h + 250.h + 20.h + (20.h + 50.h + 20.h + 150.h + 20.h))
            // : (558.h + 250.h + (20.h + 50.h + 20.h)),//不隐藏
            : (558.h + 250.h), //隐藏
    color: ColorResource.LIGHT_GRAY_PAGE_BACKGROUND_COLOR,
    child: Stack(children: [
      Container(
        height: 558.h,
        decoration: BoxDecoration(gradient: LinearGradient(colors: _color)),
      ),
      Column(
        children: [
          HeaderBox(),
          SizedBox(height: 40.h),
          BtnList(),
          SizedBox(height: 20.h),
          _missionList(context),
          Expanded(
            child: Align(
              alignment: Alignment.center,
              child: Container(
                height: 68.h,
                child: TabBox(),
              ),
            ),
            // margin: EdgeInsets.only(top: 20.w),
            // height: 68.w,
            // child: TabBox(),
          ),
        ],
      )
    ]),
  );
}

_contentView(yejitype, type, HomePageLogic logic) {
  // return Container(
  //   color: ColorResource.BLACK_COMMON_COLOR,
  // );
  // 动态获取业绩内容
  switch (yejitype) {
    case '0': // 个人业绩
      return SmartRefresher(
          controller: logic.myInforefreshController,
          onRefresh: () {
            logic.getHomePageTotalData("", "", "", "");
          },
          enablePullDown: true,
          enablePullUp: false,
          child: Container(
            margin: EdgeInsets.only(bottom: 20.w),
            child: BoxScore(type: type),
          ));
    case '1': // 团队业绩
      return TableDate();
    default:
      return Container();
  }
}

class TabBox extends StatefulWidget {
  const TabBox({super.key});

  @override
  State<TabBox> createState() => _TabBoxState();
}

class _TabBoxState extends State<TabBox> {
  String resultSelect = '0'; // 业绩类型
  String dateType = '0'; // 时间筛选
  final logic = Get.find<HomePageLogic>();
  final state = Get.find<HomePageLogic>().state;
  final statePulic = Get.find<Public>().state;

  @override
  void initState() {
    super.initState();
    // 获取版本更新信息
    getUpgradeInfo();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> _initResultList() {
      var tempList = [
        {
          "name": "个人业绩",
          "type": "0",
        },
        {
          "name": "团队业绩",
          "type": "1",
        }
      ].map((e) {
        final selected = e['type'];
        return GestureDetector(
          onTap: () {
            setState(() {
              resultSelect = selected.toString();
              if (state.yeJiType == resultSelect) {
                return;
              }
              state.yeJiType = selected;
              print("state.yeJiType-----${state.yeJiType}");
              if (state.yeJiType == "0") {
                logic.getHomePageTotalData("", "", "", "");
              } else {
                logic.getTeamData(true);
              }
              // logic.getHomePageTotalData(statePulic.userInfo["employeeNo"], "", "", "");
            });
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(0, 0, 30.w, 0),
            alignment: Alignment.bottomLeft,
            child: Text(
              "${e['name'] ?? ''}",
              style: TextStyle(
                color: selected == resultSelect
                    ? ColorResource.BLACK_COMMON_COLOR
                    : ColorResource.GRAY_COMMON_COLOR,
                fontSize: selected == resultSelect ? 34.w : 30.w,
                fontWeight: selected == resultSelect
                    ? FontWeight.w900
                    : FontWeight.normal,
              ),
            ),
          ),
        );
      });
      return tempList.toList();
    }

    List<Widget> _initDateList() {
      var tempList = [
        {
          "name": "今日",
          "type": "0",
        },
        {
          "name": "本周",
          "type": "1",
        },
        {
          "name": "本月",
          "type": "2",
        }
      ].map((e) {
        final selected = e['type'];
        return GestureDetector(
            onTap: () {
              setState(() {
                dateType = selected.toString();
                if (state.dateType == dateType) return;
                state.dateType = dateType;
                print("state.yeJiType-----${state.yeJiType}");
                if (state.yeJiType == "0") {
                  logic.getHomePageTotalData("", "", "", "");
                } else {
                  logic.getTeamData(true);
                }
              });
            },
            child: Container(
              // margin: EdgeInsets.fromLTRB(0, 0, 20, 0),
              alignment: Alignment.center,
              height: 50.w,
              width: 100.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25.w),
                  color: selected == dateType
                      ? Color.fromRGBO(250, 35, 33, 1)
                      : Color.fromRGBO(247, 224, 224, 1)),
              child: Text(
                "${e['name']}",
                style: TextStyle(
                    color: selected == dateType
                        ? Colors.white
                        : Color.fromRGBO(250, 35, 33, 1),
                    fontSize: 26.w),
              ),
            ));
      });
      return tempList.toList();
    }

    // 动态获取业绩内容
    Widget getBodys() {
      switch (resultSelect) {
        case '0': // 个人业绩
          return BoxScore(
            type: dateType,
          );
        case '1': // 团队业绩
          return TableDate();
        default:
          return Container();
      }
    }

    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: EdgeInsets.fromLTRB(20.w, 0, 0, 0),
            child: Row(
              children: _initResultList(),
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(0, 0, 20.w, 0),
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40.w),
                color: Color.fromRGBO(247, 224, 224, 1)),
            child: Row(
              children: _initDateList(),
            ),
          ),
        ],
      ),
    );
  }
}

// 个人业绩
class BoxScore extends StatelessWidget {
  String type = '0';
  String inName = '今日收款';
  String outName = '今日欠款';
  BoxScore({super.key, required this.type});

  Widget _card(String name, String num,
      {Color? textcolor, Function()? onClick}) {
    List canNotClick = ["下单数量", "今日欠款"];
    return GestureDetector(
      onTap: onClick,
      child: Container(
        width: 340.w,
        height: 170.w,
        padding: EdgeInsets.fromLTRB(20.w, 20.w, 20.w, 20.w),
        decoration: BoxDecoration(boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(24, 24, 24, .1),
            blurRadius: 5.w,
            spreadRadius: 0,
          ),
        ], borderRadius: BorderRadius.circular(15.w), color: Colors.white),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              num,
              style: TextStyle(
                  fontSize: 40.w,
                  fontWeight: FontWeight.bold,
                  color: textcolor ?? ColorResource.BLACK_NORMAL_TITLE_COLOR),
            ),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Text(
                name,
                style: TextStyle(
                  fontSize: 30.w,
                  color: textcolor ?? ColorResource.GRAY_EDIT_COLOR,
                ),
              ),
              canNotClick.contains(name)
                  ? Spacer()
                  : Icon(
                      Icons.chevron_right,
                      size: 40.w,
                      color: textcolor ?? Colors.grey[400],
                    )
            ])
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = Get.find<HomePageLogic>().state;
    String startTime = calendarManager(int.parse(state.dateType ?? "0"));
    String endTime = calendarManager(0);
    switch (type) {
      case '0':
        inName = '今日收款';
        outName = '今日欠款';
        break;
      case '1':
        inName = '本周收款';
        outName = '本周欠款';
        break;
      case '2':
        inName = '本月收款';
        outName = '本月欠款';
        break;
    }
    return GetBuilder<HomePageLogic>(
        builder: (_) => Container(
              margin: EdgeInsets.fromLTRB(20.w, 0.w, 20.w, 0.w),
              child: Wrap(
                spacing: 20.w, //主轴上子控件的间距
                runSpacing: 20.w, //交叉轴上子控件之间的间距
                alignment: WrapAlignment.spaceBetween,
                children: [
                  _card('业务员下单',
                      state.personalDataInfo["ywDataCountStr"] ?? "0.00",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'YW',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('店主下单',
                      state.personalDataInfo["dzDataCountStr"] ?? "0.00",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'DZ',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('退货金额',
                      state.personalDataInfo["thDataCountStr"] ?? "0.00",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'TH',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('后台下单',
                      state.personalDataInfo["htDataCountStr"] ?? "0.00",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'HT',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('下单数量',
                      state.personalDataInfo["xdDataCount"]?.toString() ?? "0",
                      onClick: () {}),
                  // _card('业务提成', state.personalDataInfo["ywtcDataCount"]?.toString() ?? "0.00"),
                  _card('拜访门店',
                      state.personalDataInfo["bfDataCount"]?.toString() ?? "0",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'BF',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('拓店数量',
                      state.personalDataInfo["tdDataCount"]?.toString() ?? "0",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'TD',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('动销门店',
                      state.personalDataInfo["dxDataCount"]?.toString() ?? "0",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'DX',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('未下单客户数',
                      state.personalDataInfo["wxdDataCount"]?.toString() ?? "0",
                      onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'XD',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card(
                      '动销SKU数',
                      state.personalDataInfo["dxSKUDataCount"]?.toString() ??
                          "0", onClick: () {
                    Get.toNamed(PageName.HOMESecondQuery, arguments: {
                      'searchSource': 'SKU',
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('$inName',
                      state.personalDataInfo["skDataCountStr"] ?? "0.00",
                      onClick: () {
                    Get.toNamed(PageName.todayCollectionPage, arguments: {
                      'startTime': startTime,
                      'endTime': endTime
                    });
                  }),
                  _card('$outName',
                      state.personalDataInfo["qkDataCountStr"] ?? "0.00",
                      onClick: () {}),
                ],
              ),
            ));
  }
}

class TableDate extends StatelessWidget {
  TableDate({super.key});

  final logic = Get.find<HomePageLogic>();
  final state = Get.find<HomePageLogic>().state;

  // 假数据，需要从后台获取
  @override
  Widget build(BuildContext context) {
    final state = Get.find<HomePageLogic>().state;
    List teamDataArray = state.teamDataInfo;
    List tableTitle = [
      {"title": "排名", "width": 100.w},
      {"title": "姓名"},
      {"title": "销售", "width": 160.w},
      {"title": "拜访", "width": 140.w},
      {"title": "动销", "width": 140.w},
    ];

    // 测试虚拟数据
    // List baseData = [
    //   {"name": "张三", "a": "9855.00", "b": "999", "c": "25", "d": "100"},
    //   {"name": "李四", "a": "9855.00", "b": "999", "c": "25", "d": "100"},
    //   {"name": "王五", "a": "9855.00", "b": "999", "c": "25", "d": "100"},
    //   {"name": "王五", "a": "9855.00", "b": "999", "c": "25", "d": "100"},
    //   {"name": "王五", "a": "9855.00", "b": "999", "c": "25", "d": "100"}
    // ];

    List<Widget> _getTableTitle() {
      var tempList = tableTitle.map((e) {
        double doubleWidth = (e['width'] ?? 0).toDouble();
        return doubleWidth == 0
            ? Expanded(
                child: Container(
                height: 100.w,
                alignment: Alignment.center,
                child: Text(
                  "${e['title']}",
                  style: TextStyle(fontSize: 24.w),
                ),
              ))
            : Container(
                width: doubleWidth,
                height: 100.w,
                alignment: Alignment.center,
                child: Text("${e['title']}", style: TextStyle(fontSize: 24.w)),
              );
      });
      return tempList.toList();
    }

    return Container(
      decoration: BoxDecoration(color: Colors.white),
      child: Column(
        children: [
          Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                      width: 1.0, color: Color.fromARGB(255, 240, 237, 237)),
                ),
              ),
              child: Row(
                children: _getTableTitle(),
              )),
          Expanded(
              child: SmartRefresher(
            controller: logic.refreshController,
            onRefresh: () {
              logic.getTeamData(true);
            },
            onLoading: () => logic.getTeamData(false),
            enablePullUp: true,
            enablePullDown: true,
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget body;
                var chString = ChRefreshString();
                if (mode == LoadStatus.idle) {
                  body = Text(chString.idleLoadingText!);
                } else if (mode == LoadStatus.loading) {
                  body = const CupertinoActivityIndicator();
                } else if (mode == LoadStatus.failed) {
                  body = Text(chString.loadFailedText ?? "");
                } else if (mode == LoadStatus.canLoading) {
                  body = Text(chString.canLoadingText!);
                } else {
                  body = Text(chString.noMoreText!,
                      style: TextStyle(color: ColorResource.GRAY_COMMON_COLOR));
                }
                return SizedBox(height: 55.0, child: Center(child: body));
              },
            ),
            child: GetBuilder<HomePageLogic>(
              builder: (controller) {
                return MediaQuery.removePadding(
                    context: context,
                    removeTop: true,
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: teamDataArray.length,
                      itemBuilder: (context, index) {
                        var e = teamDataArray[index];
                        String indexStr = (index + 1).toString();
                        // List iconPath = [
                        //   "https://qiniu.tfzhongchukeji.com/pay/64f8401affde233336a8ae0d.png",
                        //   "https://qiniu.tfzhongchukeji.com/pay/64f84029ffde233336a8ae0e.png",
                        //   "https://qiniu.tfzhongchukeji.com/pay/64f84037ffde233336a8ae0f.png"
                        // ];
                        List imgPath = [
                          'assets/images/rank_one.png',
                          'assets/images/rank_two.png',
                          'assets/images/rank_three.png'
                        ];

                        return Row(
                          children: [
                            Container(
                              width: 100.w,
                              height: 80.w,
                              alignment: Alignment.center,
                              child: index > 2
                                  ? Text(indexStr,
                                      style: TextStyle(fontSize: 24.w))
                                  : Image.asset(imgPath[index],
                                      width: 50.w,
                                      height: 50.w,
                                      alignment: Alignment.center,
                                      fit: BoxFit.cover),
                            ),
                            Expanded(
                                child: Container(
                              alignment: Alignment.center,
                              child: Text(e['employeeName'] ?? "",
                                  style: TextStyle(fontSize: 24.w)),
                            )),
                            Container(
                              width: 160.w,
                              alignment: Alignment.center,
                              child: Text((e['xsAmt'] ?? 0).toString(),
                                  style: TextStyle(fontSize: 24.w)),
                            ),
                            Container(
                              width: 140.w,
                              alignment: Alignment.center,
                              child: Text((e['bfConsumerNum'] ?? 0).toString(),
                                  style: TextStyle(fontSize: 24.w)),
                            ),
                            Container(
                              width: 140.w,
                              alignment: Alignment.center,
                              child: Text((e['dxConsumerNum'] ?? 0).toString(),
                                  style: TextStyle(fontSize: 24.w)),
                            )
                          ],
                        );
                      },
                    ));
              },
            ),
          )),
        ],
      ),
    );
  }
}

// 内容块
class ContentBox extends StatelessWidget {
  const ContentBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      transform: Matrix4.translation(v.Vector3(0.0, -90.0.w, 0.0)),
      child: Column(
        children: [BtnList(), TabBox()],
      ),
    );
  }
}

// 任务列表
_missionList(BuildContext context) {
  final homeState = Get.find<HomePageLogic>().state;
  return Visibility(
      visible: homeState.missionCount > 0,
      child: Container(
        margin: EdgeInsets.only(left: 20.w, right: 20.w),
        padding:
            EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
        height: homeState.missionCount > 1
            ? (20.h + 50.h + 20.h + 150.h + 20.h + 150.h + 20.h)
            : homeState.missionCount > 0
                ? (20.h + 50.h + 20.h + 150.h + 20.h)
                : (20.h + 50.h + 20.h),
        decoration: BoxDecoration(
          color: ColorResource.WHITE_COMMON_COLOR,
          // color: Colors.yellow,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Text("任务返利活动",
                    style: TextStyle(
                        color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w600)),
                Spacer(),
                InkWell(
                  onTap: () {
                    // MyCommonUtils.showToast("跳转任务列表页面");
                    Get.toNamed(PageName.MissionSystemListPage)
                        ?.then((value) => {});
                  },
                  child: Row(
                    children: [
                      Text("更多任务",
                          style: TextStyle(
                              color: ColorResource.BLACK_NORMAL_TITLE_COLOR,
                              fontSize: 24.sp)),
                      Container(
                        width: 30.w,
                        height: 30.h,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(20.r),
                          child: ExtendedImage.asset(ImageResource.ARROW_RIGHT,
                              color: ColorResource.BLACK_COMMON_COLOR),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
            Visibility(
                visible: homeState.missionCount > 0,
                child: SizedBox(height: 20.h)),
            Visibility(
                visible: homeState.missionCount > 0,
                child: Container(
                  height: 150.h,
                  padding: EdgeInsets.only(
                      left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
                  decoration: BoxDecoration(
                    color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text("云商代客下单",
                              style: TextStyle(
                                  color: ColorResource.BLACK_NORMAL_TITLE_COLOR,
                                  fontSize: 30.sp)),
                          Spacer(),
                          Container(
                            alignment: Alignment.center,
                            width: 150.w,
                            height: 50.h,
                            decoration: BoxDecoration(
                              color: ColorResource.ORANGE_RED_COMMON_COLOR,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(25.r)),
                            ),
                            child: InkWell(
                              onTap: () {
                                Get.toNamed(PageName.CustomerListPage)
                                    ?.then((value) => {});
                              },
                              child: Text(
                                "去下单",
                                style: TextStyle(
                                    color: ColorResource.WHITE_COMMON_COLOR,
                                    fontSize: 24.sp,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                          )
                        ],
                      ),
                      Spacer(),
                      Text("订单完成配送，实付金额每满10元领1福分",
                          style: TextStyle(
                              color: ColorResource.GRAY_TITLE_COLOR,
                              fontSize: 24.sp)),
                    ],
                  ),
                )),
            Visibility(
                visible: homeState.missionCount > 1,
                child: SizedBox(height: 20.h)),
            Visibility(
                visible: homeState.missionCount > 1,
                child: Container(
                  height: 150.h,
                  padding: EdgeInsets.only(
                      left: 20.w, right: 20.w, top: 20.h, bottom: 20.h),
                  decoration: BoxDecoration(
                    color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text("销售订货",
                              style: TextStyle(
                                  color: ColorResource.BLACK_NORMAL_TITLE_COLOR,
                                  fontSize: 30.sp)),
                          Spacer(),
                          Container(
                            alignment: Alignment.center,
                            width: 150.w,
                            height: 50.h,
                            decoration: BoxDecoration(
                              color: ColorResource.ORANGE_RED_COMMON_COLOR,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(25.r)),
                            ),
                            child: InkWell(
                              onTap: () {
                                Get.toNamed(PageName.CustomerListPage)
                                    ?.then((value) => {});
                              },
                              child: Text(
                                "去下单",
                                style: TextStyle(
                                    color: ColorResource.WHITE_COMMON_COLOR,
                                    fontSize: 24.sp,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                          )
                        ],
                      ),
                      Spacer(),
                      Text("订单完成配送，实付金额每满10元领1福分",
                          style: TextStyle(
                              color: ColorResource.GRAY_TITLE_COLOR,
                              fontSize: 24.sp)),
                    ],
                  ),
                )),
          ],
        ),
      ));
}

// 按钮
class BtnList extends StatelessWidget {
  BtnList({super.key});

  @override
  Widget build(BuildContext context) {
    final pulicState = Get.find<Public>().state;
    _MenuListView(BuildContext context) {
      var tempList = [];
      if (UserAppModuleUtils().moduleCanShow('custom')) {
        tempList.add({
          "name": "客户",
          "path": PageName.CustomerListPage,
          "icon": 'assets/images/kehu.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('replenishment')) {
        tempList.add({
          "name": "补货",
          "path": PageName.ReplenishmentPage,
          "icon": 'assets/images/buhuo.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('arrears')) {
        tempList.add({
          "name": "欠款",
          "path": PageName.DeptCustomerListPage,
          "icon": 'assets/images/qiankuan.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('expand_shop')) {
        tempList.add({
          "name": "拓店",
          "path": PageName.CustomerBindEmployeeListPage,
          "icon": 'assets/images/tuodian.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('return_warehouse')) {
        tempList.add({
          "name": "返仓",
          "path": PageName.ReplenishmentPage,
          "icon": 'assets/images/fancang.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('inventory')) {
        tempList.add({
          "name": "盘点",
          "path": PageName.ReplenishmentPage,
          "icon": 'assets/images/pandian.png'
        });
      }
      if (UserAppModuleUtils().moduleCanShow('unload')) {
        tempList.add({
          "name": "卸货",
          "path": PageName.CloudCommerceUnloadPage,
          "icon": 'assets/images/xiehuo.png'
        });
      }
      if (SpUtil.getString('showShopScan', defValue: '0') != '0') {
        tempList.add({
          "name": "店铺预览",
          "path": PageName.SelectShopPage,
          "icon": 'assets/images/shop_preview_icon.png'
        });
      }
      return ListView.builder(
        padding: EdgeInsets.only(left: 0.w),
        physics: const ScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: tempList.length,
        itemBuilder: (context, index) {
          double width = (1.sw - 48.w) / 4.5;
          var item = tempList[index];
          return GestureDetector(
            onTap: () {
              if (item['name'].toString().contains('补货')) {
                Get.toNamed("${item['path']}", arguments: {'tabIndex': 0});
              } else if (item['name'].toString().contains('返仓')) {
                Get.toNamed("${item['path']}", arguments: {'tabIndex': 1});
              } else if (item['name'].toString().contains('盘点')) {
                Get.toNamed("${item['path']}", arguments: {'tabIndex': 2});
              } else if (item['name'].toString().contains('店铺预览')) {
                // 如果只绑定了一个经销商直接跳转网页
                String shopId =
                    SpUtil.getString('showShopScan', defValue: '0')!;
                Map fstShopInfo = {'externalCode': shopId};
                Get.toNamed(PageName.ShopPreviewPage, arguments: fstShopInfo);
              } else {
                Get.toNamed("${item['path']}");
              }
            },
            child: Container(
              height: 210.h,
              width: width,
              child: Column(children: [
                Stack(
                  children: [
                    Image.asset(
                      "${item["icon"]}",
                      width: 160.w,
                      height: 160.w,
                      fit: BoxFit.fill,
                    ),
                    Container(
                        alignment: Alignment.center,
                        width: width,
                        height: 50.w,
                        margin: EdgeInsets.only(top: 130.w),
                        child: Text(
                          "${item["name"]}",
                          style: TextStyle(
                            fontSize: 30.w,
                            fontWeight: FontWeight.w800,
                          ),
                        ))
                  ],
                )
              ]),
            ),
          );
        },
      );
    }

    return Container(
      height: 210.w,
      padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
      margin: EdgeInsets.fromLTRB(24.w, 0, 24.w, 0.w),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.w),
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(24, 24, 24, .2),
              blurRadius: 10.w,
              spreadRadius: 0,
              offset: Offset(0, 0),
            ),
          ]),
      // child: Row(children: _initListData()),
      child: _MenuListView(context),
    );
  }
}

//弹框切换店铺

// 头部组件
class HeaderBox extends StatefulWidget {
  const HeaderBox({super.key});

  @override
  State<HeaderBox> createState() => _HeaderBoxState();
}

class _HeaderBoxState extends State<HeaderBox> {
  final state = Get.find<Public>().state;
  final logic = Get.find<Public>();
  final homeState = Get.find<HomePageLogic>().state;

  @override
  Widget build(BuildContext context) {
    List<Color> _color = [
      Color.fromRGBO(255, 112, 29, 1),
      Color.fromRGBO(255, 41, 29, 1)
    ];
    return GetBuilder<Public>(
        builder: (_) => Row(
              children: [
                Expanded(
                    child: Container(
                  // decoration: BoxDecoration(gradient: LinearGradient(colors: _color)),
                  // color: Color.fromRGBO(153, 29, 255, 1),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppBar(
                        foregroundColor: Colors.white,
                        title: Text('${SpUtil.getString('branchName') ?? ''}',
                            style: TextStyle(
                                fontSize: 34.sp, color: Colors.white)),
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        centerTitle: true,
                        actions: [
                          GestureDetector(
                            onTap: () {
                              if (state.strSwitch.isNotEmpty) {
                                _showDialog(
                                    context, state.userInfo["branches"]);
                              }
                            },
                            child: Container(
                              // decoration: BoxDecoration(color: Colors.green),
                              alignment: Alignment.center,
                              margin: EdgeInsets.only(right: 32.w),
                              child: Text(
                                // '切换仓库',
                                "${state.strSwitch}",
                                style: TextStyle(
                                    fontSize: 28.sp, color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.fromLTRB(20.w, 20.w, 40.w, 1.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            GestureDetector(
                              onTap: () {
                                // _showDialog(context, state.userInfo["branches"]);
                              },
                              child: Text(
                                // '岳麓区吴彦祖',
                                '${state.userInfo["employee"]?["employeeName"] ?? ""}',
                                style: TextStyle(
                                    fontSize: 36.w,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                            SizedBox(height: 32.w),
                            Text(
                              // '999.00',
                              "${homeState.todayOrderMount.toStringAsFixed(2)}",
                              style: TextStyle(
                                  fontSize: 60.w,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 16.w),
                            Text(
                              '今日订单金额（元）',
                              style: TextStyle(
                                  fontSize: 28.w,
                                  color: Color.fromARGB(255, 243, 222, 222)),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ))
              ],
            ));
  }

  void _showDialog(BuildContext context, branches) {
    if (branches.length > 1) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return StatefulBuilder(builder: (context, states) {
              return Dialog(
                insetPadding: const EdgeInsets.only(left: 32, right: 32),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(32.r)),
                  side: BorderSide(color: Colors.white),
                ),
                child: Container(
                  width: 1.sw,
                  height: 600.w,
                  decoration: BoxDecoration(
                    // color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(32.r)),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          // padding: EdgeInsets.fromLTRB(15.w, 48.w, 15, 20),
                          margin: EdgeInsets.only(
                              left: 48.w, top: 48.w, right: 48.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                margin: EdgeInsets.only(bottom: 32.w),
                                child: Text(
                                  '选择仓库',
                                  style: TextStyle(
                                      fontSize: 32.sp,
                                      color: ColorResource.BLACK_COMMON_COLOR,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Expanded(
                                  child: ListView.builder(
                                itemBuilder: (context, index) {
                                  var itemEntry = branches[index];
                                  return _dialogItem(
                                      context, index, itemEntry, states);
                                },
                                itemCount: branches.length,
                              )),
                            ],
                          ),
                        ),
                      ),
                      Container(
                        height: 2.w,
                        color:
                            ColorResource.LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                      ),
                      Container(
                        height: 96.w,
                        child: Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: InkWell(
                                onTap: () {
                                  // MyCommonUtils.showToast('取消');
                                  Get.back();
                                },
                                child: Container(
                                  // color:Colors.green,
                                  width: 1.sw,
                                  alignment: Alignment.center,
                                  child: Text(
                                    '取消',
                                    style: TextStyle(
                                        fontSize: 32.sp,
                                        color:
                                            ColorResource.BLACK_COMMON_COLOR),
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              width: 2.w,
                              color: ColorResource
                                  .LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                            ),
                            Expanded(
                              flex: 1,
                              child: InkWell(
                                onTap: () {
                                  // MyCommonUtils.showToast('切换');

                                  SpUtil.putString("branchNo", state.branchNo);
                                  SpUtil.putString(
                                      "branchName", state.branchName);
                                  setState(() {});
                                  // SpUtil.putString("branchNo", branches[0]['branchNo']);
                                  Get.back();
                                },
                                child: Container(
                                  width: 1.sw,
                                  // color:Colors.blue,
                                  alignment: Alignment.center,
                                  child: Text(
                                    '切换',
                                    style: TextStyle(
                                        fontSize: 32.sp,
                                        color: ColorResource.RED_COMMON_COLOR),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            });
          });
    } else {
      MyCommonUtils.showToast("无法切换仓库");
    }
  }

  _dialogItem(BuildContext context, int index, itemEntry, states) {
    return GestureDetector(
      onTap: () {
        state.selectedindex = index;
        state.branchName = "${itemEntry["branchName"]}";
        state.branchNo = "${itemEntry["branchNo"]}";
        // logic.changebranchInfo(itemEntry);

        ///更新dialog
        states(() {});

        ///更新底下布局
        // setState(() {});
      },
      child: Container(
        height: 88.w,
        margin: EdgeInsets.only(bottom: 32.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: index == state.selectedindex
                ? Border.all(color: ColorResource.RED_TITLE_COLOR, width: 2.w)
                : null,
            color: index == state.selectedindex
                ? Color(0x1aff211a)
                : ColorResource.GRAY_F7F8FA_COLOR),
        child: Text(
          '${itemEntry["branchName"]}',
          style: TextStyle(
              color: index == state.selectedindex
                  ? ColorResource.RED_TITLE_COLOR
                  : ColorResource.BLACK_COMMON_COLOR,
              fontSize: 28.sp),
        ),
      ),
    );
  }
}
