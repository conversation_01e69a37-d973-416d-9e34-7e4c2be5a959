import 'package:fuduoduo/common/apis.dart';

class B2bCustomerInfoPageState {
  String branchNo = ""; // 仓库编号
  String branchId = ""; // 门店ID
  String branchName = ""; // 门店名称
  String branchAddr = ""; // 门店地址
  String consumerNo = ""; // 门店编号（兼容旧版本）
  String consumerName = ''; // 门店名称（兼容旧版本）
  String consumerAddress = ''; // 门店地址（兼容旧版本）
  dynamic status = ''; // 门店状态是否需要签到，签退
  String visitLogId = ""; // 访问日志ID
  Map consumerInfo = {}; //门店信息（兼容旧版本）
  Map branchInfo = {}; // 新的门店详细信息
  Map monthlyOrderStats = {}; // 月度订单统计数据
  double effectiveMaxDistance = 500; // 有效距离 米
  dynamic userLocation = {}; // 用户的定位地址
  bool isAuthorityEdit = true; // 业务员是否有修改权限
  bool isNeedVisit = true;//默认需要签到
  bool isNeedPhoto = true;//默认需要门头照
  List cartData = []; // 购物车信息
  int userAcountNum = 0;

  String fromPage = ''; // 代客下单页面跳转标识
  B2bCustomerInfoPageState() {
    ///Initialize variables
  }
}
