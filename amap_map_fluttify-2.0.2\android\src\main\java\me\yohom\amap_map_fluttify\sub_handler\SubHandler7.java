//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler7 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.AMap::removeOnIndoorBuildingActiveListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnIndoorBuildingActiveListener var1 = (com.amap.api.maps.AMap.OnIndoorBuildingActiveListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnIndoorBuildingActiveListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnIndoorBuildingActiveListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::removeOnMyLocationChangeListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMap.OnMyLocationChangeListener var1 = (com.amap.api.maps.AMap.OnMyLocationChangeListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::removeOnMyLocationChangeListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.removeOnMyLocationChangeListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setWorldVectorMapStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setWorldVectorMapStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setWorldVectorMapStyle(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::getCurrentStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::getCurrentStyle(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getCurrentStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::accelerateNetworkInChinese", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::accelerateNetworkInChinese(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.accelerateNetworkInChinese(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMap::setConstructingRoadEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMap __this__ = (com.amap.api.maps.AMap) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMap@" + __this__ + "::setConstructingRoadEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setConstructingRoadEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::getMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::getMap(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMap __result__ = null;
                try {
                    __result__ = __this__.getMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onCreate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onCreate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onCreate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onResume", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onResume(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onResume();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onPause", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onPause(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onPause();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onDestroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onDestroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onLowMemory", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onLowMemory(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onLowMemory();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::onSaveInstanceState", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::onSaveInstanceState(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onSaveInstanceState(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.MapView::setVisibility", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.MapView __this__ = (com.amap.api.maps.MapView) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.MapView@" + __this__ + "::setVisibility(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisibility(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getLatitude", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::getLatitude(" + "" + ")");
                }
            
                // invoke native method
                Double __result__ = null;
                try {
                    __result__ = __this__.getLatitude();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setLatitude", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::setLatitude(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLatitude(var1.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getLongitude", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::getLongitude(" + "" + ")");
                }
            
                // invoke native method
                Double __result__ = null;
                try {
                    __result__ = __this__.getLongitude();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setLongitude", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::setLongitude(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLongitude(var1.doubleValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getSpeed", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::getSpeed(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getSpeed();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setSpeed", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::setSpeed(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSpeed(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getBearing", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::getBearing(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getBearing();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setBearing", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::setBearing(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setBearing(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::getTime", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::getTime(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getTime();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::setTime", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::setTime(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTime(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceLocation::copy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceLocation __this__ = (com.amap.api.trace.TraceLocation) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceLocation@" + __this__ + "::copy(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.trace.TraceLocation __result__ = null;
                try {
                    __result__ = __this__.copy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::getInstance", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.content.Context var0 = (android.content.Context) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.LBSTraceClient::getInstance(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.trace.LBSTraceClient __result__ = null;
                try {
                    __result__ = com.amap.api.trace.LBSTraceClient.getInstance(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::queryProcessedTrace", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                java.util.List<com.amap.api.trace.TraceLocation> var2 = (java.util.List<com.amap.api.trace.TraceLocation>) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                com.amap.api.trace.TraceListener var4 = (com.amap.api.trace.TraceListener) ((Map<String, Object>) __args__).get("var4");
            
                // ref
                com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.LBSTraceClient@" + __this__ + "::queryProcessedTrace(" + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.queryProcessedTrace(var1.intValue(), var2, var3.intValue(), var4);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::startTrace", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.trace.TraceStatusListener var1 = (com.amap.api.trace.TraceStatusListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.LBSTraceClient@" + __this__ + "::startTrace(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.startTrace(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::stopTrace", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.LBSTraceClient@" + __this__ + "::stopTrace(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.stopTrace();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.LBSTraceClient::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.LBSTraceClient __this__ = (com.amap.api.trace.LBSTraceClient) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.LBSTraceClient@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::add", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::add(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.add(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setProperCamera", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::setProperCamera(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setProperCamera(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::zoopToSpan", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::zoopToSpan(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.zoopToSpan();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getTraceStatus", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::getTraceStatus(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTraceStatus();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setTraceStatus", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::setTraceStatus(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTraceStatus(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getDistance", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::getDistance(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getDistance();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setDistance", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::setDistance(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDistance(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::getWaitTime", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::getWaitTime(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getWaitTime();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.trace.TraceOverlay::setWaitTime", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.trace.TraceOverlay __this__ = (com.amap.api.trace.TraceOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.trace.TraceOverlay@" + __this__ + "::setWaitTime(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setWaitTime(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.offlineservice.AMapPermissionActivity::onRequestPermissionsResult_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    String[] var2 = (String[]) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    int[] var3 = (int[]) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.offlineservice.AMapPermissionActivity __this__ = (com.amap.api.offlineservice.AMapPermissionActivity) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onRequestPermissionsResult(var1.intValue(), var2, var3);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::newInstance_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.SupportMapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.SupportMapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.SupportMapFragment.newInstance();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::newInstance__com_amap_api_maps_AMapOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.SupportMapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.AMapOptions var0 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.SupportMapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.SupportMapFragment.newInstance(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::getMap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMap> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMap __result__ = null;
                    try {
                        __result__ = __this__.getMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onAttach_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.app.Activity var1 = (android.app.Activity) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onAttach(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onCreate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onCreate(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onResume_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onResume();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onPause_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onPause();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onDestroyView_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroyView();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onDestroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onLowMemory_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onLowMemory();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onSaveInstanceState_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onSaveInstanceState(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::setArguments_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setArguments(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::setUserVisibleHint_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setUserVisibleHint(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setScaleControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setScaleControlsEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZoomControlsEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setCompassEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCompassEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setMyLocationButtonEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMyLocationButtonEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setScrollGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setScrollGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZoomGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setTiltGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTiltGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setRotateGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRotateGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setAllGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAllGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLogoPosition(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZoomPosition(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getZoomPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getZoomPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isScaleControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isScaleControlsEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isZoomControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isZoomControlsEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isCompassEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isCompassEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isMyLocationButtonEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isMyLocationButtonEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isScrollGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isScrollGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isZoomGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isZoomGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isTiltGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isTiltGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isRotateGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isRotateGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getLogoPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getLogoPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isIndoorSwitchEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isIndoorSwitchEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setIndoorSwitchEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setIndoorSwitchEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoMarginRate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLogoMarginRate(var1.intValue(), var2.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getLogoMarginRate_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getLogoMarginRate(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoLeftMargin_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLogoLeftMargin(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoBottomMargin_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLogoBottomMargin(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomInByScreenCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setZoomInByScreenCenter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setGestureScaleByMapCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setGestureScaleByMapCenter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isGestureScaleByMapCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isGestureScaleByMapCenter();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLogoCenter(var1.intValue(), var2.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindowUpdateTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInfoWindowUpdateTime(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindowUpdateTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowUpdateTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindowType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInfoWindowType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindowType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindow_batch", (__argsBatch__, __methodResult__) -> {
                List<android.view.View> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.view.View __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindow();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoContent_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.view.View var1 = (android.view.View) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInfoContent(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindow_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.view.View var1 = (android.view.View) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInfoWindow(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoContents_batch", (__argsBatch__, __methodResult__) -> {
                List<android.view.View> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.view.View __result__ = null;
                    try {
                        __result__ = __this__.getInfoContents();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomIn_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.zoomIn();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomOut_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.zoomOut();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::scrollBy_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.scrollBy(var0.floatValue(), var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomTo_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.zoomTo(var0.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomBy__double_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.zoomBy(var0.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomBy__double__android_graphics_Point_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    android.graphics.Point var1 = (android.graphics.Point) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.zoomBy(var0.floatValue(), var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newCameraPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.CameraPosition var0 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newCameraPosition(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLng(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngZoom_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngZoom(var0, var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBounds__com_amap_api_maps_model_LatLngBounds__int_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBounds(var0, var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeLatLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.changeLatLng(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeBearing_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.changeBearing(var0.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeTilt_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.changeTilt(var0.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBounds__com_amap_api_maps_model_LatLngBounds__int__int__int_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBounds(var0, var1.intValue(), var2.intValue(), var3.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBoundsRect_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CameraUpdate> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                    // ref arg
                    Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.CameraUpdate __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBoundsRect(var0, var1.intValue(), var2.intValue(), var3.intValue(), var4.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapException::getErrorMessage_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapException __this__ = (com.amap.api.maps.AMapException) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getErrorMessage();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::logoPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.logoPosition(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::zOrderOnTop_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.zOrderOnTop(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::mapType_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.mapType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::camera_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.CameraPosition var1 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.camera(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::scaleControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.scaleControlsEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::zoomControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.zoomControlsEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::compassEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.compassEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::scrollGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.scrollGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::zoomGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.zoomGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::tiltGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.tiltGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::rotateGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions __result__ = null;
                    try {
                        __result__ = __this__.rotateGesturesEnabled(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getLogoPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getLogoPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getZOrderOnTop_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getZOrderOnTop();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getMapType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getMapType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getCamera_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.CameraPosition> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.CameraPosition __result__ = null;
                    try {
                        __result__ = __this__.getCamera();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getScaleControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getScaleControlsEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getZoomControlsEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getZoomControlsEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getCompassEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getCompassEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getScrollGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getScrollGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getZoomGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getZoomGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getTiltGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getTiltGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::getRotateGesturesEnabled_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.getRotateGesturesEnabled();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMapOptionsCreator::newArray_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMapOptions[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMapOptionsCreator __this__ = (com.amap.api.maps.AMapOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMapOptions[] __result__ = null;
                    try {
                        __result__ = __this__.newArray(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CoordinateConverter::from_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CoordinateConverter> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // enum arg
                    com.amap.api.maps.CoordinateConverter.CoordType var1 = com.amap.api.maps.CoordinateConverter.CoordType.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                    // ref
                    com.amap.api.maps.CoordinateConverter __this__ = (com.amap.api.maps.CoordinateConverter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.CoordinateConverter __result__ = null;
                    try {
                        __result__ = __this__.from(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CoordinateConverter::coord_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.CoordinateConverter> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.CoordinateConverter __this__ = (com.amap.api.maps.CoordinateConverter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.CoordinateConverter __result__ = null;
                    try {
                        __result__ = __this__.coord(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CoordinateConverter::convert_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.CoordinateConverter __this__ = (com.amap.api.maps.CoordinateConverter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.convert();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CoordinateConverter::isAMapDataAvailable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
            
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.CoordinateConverter.isAMapDataAvailable(var0.doubleValue(), var2.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap.InfoWindowAdapter::getInfoWindow_batch", (__argsBatch__, __methodResult__) -> {
                List<android.view.View> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.Marker var1 = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap.InfoWindowAdapter __this__ = (com.amap.api.maps.AMap.InfoWindowAdapter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.view.View __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindow(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap.InfoWindowAdapter::getInfoContents_batch", (__argsBatch__, __methodResult__) -> {
                List<android.view.View> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.Marker var1 = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.AMap.InfoWindowAdapter __this__ = (com.amap.api.maps.AMap.InfoWindowAdapter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.view.View __result__ = null;
                    try {
                        __result__ = __this__.getInfoContents(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.AMap.ImageInfoWindowAdapter::getInfoWindowUpdateTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.AMap.ImageInfoWindowAdapter __this__ = (com.amap.api.maps.AMap.ImageInfoWindowAdapter) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getInfoWindowUpdateTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::setPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoints(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::resetIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.resetIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::setTotalDuration_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTotalDuration(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::startSmoothMove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.startSmoothMove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::stopMove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.stopMove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::getObject_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.BasePointOverlay> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.BasePointOverlay __result__ = null;
                    try {
                        __result__ = __this__.getObject();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::getPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::getIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::removeMarker_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.removeMarker();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::setPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPosition(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::setRotate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRotate(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.MovingPointOverlay::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.MovingPointOverlay __this__ = (com.amap.api.maps.utils.overlay.MovingPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setPoints_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoints(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setTotalDuration_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTotalDuration(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::startSmoothMove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.startSmoothMove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::stopMove_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.stopMove();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::getMarker_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Marker> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.Marker __result__ = null;
                    try {
                        __result__ = __this__.getMarker();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::getPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.model.LatLng __result__ = null;
                    try {
                        __result__ = __this__.getPosition();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::getIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::resetIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.resetIndex();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::destroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.destroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::removeMarker_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.removeMarker();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setPosition_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPosition(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setDescriptor_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDescriptor(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setRotate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRotate(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::setVisible_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setVisible(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::newInstance_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.TextureSupportMapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.TextureSupportMapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.TextureSupportMapFragment.newInstance();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::newInstance__com_amap_api_maps_AMapOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.TextureSupportMapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.AMapOptions var0 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.TextureSupportMapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.TextureSupportMapFragment.newInstance(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::getMap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMap> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMap __result__ = null;
                    try {
                        __result__ = __this__.getMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onAttach_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.app.Activity var1 = (android.app.Activity) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onAttach(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onCreate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onCreate(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onResume_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onResume();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onPause_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onPause();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onDestroyView_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroyView();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onDestroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onLowMemory_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onLowMemory();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::onSaveInstanceState_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onSaveInstanceState(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::setArguments_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setArguments(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.TextureSupportMapFragment::setUserVisibleHint_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.TextureSupportMapFragment __this__ = (com.amap.api.maps.TextureSupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setUserVisibleHint(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::newInstance_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.MapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.MapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.MapFragment.newInstance();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::newInstance__com_amap_api_maps_AMapOptions_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.MapFragment> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.maps.AMapOptions var0 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    com.amap.api.maps.MapFragment __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.MapFragment.newInstance(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::getMap_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.AMap> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.maps.AMap __result__ = null;
                    try {
                        __result__ = __this__.getMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onAttach_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.app.Activity var1 = (android.app.Activity) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onAttach(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onCreate_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onCreate(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onResume_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onResume();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onPause_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onPause();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onDestroyView_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroyView();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onDestroy_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onDestroy();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onLowMemory_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onLowMemory();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::onSaveInstanceState_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.onSaveInstanceState(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::setArguments_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setArguments(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapFragment::setUserVisibleHint_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.maps.MapFragment __this__ = (com.amap.api.maps.MapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setUserVisibleHint(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.CustomRenderer::OnMapReferencechanged_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.maps.CustomRenderer __this__ = (com.amap.api.maps.CustomRenderer) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.OnMapReferencechanged();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::initialize_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.content.Context var0 = (android.content.Context) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        com.amap.api.maps.MapsInitializer.initialize(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::setNetWorkEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var0 = (boolean) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        com.amap.api.maps.MapsInitializer.setNetWorkEnable(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::getNetWorkEnable_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.MapsInitializer.getNetWorkEnable();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::setApiKey_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var0 = (String) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        com.amap.api.maps.MapsInitializer.setApiKey(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::getVersion_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.MapsInitializer.getVersion();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::loadWorldGridMap_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var0 = (boolean) ((Map<String, Object>) __args__).get("var0");
            
                    // ref
            
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        com.amap.api.maps.MapsInitializer.loadWorldGridMap(var0);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.maps.MapsInitializer::isLoadWorldGridMap_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
            
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = com.amap.api.maps.MapsInitializer.isLoadWorldGridMap();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
        }};
    }
}
