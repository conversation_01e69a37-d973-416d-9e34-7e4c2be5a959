//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "MAMapViewDelegate_Anonymous.h"
#import <Flutter/Flutter.h>
#import "FluttifyMessageCodec.h"
#import <objc/runtime.h>

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation MAMapViewDelegate_Anonymous

- (instancetype) initWithFlutterPluginRegistrar: (NSObject <FlutterPluginRegistrar> *) registrar {
  self = [super init];
  if (self) {
    _registrar = registrar;
  }

  return self;
}

- (void)mapViewRegionChanged : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewRegionChanged");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewRegionChanged" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapView : (MAMapView*)mapView regionWillChangeAnimated: (BOOL)animated
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_regionWillChangeAnimated");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* arganimated = @(animated);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_regionWillChangeAnimated" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"animated": arganimated == nil ? [NSNull null] : arganimated}];
  });
  
}

- (void)mapView : (MAMapView*)mapView regionDidChangeAnimated: (BOOL)animated
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_regionDidChangeAnimated");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* arganimated = @(animated);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_regionDidChangeAnimated" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"animated": arganimated == nil ? [NSNull null] : arganimated}];
  });
  
}

- (void)mapView : (MAMapView*)mapView regionWillChangeAnimated: (BOOL)animated wasUserAction: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_regionWillChangeAnimated_wasUserAction");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* arganimated = @(animated);
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_regionWillChangeAnimated_wasUserAction" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"animated": arganimated == nil ? [NSNull null] : arganimated, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapView : (MAMapView*)mapView regionDidChangeAnimated: (BOOL)animated wasUserAction: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_regionDidChangeAnimated_wasUserAction");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* arganimated = @(animated);
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_regionDidChangeAnimated_wasUserAction" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"animated": arganimated == nil ? [NSNull null] : arganimated, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapView : (MAMapView*)mapView mapWillMoveByUser: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_mapWillMoveByUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_mapWillMoveByUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapView : (MAMapView*)mapView mapDidMoveByUser: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_mapDidMoveByUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_mapDidMoveByUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapView : (MAMapView*)mapView mapWillZoomByUser: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_mapWillZoomByUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_mapWillZoomByUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapView : (MAMapView*)mapView mapDidZoomByUser: (BOOL)wasUserAction
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_mapDidZoomByUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* argwasUserAction = @(wasUserAction);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_mapDidZoomByUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"wasUserAction": argwasUserAction == nil ? [NSNull null] : argwasUserAction}];
  });
  
}

- (void)mapViewWillStartLoadingMap : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewWillStartLoadingMap");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewWillStartLoadingMap" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapViewDidFinishLoadingMap : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewDidFinishLoadingMap");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewDidFinishLoadingMap" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapViewDidFailLoadingMap : (MAMapView*)mapView withError: (NSError*)error
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewDidFailLoadingMap_withError");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  NSError* argerror = error;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewDidFailLoadingMap_withError" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"error": argerror == nil ? [NSNull null] : argerror}];
  });
  
}

- (MAAnnotationView*)mapView : (MAMapView*)mapView viewForAnnotation: (id<MAAnnotation>)annotation
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_viewForAnnotation");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  id<MAAnnotation> argannotation = annotation;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_viewForAnnotation"
                arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"annotation": argannotation == nil ? [NSNull null] : argannotation}
                   result:^(id result) {}]; // 由于结果是异步返回, 这里用不上, 所以就不生成代码了
  });
  
  // 由于flutter无法同步调用method channel, 所以暂不支持有返回值的回调方法
  // 相关issue https://github.com/flutter/flutter/issues/28310
  NSLog(@"暂不支持有返回值的回调方法");
  
  ////////////////////////////如果需要手写代码, 请写在这里/////////////////////////////
UIImage* icon = (UIImage *) objc_getAssociatedObject(annotation, (const void *) 1);
  NSArray<UIImage*>* icons = (NSArray<UIImage*>*) objc_getAssociatedObject(annotation, (const void *) 11);
  NSNumber* duration = (NSNumber*) objc_getAssociatedObject(annotation, (const void *) 12);
  NSNumber* draggable = objc_getAssociatedObject(annotation, (const void *) 2);
  NSNumber* rotateAngle = objc_getAssociatedObject(annotation, (const void *) 3);
  NSNumber* infoWindowEnabled = objc_getAssociatedObject(annotation, (const void *) 4);
  NSNumber* anchorU = objc_getAssociatedObject(annotation, (const void *) 5);
  NSNumber* anchorV = objc_getAssociatedObject(annotation, (const void *) 6);
  // 7上绑的是自定义数据, 这里不需要
  // 8和9是原先的width和height, 升级foundation之后不再需要
    NSNumber* visible = objc_getAssociatedObject(annotation, (const void *) 10);
    NSNumber* opacity = objc_getAssociatedObject(annotation, (const void *) 13);

  //用户当前位置大头针
  if ([annotation isKindOfClass:[MAUserLocation class]]) {
    return nil;
  }

  if ([annotation isKindOfClass:[MAPointAnnotation class]]) {
      MAAnnotationView* annotationView;
      // 如果没有指定icon就使用m自带的annotation
      if (icon == nil) {
          annotationView = (MAPinAnnotationView*)[mapView dequeueReusableAnnotationViewWithIdentifier:@"pinAnnotationReuseIndentifier"];
          if (annotationView == nil) {
              annotationView = [[MAPinAnnotationView alloc] initWithAnnotation:annotation reuseIdentifier:@"pinAnnotationReuseIndentifier"];
          }
      } else {
          annotationView = (MAAnnotationView*)[mapView dequeueReusableAnnotationViewWithIdentifier:@"customAnnotationReuseIndentifier"];
          if (annotationView == nil) {
              annotationView = [[MAAnnotationView alloc] initWithAnnotation:annotation reuseIdentifier:@"customAnnotationReuseIndentifier"];
          }
      }
      if (icons != nil && (NSNull*) icon != [NSNull null]) {
          annotationView.imageView.animationImages = icons;
          annotationView.imageView.animationDuration = [duration doubleValue];
          [annotationView.imageView startAnimating];
      }
      if (icon != nil && (NSNull*) icon != [NSNull null]) annotationView.image = icon;
      if (draggable != nil) annotationView.draggable = [draggable boolValue];
      if (infoWindowEnabled != nil) annotationView.canShowCallout = [infoWindowEnabled boolValue];
      // 旋转角度
      if (rotateAngle != nil) {
          annotationView.imageView.transform = CGAffineTransformRotate(CGAffineTransformIdentity, -[rotateAngle doubleValue] / 180.0 * M_PI);
      }
      // 锚点
      if (anchorU != nil && anchorV != nil
          && (NSNull*) anchorU != [NSNull null] && (NSNull*) anchorV != [NSNull null]) {
          annotationView.layer.anchorPoint = CGPointMake([anchorU doubleValue], [anchorV doubleValue]);
      }
      // 是否可见
      if (visible != nil && (NSNull*) visible != [NSNull null]) {
          annotationView.hidden = ![visible boolValue];
      }
      // 透明度
      if (opacity != nil && (NSNull*) opacity != [NSNull null]) {
          annotationView.alpha = [opacity doubleValue];
      }
      return annotationView;
  }
  ////////////////////////////////////////////////////////////////////////////////
  
  return (MAAnnotationView*) nil;
}

- (void)mapView : (MAMapView*)mapView didAddAnnotationViews: (NSArray*)views
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didAddAnnotationViews");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  NSArray* argviews = views;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didAddAnnotationViews" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"views": argviews == nil ? [NSNull null] : argviews}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didSelectAnnotationView: (MAAnnotationView*)view
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didSelectAnnotationView");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didSelectAnnotationView" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didDeselectAnnotationView: (MAAnnotationView*)view
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didDeselectAnnotationView");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didDeselectAnnotationView" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview}];
  });
  
}

- (void)mapViewWillStartLocatingUser : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewWillStartLocatingUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewWillStartLocatingUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapViewDidStopLocatingUser : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewDidStopLocatingUser");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewDidStopLocatingUser" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didUpdateUserLocation: (MAUserLocation*)userLocation updatingLocation: (BOOL)updatingLocation
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didUpdateUserLocation_updatingLocation");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAUserLocation* arguserLocation = userLocation;
  // primitive callback arg
  NSNumber* argupdatingLocation = @(updatingLocation);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didUpdateUserLocation_updatingLocation" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"userLocation": arguserLocation == nil ? [NSNull null] : arguserLocation, @"updatingLocation": argupdatingLocation == nil ? [NSNull null] : argupdatingLocation}];
  });
  
}

- (void)mapViewRequireLocationAuth : (CLLocationManager*)locationManager
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapViewRequireLocationAuth");
  }

  // convert to jsonable arg
  // ref callback arg
  CLLocationManager* arglocationManager = locationManager;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapViewRequireLocationAuth" arguments:@{@"locationManager": arglocationManager == nil ? [NSNull null] : arglocationManager}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didFailToLocateUserWithError: (NSError*)error
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didFailToLocateUserWithError");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  NSError* argerror = error;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didFailToLocateUserWithError" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"error": argerror == nil ? [NSNull null] : argerror}];
  });
  
}

- (void)mapView : (MAMapView*)mapView annotationView: (MAAnnotationView*)view didChangeDragState: (MAAnnotationViewDragState)newState fromOldState: (MAAnnotationViewDragState)oldState
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_annotationView_didChangeDragState_fromOldState");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;
  // enum callback arg
  NSNumber* argnewState = @((NSInteger) newState);
  // enum callback arg
  NSNumber* argoldState = @((NSInteger) oldState);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_annotationView_didChangeDragState_fromOldState" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview, @"newState": argnewState == nil ? [NSNull null] : argnewState, @"oldState": argoldState == nil ? [NSNull null] : argoldState}];
  });
  
}

- (MAOverlayRenderer*)mapView : (MAMapView*)mapView rendererForOverlay: (id<MAOverlay>)overlay
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_rendererForOverlay");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  id<MAOverlay> argoverlay = overlay;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_rendererForOverlay"
                arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"overlay": argoverlay == nil ? [NSNull null] : argoverlay}
                   result:^(id result) {}]; // 由于结果是异步返回, 这里用不上, 所以就不生成代码了
  });
  
  // 由于flutter无法同步调用method channel, 所以暂不支持有返回值的回调方法
  // 相关issue https://github.com/flutter/flutter/issues/28310
  NSLog(@"暂不支持有返回值的回调方法");
  
  ////////////////////////////如果需要手写代码, 请写在这里/////////////////////////////
// 线
  if ([overlay isKindOfClass:[MAPolyline class]])
  {
      NSNumber* width = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 1);
      NSNumber* strokeColor = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 2);
      UIImage* texture = (UIImage *) objc_getAssociatedObject(overlay, (const void *) 3);
      NSNumber* lineCapType = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 4);
      NSNumber* lineJoinType = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 5);
      NSNumber* dashType = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 6);

      MAPolylineRenderer *polylineRenderer = [[MAPolylineRenderer alloc] initWithPolyline:overlay];

      polylineRenderer.lineWidth = [width doubleValue];
      // 描边颜色
      NSUInteger rgba = [strokeColor unsignedIntegerValue];
      float components[4];
      for (int i = 3; i >= 0; i--) {
          components[i] = (rgba & 0xff) / 255.0;
          rgba >>= 8;
      }
      polylineRenderer.strokeColor  = [UIColor colorWithRed:components[1] green:components[2] blue:components[3] alpha:components[0]];
      if (texture != nil) polylineRenderer.strokeImage = texture;
      if (lineCapType != nil) polylineRenderer.lineCapType = (MALineCapType) [lineCapType integerValue];
      if (lineJoinType != nil) polylineRenderer.lineJoinType = (MALineJoinType) [lineJoinType integerValue];
      if (dashType != nil) polylineRenderer.lineDashType = (MALineDashType) [dashType integerValue];

      // 这次调用完成后 清空栈
      [STACK removeAllObjects];
      return polylineRenderer;
  }

  // 多边形
  if ([overlay isKindOfClass:[MAPolygon class]])
  {
      NSNumber* width = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 1);
      NSNumber* strokeColor = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 2);
      NSNumber* fillColor = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 3);

      MAPolygonRenderer *polygonRenderer = [[MAPolygonRenderer alloc] initWithPolygon:overlay];

      if (width != nil) polygonRenderer.lineWidth = [width doubleValue];
      // 描边颜色
      NSUInteger rgba = [strokeColor unsignedIntegerValue];
      float components[4];
      for (int i = 3; i >= 0; i--) {
          components[i] = (rgba & 0xff) / 255.0;
          rgba >>= 8;
      }
      polygonRenderer.strokeColor  = [UIColor colorWithRed:components[1] green:components[2] blue:components[3] alpha:components[0]];

      // 填充颜色
      rgba = [fillColor unsignedIntegerValue];
      for (int i = 3; i >= 0; i--) {
          components[i] = (rgba & 0xff) / 255.0;
          rgba >>= 8;
      }
      polygonRenderer.fillColor  = [UIColor colorWithRed:components[1] green:components[2] blue:components[3] alpha:components[0]];

      // 这次调用完成后 清空栈
      [STACK removeAllObjects];
      return polygonRenderer;
  }

  // 圆
  if ([overlay isKindOfClass:[MACircle class]])
  {
      NSNumber* width = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 1);
      NSNumber* strokeColor = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 2);
      NSNumber* fillColor = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 3);

      MACircleRenderer *circleRenderer = [[MACircleRenderer alloc] initWithCircle:overlay];

      // 宽度
      if (width != nil) circleRenderer.lineWidth = [width doubleValue];

      // 描边颜色
      NSUInteger rgba = [strokeColor unsignedIntegerValue];
      float components[4];
      for (int i = 3; i >= 0; i--) {
          components[i] = (rgba & 0xff) / 255.0;
          rgba >>= 8;
      }
      circleRenderer.strokeColor  = [UIColor colorWithRed:components[1] green:components[2] blue:components[3] alpha:components[0]];

      // 填充颜色
      rgba = [fillColor unsignedIntegerValue];
      for (int i = 3; i >= 0; i--) {
          components[i] = (rgba & 0xff) / 255.0;
          rgba >>= 8;
      }
      circleRenderer.fillColor  = [UIColor colorWithRed:components[1] green:components[2] blue:components[3] alpha:components[0]];

      // 这次调用完成后 清空栈
      [STACK removeAllObjects];
      return circleRenderer;
  }

  // 瓦片图
  if ([overlay isKindOfClass:[MATileOverlay class]])
  {
      MATileOverlayRenderer *tileOverlayRenderer = [[MATileOverlayRenderer alloc] initWithTileOverlay:overlay];
      return tileOverlayRenderer;
  }

  // 海量点
  if ([overlay isKindOfClass:[MAMultiPointOverlay class]])
  {
      UIImage* icon = (UIImage *) objc_getAssociatedObject(overlay, (const void *) 1);
      NSNumber* width = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 2);
      NSNumber* height = (NSNumber *) objc_getAssociatedObject(overlay, (const void *) 3);

      MAMultiPointOverlayRenderer *multiPointOverlayRenderer = [[MAMultiPointOverlayRenderer alloc] initWithMultiPointOverlay: overlay];
      if (icon != nil) {
          multiPointOverlayRenderer.icon = icon;
      }
      if (width != nil && height != nil) {
          multiPointOverlayRenderer.pointSize = CGSizeMake([width doubleValue], [height doubleValue]);
      }
      return multiPointOverlayRenderer;
  }

  // 图片
  if ([overlay isKindOfClass:[MAGroundOverlay class]])
  {
      MAGroundOverlayRenderer *groundPverlayRenderer = [[MAGroundOverlayRenderer alloc] initWithGroundOverlay: overlay];
      return groundPverlayRenderer;
  }
  ////////////////////////////////////////////////////////////////////////////////
  
  return (MAOverlayRenderer*) nil;
}

- (void)mapView : (MAMapView*)mapView didAddOverlayRenderers: (NSArray*)overlayRenderers
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didAddOverlayRenderers");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  NSArray* argoverlayRenderers = overlayRenderers;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didAddOverlayRenderers" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"overlayRenderers": argoverlayRenderers == nil ? [NSNull null] : argoverlayRenderers}];
  });
  
}

- (void)mapView : (MAMapView*)mapView annotationView: (MAAnnotationView*)view calloutAccessoryControlTapped: (UIControl*)control
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_annotationView_calloutAccessoryControlTapped");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;
  // ref callback arg
  UIControl* argcontrol = control;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_annotationView_calloutAccessoryControlTapped" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview, @"control": argcontrol == nil ? [NSNull null] : argcontrol}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didAnnotationViewCalloutTapped: (MAAnnotationView*)view
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didAnnotationViewCalloutTapped");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didAnnotationViewCalloutTapped" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didAnnotationViewTapped: (MAAnnotationView*)view
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didAnnotationViewTapped");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAAnnotationView* argview = view;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didAnnotationViewTapped" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"view": argview == nil ? [NSNull null] : argview}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didChangeUserTrackingMode: (MAUserTrackingMode)mode animated: (BOOL)animated
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didChangeUserTrackingMode_animated");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // enum callback arg
  NSNumber* argmode = @((NSInteger) mode);
  // primitive callback arg
  NSNumber* arganimated = @(animated);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didChangeUserTrackingMode_animated" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"mode": argmode == nil ? [NSNull null] : argmode, @"animated": arganimated == nil ? [NSNull null] : arganimated}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didChangeOpenGLESDisabled: (BOOL)openGLESDisabled
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didChangeOpenGLESDisabled");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // primitive callback arg
  NSNumber* argopenGLESDisabled = @(openGLESDisabled);

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didChangeOpenGLESDisabled" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"openGLESDisabled": argopenGLESDisabled == nil ? [NSNull null] : argopenGLESDisabled}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didTouchPois: (NSArray*)pois
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didTouchPois");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  NSArray* argpois = pois;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didTouchPois" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"pois": argpois == nil ? [NSNull null] : argpois}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didSingleTappedAtCoordinate: (CLLocationCoordinate2D)coordinate
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didSingleTappedAtCoordinate");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // struct callback arg
  NSValue* argcoordinate = [NSValue value:&coordinate withObjCType:@encode(CLLocationCoordinate2D)];
  

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didSingleTappedAtCoordinate" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"coordinate": argcoordinate == nil ? [NSNull null] : argcoordinate}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didLongPressedAtCoordinate: (CLLocationCoordinate2D)coordinate
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didLongPressedAtCoordinate");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // struct callback arg
  NSValue* argcoordinate = [NSValue value:&coordinate withObjCType:@encode(CLLocationCoordinate2D)];
  

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didLongPressedAtCoordinate" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"coordinate": argcoordinate == nil ? [NSNull null] : argcoordinate}];
  });
  
}

- (void)mapInitComplete : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapInitComplete");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapInitComplete" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didIndoorMapShowed: (MAIndoorInfo*)indoorInfo
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didIndoorMapShowed");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAIndoorInfo* argindoorInfo = indoorInfo;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didIndoorMapShowed" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"indoorInfo": argindoorInfo == nil ? [NSNull null] : argindoorInfo}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didIndoorMapFloorIndexChanged: (MAIndoorInfo*)indoorInfo
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didIndoorMapFloorIndexChanged");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAIndoorInfo* argindoorInfo = indoorInfo;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didIndoorMapFloorIndexChanged" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"indoorInfo": argindoorInfo == nil ? [NSNull null] : argindoorInfo}];
  });
  
}

- (void)mapView : (MAMapView*)mapView didIndoorMapHidden: (MAIndoorInfo*)indoorInfo
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::mapView_didIndoorMapHidden");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;
  // ref callback arg
  MAIndoorInfo* argindoorInfo = indoorInfo;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"mapView_didIndoorMapHidden" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView, @"indoorInfo": argindoorInfo == nil ? [NSNull null] : argindoorInfo}];
  });
  
}

- (void)offlineDataWillReload : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::offlineDataWillReload");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"offlineDataWillReload" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}

- (void)offlineDataDidReload : (MAMapView*)mapView
{
  FlutterMethodChannel *channel = [FlutterMethodChannel
        methodChannelWithName:[NSString stringWithFormat:@"MAMapViewDelegate::Callback@%@:%@", NSStringFromClass([self class]), @(self.hash)]
              binaryMessenger:[_registrar messenger]
                        codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
  // print log
  if (enableLog) {
    NSLog(@"MAMapViewDelegate::offlineDataDidReload");
  }

  // convert to jsonable arg
  // ref callback arg
  MAMapView* argmapView = mapView;

  dispatch_async(dispatch_get_main_queue(), ^{
    [channel invokeMethod:@"offlineDataDidReload" arguments:@{@"mapView": argmapView == nil ? [NSNull null] : argmapView}];
  });
  
}


@end
