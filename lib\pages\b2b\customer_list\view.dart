import 'dart:async';

import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:fuduoduo/utils/color_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:get/get.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_pickers/time_picker/model/date_mode.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_pickers/style/picker_style.dart';
import 'package:flutter_pickers/time_picker/model/suffix.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../../../provider/event/route_bind_event.dart';
import '../../../resource/color_resource.dart';
import '../../../resource/image_resource.dart';
import '../../../store/index.dart';
import '../../../utils/open_third_url_utils.dart';
import 'logic.dart';
import 'package:fuduoduo/widget/ImageLoad.dart';
import 'package:fuduoduo/utils/open_third_url_utils.dart';

class B2bCustomerListPage extends StatefulWidget {
  const B2bCustomerListPage({super.key});

  @override
  State<B2bCustomerListPage> createState() => B2bCustomerListPageState();
}

class B2bCustomerListPageState extends State<B2bCustomerListPage> {
  final logic = Get.put(B2bCustomerListPageLogic());
  final state = Get.find<B2bCustomerListPageLogic>().state;
  final publicState = Get.find<Public>().state;
  double clsBoxWidth = 210.w; // 分类宽度
  double typeItemHeight = 80.w; //类型高度

  StreamSubscription? _routeBindSubscription;

  @override
  void initState() {
    super.initState();
    logic.requestPermission();
    // logic.getUnbindRouteCustomerList();
    // logic.getRouteOption();

    // _routeBindSubscription =
    //     routBindEventBus.on<RouteBindEvent>().listen((event) {
    //   logic.getUnbindRouteCustomerList();
    // });
  }

  @override
  Widget build(BuildContext context) {
    // print("userInfo=>${SpUtil.getObject('userInfo')}");
    dynamic userInfo = SpUtil.getObject('userInfo');
    dynamic employee = userInfo['employee'] ?? {};
    return Scaffold(
      backgroundColor: Color(0XFFF5F5F5),
      // appBar: MyCommonUtils.customAppBar('客户'),
      appBar: _AppBar(context),
      body: GetBuilder<B2bCustomerListPageLogic>(
          builder: (_) => Row(
                children: [
                  _ClsBox(context),
                  SizedBox(
                    width: 12.w,
                  ),
                  Flexible(child: _ContentBox(context))
                ],
              )),
      floatingActionButton: Visibility(
          //1、当前用户角色为业务员 2、当前用户开通账号 3、该经销商开通同福云商
          visible: (employee["duty"] == "4") &&
              (SpUtil.getBool("StartFlag") == true) &&
              (SpUtil.getBool("CloudFlag") == true),
          child: InkWell(
            onTap: () {
              // MyCommonUtils.showToast("跳转云商专属邀请码页面");
              Get.toNamed(PageName.InvitationCodePage);
            },
            child: Opacity(
              opacity: 1,
              child: Container(
                width: 210.w,
                height: 96.h,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  // color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                  // borderRadius: BorderRadius.only(
                  //     topLeft: Radius.circular(50.r),
                  //     bottomLeft: Radius.circular(50.r)
                  // )
                ),
                child: Image.asset(
                  ImageResource.ICON_INVITATION,
                  width: 210.w,
                  height: 96.h,
                  fit: BoxFit.cover,
                ),
                // child: Row(
                //   children: [
                //     Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         Expanded(
                //             flex: 1,
                //             child: Container(
                //               alignment: Alignment.topCenter,
                //               margin: EdgeInsets.only(top: 15.h),
                //               child: Text(
                //                 '出示云商',
                //                 style: TextStyle(
                //                     fontSize: 22.sp,
                //                     color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                //                     fontWeight: FontWeight.w600
                //                 ),
                //               ),
                //             )
                //         ),
                //         Expanded(
                //             flex: 1,
                //             child: Container(
                //               alignment: Alignment.bottomCenter,
                //               margin: EdgeInsets.only(bottom: 15.h),
                //               child: Text(
                //                 '专属邀请码',
                //                 style: TextStyle(
                //                     fontSize: 22.sp,
                //                     color: ColorResource.BLACK_BOLD_TITLE_COLOR,
                //                     fontWeight: FontWeight.w600
                //                 ),
                //               ),
                //             )
                //         )
                //       ],
                //     ),
                //     Spacer(),
                //     Column(
                //       children: [
                //         QrImageView(
                //           padding: EdgeInsets.all(0),
                //           data: 'This is a simple QR code',
                //           version: QrVersions.auto,
                //           size: 35,
                //           gapless: false,
                //         ),
                //         // Container(
                //         //   margin: EdgeInsets.only(left: 20.w),
                //         //   width: 70.w,
                //         //   height: 70.w,
                //         //   child: ClipRRect(
                //         //     // borderRadius: BorderRadius.circular(60.r),
                //         //     child: Image.asset(
                //         //       ImageResource.DEFAULT_IMAGE,
                //         //       width: 70.w,
                //         //       height: 70.w,
                //         //       fit: BoxFit.cover,
                //         //     ),
                //         //   ),
                //         // ),
                //         Spacer(),
                //       ],
                //     ),
                //   ],
                // ),
              ),
            ),
          )),
    );
  }

  // 内容
  _ContentBox(BuildContext context) {
    if (state.customerList.isEmpty) {
      return MyCommonUtils.noDataBuilder(context,
          marginTop: 20.h,
          noDataImage: 'assets/images/img_empty.png', noDataString: '暂无数据~');
    } else {
      return Column(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
              color: Colors.white,
              child: RefreshIndicator(
                onRefresh: logic.onRefresh,
                child: NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification scrollInfo) {
                    if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
                      // 滚动到底部，加载更多
                      logic.loadMore();
                    }
                    return true;
                  },
                  child: ListView.builder(
                    itemCount: state.customerList.length + (state.hasMore ? 1 : 0),
                    itemBuilder: (context, index) {
                      // 加载更多指示器
                      if (index == state.customerList.length) {
                        return Container(
                          padding: EdgeInsets.symmetric(vertical: 20.w),
                          alignment: Alignment.center,
                          child: Text(
                            "上拉加载更多",
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 24.w,
                            ),
                          ),
                        );
                      }

                      // 门店项
                      dynamic item = state.customerList[index];
                      return InkWell(
                        onTap: () async {
                          // 获取用户信息
                          final publicLogic = Get.put(Public());
                          if (SpUtil.getString('access_token')!.isNotEmpty) {
                            publicLogic.getUserInfo();
                          }
                          await Future.delayed(const Duration(milliseconds: 100));
                          //跳转工作台页面
                          Get.toNamed(PageName.B2bCustomerInfoPage, parameters: {
                            "consumerNo": item['consumerNo'] ?? "",
                            "consumerName": item['consumerName'] ?? "",
                            "consumerAddress": item['shippingAddress'] ?? "",
                            "branchAddr": item['branchAddr'] ?? "",
                            "branchId": item['branchId'] ?? "",
                            "branchNo": item['branchNo'] ?? "",
                            "branchName": item['branchName'] ?? "",
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(0, 20.w, 0, 10.w),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                width: 1.0.w,
                                color: Color.fromARGB(255, 240, 237, 237),
                              ),
                            ),
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ImageLoad.loadWidget(item["branchImages"] ?? item["imgUrls"] ?? "", size: 132),
                                  SizedBox(width: 20.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "${item['branchName']}",
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(fontSize: 28.w),
                                        ),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    margin: EdgeInsets.symmetric(vertical: 14.w),
                                                    child: Row(
                                                      children: [
                                                        Text(
                                                          '距上次拜访:    ',
                                                          style: TextStyle(
                                                            color: Colors.grey[600],
                                                            fontSize: 24.w,
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: Text("${item['lastVisitDay'] > -1 ? '${item['lastVisitDay']}天' : '从未拜访'}",style: TextStyle(
                                                            fontSize: 23.w,
                                                          )),
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                  Row(
                                                    crossAxisAlignment: CrossAxisAlignment.center,
                                                    mainAxisAlignment: MainAxisAlignment.center,
                                                    children: [
                                                      Text(
                                                        '距上次订货:    ',
                                                        style: TextStyle(
                                                          color: Colors.grey[600],
                                                          fontSize: 24.w,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        child: Text("${item['lastActivityDay'] > -1 ? '${item['lastActivityDay']}天' : '从未下单'}",style: TextStyle(
                                                          fontSize: 23.w,
                                                        ),),
                                                      )
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ),
                                            // Container(
                                            //   height: 74.w,
                                            //   width: 1.w,
                                            //   color: ColorResource.COLOR_E7E7E7_COLOR,
                                            //   margin: EdgeInsets.symmetric(horizontal: 30.w),
                                            // ),
                                            Column(
                                              children: [
                                                InkWell(
                                                  onTap: () {
                                                    // 拨打电话
                                                    OpenThirdUrlUtils.callPhone(item['contactPhone']);
                                                  },

                                                  child: Container(
                                                      height: 69.w,
                                                      width: 69.w,
                                                      child: Icon(
                                                        Icons.phone_rounded,
                                                        size: 36.w,
                                                        color: Colors.green,
                                                      ),
                                                  ),


                                                  // child: Container(
                                                  //   alignment: Alignment.center,
                                                  //   margin: EdgeInsets.only(right: 24.w),
                                                  //   height: 40.w,
                                                  //   width: 40.w,
                                                  //   decoration: BoxDecoration(
                                                  //     color: ColorResource.WHITE_COMMON_COLOR,
                                                  //     gradient: LinearGradient(
                                                  //       colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                                                  //       begin: Alignment.centerLeft,
                                                  //       end: Alignment.centerRight,
                                                  //     ),
                                                  //     borderRadius: BorderRadius.all(Radius.circular(24.w)),
                                                  //   ),
                                                  //   child: Icon(
                                                  //     Icons.phone_rounded,
                                                  //     size: 30.w,
                                                  //     color: ColorResource.WHITE_COMMON_COLOR,
                                                  //   ),
                                                  // ),
                                                ),
                                                // SizedBox(height: 30.h),
                                                InkWell(
                                                  onTap: () {
                                                    // 导航
                                                    showSignIn(context, index);
                                                  },
                                                    child: Container(
                                                      height: 69.w,
                                                      width: 69.w,
                                                      child:  Icon(
                                                        Icons.near_me,
                                                        size: 36.w,
                                                        color: Colors.deepOrange,
                                                      )
                                                    )

                                                  // child: Container(
                                                  //   alignment: Alignment.center,
                                                  //   margin: EdgeInsets.only(right: 24.w),
                                                  //   height: 40.w,
                                                  //   width: 40.w,
                                                  //   decoration: BoxDecoration(
                                                  //     color: ColorResource.WHITE_COMMON_COLOR,
                                                  //     gradient: LinearGradient(
                                                  //       colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                                                  //       begin: Alignment.centerLeft,
                                                  //       end: Alignment.centerRight,
                                                  //     ),
                                                  //     borderRadius: BorderRadius.all(Radius.circular(24.w)),
                                                  //   ),
                                                  //   child: Icon(
                                                  //     Icons.near_me,
                                                  //     size: 30.w,
                                                  //     color: ColorResource.WHITE_COMMON_COLOR,
                                                  //   ),
                                                  // ),
                                                ),
                                              ],
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12.w),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    size: 36.w,
                                    color: Colors.red[400],
                                  ),
                                  Expanded(
                                    child: Container(
                                      child: Text(
                                        "${item['branchAddr'] ?? '暂无地址'}",
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 26.w,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          )
        ],
      );
    }
  }

  // 头部标题
  _AppBar(BuildContext context) {
    return AppBar(
        foregroundColor: Colors.white,
        title: Text(
          "客户",
          style: TextStyle(color: Colors.white),
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR, begin: Alignment.centerLeft, end: Alignment.centerRight),
          ),
        ),

        leading: IconButton(
            onPressed: () {
              Get.offAllNamed(PageName.B2BTAB);
            },
            icon: Icon(Icons.arrow_back_ios_rounded,
                color: ColorResource.WHITE_COMMON_COLOR)),
        actions: [
          InkWell(
            onTap: () {
              Get.toNamed(PageName.B2bCustomerSearchPage);
            },
            child: Container(
              height: 88.w,
              alignment: Alignment.center,
              padding: EdgeInsets.only(right: 32.w, left: 20.w),
              child: Icon(
                Icons.search,
                size: 48.w,
                color: Colors.white,
              ),
            ),
          )
        ]);
  }

  // 分类模块
  _ClsBox(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          boxShadow: [
            //阴影效果
            BoxShadow(
              offset: Offset(0, 10.w), //阴影在X轴和Y轴上的偏移
              color: Color.fromARGB(255, 220, 217, 217), //阴影颜色
              blurRadius: 3.0, //阴影程度
              spreadRadius: 0, //阴影扩散的程度 取值可以正数,也可以是负数
            ),
          ],
          color: Colors.white,
        ),
        width: clsBoxWidth,
        // color: Colors.grey[100],
        child: Visibility(
          visible: state.typeList.length > 0,
          child: ListView(
            children: state.routeList.map((e) {
              print('xxxx $e  ');
              return _RouteItem(context, e);
            }).toList(),
          ),
        ));
  }

  // 线路列表
  Widget _RouteItem(BuildContext context, obj) {
    String _id = obj['routeNo'];
    String _selected = state.typeList[state.selectedTypeIndex]['type'];
    bool isSelect = state.selectedRouteId == _id;

    List<Widget> _getTypeItem() {
      List<Widget> DomList = [];
      for (var i = 0; i < state.typeList.length; i++) {
        dynamic item = state.typeList[i];
        bool isSelect = _selected == item['type'];
        DomList.add(InkWell(
          onTap: () {
            print("点击了分类: ${item["name"]}, index: $i");
            logic.setSelectedTypeIndex(context, i);
          },
          child: Container(
            width: clsBoxWidth,
            height: typeItemHeight,
            padding: EdgeInsets.fromLTRB(15.w, 0, 0, 0),
            alignment: Alignment.centerLeft,
            child: Text(
              "${item["name"]}",
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 24.w,
                  color: isSelect ? ColorResource.RED_COMMON_COLOR : Colors.black87),
            ),
          ),
        ));
      }
      return DomList.toList();
    }

    return Container(
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10.h),
      color: isSelect ? Colors.white : Colors.transparent,
      child: Column(
        children: [
          InkWell(
            onTap: () {
              // logic.setSelectedRouteId(context, _id);
              state.selectedRouteIndex = state.routeList.indexOf(obj);
            },
            child: Container(
              width: clsBoxWidth,
              height: 90.w,
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.fromLTRB(0, 0, 10.w, 0),
                    width: 6.w,
                    height: 20.h,
                    decoration: BoxDecoration(
                      color: isSelect ? Colors.red : Colors.transparent,
                      borderRadius: BorderRadius.only(
                          topRight: Radius.circular(3.w),
                          bottomRight: Radius.circular(3.w)),
                    ),
                  ),
                  Container(
                    width: 150.w,
                    child: Text(
                      "${obj['routeName']}",
                      style: TextStyle(
                          fontSize: 28.w,
                          overflow: TextOverflow.ellipsis,
                          color: isSelect ? Colors.black : Colors.grey[400]),
                    ),
                  ),
                  // Visibility(
                  //     visible: state.selectedRouteId.isNotEmpty,
                  //     child: Icon(
                  //       Icons.expand_more,
                  //       size: 35.w,
                  //       color: isSelect ? Colors.black : Colors.transparent,
                  //     ))
                ],
              ),
            ),
          ),
          Container(
            height: (isSelect && state.selectedRouteId.isNotEmpty)
                ? (typeItemHeight * state.typeList.length)
                : 0,
            child: Column(
              children: _getTypeItem(),
            ),
          )
        ],
      ),
    );
  }

  void showSignIn(context, index) async {
    print("state.customerList[index]['longitude'] ${state.customerList[index]['longitude']}");

    // 安全获取经纬度
    String longitudeStr = state.customerList[index]['longitude']?.toString() ?? '0.0';
    String latitudeStr = state.customerList[index]['latitude']?.toString() ?? '0.0';

    double longitude = double.tryParse(longitudeStr) ?? 0.0;
    double latitude = double.tryParse(latitudeStr) ?? 0.0;

    if (longitude == 0.0 && latitude == 0.0) {
      MyCommonUtils.showToast('门店经纬度信息无效');
      return;
    }
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              return false;
            },
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(30.w))),
              child: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setDiaLogState) {
                return SingleChildScrollView(
                  child: Container(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(30.w)),
                          ),
                          padding: EdgeInsets.fromLTRB(30.w, 30.w, 30.w, 0.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                '导航（打开地图）',
                                style: TextStyle(
                                    fontSize: 28.w,
                                    fontWeight: FontWeight.bold),
                              ),
                              Stack(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 10.w),
                                    height: 300.w,
                                    alignment: Alignment.center,
                                    color: Colors.grey,
                                    child: AmapView(
                                      mapType: MapType.Standard,
                                      showZoomControl: false,
                                      rotateGestureEnabled: false,
                                      zoomLevel: 18,
                                      showCompass: false,
                                      showScaleControl: false,
                                      centerCoordinate: LatLng(
                                          latitude,
                                          longitude),
                                      maskDelay:
                                          const Duration(milliseconds: 500),
                                      onMapCreated: (controller) async {
                                        logic.controller = controller;
                                        logic.mapAddMarker(
                                            latitudeStr,
                                            longitudeStr);
                                        if (mounted) setState(() {});
                                      },
                                      // onMapClicked: (latLng) async {
                                      // _latLng = latLng;
                                      // _mapAddMarker(latLng);
                                      // }
                                    ),
                                  ),
                                  Positioned(
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      width: 1.sw,
                                      padding: EdgeInsets.symmetric(
                                          vertical: 14.w, horizontal: 8.w),
                                      color: ColorUtil.fromHex('#66000000'),
                                      child: Text(
                                        "${state.customerList[index]['branchAddr'] ?? '暂无地址'}",
                                        overflow: TextOverflow.fade,
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 20.w),
                                      ),
                                    ),
                                    bottom: 0,
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1.w,
                          width: 1.sw,
                          margin: EdgeInsets.only(top: 40.w),
                          color:
                              ColorResource.LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                        ),
                        Container(
                          child: Row(
                            children: [
                              Expanded(
                                child: InkWell(
                                  child: Container(
                                    child: Text('取消'),
                                    alignment: Alignment.center,
                                    padding:
                                        EdgeInsets.symmetric(vertical: 12.w),
                                  ),
                                  onTap: () {
                                    Get.back();
                                  },
                                ),
                              ),
                              Container(
                                height: 96.w,
                                width: 1.w,
                                color: ColorResource
                                    .LIGHT_GRAY_DIVIDER_BACKGROUND_COLOR,
                              ),
                              Expanded(
                                child: InkWell(
                                  child: Container(
                                      alignment: Alignment.center,
                                      padding:
                                          EdgeInsets.symmetric(vertical: 12.w),
                                      child: Text('导航')),
                                  onTap: () {
                                    OpenThirdUrlUtils.showNavigationList(
                                        latitudeStr,
                                        longitudeStr,
                                        state.customerList[index]['branchName']?.toString() ?? '');
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                );
              }),
            ),
          );
        });
  }

  @override
  void dispose() {
    if (_routeBindSubscription != null) {
      _routeBindSubscription!.cancel();
    }
    super.dispose();
  }
}
