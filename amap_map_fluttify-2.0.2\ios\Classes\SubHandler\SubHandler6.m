//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler6.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler6)
- (NSDictionary<NSString*, Handler>*) getSubHandler6 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MATileOverlayPath::set_contentScaleFactor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat contentScaleFactor = [args[@"contentScaleFactor"] floatValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                [dataValue getValue:&ref];
        
                ref.contentScaleFactor = contentScaleFactor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_index_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger index = [args[@"index"] longValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                [dataValue getValue:&ref];
        
                ref.index = index;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_requestId_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger requestId = [args[@"requestId"] longValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                [dataValue getValue:&ref];
        
                ref.requestId = requestId;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomCalloutView::set_userData_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // id arg
                id userData;
                // jsonable
                if ([args[@"userData"] isKindOfClass:[NSNumber class]]
                        || [args[@"userData"] isKindOfClass:[NSString class]]
                        || [args[@"userData"] isKindOfClass:[NSArray class]]
                        || [args[@"userData"] isKindOfClass:[NSDictionary class]]) {
                    userData = args[@"userData"];
                }
                // non jsonable
                else {
                    userData = args[@"userData"];
                }
        
                // ref
                MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.userData = userData;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOfflineItemCommonCity::set_province_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAOfflineItem* province = (MAOfflineItem*) (args[@"province"] == [NSNull null] ? nil : args[@"province"]);
        
                // ref
                MAOfflineItemCommonCity* ref = (MAOfflineItemCommonCity*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.province = province;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateBounds::set_northEast_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* northEastValue = (NSValue*) args[@"northEast"];
                CLLocationCoordinate2D northEast;
                if (northEastValue != nil && (NSNull*) northEastValue != [NSNull null]) {
                  [northEastValue getValue:&northEast];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"northEast不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateBounds ref;
                [dataValue getValue:&ref];
        
                ref.northEast = northEast;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateBounds::set_southWest_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* southWestValue = (NSValue*) args[@"southWest"];
                CLLocationCoordinate2D southWest;
                if (southWestValue != nil && (NSNull*) southWestValue != [NSNull null]) {
                  [southWestValue getValue:&southWest];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"southWest不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateBounds ref;
                [dataValue getValue:&ref];
        
                ref.southWest = southWest;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateSpan::set_latitudeDelta_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDegrees latitudeDelta = [args[@"latitudeDelta"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateSpan ref;
                [dataValue getValue:&ref];
        
                ref.latitudeDelta = latitudeDelta;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateSpan::set_longitudeDelta_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDegrees longitudeDelta = [args[@"longitudeDelta"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateSpan ref;
                [dataValue getValue:&ref];
        
                ref.longitudeDelta = longitudeDelta;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateRegion::set_center_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* centerValue = (NSValue*) args[@"center"];
                CLLocationCoordinate2D center;
                if (centerValue != nil && (NSNull*) centerValue != [NSNull null]) {
                  [centerValue getValue:&center];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"center不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateRegion ref;
                [dataValue getValue:&ref];
        
                ref.center = center;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACoordinateRegion::set_span_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* spanValue = (NSValue*) args[@"span"];
                MACoordinateSpan span;
                if (spanValue != nil && (NSNull*) spanValue != [NSNull null]) {
                  [spanValue getValue:&span];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"span不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateRegion ref;
                [dataValue getValue:&ref];
        
                ref.span = span;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapPoint::set_x_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double x = [args[@"x"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapPoint ref;
                [dataValue getValue:&ref];
        
                ref.x = x;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapPoint::set_y_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double y = [args[@"y"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapPoint ref;
                [dataValue getValue:&ref];
        
                ref.y = y;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapSize::set_width_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double width = [args[@"width"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapSize ref;
                [dataValue getValue:&ref];
        
                ref.width = width;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapSize::set_height_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double height = [args[@"height"] doubleValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapSize ref;
                [dataValue getValue:&ref];
        
                ref.height = height;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapRect::set_origin_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* originValue = (NSValue*) args[@"origin"];
                MAMapPoint origin;
                if (originValue != nil && (NSNull*) originValue != [NSNull null]) {
                  [originValue getValue:&origin];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"origin不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapRect ref;
                [dataValue getValue:&ref];
        
                ref.origin = origin;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapRect::set_size_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* sizeValue = (NSValue*) args[@"size"];
                MAMapSize size;
                if (sizeValue != nil && (NSNull*) sizeValue != [NSNull null]) {
                  [sizeValue getValue:&size];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"size不能为null"]);
                  return;
                }
        
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapRect ref;
                [dataValue getValue:&ref];
        
                ref.size = size;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_visibile_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL visibile = [args[@"visibile"] boolValue];
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visibile = visibile;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_duration_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSTimeInterval duration = [args[@"duration"] doubleValue];
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.duration = duration;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_loop_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL loop = [args[@"loop"] boolValue];
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.loop = loop;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_maxParticles_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger maxParticles = [args[@"maxParticles"] longValue];
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.maxParticles = maxParticles;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.icon = icon;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_startParticleSize_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* startParticleSizeValue = (NSValue*) args[@"startParticleSize"];
                CGSize startParticleSize;
                if (startParticleSizeValue != nil && (NSNull*) startParticleSizeValue != [NSNull null]) {
                  [startParticleSizeValue getValue:&startParticleSize];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"startParticleSize不能为null"]);
                  return;
                }
        
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.startParticleSize = startParticleSize;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleLifeTime_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSTimeInterval particleLifeTime = [args[@"particleLifeTime"] doubleValue];
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleLifeTime = particleLifeTime;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleStartColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleColorGenerate> particleStartColor = (id<MAParticleColorGenerate>) (args[@"particleStartColor"] == [NSNull null] ? nil : args[@"particleStartColor"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleStartColor = particleStartColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleStartSpeed_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleVelocityGenerate> particleStartSpeed = (id<MAParticleVelocityGenerate>) (args[@"particleStartSpeed"] == [NSNull null] ? nil : args[@"particleStartSpeed"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleStartSpeed = particleStartSpeed;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleEmissionModule_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAParticleEmissionModuleOC* particleEmissionModule = (MAParticleEmissionModuleOC*) (args[@"particleEmissionModule"] == [NSNull null] ? nil : args[@"particleEmissionModule"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleEmissionModule = particleEmissionModule;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleShapeModule_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAParticleShapeModule> particleShapeModule = (id<MAParticleShapeModule>) (args[@"particleShapeModule"] == [NSNull null] ? nil : args[@"particleShapeModule"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleShapeModule = particleShapeModule;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleOverLifeModule_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAParticleOverLifeModuleOC* particleOverLifeModule = (MAParticleOverLifeModuleOC*) (args[@"particleOverLifeModule"] == [NSNull null] ? nil : args[@"particleOverLifeModule"]);
        
                // ref
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.particleOverLifeModule = particleOverLifeModule;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_url_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* url = (NSString*) args[@"url"];
        
                // ref
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.url = url;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_key_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* key = (NSString*) args[@"key"];
        
                // ref
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.key = key;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_Id_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* Id = (NSString*) args[@"Id"];
        
                // ref
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.Id = Id;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_visible_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL visible = [args[@"visible"] boolValue];
        
                // ref
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visible = visible;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayRenderer::set_strokeImage_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* strokeImage = (UIImage*) (args[@"strokeImage"] == [NSNull null] ? nil : args[@"strokeImage"]);
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.strokeImage = strokeImage;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayRenderer::set_alpha_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat alpha = [args[@"alpha"] floatValue];
        
                // ref
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.alpha = alpha;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorNode::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorNode::set_weight_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float weight = [args[@"weight"] floatValue];
        
                // ref
                MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.weight = weight;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_type_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAHeatMapType type = (MAHeatMapType) [args[@"type"] integerValue];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.type = type;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_visible_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL visible = [args[@"visible"] boolValue];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visible = visible;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_inputNodes_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<MAHeatMapVectorNode*>* inputNodes = (NSArray<MAHeatMapVectorNode*>*) args[@"inputNodes"];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.inputNodes = inputNodes;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_size_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDistance size = [args[@"size"] doubleValue];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.size = size;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_gap_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat gap = [args[@"gap"] floatValue];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.gap = gap;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_colors_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<UIColor*>* colors = (NSArray<UIColor*>*) args[@"colors"];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.colors = colors;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_startPoints_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSArray<NSNumber*>* startPoints = (NSArray<NSNumber*>*) args[@"startPoints"];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.startPoints = startPoints;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_opacity_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat opacity = [args[@"opacity"] floatValue];
        
                // ref
                MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.opacity = opacity;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlay::set_option_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorOverlayOptions* option = (MAHeatMapVectorOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
                MAHeatMapVectorOverlay* ref = (MAHeatMapVectorOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.option = option;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_customID_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* customID = (NSString*) args[@"customID"];
        
                // ref
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.customID = customID;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_title_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* title = (NSString*) args[@"title"];
        
                // ref
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.title = title;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_subtitle_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* subtitle = (NSString*) args[@"subtitle"];
        
                // ref
                MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.subtitle = subtitle;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_height_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat height = [args[@"height"] floatValue];
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.height = height;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_heightScale_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat heightScale = [args[@"heightScale"] floatValue];
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.heightScale = heightScale;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_topColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* topColor = (UIColor*) (args[@"topColor"] == [NSNull null] ? nil : args[@"topColor"]);
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.topColor = topColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_sideColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* sideColor = (UIColor*) (args[@"sideColor"] == [NSNull null] ? nil : args[@"sideColor"]);
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.sideColor = sideColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_visibile_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL visibile = [args[@"visibile"] boolValue];
        
                // ref
                MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visibile = visibile;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATracePoint::set_latitude_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDegrees latitude = [args[@"latitude"] doubleValue];
        
                // ref
                MATracePoint* ref = (MATracePoint*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.latitude = latitude;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATracePoint::set_longitude_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDegrees longitude = [args[@"longitude"] doubleValue];
        
                // ref
                MATracePoint* ref = (MATracePoint*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.longitude = longitude;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_loc_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* locValue = (NSValue*) args[@"loc"];
                CLLocationCoordinate2D loc;
                if (locValue != nil && (NSNull*) locValue != [NSNull null]) {
                  [locValue getValue:&loc];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"loc不能为null"]);
                  return;
                }
        
        
                // ref
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.loc = loc;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_angle_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double angle = [args[@"angle"] doubleValue];
        
                // ref
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.angle = angle;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_speed_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double speed = [args[@"speed"] doubleValue];
        
                // ref
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.speed = speed;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_time_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                double time = [args[@"time"] doubleValue];
        
                // ref
                MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.time = time;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAArc::set_startCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* startCoordinateValue = (NSValue*) args[@"startCoordinate"];
                CLLocationCoordinate2D startCoordinate;
                if (startCoordinateValue != nil && (NSNull*) startCoordinateValue != [NSNull null]) {
                  [startCoordinateValue getValue:&startCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"startCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAArc* ref = (MAArc*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.startCoordinate = startCoordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAArc::set_passedCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* passedCoordinateValue = (NSValue*) args[@"passedCoordinate"];
                CLLocationCoordinate2D passedCoordinate;
                if (passedCoordinateValue != nil && (NSNull*) passedCoordinateValue != [NSNull null]) {
                  [passedCoordinateValue getValue:&passedCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"passedCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAArc* ref = (MAArc*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.passedCoordinate = passedCoordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAArc::set_endCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* endCoordinateValue = (NSValue*) args[@"endCoordinate"];
                CLLocationCoordinate2D endCoordinate;
                if (endCoordinateValue != nil && (NSNull*) endCoordinateValue != [NSNull null]) {
                  [endCoordinateValue getValue:&endCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"endCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAArc* ref = (MAArc*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.endCoordinate = endCoordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_showsAccuracyRing_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsAccuracyRing = [args[@"showsAccuracyRing"] boolValue];
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsAccuracyRing = showsAccuracyRing;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_showsHeadingIndicator_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsHeadingIndicator = [args[@"showsHeadingIndicator"] boolValue];
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsHeadingIndicator = showsHeadingIndicator;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_fillColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* fillColor = (UIColor*) (args[@"fillColor"] == [NSNull null] ? nil : args[@"fillColor"]);
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.fillColor = fillColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_strokeColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* strokeColor = (UIColor*) (args[@"strokeColor"] == [NSNull null] ? nil : args[@"strokeColor"]);
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.strokeColor = strokeColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_lineWidth_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat lineWidth = [args[@"lineWidth"] floatValue];
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lineWidth = lineWidth;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_locationDotBgColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* locationDotBgColor = (UIColor*) (args[@"locationDotBgColor"] == [NSNull null] ? nil : args[@"locationDotBgColor"]);
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.locationDotBgColor = locationDotBgColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_locationDotFillColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* locationDotFillColor = (UIColor*) (args[@"locationDotFillColor"] == [NSNull null] ? nil : args[@"locationDotFillColor"]);
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.locationDotFillColor = locationDotFillColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_enablePulseAnnimation_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL enablePulseAnnimation = [args[@"enablePulseAnnimation"] boolValue];
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.enablePulseAnnimation = enablePulseAnnimation;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_image_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
                // ref
                MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.image = image;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MABaseOverlay::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MABaseOverlay::set_boundingMapRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* boundingMapRectValue = (NSValue*) args[@"boundingMapRect"];
                MAMapRect boundingMapRect;
                if (boundingMapRectValue != nil && (NSNull*) boundingMapRectValue != [NSNull null]) {
                  [boundingMapRectValue getValue:&boundingMapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"boundingMapRect不能为null"]);
                  return;
                }
        
        
                // ref
                MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.boundingMapRect = boundingMapRect;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_mapType_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAMapType mapType = (MAMapType) [args[@"mapType"] integerValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.mapType = mapType;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_centerCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
                CLLocationCoordinate2D centerCoordinate;
                if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
                  [centerCoordinateValue getValue:&centerCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"centerCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.centerCoordinate = centerCoordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_region_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* regionValue = (NSValue*) args[@"region"];
                MACoordinateRegion region;
                if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
                  [regionValue getValue:&region];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"region不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.region = region;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_visibleMapRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* visibleMapRectValue = (NSValue*) args[@"visibleMapRect"];
                MAMapRect visibleMapRect;
                if (visibleMapRectValue != nil && (NSNull*) visibleMapRectValue != [NSNull null]) {
                  [visibleMapRectValue getValue:&visibleMapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"visibleMapRect不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visibleMapRect = visibleMapRect;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_limitRegion_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* limitRegionValue = (NSValue*) args[@"limitRegion"];
                MACoordinateRegion limitRegion;
                if (limitRegionValue != nil && (NSNull*) limitRegionValue != [NSNull null]) {
                  [limitRegionValue getValue:&limitRegion];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"limitRegion不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.limitRegion = limitRegion;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_limitMapRect_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* limitMapRectValue = (NSValue*) args[@"limitMapRect"];
                MAMapRect limitMapRect;
                if (limitMapRectValue != nil && (NSNull*) limitMapRectValue != [NSNull null]) {
                  [limitMapRectValue getValue:&limitMapRect];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"limitMapRect不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.limitMapRect = limitMapRect;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.zoomLevel = zoomLevel;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_minZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat minZoomLevel = [args[@"minZoomLevel"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.minZoomLevel = minZoomLevel;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_maxZoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat maxZoomLevel = [args[@"maxZoomLevel"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.maxZoomLevel = maxZoomLevel;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotationDegree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.rotationDegree = rotationDegree;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_cameraDegree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.cameraDegree = cameraDegree;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomingInPivotsAroundAnchorPoint_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL zoomingInPivotsAroundAnchorPoint = [args[@"zoomingInPivotsAroundAnchorPoint"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.zoomingInPivotsAroundAnchorPoint = zoomingInPivotsAroundAnchorPoint;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL zoomEnabled = [args[@"zoomEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.zoomEnabled = zoomEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_scrollEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL scrollEnabled = [args[@"scrollEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.scrollEnabled = scrollEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotateEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL rotateEnabled = [args[@"rotateEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.rotateEnabled = rotateEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotateCameraEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL rotateCameraEnabled = [args[@"rotateCameraEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.rotateCameraEnabled = rotateCameraEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsBuildings_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsBuildings = [args[@"showsBuildings"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsBuildings = showsBuildings;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsLabels_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsLabels = [args[@"showsLabels"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsLabels = showsLabels;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showTraffic_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showTraffic = [args[@"showTraffic"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showTraffic = showTraffic;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_touchPOIEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL touchPOIEnabled = [args[@"touchPOIEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.touchPOIEnabled = touchPOIEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsCompass_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsCompass = [args[@"showsCompass"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsCompass = showsCompass;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_compassOrigin_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* compassOriginValue = (NSValue*) args[@"compassOrigin"];
                CGPoint compassOrigin;
                if (compassOriginValue != nil && (NSNull*) compassOriginValue != [NSNull null]) {
                  [compassOriginValue getValue:&compassOrigin];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"compassOrigin不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.compassOrigin = compassOrigin;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsScale_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsScale = [args[@"showsScale"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsScale = showsScale;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_scaleOrigin_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* scaleOriginValue = (NSValue*) args[@"scaleOrigin"];
                CGPoint scaleOrigin;
                if (scaleOriginValue != nil && (NSNull*) scaleOriginValue != [NSNull null]) {
                  [scaleOriginValue getValue:&scaleOrigin];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"scaleOrigin不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.scaleOrigin = scaleOrigin;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_logoCenter_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* logoCenterValue = (NSValue*) args[@"logoCenter"];
                CGPoint logoCenter;
                if (logoCenterValue != nil && (NSNull*) logoCenterValue != [NSNull null]) {
                  [logoCenterValue getValue:&logoCenter];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"logoCenter不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.logoCenter = logoCenter;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_maxRenderFrame_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSUInteger maxRenderFrame = [args[@"maxRenderFrame"] unsignedIntegerValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.maxRenderFrame = maxRenderFrame;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_isAllowDecreaseFrame_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL isAllowDecreaseFrame = [args[@"isAllowDecreaseFrame"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.isAllowDecreaseFrame = isAllowDecreaseFrame;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_openGLESDisabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL openGLESDisabled = [args[@"openGLESDisabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.openGLESDisabled = openGLESDisabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_renderringDisabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL renderringDisabled = [args[@"renderringDisabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.renderringDisabled = renderringDisabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
                CGPoint screenAnchor;
                if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
                  [screenAnchorValue getValue:&screenAnchor];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"screenAnchor不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.screenAnchor = screenAnchor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsWorldMap_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSNumber* showsWorldMap = (NSNumber*) args[@"showsWorldMap"];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsWorldMap = showsWorldMap;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_mapLanguage_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSNumber* mapLanguage = (NSNumber*) args[@"mapLanguage"];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.mapLanguage = mapLanguage;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_selectedAnnotations_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<NSObject*>* selectedAnnotations = (NSArray<NSObject*>*) args[@"selectedAnnotations"];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.selectedAnnotations = selectedAnnotations;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsUserLocation_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsUserLocation = [args[@"showsUserLocation"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsUserLocation = showsUserLocation;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_customizeUserLocationAccuracyCircleRepresentation_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL customizeUserLocationAccuracyCircleRepresentation = [args[@"customizeUserLocationAccuracyCircleRepresentation"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.customizeUserLocationAccuracyCircleRepresentation = customizeUserLocationAccuracyCircleRepresentation;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_userTrackingMode_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAUserTrackingMode userTrackingMode = (MAUserTrackingMode) [args[@"userTrackingMode"] integerValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.userTrackingMode = userTrackingMode;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_distanceFilter_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDistance distanceFilter = [args[@"distanceFilter"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.distanceFilter = distanceFilter;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_desiredAccuracy_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationAccuracy desiredAccuracy = [args[@"desiredAccuracy"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.desiredAccuracy = desiredAccuracy;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_headingFilter_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDegrees headingFilter = [args[@"headingFilter"] doubleValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.headingFilter = headingFilter;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_pausesLocationUpdatesAutomatically_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL pausesLocationUpdatesAutomatically = [args[@"pausesLocationUpdatesAutomatically"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.pausesLocationUpdatesAutomatically = pausesLocationUpdatesAutomatically;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_allowsBackgroundLocationUpdates_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL allowsBackgroundLocationUpdates = [args[@"allowsBackgroundLocationUpdates"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.allowsBackgroundLocationUpdates = allowsBackgroundLocationUpdates;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsIndoorMap_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsIndoorMap = [args[@"showsIndoorMap"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsIndoorMap = showsIndoorMap;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsIndoorMapControl_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showsIndoorMapControl = [args[@"showsIndoorMapControl"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showsIndoorMapControl = showsIndoorMapControl;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapView::set_customMapStyleEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL customMapStyleEnabled = [args[@"customMapStyleEnabled"] boolValue];
        
                // ref
                MAMapView* ref = (MAMapView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.customMapStyleEnabled = customMapStyleEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_fillColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* fillColor = (UIColor*) (args[@"fillColor"] == [NSNull null] ? nil : args[@"fillColor"]);
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.fillColor = fillColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_strokeColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* strokeColor = (UIColor*) (args[@"strokeColor"] == [NSNull null] ? nil : args[@"strokeColor"]);
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.strokeColor = strokeColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineWidth_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat lineWidth = [args[@"lineWidth"] floatValue];
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lineWidth = lineWidth;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineJoinType_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MALineJoinType lineJoinType = (MALineJoinType) [args[@"lineJoinType"] integerValue];
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lineJoinType = lineJoinType;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineCapType_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MALineCapType lineCapType = (MALineCapType) [args[@"lineCapType"] integerValue];
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lineCapType = lineCapType;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_miterLimit_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat miterLimit = [args[@"miterLimit"] floatValue];
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.miterLimit = miterLimit;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineDashType_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MALineDashType lineDashType = (MALineDashType) [args[@"lineDashType"] integerValue];
        
                // ref
                MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lineDashType = lineDashType;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"RefClass::isKindOfMAOfflineCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineCity class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineItemNationWide": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineItemNationWide class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiPoint class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAGroundOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAGroundOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPolygonRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPolygonRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPinAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPinAnnotationView class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapNode class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapGradient": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapGradient class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapTileOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMapStatus": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMapStatus class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPointAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPointAnnotation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACircle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACircle class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAArcRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAArcRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineMapViewController": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineMapViewController class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMapCustomStyleOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMapCustomStyleOptions class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPolygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPolygon class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPolyline class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiColoredPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiColoredPolylineRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAAnimatedAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAAnimatedAnnotation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiTexturePolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiTexturePolylineRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorGridNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorGridNode class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorGrid": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorGrid class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorGridOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorGridOverlayOptions class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorGridOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorGridOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineProvince": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineProvince class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorOverlayRender": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorOverlayRender class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATileOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineItem class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAGeodesicPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAGeodesicPolyline class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATouchPoi": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATouchPoi class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMVTTileOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMVTTileOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineItemMunicipality": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineItemMunicipality class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorGridOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorGridOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiPolyline class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATraceManager": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATraceManager class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiPointOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiPointOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAIndoorFloorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAIndoorFloorInfo class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAIndoorInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAIndoorInfo class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAPolylineRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAPolylineRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAAnnotationMoveAnimation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAAnnotationMoveAnimation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAShape": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAShape class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAAnnotationView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAAnnotationView class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATileOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACustomCalloutView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACustomCalloutView class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineItemCommonCity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineItemCommonCity class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOfflineMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOfflineMap class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACircleRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACircleRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleRandomVelocityGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleRandomVelocityGenerate class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleRandomColorGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleRandomColorGenerate class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleConstantRotationGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleConstantRotationGenerate class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleCurveSizeGenerate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleCurveSizeGenerate class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleEmissionModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleEmissionModuleOC class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleSinglePointShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleSinglePointShapeModule class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleRectShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleRectShapeModule class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleOverLifeModuleOC": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleOverLifeModuleOC class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleOverlayOptions class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAParticleOverlayOptionsFactory": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAParticleOverlayOptionsFactory class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMVTTileOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMVTTileOverlayOptions class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMVTTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMVTTileOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAOverlayRenderer": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAOverlayRenderer class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAUserLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAUserLocation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorNode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorNode class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorItem class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorOverlayOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorOverlayOptions class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAHeatMapVectorOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAHeatMapVectorOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiPointItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiPointItem class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAMultiPointOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAMultiPointOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACustomBuildingOverlayOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACustomBuildingOverlayOption class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMACustomBuildingOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MACustomBuildingOverlay class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATracePoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATracePoint class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMATraceLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MATraceLocation class]];
            methodResult(@(isTargetType));
        },
        
        @"RefClass::isKindOfMAArc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // 引用对象
            NSObject* __this__ = ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
        
            BOOL isTargetType = [__this__ isKindOfClass:[MAArc class]];
            methodResult(@(isTargetType));
        },
        
    };
}

@end
