/*
 * 项目名：福多多APP
 * 作者：刘超
 * 创建时间：2023年09月10日16:50:25
 * 修改时间：2023年09月10日16:50:25
 */

import 'config.dart';

class Apis {
  // static String baseUrl = "https://seller.annto.com/"; // 客户通生产环境 003 123456
  // static String baseUrl = "https://bizhub-ver.annto.com/prod-api/"; // 安得的   客户通预发环境 fx001 123456
  static String baseUrl = "https://bizhub-uat.annto.com/prod-api/"; // 安得的   客户通测试环境 fx001 123456
  // static String baseUrl = "http://testad-erp.zhongkeshangruan.cn/prod-api/"; //中科的  客户通测试环境 fx001 123456
  // static String baseUrl = "http://10.71.62.122:8080/"; // 本地环境（简辉1）
  // static String baseUrl = "http://192.168.0.111:8080/"; // 本地环境（简辉2）
  // static String baseUrl = "http://192.168.0.198:8080/"; // 本地环境（李时轩）
  // static String baseUrl = "http://192.168.0.124:8080/"; // 本地环境（蒋剑超）
  // static String baseUrl = "http://192.168.0.100:8080/"; // 本地环境（刘聪）
  // static String baseUrl = "http://192.168.0.26:8080/"; // 本地环境（谢锦林）

  // 引入安得saas接口
  static const SaasBaseUrlVer = "https://saasapi-ver.annto.com/";
  static const SaasBaseUrl = "https://saasapi.annto.com/";
  static const SaasBaseUrlUat = "https://icloudapi-uat.annto.com/";
  // static const SaasBaseUrlUat = "https://saasapi-uat.annto.com/";

  // static const ServeBaseUrl = "http://39.100.74.82/html/"; //用户协议、隐私协议测试环境
  static const ServeBaseUrl =
      "https://fdd-pre-admin.tongfuyouxuan.com/html/html/"; //用户协议、隐私协议测试环境

  // static const imgCdnUrl = AppConfig.isWeb ? "https://124.232.152.50:9300" : "http://124.232.152.50:9300"; //中科-测试
  // static const imgCdnUrl = AppConfig.isWeb ? "https://39.100.90.165:9300" : "http://39.100.90.165:9300"; //同福-测试【一期】
  // static const imgCdnUrl = AppConfig.isWeb ? "https://47.92.87.49:9300" : "http://47.92.87.49:9300"; //同福-测试【二期】
  // static const imgCdnUrl = AppConfig.isWeb
  //     ? "https://annto-cos-biz04-1317294607.cos.ap-guangzhou.myqcloud.com" //安得-最新 -正式
  //     // : "http://annto-cos-biz04-1317294607.cos.ap-guangzhou.myqcloud.com"; //安得-正式
  //     : "https://annto-cos-biz01-1317294607.cos.ap-guangzhou.myqcloud.com";
  static const imgCdnUrl =
      "https://annto-cos-biz04-1317294607.cos.ap-guangzhou.myqcloud.com"; //安得-最新 -正式

  static const outCdnUrl =
      AppConfig.isWeb ? "https://www.xxxx.com/#" : "http://www.xxxx.com/#";

  //--------------------------------请求地址示例-------------------------------------------------
  /// 示例---start
  static const getDataFromServer = "erp/api/getDataFromServer";

  /// 示例---end
  ///
  /// seller.annto.com/app-privacy.html
  /// seller.annto.com/app-serve.html
  //--------------------------------【用户协议、隐私协议】----------------------------------------------
  static const userServe = "https://seller.annto.com/app-privacy.html"; //用户协议
  static const privacyServe =
      "https://seller.annto.com/app-privacy.html"; //隐私协议

  // static const userServe = ServeBaseUrl + "app-serve.html"; //用户协议
  // static const privacyServe = ServeBaseUrl + "app-privacy.html"; //隐私协议

  //--------------------------------【通用】模块相关接口-------------------------------------------------
  // saas的接口
  //

  /// 检测更新
  static const checkUpgrade = "tenant/version/releaseList";

  /// 多用户登录
  // static const loginByPassword = "auth/login";
  /// 单用户登录
  static const loginByPassword = "auth/appLogin";

  // saas 验证码登录接口
  static const loginByCode2Saas = "api-saas-auth/core/public/sms/loginV2";
  // 调新接口可以获取应用下的token
  static const loginByrefreshToken =
      "api-saas-auth/core/application/refreshTokenAndDefaultTenantByApplication";

  static const loginByPassword2Saas = "api-saas-auth/core/public/pwd/login";
  static const getLoginInfo = "system/login/getLoginInfo";
  // 账号密码登录
  static const loginPwd2Saas = 'api-saas-auth/core/public/pwd/login';
  static const loginPwd2Anyunda = 'system/public/pwd/loginByApp';
  // 切换租户
  static const switchTenant2Saas = 'api-saas-auth/core/user/switch/tenant';

  /// 查询手机号是否验证
  static const getValidateFlagInfo = "system/user/getNowUser";

  /// 手机号验证码登录
  static const loginByPhoneNumber = "auth/codeLogin";

  /// 提交手机号已验证
  static const submitPhoneNumberVerified = "system/user/codeLoginVerifyPhone";

  /// 获取经销商列表
  static const getDealerInfoList = "auth/codeByTen";

  /// 获取用户列表
  static const getDealerUserList = "auth/tenByUser";

  /// 获取任务中心H5地址
  static const getMissionCenterUrl = "miniapi/merchant/login";

  /// 获取验证码（通用）
  static const getVerificationCode = "miniapi/user/sendAuthCode";

  /// 获取验证码（通用） -saas
  static const getVerificationCode2Saas =
      "api-saas-auth/core/public/sms/sendVerificationCode";
  static const getCode2Saas = "api-saas-auth/core/sms/sendVerificationCode";

  // 更改手机号码
  static const updateUserMobile =
      "api-saas-auth/core/userInfo/updateUserMobile";

  // 重置密码
  static const forgetAndReset = "system/public/pwd/forgetAndResetByApp";

  // 获取租户信息
  static const getTenant = 'api-saas-auth/core/user/multi/tenant';

  /// 查询当前用户的经销商编号
  static const searchDealerNo = "miniapi/common/tenant";

  /// 单用户退出登录
  static const logout = "auth/logout";

  /// 用户信息
  static const getAppUserInfo = "system/user/getAppInfo";

  /// 经销商信息
  static const getCloudShopInfo = "miniapi/merchant/getTenant";

  /// 图片上传
  static const uploadImageFile = "miniapi/common/file/uploadImage";

  /// 获取系统字典  refund_type=退货原因
  static const getSysDict = "/miniapi/common/getSysDict?code=";

  /// 获取字典信息（规格）
  static const getUnitDictionaryList = "warehouse/dict/type/item_unit_type";

  /// 获取单据类型 (单据类型)
  static const getSheetType = 'system/dict/data/type/sheet_type';
  /// 获取B2b价格码
  static const getSalePriceCode = 'system/dict/data/type/sale_price_code';

  /// 获取字典信息（订单来源）
  static const getSheetSourceDictionaryList =
      "system/dict/data/type/sheet_source";

  // 校验用户是否是灰度用户
  static const checkGray = "/tenant/tenant/checkGray";

  ///车销一键退货
  static const appSalesReturn = "miniapi/outOrder/appSalesReturn";

  ///访销-一键退货 取消出库单
  static const appCancellOrder = "miniapi/outOrder/appCancellOrder";

  //--------------------------------【首页】模块相关接口-------------------------------------------------
  /// 首页汇总数据(个人业绩)
  static const getHomePageTotalData = "miniapi/index/getIndexDataCount";

  /// 首页汇总数据(团队业绩)
  static const getHomePageTotalTeamData = "miniapi/index/getIndexTeamDataCount";

  /// 首页个人业绩 二级页面列表数据（除今日收款以外的数据）
  static const getIndexTkData = "miniapi/index/getIndexTkData";

  /// 首页个人业绩 二级页面列表数据（今日收款数据）
  static const getIndexSkData = "miniapi/index/getIndexSkData";

  //--------------------------------【客户】模块相关接口-------------------------------------------------
  /// 渠道下拉框
  static const channelOption = "miniapi/consumer/channelOption";

  /// 线路下拉框
  static const routeOption = "miniapi/visit/routeOption";

  /// 路线拜访客户列表
  static const allVisitConsumerList = "miniapi/visit/allVisitConsumerList";

  /// 历史拜访客户列表
  static const historyVisitConsumerList =
      "miniapi/visit/historyVisitConsumerList";

  /// 附近拜访客户列表
  static const nearVisitConsumerList = "miniapi/visit/nearVisitConsumerList";

  /// 超期未拜访客户列表
  static const overtimeVisitConsumerList =
      "miniapi/visit/overtimeVisitConsumerList";

  /// 待拜访客户列表
  static const waitVisitConsumerList = "miniapi/visit/waitVisitConsumerList";

  /// 获取门店（未绑定业务员）列表信息
  static const getUnbindCustomerList = "miniapi/consumer/list";

  /// 流失客户列表
  static const lostVisitConsumerList = "miniapi/base/consumer/lostList";

  /// 获取未绑定的线路客户列表
  static const getUnbindRouteCustomerList = "miniapi/visit/noRouteConsumerList";

  /// 签到或签退标识
  static const signInOrOutFlag = "miniapi/visit/signInOrOutFlag";

  /// 签到
  static const signIn = "miniapi/visit/signIn";

  /// 签退
  static const signOut = "miniapi/visit/signOut";

  /// 放弃拜访
  static const waive = "miniapi/visit/waive";

  /// 客户信息
  static const consumerByNo = "miniapi/consumer/infoByNo";

  /// 关键词搜索门店
  static const waitVisitConsumerListByConsumerName =
      "miniapi/visit/waitVisitConsumerListByConsumerName";

  /// 销售页面-订货会分类列表
  static const listActivityTemplate =
      "miniapi/appActivity/listActivityTemplate";

  /// 销售页面-获取订货会商品列表
  static const getAppActivityItemList = "miniapi/appActivity/getItemList";

  /// 赠品页面-获取订货会赠品商品列表
  static const listGiftByActivityId =
      "miniapi/appActivity/listGiftByActivityId";

  //--------------------------------【访销订货/ 车】模块相关接口-------------------------------------------------
  /// 分类
  static const getItemClsTreeNode = "miniapi/common/getItemClsTreeNode";

  /// 销售or赠品or按品退货-商品列表 带库存
  static const getItemByCls = "miniapi/common/getItemByCls";

  // 多组织的商品列表
  static const getMultiOrgItemByCls = "miniapi/common/multiOrg/getItemList";

  /// 获取商品最近销售价格
  static const getLatestPriceByGoods = "miniapi/itemOrderGoods/getLatestPrice";

  /// 换货商品列表 不带库存  换货操作需要
  static const getItemList = "miniapi/common/getItemList";

  /// 兑换所使用的查询商品接口
  static const getCashItemList = "miniapi/common/getCashItemList";

  /// 兑换申请单
  static const saveCashApply = '/miniapi//itemOrderGoods/saveCashApply';

  /// 获取兑换记录列表
  static const getCashApplyList = '/miniapi/itemOrderGoods/getCashApplyList';

  /// 按单退货列表详情 后面有参数
  static const getOutSheetDetailList =
      "miniapi/itemOrderGoods/getOutSheetDetailList/";

  /// 按单退货列表
  static const getOutSheetMasterList =
      "miniapi/itemOrderGoods/getOutSheetMasterList";

  /// 提交换货
  static const saveSwapApply = "miniapi/itemOrderGoods/saveSwapApply";

  /// 换货提交接口
  static const saveApply = "miniapi/itemOrderGoods/saveApply";

  /// 保存购物车
  static const selfCartSave = "miniapi/selfCart/save";

  /// 删除购物车
  static const deleteCart = "miniapi/selfCart/delete";

  /// 获取购物车信息
  static const getCartInfo = "miniapi/selfCart/info";

  /// 结算验证库存
  static const settleCommitCheckStock =
      "miniapi/selfCart/settleCommitCheckStock";

  /// 购物车获取退货库
  static const getBranchAndRefund = "miniapi/common/getBranchAndRefund/";

  /// 获取门店余额 (应收、预收)
  static const consumerBalance = "miniapi/selfCart/consumerBalance";

  /// 结算
  static const settleCommit = "miniapi/selfCart/settleCommit";

  /// 打印次数统计
  static const printNumberCount = 'miniapi/outOrder/print/incr/';

  /// 资金账户列表查询
  static const fundAccountList = "miniapi/fund-account/list";

  /// 查询客户多地址关联列表
  static const fundAddressList = "warehouse/consumerMerge/list";

  /// 多地址关联修改
  static const editAddress = "warehouse/consumerMerge";

  /// 多地址关联新增
  static const newAddress = "warehouse/consumerMerge";

  /// 查询促销
  static const promoteList = "miniapi/selfCart/promoteList";

  //--------------------------------【欠款收款】模块相关接口-------------------------------------------------
  /// 查询换货当前用户的仓库
  static const exchangeGoodsBranchList = "miniapi/common/getBranchByUser";

  /// 获取欠款汇总列表
  static const getDeptCustomerList = "miniapi/app-dept/listByConsumerSumDebt";

  /// 获取客户欠款列表
  static const getDeptBillList = "miniapi/app-dept/listDeptByConsumerNo";

  /// 查询收款资金账户
  static const getReceivedFundsAccount =
      "miniapi/app/account/queryReceivedFundsAccount";

  /// 获取客户还款列表
  static const getDeptRepaymentList =
      "miniapi/app-dept/listByConsumerDebtPayment";

  /// 获取资金账户列表
  static const getMoneyAccountList = "miniapi/fund-account/list";

  /// 提交欠款结算信息
  static const submitRepaymentSettlementInfo = "miniapi/app-dept/settlement";

  //--------------------------------【订单查询】模块相关接口-------------------------------------------------
  /// 获取订单查询销售列表
  static const getOrderQuerySaleList =
      "miniapi/outOrder/getErpListOutMasterByVo";

  /// 获取订单查询销售详情
  static const getOrderQuerySaleDetail =
      "miniapi/outOrder/getErpDetailListBySheetNo";

  /// 获取订单查询退货列表
  static const getOrderQueryReturnList =
      "miniapi/receipt/getErpListInMasterByVo";

  /// 获取订单查询退货详情
  static const getOrderQueryReturnDetail =
      "miniapi/receipt/getErpInDetailListBySheetNo/";

  /// 获取订单查询换货列表
  static const getOrderQueryExchangeList =
      "miniapi/checkOrder/getCheckApplyList";

  /// 获取订单查询换货详情
  static const getOrderQueryExchangeDetail =
      "miniapi/checkOrder/getCheckApplyInfo";

  //--------------------------------【订货会】模块相关接口-------------------------------------------------
  /// 获取订货会列表
  static const getOrderMeetingList = "miniapi/appActivity/main-list";

  /// 获取订货会详情
  static const getOrderMeetingDetailInfo = "miniapi/appActivity/getInfo";

  /// 获取订货会充值记录列表
  static const getOrderMeetingChargeRecordList =
      "miniapi/appActivity/listRecharge";

  /// 获取订货会销售记录列表
  static const getOrderMeetingSaleRecordList =
      "miniapi/outOrder/getErpListOutMasterByVo";

  //  提交订货会充值信息
  static const SubmitOrderMeetingChargeInfo = "miniapi/appActivity/addRecharge";

  //--------------------------------【订货会-已废弃】模块相关接口-------------------------------------------------
  /// 获取订货会品牌列表
  static const getOrderMeetingBrandList = "warehouse/wmsBaseBrand/list";

  //--------------------------------【费用管理】模块相关接口-------------------------------------------------
  /// 新建费用管理信息
  static const createCostAdministrationInfo =
      "miniapi/financeFee/saveFinanceFee";

  /// 获取费用管理类型列表
  static const getCostAdministrationTypeList = "miniapi/financeFee/option";

  //--------------------------------【设备管理】模块相关接口-------------------------------------------------
  /// 获取设备汇总信息
  static const getDeviceSummaryInfo =
      "miniapi/itemOrderGoods/getApplyMasterListCount";

  /// 获取已有设备列表
  static const getExistDeviceList =
      "miniapi/itemOrderGoods/getEquipmentApplyMasterList";

  /// 获取可选设备列表
  static const getChooseDeviceList = "miniapi/common/getItemByCls";

  /// 提交新增设备信息
  static const submitAddDeviceInfo =
      "miniapi/itemOrderGoods/saveEquipmentApply";

  /// 提交设备退回信息
  static const submitReturnDeviceInfo =
      "miniapi/itemOrderGoods/returnEquipmentApply";

  //--------------------------------【云商绑定】模块相关接口-------------------------------------------------
  /// 门店绑定云商
  static const bindCustomerToCloud = "miniapi/consumer/bindingConsumer";

  /// 查询用户是否启用
  static const getUserStatusInfo = "miniapi/common/saleCodeCheck";

  /// 查询云商数据同步开关是否开启
  static const getCloudFlagInfo = "miniapi/common/merchant/switch";

  /// 获取云商绑定邀请码信息
  static const getCloudInvitationCodeInfo = "miniapi/common/getSaleCode";

  /// 获取云商绑定门店码信息
  static const getCloudBindCodeInfo = "miniapi/consumer/getSaleStoreCode/";

  /// 查询租户云商店铺名称
  static const getCloudShopNameInfo = "miniapi/common/ys/name";

  ///查询门店绑定信息
  static const getShopBindingInfo = "miniapi/consumer/store/bind";

  /// 查询云商门店信息
  static const getShopCloudInfo = "miniapi/common/merchant/shop";

  //--------------------------------【理货拍照】模块相关接口-------------------------------------------------
  /// 获取理货拍照历史列表信息
  static const getTallyPhotoList = "miniapi/tallying/queryList";

  /// 提交理货拍照新增信息
  static const submitTallyPhotoAddInfo = "miniapi/tallying/addTallyingRegister";

  //--------------------------------【预收款】模块相关接口-------------------------------------------------

  /// 获取门店余额 (应收、预收)
  static const getConsumerBalanceInfo = "miniapi/selfCart/consumerBalance";

  /// 获取预收款充值列表
  static const getAdvancePaymentChargeList =
      "miniapi/consumer/advanceRechargeList";

  /// 提交预收款充值信息
  static const SubmitAdvancePaymentChargeInfo =
      "miniapi/consumer/advanceRecharge";

  //--------------------------------【对账单】模块相关接口-------------------------------------------------
  /// 获取对账单列表地址
  static const getCheckAccountUrl =
      "miniapi/reconciliation/createReconciliationSign";

  /// 获取对账单下载地址
  static const getCheckAccountDownloadUrl =
      "miniapi/reconciliation/downloadMerchantReconciliation";

  // static const getCheckAccountDownloadUrl = "miniapi/reconciliation/get";

  //--------------------------------【代课下单】模块相关接口-------------------------------------------------
  /// 获取代客下单客户列表
  static const getPlaceOrderCustomerList = "miniapi/consumer/getConsumerList";

  /// 获取代客下单列表地址
  static const getPlaceOrderListUrl = "miniapi/consumer/createOrderSign/";

  /// 获取代客下单二维码地址
  static const getPlaceOrderQrcodeUrl = "miniapi/createCodeSign/";

  //--------------------------------【陈列管理】模块相关接口-------------------------------------------------
  /// 陈列计划--列表数据
  static const getDisplayManageList = '/miniapi/display/planList';

  /// 陈列计划--解约
  static const terminateDisplayManageList = '/miniapi/display/terminatePlan';

  /// 陈列计划--陈列打卡列表
  static const displayManageClockInList = '/miniapi/display/registerList/';

  /// 陈列计划--新增陈列打卡
  static const addClockInList = '/miniapi/display/addRegister';

  /// 陈列计划--新增陈列计划
  static const addDisplayPlan = '/miniapi/display/addPlan';

  /// 陈列计划--陈列类型列表
  static const displayType = '/miniapi/display/option';

  /// 陈列计划--兑付记录
  static const cashRecordList = '/miniapi/display/deliveryList/';

  /// 陈列计划--新增兑付
  static const addCashRecord = '/miniapi/display/addDelivery';

  /// 陈列计划--兑付产品列表
  static const exchangeProductList = '/miniapi/display/itemList/';

  //----------------------------------------------【库存】模块相关接口-----------------------------------------------
  /// 查询出入口商品列表
  static const getItemOutInList = '/miniapi/common/getItemOutInList';

  /// 查询往来单位
  static const getCompanyList = '/miniapi/common/getCompanyList';

  /// 出库列表
  static const getListOutMasterByVo =
      '/miniapi/outOrder/getWmsListOutMasterByVo';

  /// 获取配送员列表
  static const getSelectCouriers = '/miniapi/app-wmsOutMaster/selectCouriers';

  /// 审核出库订单
  static const appApproveOrder = '/miniapi/outOrder/appWmsEditOrder';

  /// 创建出库单
  static const saveAppOrder = '/miniapi/outOrder/saveWmsAppOrder';

  /// 根据单号查询出入库详情
  static const getDetailListBySheetNo =
      '/miniapi/outOrder/getWmsDetailListBySheetNo';

  /// 商品库存列表
  static const getItemStockBatch = '/miniapi/visit/getItemStockBatch';

  /// 提交盘点单
  static const saveCheckApply = '/miniapi/checkOrder/saveCheckApply';

  /// 盘点详情
  static const getCheckApplyInfo = '/miniapi/checkOrder/getCheckApplyInfo';

  /// 保存盘点信息
  static const saveCheckApplyInfo = 'miniapi/checkOrder/saveDetail';

  /// 提交盘点信息
  static const submitCheckApplyInfo = 'miniapi/checkOrder/auditApplyDeatils/';

  /// 盘点列表
  static const getCheckApplyList = '/miniapi/checkOrder/getCheckApplyList';
  static const getCheckApplyListNew =
      '/miniapi/checkOrder/getOrderGrabbingList';
  static const setOrderGrabbing = '/miniapi/checkOrder/setOrderGrabbing';

  /// 查询该商品所有库存信息
  static const getStockListByItem = '/miniapi/common/getStockListByItem';

  /// 入库列表
  static const getInList = '/miniapi/receipt/getInList';

  /// 获得入库单详情
  static const getInDetail = '/miniapi/receipt/getInDetail';

  /// 入库 审核入库单
  static const approveFlag = '/miniapi/receipt/approveFlag';

  /// 新建入库单
  static const addIn = '/miniapi/receipt/addIn';

  //--------------------------------【云商卸货】模块相关接口-------------------------------------------------
  /*
  /// 云商卸货订单列表
  static const cloudUnloadList = '/miniapi/outOrder/getWmsListOutMasterByVo';

  /// 订单详情
  static const cloudUnloadOrderDetail =
      '/miniapi/outOrder/getWmsDetailListBySheetNo';

  */

  /// 云商卸货订单列表
  static const cloudUnloadList = '/miniapi/outOrder/getErpListOutMasterByVo';

  /// 订单详情
  static const cloudUnloadOrderDetail =
      '/miniapi/outOrder/getErpDetailListBySheetNo';

  /// 卸货
  static const cloudUnloadItem = '/miniapi/outOrder/unload/';

  /// 卸货--促销品
  static const newCloudUnloadItem = '/miniapi/outOrder/changeItem';

  /// 提交
  static const submitUnloadOrderInfo = '/miniapi/outOrder/approve';

  /// 一键卸货
  static const oneStepUnloadOrderInfo = '/miniapi/outOrder/unload';

  //--------------------------------【拓店】模块相关接口-------------------------------------------------
  /// 存储信息
  static const consumerSaveConsumer = '/miniapi/consumer/saveConsumer';

  /// 存储信息
  static const consumerUpdateConsumer = '/miniapi/consumer/updateConsumer';

  /// 上传门头照
  static const consumerUploadImage = '/miniapi/consumer/uploadImage';

  /// 门店绑定
  static const consumerBindingConsumer = '/miniapi/consumer/bindingConsumer';

  /// 获取客户下拉 get
  static const consumerOption = '/miniapi/consumer/option';

  /// 渠道下拉框 get
  static const consumerChannelOption = '/miniapi/consumer/channelOption';

  /// 部门下拉框 get
  static const consumerDeptOption = '/miniapi/consumer/deptOption';

  /// 等级价格下拉框 get
  static const consumerPriceLevelOption = '/miniapi/consumer/priceLevelOption';

  /// 路线下拉框 get
  static const consumerRouteOption = '/miniapi/consumer/routeOption';
  /// 所属区域 get
  static const consumerGetAreaList = '/miniapi/consumer/area/getAreaList';

  //--------------------------------【车销-补货】模块相关接口-------------------------------------------------
  /// 补货提交接口
  static const saveAllotApply = '/miniapi/itemOrderGoods/saveAllotApply';

  /// 补货单列表
  static const receiptGetInList = '/miniapi/receipt/getInList';

  /// 补货盘点数据列表
  static const getPDItemStockBatch = '/miniapi/common/getPDItemStockBatch';

  ///车销退货仓返仓列表
  static const getRefundItemList = '/miniapi/common/getRefundItemList';

  /// 装车
  static const receiptCarLoading = '/miniapi/receipt/carLoading';

  /// 一键返仓
  static const clickBackWarehouse =
      '/miniapi/itemOrderGoods/clickBackWarehouse';

  /// 选品返仓
  static const clickOptional =
      '/miniapi/itemOrderGoods/clickBackWarehouse/optional';

  //--------------------------------【我的-交账】模块相关接口-------------------------------------------------
  /// 今日应交账
  static const getEmployeeSheetAmtSum =
      '/miniapi/common/getEmployeeSheetAmtSum';

  /// 获取交账信息
  static const getAccountMoneyInfo = '/miniapi/submit/account/submitDate';

  /// 提交交账信息
  static const submitAccountMoneyInfo = '/miniapi/submit/account/save';

  /// 获取交账信息
  static const getAccountDetailInfo = 'miniapi/submit/account/sheetNo';

  /// 获取历史交账列表信息
  static const getAccountHistoryListInfo =
      'miniapi/submit/account/getSubmitHisDataByDate';

  //--------------------------------【库存-调拨】模块相关接口-------------------------------------------------
  /// 调拨出库单列表
  static const getWmsListOutMasterByVo =
      '/miniapi/outOrder/getWmsListOutMasterByVo';

  /// 调拨出库单审核出库
  static const appWmsApproveOrder = '/miniapi/outOrder/appWmsApproveOrder';

  /// 调拨出库单详情
  static const getWmsDetailListBySheetNo =
      '/miniapi/outOrder/getWmsDetailListBySheetNo';

  /// 保存修改调拨出库单详情
  static const saveAllocateOutDetail = 'miniapi/outOrder/appDCOutEdit';

  /// 查询仓库列表
  static const getBranches = '/miniapi/common/getBranches';

  ///配送员当前交账
  static const getDriverSubmit = '/miniapi/driverSubmit/account/sheetNo';

  ///获取配送员员交账历史列表数据
  static const getSubmitHisDataByDate =
      '/miniapi/driverSubmit/account/getSubmitHisDataByDate';

  ///配送员提交交账
  static const getDriverSubmitSave = '/miniapi/driverSubmit/account/save';

  //-------------------------------【老板报表】模块相关接口-------------------------------------------------
  /// 查询老板报表总数据
  // static const getBossFormAllInfo = '/miniapi/report/allInfo';
  // 联调用
  static const getBossFormAllInfo = '/miniapi/report/allInfo';

  /// 1查询首页商品销售金额明细总数
  static const itemSalesListHead = '/miniapi/report/itemSalesListHead';

  ///查询首页商品销售金额排行榜-实销金额或查看更多
  static const getItemActualSalesList =
      '/miniapi/report/getItemActualSalesList';

  /// 2查询首页商品销售明细头部总数
  static const getItemSoldSum = '/miniapi/report/getItemSoldSum';

  ///查询首页商品销售金额排行榜-实销数量
  static const getItemActualQuantitySoldList =
      '/miniapi/report/getItemActualQuantitySoldList';

  /// 3查询业务员退货金额详情正常品、临期品、过期品、其他金额总数
  static const itemReturnDetailListSum =
      '/miniapi/report/itemReturnDetailListSum';

  ///商品退货金额排行榜-查看全部列表
  static const itemReturnDetailList = '/miniapi/report/itemReturnDetailList';

  /// 4查询首页门店销售金额明细头部总数
  static const getConsumerSum = '/miniapi/report/getConsumerSum';

  /// 查询首页门店销售金额排行榜-实销金额/详情页面列表
  static const getConsumerActualSalesList =
      '/miniapi/report/getConsumerActualSalesList';

  /// 5查询首页门店销售金额排行榜-退货金额/退货金额列表
  static const getConsumerReturnAmtDetailSum =
      '/miniapi/report/getConsumerReturnAmtDetailSum';
  static const getConsumerReturnAmtDetailList =
      '/miniapi/report/getConsumerReturnAmtDetailList';

  /// 6查询首页业务员销售金额排行榜-实销金额/详情页面列表
  static const getEmployeeSum = '/miniapi/report/getEmployeeSum';
  static const getEmployeeActualSalesList =
      '/miniapi/report/getEmployeeActualSalesList';

  /// 7查询首页业务员销售金额排行榜-退货金额/退货金额列表
  static const getEmployeeReturnAmtDetailSum =
      '/miniapi/report/getEmployeeReturnAmtDetailSum';
  static const getEmployeeReturnAmtDetailList =
      '/miniapi/report/getEmployeeReturnAmtDetailList';

  /// 商品销售金额-实销金额
  static const getBossFormItemActualSalesList =
      '/miniapi/report/getItemActualSalesList';

  /// 商品销售金额-订单金额
  static const getBossFormItemOrderAmtList =
      '/miniapi/report/getItemOrderAmtList';

  /// 商品销售金额-退款金额
  static const getBossFormItemReturnAmtList =
      '/miniapi/report/getItemReturnAmtList';

  /// 商品销量-实销数量
  static const getBossFormItemActualQuantitySoldList =
      '/miniapi/report/getItemActualQuantitySoldList';

  /// 商品销售金额-订单数量
  static const getBossFormItemOrderVolumeList =
      '/miniapi/report/itemOrderVolumeList';

  /// 商品销售金额-退款金额
  static const getBossFormItemReturnNumList =
      '/miniapi/report/getItemReturnNumList';

  /// 门店销售金额-实销金额
  static const getBossFormConsumerActualSalesList =
      '/miniapi/report/getConsumerActualSalesList';

  /// 门店销售金额-订单金额
  static const getBossFormConsumerOrderAmtList =
      '/miniapi/report/getConsumerOrderAmtList';

  /// 门店销售金额-退货金额
  static const getBossFormConsumerReturnAmtList =
      '/miniapi/report/getConsumerReturnAmtList';

  /// 业务员销售金额-实销金额
  static const getBossFormEmployeeActualSalesList =
      '/miniapi/report/getEmployeeActualSalesList';

  /// 门店销售金额-订单金额
  static const getBossFormEmployeeOrderAmtList =
      '/miniapi/report/getEmployeeOrderAmtList';

  /// 门店销售金额-退货金额
  static const getBossFormEmployeeReturnAmtList =
      '/miniapi/report/getEmployeeReturnAmtList';

  /// 业务员退货金额-退货金额
  static const getBossFormEmployeeReturnAmtDetailList =
      '/miniapi/report/getEmployeeReturnAmtList';

  ///业务员门店销售明细列表查询
  static const getEmployeeConsumerSumList =
      '/miniapi/report/getEmployeeConsumerSumList';

  ///查询业务员门店统计金额
  static const getEmployeeConsumerSum =
      '/miniapi/report/getEmployeeConsumerSum';

  ///查询业务员门店item详情金额
  static const getEmployeeConsumerItemDetailList =
      '/miniapi/report/getEmployeeConsumerItemDetailList';

  //-------------------------------【物流配送】模块相关接口-------------------------------------------------
  /// 获取待配送订单单列表
  static const getSellOrderList = '/miniapi/getSellOrderList';

  /// 待配送订单排序
  static const changeOrderSort = '/miniapi/changeOrderSort';

  /// 待配送订单装车
  static const batchLoadCar = '/miniapi/batchLoadCar';

  /// 待配送订单---地图预览数据
  static const getSellConsumerMapList = '/miniapi/getSellConsumerMapList';

  /// 销售单详情
  static const getSellOrderDetail = '/miniapi/getSellOrderDetail/';

  /// 退货单详情
  static const getRefundOrderDetail = '/miniapi/getRefundOrderDetail/';

  /// 获取更换的配送员列表
  static const getCourierOption = '/miniapi/getCourierOption';

  /// 更换配送员
  static const changeCourier = '/miniapi/changeCourier';

  /// 获取配送订单列表
  static const getDistributionList = '/miniapi/getDistributionList';

  /// 获取配送订单详情
  static const getDistributionDetails = '/miniapi/getDistributionDetails/';

  /// 配送中订单：差异录入获取全部订单和商品
  static const getDistributionItemDetails =
      '/miniapi/getDistributionItemDetails/';

  /// 配送中订单：差异录入确认、整单拒收
  static const submitDifference = '/miniapi/submitDifference';

  /// 配送中订单：差异录入确认、整单拒收
  static const confirmGoods = '/miniapi/confirmGoods/';

  /// 配送中订单：获取结算金额
  static const getDistributionSettleAmount =
      '/miniapi/getDistributionSettleAmount/';

  /// 配送中订单：结算提交
  static const logisticsSettleCommit = '/miniapi/settleCommit';

  /// 修改手机号发送验证码：
  static const modifiedPhoneNumberSendMsgCode = '/miniapi/user/loginSendSms';

  /// 在线获取二维码支付：
  static const onlinePay = '/miniapi/appPay/onlinePay';

  /// 在线获取二维码支付结果：
  static const obtainPayResult = '/miniapi/appPay/obtainPayResult';

  /// 修改手机号
  static const modifiedPhoneNumber = '/miniapi/user/changePhoneNumber';

  /// 验证手机号
  static const verifyPhoneNumber = 'miniapi/user/verifyPhone';

  /// 忘记密码手机号发送验证码：
  static const noLoginSendMsgCode = '/miniapi/user/unLoginSendSms';

  /// 修改密码
  static const modifiedPassword = '/miniapi/user/changePassword';

  /// 库存查询--获取品牌列表
  static const getBaseBrand = '/miniapi/common/getBaseBrand';

  /// 库存查询--获取仓库列表
  static const getBranchesForStock = '/miniapi/common/getBranchesForStock';
  /// 商品销售 --获取仓库列表
  static const getBranchScope = '/miniapi/common/getBranchScope';

  /// 库存查询--获取类别列表
  static const getInventoryItemClsTreeNode =
      '/miniapi/common/getItemClsTreeNode';

  /// 库存查询
  static const getItemStockListForApp =
      '/miniapi/common/getItemStockListForApp';

  /// 库存查询--明细
  static const getAvailableStock = '/miniapi/common/getAvailableStock';

  /// 处理品销售列表
  static const dispose_item_saleList = '/miniapi/dispose_item_sale/stock';

  /// 处理品销售---购物车列表
  static const dispose_item_saleCart = '/miniapi/selfCart/info';

  /// 处理品销售---获取处理品销售仓库
  static const refundBranch = '/miniapi/common/refundBranch/';

  /// 首页--校验是否显示店铺预览
  static const checkShowShopScan = '/miniapi/consumer/shop/preview';

  // 补货列表增加购物车功能
  static const REPLENISH_CART_SAVE = '/miniapi/replenishCart/save'; //保存接口
  static const REPLENISH_CART_DEL = '/miniapi/replenishCart/delete'; //删除接口
  static const REPLENISH_CART_SEARCH = '/miniapi/replenishCart/info'; //查询购物车接口

  // 你可以添加一个方法来修改 baseUrl
  void setBaseUrl(String newBaseUrl) {
    baseUrl = newBaseUrl;
  }
}
