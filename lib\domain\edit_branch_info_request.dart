/// 保存客户详情接口请求参数模型
class EditBranchInfoRequest {
  String? branchId;
  String? branchName;
  String? contactName;
  String? contactPhone;
  String? channelId;
  String? groupId;
  int? status;
  String? areaId;
  String? firstAreaCityId;
  String? secondAreaCityId;
  String? threeAreaCityId;
  double? latitude;
  double? longitude;
  String? branchAddr;
  String? memo;
  int? hdfkSupport; // 货到付款支持
  int? isPayOnline; // 是否在线支付
  double? hdfkMaxAmt; // 货到付款最大金额
  String? provinceName;
  String? cityName;
  String? districtName;
  String? nextBranchLifecycleMessage;
  int? lifecycleStage;
  String? distributionChannel;
  String? distributionMode;
  String? profitMode;
  String? merchantNo;
  String? merchantApplyNo;
  String? merchantFullName;

  EditBranchInfoRequest({
    this.branchId,
    this.branchName,
    this.contactName,
    this.contactPhone,
    this.channelId,
    this.groupId,
    this.status,
    this.areaId,
    this.firstAreaCityId,
    this.secondAreaCityId,
    this.threeAreaCityId,
    this.latitude,
    this.longitude,
    this.branchAddr,
    this.memo,
    this.hdfkSupport,
    this.isPayOnline,
    this.hdfkMaxAmt,
    this.provinceName,
    this.cityName,
    this.districtName,
    this.nextBranchLifecycleMessage,
    this.lifecycleStage,
    this.distributionChannel,
    this.distributionMode,
    this.profitMode,
    this.merchantNo,
    this.merchantApplyNo,
    this.merchantFullName,
  });

  EditBranchInfoRequest.fromJson(Map<String, dynamic> json) {
    branchId = json['branchId'];
    branchName = json['branchName'];
    contactName = json['contactName'];
    contactPhone = json['contactPhone'];
    channelId = json['channelId'];
    groupId = json['groupId'];
    status = json['status'];
    areaId = json['areaId'];
    firstAreaCityId = json['firstAreaCityId'];
    secondAreaCityId = json['secondAreaCityId'];
    threeAreaCityId = json['threeAreaCityId'];
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
    branchAddr = json['branchAddr'];
    memo = json['memo'];
    hdfkSupport = json['hdfkSupport'];
    isPayOnline = json['isPayOnline'];
    hdfkMaxAmt = json['hdfkMaxAmt']?.toDouble();
    provinceName = json['provinceName'];
    cityName = json['cityName'];
    districtName = json['districtName'];
    nextBranchLifecycleMessage = json['nextBranchLifecycleMessage'];
    lifecycleStage = json['lifecycleStage'];
    distributionChannel = json['distributionChannel'];
    distributionMode = json['distributionMode'];
    profitMode = json['profitMode'];
    merchantNo = json['merchantNo'];
    merchantApplyNo = json['merchantApplyNo'];
    merchantFullName = json['merchantFullName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['branchId'] = branchId;
    data['branchName'] = branchName;
    data['contactName'] = contactName;
    data['contactPhone'] = contactPhone;
    data['channelId'] = channelId;
    data['groupId'] = groupId;
    data['status'] = status;
    data['areaId'] = areaId;
    data['firstAreaCityId'] = firstAreaCityId;
    data['secondAreaCityId'] = secondAreaCityId;
    data['threeAreaCityId'] = threeAreaCityId;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['branchAddr'] = branchAddr;
    data['memo'] = memo;
    data['hdfkSupport'] = hdfkSupport;
    data['isPayOnline'] = isPayOnline;
    data['hdfkMaxAmt'] = hdfkMaxAmt;
    data['provinceName'] = provinceName;
    data['cityName'] = cityName;
    data['districtName'] = districtName;
    data['nextBranchLifecycleMessage'] = nextBranchLifecycleMessage;
    data['lifecycleStage'] = lifecycleStage;
    data['distributionChannel'] = distributionChannel;
    data['distributionMode'] = distributionMode;
    data['profitMode'] = profitMode;
    data['merchantNo'] = merchantNo;
    data['merchantApplyNo'] = merchantApplyNo;
    data['merchantFullName'] = merchantFullName;
    return data;
  }
}
