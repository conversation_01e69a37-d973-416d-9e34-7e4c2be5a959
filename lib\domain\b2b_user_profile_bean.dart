/*
 * 项目名：福多多APP
 * 作者：AI Assistant
 * 创建时间：2025年07月17日
 * 修改时间：2025年07月17日
 */

/// B2B用户信息响应数据结构
class B2bUserProfileBean {
  String? msg;
  String? postGroup;
  int? code;
  B2bUserData? data;
  String? roleGroup;

  B2bUserProfileBean({
    this.msg,
    this.postGroup,
    this.code,
    this.data,
    this.roleGroup,
  });

  B2bUserProfileBean.fromJson(Map<String, dynamic> json) {
    msg = json['msg'];
    postGroup = json['postGroup'];
    code = json['code'];
    data = json['data'] != null ? B2bUserData.fromJson(json['data']) : null;
    roleGroup = json['roleGroup'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['msg'] = this.msg;
    data['postGroup'] = this.postGroup;
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['roleGroup'] = this.roleGroup;
    return data;
  }
}

/// B2B用户数据
class B2bUserData {
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? userId;
  String? sysCode;
  String? dcId;
  String? supplierId;
  String? deptId;
  String? userName;
  String? nickName;
  String? email;
  String? phonenumber;
  String? sex;
  String? saasUserCode;
  String? verificationCode;
  String? avatar;
  String? password;
  String? status;
  String? delFlag;
  String? loginIp;
  String? loginDate;
  String? remark;
  dynamic dept;
  List<B2bUserRole>? roles;
  dynamic roleIds;
  dynamic postIds;
  dynamic roleId;
  int? colonelId;
  String? funcScop;
  String? areaId;
  String? brandId;
  bool? admin;

  B2bUserData({
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.userId,
    this.sysCode,
    this.dcId,
    this.supplierId,
    this.deptId,
    this.userName,
    this.nickName,
    this.email,
    this.phonenumber,
    this.sex,
    this.saasUserCode,
    this.verificationCode,
    this.avatar,
    this.password,
    this.status,
    this.delFlag,
    this.loginIp,
    this.loginDate,
    this.remark,
    this.dept,
    this.roles,
    this.roleIds,
    this.postIds,
    this.roleId,
    this.colonelId,
    this.funcScop,
    this.areaId,
    this.brandId,
    this.admin,
  });

  B2bUserData.fromJson(Map<String, dynamic> json) {
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    userId = json['userId'];
    sysCode = json['sysCode'];
    dcId = json['dcId'];
    supplierId = json['supplierId'];
    deptId = json['deptId'];
    userName = json['userName'];
    nickName = json['nickName'];
    email = json['email'];
    phonenumber = json['phonenumber'];
    sex = json['sex'];
    saasUserCode = json['saasUserCode'];
    verificationCode = json['verificationCode'];
    avatar = json['avatar'];
    password = json['password'];
    status = json['status'];
    delFlag = json['delFlag'];
    loginIp = json['loginIp'];
    loginDate = json['loginDate'];
    remark = json['remark'];
    dept = json['dept'];
    if (json['roles'] != null) {
      roles = <B2bUserRole>[];
      json['roles'].forEach((v) {
        roles!.add(B2bUserRole.fromJson(v));
      });
    }
    roleIds = json['roleIds'];
    postIds = json['postIds'];
    roleId = json['roleId'];
    colonelId = json['colonelId'];
    funcScop = json['funcScop'];
    areaId = json['areaId'];
    brandId = json['brandId'];
    admin = json['admin'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['updateBy'] = this.updateBy;
    data['updateTime'] = this.updateTime;
    data['userId'] = this.userId;
    data['sysCode'] = this.sysCode;
    data['dcId'] = this.dcId;
    data['supplierId'] = this.supplierId;
    data['deptId'] = this.deptId;
    data['userName'] = this.userName;
    data['nickName'] = this.nickName;
    data['email'] = this.email;
    data['phonenumber'] = this.phonenumber;
    data['sex'] = this.sex;
    data['saasUserCode'] = this.saasUserCode;
    data['verificationCode'] = this.verificationCode;
    data['avatar'] = this.avatar;
    data['password'] = this.password;
    data['status'] = this.status;
    data['delFlag'] = this.delFlag;
    data['loginIp'] = this.loginIp;
    data['loginDate'] = this.loginDate;
    data['remark'] = this.remark;
    data['dept'] = this.dept;
    if (this.roles != null) {
      data['roles'] = this.roles!.map((v) => v.toJson()).toList();
    }
    data['roleIds'] = this.roleIds;
    data['postIds'] = this.postIds;
    data['roleId'] = this.roleId;
    data['colonelId'] = this.colonelId;
    data['funcScop'] = this.funcScop;
    data['areaId'] = this.areaId;
    data['brandId'] = this.brandId;
    data['admin'] = this.admin;
    return data;
  }
}

/// B2B用户角色
class B2bUserRole {
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? roleId;
  String? sysCode;
  String? dcId;
  String? roleName;
  String? roleKey;
  int? roleSort;
  String? dataScope;
  bool? menuCheckStrictly;
  bool? deptCheckStrictly;
  String? status;
  String? delFlag;
  String? remark;
  String? funcScop;
  bool? flag;
  dynamic menuIds;
  dynamic deptIds;
  dynamic permissions;
  dynamic funScopes;
  bool? admin;

  B2bUserRole({
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.roleId,
    this.sysCode,
    this.dcId,
    this.roleName,
    this.roleKey,
    this.roleSort,
    this.dataScope,
    this.menuCheckStrictly,
    this.deptCheckStrictly,
    this.status,
    this.delFlag,
    this.remark,
    this.funcScop,
    this.flag,
    this.menuIds,
    this.deptIds,
    this.permissions,
    this.funScopes,
    this.admin,
  });

  B2bUserRole.fromJson(Map<String, dynamic> json) {
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    roleId = json['roleId'];
    sysCode = json['sysCode'];
    dcId = json['dcId'];
    roleName = json['roleName'];
    roleKey = json['roleKey'];
    roleSort = json['roleSort'];
    dataScope = json['dataScope'];
    menuCheckStrictly = json['menuCheckStrictly'];
    deptCheckStrictly = json['deptCheckStrictly'];
    status = json['status'];
    delFlag = json['delFlag'];
    remark = json['remark'];
    funcScop = json['funcScop'];
    flag = json['flag'];
    menuIds = json['menuIds'];
    deptIds = json['deptIds'];
    permissions = json['permissions'];
    funScopes = json['funScopes'];
    admin = json['admin'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['updateBy'] = this.updateBy;
    data['updateTime'] = this.updateTime;
    data['roleId'] = this.roleId;
    data['sysCode'] = this.sysCode;
    data['dcId'] = this.dcId;
    data['roleName'] = this.roleName;
    data['roleKey'] = this.roleKey;
    data['roleSort'] = this.roleSort;
    data['dataScope'] = this.dataScope;
    data['menuCheckStrictly'] = this.menuCheckStrictly;
    data['deptCheckStrictly'] = this.deptCheckStrictly;
    data['status'] = this.status;
    data['delFlag'] = this.delFlag;
    data['remark'] = this.remark;
    data['funcScop'] = this.funcScop;
    data['flag'] = this.flag;
    data['menuIds'] = this.menuIds;
    data['deptIds'] = this.deptIds;
    data['permissions'] = this.permissions;
    data['funScopes'] = this.funScopes;
    data['admin'] = this.admin;
    return data;
  }
}
