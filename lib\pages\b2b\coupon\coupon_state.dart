import 'package:get/get.dart';

/// 优惠券数据模型
class CouponModel {
  final int sysCode;
  final int couponId;
  final String couponTemplateId;
  final String couponName;
  final String state; // 0-可领取, 1-未使用, 2-已使用, 3-已过期
  final String expirationDateStart;
  final String expirationDateEnd;
  final int spuScope; // 商品使用范围(数据字典): 0-所有商品可用(全场), 1-指定商品可用(品类), 2-指定品牌可用(品牌)
  final int discountType; // 优惠类型(数据字典): 0-满减券, 1-折扣券
  final double discountAmt; // 优惠金额,满多少可以减多少元(满减券设置)
  final double discountPercent; // 优惠折扣,如9折则填0.9(折扣券设置)
  final double triggerAmt; // 优惠折扣,如9折则填0.9(折扣券设置)
  final String tabsType; // available-可领取 unused-未使用 used-已使用

  CouponModel({
    required this.sysCode,
    required this.couponId,
    required this.couponTemplateId,
    required this.couponName,
    required this.state,
    required this.expirationDateStart,
    required this.expirationDateEnd,
    required this.spuScope,
    required this.discountType,
    required this.discountAmt,
    required this.discountPercent,
    required this.triggerAmt,
    required this.tabsType,
  });

  factory CouponModel.fromJson(Map<String, dynamic> json) {
    return CouponModel(
      sysCode: json['sysCode'] ?? 0,
      couponId: json['couponId'] ?? 0,
      couponTemplateId: json['couponTemplateId'] ?? '',
      couponName: json['couponName'] ?? '',
      state: json['state'] ?? '0',
      expirationDateStart: json['expirationDateStart'] ?? '',
      expirationDateEnd: json['expirationDateEnd'] ?? '',
      spuScope: json['spuScope'] ?? 0,
      discountType: json['discountType'] ?? 0,
      discountAmt: (json['discountAmt'] ?? 0.0).toDouble(),
      discountPercent: (json['discountPercent'] ?? 0.0).toDouble(),
      triggerAmt: (json['triggerAmt'] ?? 0.0).toDouble(),
      tabsType: json['tabsType'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sysCode': sysCode,
      'couponId': couponId,
      'couponTemplateId': couponTemplateId,
      'couponName': couponName,
      'state': state,
      'expirationDateStart': expirationDateStart,
      'expirationDateEnd': expirationDateEnd,
      'spuScope': spuScope,
      'discountType': discountType,
      'discountAmt': discountAmt,
      'discountPercent': discountPercent,
      'triggerAmt': triggerAmt,
      'tabsType': tabsType,
    };
  }

  /// 获取优惠券显示文本
  String get displayAmount {
    if (discountType == 1) {
      // 折扣券
      return '${(discountPercent).toInt()}折';
    } else {
      // 满减券
      return '¥${discountAmt.toInt()}';
    }
  }

  /// 获取使用条件文本
  String get conditionText {
    // if (discountType == 1) {
    //   // 折扣券
    //   return '满${triggerAmt.toInt()}元可用';
    // } else {
    //   // 满减券
    //   return '满${discountAmt.toInt()}元可用';
    // }

    return '满${triggerAmt.toInt()}元可用';
  }

  /// 获取商品范围文本
  /// 0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）
  String get scopeText {
    switch (spuScope) {
      case 0:
        return '全场券';
      case 1:
        return '入驻商券';
      case 2:
        return '品类券';
      case 3:
        return '品牌券';
      case 4:
        return '商品券';
      default:
        return '全场券';
    }
  }

  /// 获取状态文本
  String get stateText {
    switch (state) {
      case '0':
        return '可领取';
      case '1':
        return '未使用';
      case '2':
        return '已使用';
      case '3':
        return '已过期';
      default:
        return '未知';
    }
  }
}

class SendCouponsData {
  String? couponTemplateId;
  String? couponId;
  String? branchName;
  int? branchId;
  int? code;
  String? msg;

  SendCouponsData(
      {required this.couponTemplateId,
      required this.couponId,
      required this.branchName,
      required this.branchId,
      required this.code,
      required this.msg});

  factory SendCouponsData.fromJson(Map<String, dynamic> json) {
    return SendCouponsData(
      couponTemplateId: json['couponTemplateId'],
      couponId: json['couponId'],
      branchName: json['branchName'],
      branchId: json['branchId'],
      code: json['code'],
      msg: json['msg'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['couponTemplateId'] = couponTemplateId;
    data['couponId'] = couponId;
    data['branchName'] = branchName;
    data['branchId'] = branchId;
    data['code'] = code;
    data['msg'] = msg;
    return data;
  }
}

/// 优惠券页面状态管理
class CouponState {
  // 当前选中的标签页索引
  RxInt currentTabIndex = 0.obs;

  // 可领取优惠券列表
  RxList<CouponModel> availableCoupons = <CouponModel>[].obs;

  // 未使用优惠券列表
  RxList<CouponModel> unusedCoupons = <CouponModel>[].obs;

  // 已使用优惠券列表
  RxList<CouponModel> usedCoupons = <CouponModel>[].obs;

  // 加载状态
  RxBool isLoading = false.obs;

  // 刷新状态
  RxBool isRefreshing = false.obs;

  // 使用规则展开状态 - 每个优惠券的展开状态
  RxMap<String, bool> expandedRules = <String, bool>{}.obs;

  // 模拟数据
  // void initMockData() {
  //   // 可领取优惠券 - 根据接口文档结构
  //   availableCoupons.value = [
  //     CouponModel(
  //       sysCode: 1001,
  //       couponId: 1,
  //       couponTemplateId: '101',
  //       couponName: '8折优惠券',
  //       state: '0', // 可领取
  //       expirationDateStart: '2024-07-01',
  //       expirationDateEnd: '2024-07-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 1, // 折扣券
  //       discountAmt: 0.0,
  //       discountPercent: 0.8,
  //       tabsType: 'available',
  //     ),
  //     CouponModel(
  //       sysCode: 1002,
  //       couponId: 2,
  //       couponTemplateId: '102',
  //       couponName: '满100减10优惠券',
  //       state: '0', // 可领取
  //       expirationDateStart: '2024-07-01',
  //       expirationDateEnd: '2024-07-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 0, // 满减券
  //       discountAmt: 100.0,
  //       discountPercent: 10.0,
  //       tabsType: 'available',
  //     ),
  //   ];

  //   // 未使用优惠券 - 根据接口文档结构
  //   unusedCoupons.value = [
  //     CouponModel(
  //       sysCode: 1003,
  //       couponId: 3,
  //       couponTemplateId: '103',
  //       couponName: '8折优惠券',
  //       state: '1', // 未使用
  //       expirationDateStart: '2024-07-01',
  //       expirationDateEnd: '2024-07-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 1, // 折扣券
  //       discountAmt: 0.0,
  //       discountPercent: 0.8,
  //       tabsType: 'unused',
  //     ),
  //     CouponModel(
  //       sysCode: 1004,
  //       couponId: 4,
  //       couponTemplateId: '104',
  //       couponName: '满100减10优惠券',
  //       state: '1', // 未使用
  //       expirationDateStart: '2024-07-01',
  //       expirationDateEnd: '2024-07-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 0, // 满减券
  //       discountAmt: 100.0,
  //       discountPercent: 10.0,
  //       tabsType: 'unused',
  //     ),
  //   ];

  //   // 已使用优惠券 - 根据接口文档结构
  //   usedCoupons.value = [
  //     CouponModel(
  //       sysCode: 1005,
  //       couponId: 5,
  //       couponTemplateId: '105',
  //       couponName: '8折优惠券',
  //       state: '2', // 已使用
  //       expirationDateStart: '2024-05-01',
  //       expirationDateEnd: '2024-05-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 1, // 折扣券
  //       discountAmt: 0.0,
  //       discountPercent: 0.8,
  //       tabsType: 'used',
  //     ),
  //     CouponModel(
  //       sysCode: 1006,
  //       couponId: 6,
  //       couponTemplateId: '106',
  //       couponName: '满2减0.6优惠券',
  //       state: '2', // 已使用
  //       expirationDateStart: '2024-05-01',
  //       expirationDateEnd: '2024-05-31',
  //       spuScope: 0, // 全场通用
  //       discountType: 0, // 满减券
  //       discountAmt: 2.0,
  //       discountPercent: 0.6,
  //       tabsType: 'used',
  //     ),
  //   ];
  // }

  // 获取当前标签页的优惠券列表
  List<CouponModel> getCurrentTabCoupons() {
    switch (currentTabIndex.value) {
      case 0:
        return availableCoupons;
      case 1:
        return unusedCoupons;
      case 2:
        return usedCoupons;
      default:
        return [];
    }
  }

  // 获取标签页标题
  List<String> getTabTitles() {
    return ['可领取', '未使用', '已使用'];
  }
}
