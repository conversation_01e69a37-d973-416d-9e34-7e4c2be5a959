import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/widget/loading_dialog.dart';
import 'package:get/get.dart';

import 'state.dart';

class B2bChooseTypeLogic extends GetxController {
  final B2bChooseTypeState state = B2bChooseTypeState();
  var dialogContext;

  @override
  void onInit() {
    super.onInit();

    // if (Get.parameters["fromPage"] != null) {
    //   state.fromPage = Get.parameters["fromPage"]!;
    //   print("fromPage=>${state.fromPage}");
    // }
  }

  //弹出loading弹出框
  void showLoadingDialog(BuildContext context, String text,
      {bool useRootNavigator = true}) {
    showDialog(
        context: context,
        useRootNavigator: useRootNavigator,
        barrierDismissible: false,
        builder: (buildContext) {
          dialogContext = buildContext;
          return LoadingDialog(
            outsideDismiss: false,
            loadingText: text,
          );
        });
  }

  //关闭弹出框
  void dismissDialog() {
    if (dialogContext != null) {
      Navigator.pop(dialogContext);
    }
  }
}
