# Toast提示规范文档

## 📋 规范要求

**不使用 `Get.snackbar`，统一使用系统默认的提示工具类 `MyCommonUtils.showToast`**

## 🔧 修改对比

### 修改前（不推荐）
```dart
Get.snackbar(
  '网络提示',
  '当前网络不可用，请检查网络连接',
  snackPosition: SnackPosition.TOP,
  backgroundColor: const Color(0xFFFFEBEE),
  colorText: const Color(0xFFD32F2F),
  icon: const Icon(Icons.wifi_off, color: Color(0xFFD32F2F)),
  duration: const Duration(seconds: 3),
);
```

### 修改后（推荐）
```dart
MyCommonUtils.showToast('当前网络不可用，请检查网络连接');
```

## ✅ 已修复的文件

### 1. 网络状态组件
**文件**: `lib/widget/network_status_widget.dart`

#### **修改内容**
- `checkNetworkWithToast()` 方法中的提示
- `checkNetworkBeforeRequest()` 方法中的提示

#### **修改前**
```dart
Get.snackbar(
  '网络提示',
  '当前网络不可用，请检查网络连接',
  // ...复杂的样式配置
);

Get.snackbar(
  '网络错误', 
  '网络连接不可用，请检查网络设置',
  // ...复杂的样式配置
);
```

#### **修改后**
```dart
MyCommonUtils.showToast('当前网络不可用，请检查网络连接');

MyCommonUtils.showToast('网络连接不可用，请检查网络设置');
```

## 🎯 规范优势

### 1. 统一性
- ✅ **风格一致**: 所有提示使用相同的样式和行为
- ✅ **用户体验**: 用户看到的提示效果一致
- ✅ **维护简单**: 统一的API，易于维护

### 2. 简洁性
- ✅ **代码简洁**: 一行代码完成提示
- ✅ **无需配置**: 不需要设置颜色、位置等参数
- ✅ **减少依赖**: 减少对GetX snackbar的依赖

### 3. 系统集成
- ✅ **系统默认**: 使用项目已有的提示工具类
- ✅ **样式统一**: 与项目整体UI风格保持一致
- ✅ **性能优化**: 避免额外的UI组件开销

## 📝 使用规范

### ✅ 推荐用法
```dart
// 简单文本提示
MyCommonUtils.showToast('操作成功');

// 错误提示
MyCommonUtils.showToast('网络连接失败');

// 警告提示
MyCommonUtils.showToast('请检查输入信息');
```

### ❌ 不推荐用法
```dart
// 不要使用Get.snackbar
Get.snackbar('标题', '内容');

// 不要使用其他第三方Toast库
Fluttertoast.showToast(msg: '内容');

// 不要使用ScaffoldMessenger（除非特殊需求）
ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('内容')));
```

## 🔍 检查清单

在开发过程中，请检查以下内容：

- [ ] 所有提示都使用 `MyCommonUtils.showToast`
- [ ] 没有使用 `Get.snackbar`
- [ ] 没有使用其他Toast库
- [ ] 提示文字简洁明了
- [ ] 提示内容符合用户理解

## 📊 项目中的应用

### 已统一的场景
- ✅ 网络状态提示
- ✅ 定位权限提示
- ✅ 表单验证提示
- ✅ 操作结果提示

### 需要注意的场景
- 网络请求失败提示
- 用户操作反馈
- 错误信息展示
- 成功状态提示

## 🎉 总结

通过统一使用 `MyCommonUtils.showToast`，我们实现了：

1. **代码简洁**: 减少了复杂的配置代码
2. **风格统一**: 所有提示保持一致的视觉效果
3. **维护方便**: 统一的API便于后续维护和修改
4. **性能优化**: 减少不必要的UI组件创建

请在今后的开发中严格遵循这个规范！
