import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pickers/pickers.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/base/base_logic.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/pages/homePages/map_help/mapFluttify.dart';
import 'package:fuduoduo/pages/homePages/picture_help/picture_help.dart';
// import 'package:fuduoduo/pages/homePages/store_info/store_info_state.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/pages/customer/info/logic.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:get/get.dart';
import 'package:flustars/flustars.dart';

// 导入新的数据模型
import '../../../domain/area_city_bean.dart';
import '../../../domain/group_list_bean.dart';
import '../../../domain/price_group_bean.dart';
import '../../../domain/channel_list_bean.dart';
import '../../../domain/edit_branch_info_request.dart';

import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:fuduoduo/resource/area_street_map_resource.dart';
import 'package:fuduoduo/resource/area_map_resource.dart';
import 'package:fuduoduo/pages/homePages/map_help/mapSearch.dart';
import 'dart:async';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import '../../../resource/string_resource.dart';


import '../../../provider/event/route_bind_event.dart';
import '../../../route/index.dart';

import 'store_info_state.dart';

class StoreInfoLogic extends BaseLogic {
  final StroeInfoState state = StroeInfoState();
  dynamic arguments = Get.arguments;

  // bool isEdit = false;
  String type = "";
  String from = "";
  String salesMan = "";
  Map customerInfo = {};

  // 编辑模式状态
  bool isEditMode = false;

  bool isHasInfo = false;

  /// 门店信息
  final TextEditingController infoEditCtr = TextEditingController();

  /// 联系人
  final TextEditingController nameEditCtr = TextEditingController();

  /// 手机号
  final TextEditingController phoneEditCtr = TextEditingController();

  final TextEditingController pwdEditCtr = TextEditingController();

  /// 线路
  final TextEditingController lineEditCtr = TextEditingController();

  // 所在地区
  final TextEditingController areaEditCtr = TextEditingController();

  ///渠道
  final TextEditingController channelEditCtr = TextEditingController();

  ///全国分组
  final TextEditingController groupEditCtr = TextEditingController();

  ///渠道类型
  final TextEditingController channelTypeEditCtr = TextEditingController();

  ///部门
  final TextEditingController deptEditCtr = TextEditingController();

  ///等级价格
  final TextEditingController priceLevelEditCtr = TextEditingController();

  /// 地址
  final TextEditingController addressEditCtr = TextEditingController();

  /// 定位地址
  final TextEditingController pointEditCtr = TextEditingController();

  /// 备注
  final TextEditingController desEditCtr = TextEditingController();

  final TextEditingController maxDebtController = TextEditingController(); // 最大可欠款金额控制器


  // 城市地区
  final TextEditingController areaCityEditCtr = TextEditingController();

  final TextEditingController dictEditCtr = TextEditingController();


  PicMange _picMange = PicMange();

  var imgUrl = ''.obs;

  double latitude = 0.0;
  double longitude = 0.0;

  /// 路线数组
  List routes = [];
  int routeIndex = -1;

  /// 渠道数组
  List channels = [];
  int channelIndex = -1;

  /// 全国分组数组
  List groups = [];
  int groupIndex = -1;

  /// 价格组数组
  List dicts = [];
  int dictIndex = -1;

  /// 渠道类型数组
  List channelTypes = [];
  int channelTypeIndex = -1;

  /// 部门数组
  List depts = [];
  int deptIndex = -1;

  /// 等级价格数组
  List priceLevels = [];
  int priceLevelIndex = -1;

  dynamic userInfo = SpUtil.getObject('userInfo');


  late MapSearch _mapSearch;
  late Location _location;
  var _index = -1;
  Poi? _poi;
  AmapController? _controller;

  Timer? _debounceTimer;  // 用来控制延迟执行的 Timer


  @override
  onInit() {
    super.onInit();
    // _getRouteOption();
    // _getChannelOption();
    // _getDeptOption();
    // _getPriceLevelOption();
    getAreaList();
    // getAreaCityList();
    getGroupList();
    getChannelList();
    getDictList();
    // arguments= {};


    if (arguments != null) {
      // print(arguments);
      // print('arguments   ${customerInfo['latitude']}');
      if (arguments['type'] != null) {
        type = arguments['type'];
      }
      if (arguments['from'] != null) {
        from = arguments['from'];
      }
      if (arguments['salesMan'] != null) {
        salesMan = arguments['salesMan'];
      }
      if (arguments['customerInfo'] != null) {
        customerInfo = arguments['customerInfo'];
      }
      print('customerInfo $customerInfo');
      isHasInfo = true;
      // 门店信息
      infoEditCtr.text = customerInfo['consumerName']??"";
      // 联系人
      nameEditCtr.text = customerInfo['contactMan']??"";
      // 手机号
      phoneEditCtr.text = customerInfo['telephoneNum']??"";
      // 地址
      addressEditCtr.text = customerInfo['shippingAddress']??"";
      // 定位
      pointEditCtr.text = customerInfo['shippingAddress']??"";
      // 图片 - 优先使用新字段 branchImages，如果没有则使用旧字段 imgUrls
      imgUrl.value = customerInfo['branchImages'] ?? customerInfo['imgUrls'] ?? "";

      desEditCtr.text = customerInfo['memo'] ?? '';

      // isEdit = true;
      // 坐标
      latitude = double.parse(customerInfo['latitude'] != null && customerInfo['latitude'].toString().isNotEmpty ? customerInfo['latitude'].toString() : "0.0");
      longitude = double.parse(customerInfo['longitude'] != null && customerInfo['longitude'].toString().isNotEmpty ? customerInfo['longitude'].toString() : "0.0");

      state.TDInitData = customerInfo['streetCode'] != null && customerInfo['streetCode'].isNotEmpty ? customerInfo['streetCode'] : customerInfo['districtCode'];
      areaEditCtr.text = "${customerInfo['provinceName']}/${customerInfo['cityName']}/${customerInfo['districtName']}/${customerInfo['streetName'] ?? ''}";
      // print('userOrgSettings ${customerInfo["isSyncB2b"]}');
      state.isSyncB2b = customerInfo["isSyncB2b"] != null ? customerInfo["isSyncB2b"] : 0;


      state.provinceName = customerInfo['provinceName'] ?? ''; // 省份
      state.cityName = customerInfo['cityName'] ?? ''; // 城市
      state.districtName = customerInfo['districtName'] ?? ''; // 区县
      state.streetName = customerInfo['streetName'] ?? ''; // 街道
      state.provinceCode = customerInfo['provinceCode'] ?? ''; // 省份编码
      state.cityCode = customerInfo['cityCode'] ?? ''; // 城市编码
      state.districtCode = customerInfo['districtCode'] ?? ''; // 区县编码
      state.streetCode = customerInfo['streetCode'] ?? ''; // 街道编码


      state.TDAreaInitData = customerInfo['areaId'];
      areaCityEditCtr.text = customerInfo['cityRegionName'];

      // 回填货到付款相关信息
      print("customerInfo['hdfkSupport'] ${customerInfo['hdfkSupport'] == 1}");
      if (customerInfo['hdfkSupport'] != null) {
        state.CashOnDelivery = customerInfo['hdfkSupport'] == 1;
      }

      // 回填最大可欠款金额
      if (customerInfo['hdfkMaxAmt'] != null && customerInfo['hdfkMaxAmt'].toString().isNotEmpty) {
        maxDebtController.text = customerInfo['hdfkMaxAmt'].toString();
      }

      // 回填B2B在线收款
      if (customerInfo['isPayOnline'] != null) {
        state.B2bOnlinePayment = customerInfo['isPayOnline'] == 1;
      }

      state.isEdit = customerInfo.isNotEmpty ? true : false;
    }

    //
    // state.level_price_required = userInfo['userOrgSettings']['level_price_required'] == '1';

    // requestPermission();
    _mapSearch = MapSearch()..init();
  }

  /// 获取线路
  _getRouteOption() {
    MyDio.get(Apis.consumerRouteOption, successCallBack: (value) {
      if (value is Map) routes = value['data'];
      List.generate(routes.length, (index) {
        dynamic item = routes[index];
        if (customerInfo.isNotEmpty) {
          if (customerInfo['routeCode'] == item['routeNo']) {
            routeIndex = index;
            lineEditCtr.text = item['routeName']??"";
          }
        }
      });
    });
  }

  /// 获取渠道
  _getChannelOption() {
    MyDio.get(Apis.consumerChannelOption, successCallBack: (value) {
      if (value is Map) channels = value['data'];
      List.generate(channels.length, (index) {
        dynamic item = channels[index];
        if (customerInfo.isNotEmpty) {
          if (customerInfo['channelCode'] == item['value']) {
            channelIndex = index;
            channelEditCtr.text = item['label'];
          }
        }
      });
    });
  }

  /// 获取部门
  _getDeptOption() {
    MyDio.get(Apis.consumerDeptOption, successCallBack: (value) {
      if (value is Map) depts = value['data'];
      if (depts.length == 0) return;
      List.generate(depts.length, (index) {
        dynamic item = depts[index];
        if (customerInfo.isNotEmpty) {
          if (customerInfo['deptId'] == item['id']) {
            deptIndex = index;
            deptEditCtr.text = item['name'];
          }
        }
      });
    });
  }

  /// 获取等级价格
  _getPriceLevelOption() {
    MyDio.get(Apis.consumerPriceLevelOption, successCallBack: (value) {
      if (value is Map) priceLevels = value['data'];

      if (priceLevels.length == 0) return;
      List.generate(priceLevels.length, (index) {
        dynamic item = priceLevels[index];
        if (customerInfo.isNotEmpty) {
          if (customerInfo['customerLevelId'] == item['id']) {
            priceLevelIndex = index;
            priceLevelEditCtr.text = item['levelName'];
          }
        }
      });
    });
  }



  // 点击选择省市区
  buildHorizontalCascader(BuildContext context) {
    TDCascader.showMultiCascader(context,
        title: '选择地址',
        // subTitles: ['请选择省份', '请选择城市', '请选择区/县'],
        subTitles: ['请选择省份', '请选择城市', '请选择区/县', '请选择街道'],
        data: state.TDData,
        initialData: state.TDInitData,
        theme: 'tab',
        onChange: (List<MultiCascaderListModel> selectData) async {
          List result = [];
          int len = selectData.length;

          state.TDInitData = selectData[len - 1].value!;
          // selectData.forEach((element) {
          //   result.add(element.label);
          // });

          if (state.TDInitData == '-1') {
            state.TDInitData = selectData[len - 2].value!;
          }

          selectData.asMap().forEach((index, element) {
            if (element.label != '暂不选择') {
              result.add(element.label);

              switch (index) {
                case 0:
                  state.provinceName = element.label!;
                  state.provinceCode = element.value!;
                  break;
                case 1:
                  state.cityName = element.label!;
                  state.cityCode = element.value!;
                  break;
                case 2:
                  state.districtName = element.label!;
                  state.districtCode = element.value!;
                  break;
                case 3:
                  state.streetName = element.label!;
                  state.streetCode = element.value!;
                  break;
                default:

              }
            } else {
              state.streetName = '';
              state.streetCode = '';
            }

          });

          state.TDSelected = result.join('/');
          areaEditCtr.text = state.TDSelected;
        },
        onClose: () {
          Navigator.of(context).pop();
        }
    );
  }

  /// 点击选择线路
  selectRoute(BuildContext context) {
    List datas = routes.map((e) => e['routeName'] ?? '').toList();
    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[routeIndex > 0 ? routeIndex : 0],
        onConfirm: (p, position) {
      routeIndex = position;
      lineEditCtr.text = p;
    });
  }

  /// 点击选择渠道
  selectChannel(BuildContext context) {
    List datas = channels.map((e) => e['label'] ?? '').toList();
    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[channelIndex > 0 ? channelIndex : 0],
        onConfirm: (p, position) {
      channelIndex = position;
      channelEditCtr.text = p;
    });
  }

  /// 点击选择全国分组
  selectGroup(BuildContext context) {
    if (groups.isEmpty) {
      MyCommonUtils.showToast("暂无分组数据");
      return;
    }
    List datas = groups.map((e) => e['groupName'] ?? '').toList();
    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[groupIndex > 0 ? groupIndex : 0],
        onConfirm: (p, position) {
      groupIndex = position;
      groupEditCtr.text = p;
    });
  }

  /// 点击选择价格组
  selectDict(BuildContext context) {
    if (dicts.isEmpty) {
      MyCommonUtils.showToast("暂无价格组数据");
      return;
    }
    List datas = dicts.map((e) => e['dictLabel'] ?? '').toList();
    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[dictIndex > 0 ? dictIndex : 0],
        onConfirm: (p, position) {
      dictIndex = position;
      dictEditCtr.text = p;
    });
  }

  /// 点击选择渠道类型
  selectChannelType(BuildContext context) {
    if (channelTypes.isEmpty) {
      MyCommonUtils.showToast("暂无渠道类型数据");
      return;
    }
    List datas = channelTypes.map((e) => e['channelName'] ?? '').toList();
    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[channelTypeIndex > 0 ? channelTypeIndex : 0],
        onConfirm: (p, position) {
      channelTypeIndex = position;
      channelTypeEditCtr.text = p;
    });
  }

  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode = !isEditMode;
    update(); // 通知UI更新
  }

  // 更新视图
  updateView() {
    update(); // 通知UI更新
  }


  /// 点击选择部门
  selectDept(BuildContext context) {
    List datas = depts.map((e) => e['name'] ?? '').toList();

    if (datas.length == 0) {
      MyCommonUtils.showToast("部门列表为空");
      return;
    }

    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[deptIndex > 0 ? deptIndex : 0],
        onConfirm: (p, position) {
          deptIndex = position;
          deptEditCtr.text = p;
        });
  }

  /// 点击选择等级价格
  selectpriceLevel(BuildContext context) {
    List datas = priceLevels.map((e) => e['levelName'] ?? '').toList();

    if (datas.length == 0) {
      MyCommonUtils.showToast("等级价格列表为空");
      return;
    }

    Pickers.showSinglePicker(context,
        data: datas, selectData: datas[priceLevelIndex > 0 ? priceLevelIndex : 0],
        onConfirm: (p, position) {
          priceLevelIndex = position;
          priceLevelEditCtr.text = p;
        });
  }



  // 输入查询地址
  getMapList(String value) async {
    _onTextChanged(value);
  }

  // 防抖函数，确保用户停止输入后才触发某些操作
  void _onTextChanged(String value) {
    // 取消之前的延迟任务
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    // 设置新的延迟任务，延迟 500 毫秒后执行
    _debounceTimer = Timer(Duration(milliseconds: 1000), () async {
      if (value == null || value.isEmpty) {
        state.pois = [];
      } else {
        state.pois = await _mapSearch.searchWithAddress(value, _location.city ?? "");
      }

      update([StringResource.B2B_STORE_INFO_ADDRESS_LIST]);
    });
  }

  _getPoint() async {
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    await AmapLocation.instance.init(iosKey: mapIOSKey);
    _location = await AmapLocation.instance.fetchLocation(mode: LocationAccuracy.High);
    SmartDialog.dismiss();
    await Future.delayed(const Duration(milliseconds: 200));
    // await _controller?.setCenterCoordinate(_location.latLng!);

  }

  void requestPermission() async {
    SmartDialog.showLoading(msg: "定位加载中...");

    // 延迟 5 秒后  如果还没定位好   取消loading 状态
    Future.delayed(Duration(seconds: 5), () {
      SmartDialog.dismiss();
    });

    // 申请权限
    if (Platform.isAndroid) {
      if (await Permission.location.request().isGranted) {
        if (_controller != null) {
          _getPoint();
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
          _getPoint();
        }
      } else {
        print("请打开定位");
      }
    } else {
      if (await Permission.location.request().isGranted &&
          await Permission.locationAlways.request().isGranted &&
          await Permission.locationWhenInUse.request().isGranted) {
        if (_controller != null) {
          _getPoint();
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
          _getPoint();
        }
      } else {
        print("请打开定位");
      }
    }
  }

  /// 启用地图选点
  openMap(BuildContext context) async {
    // 同意协议
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    // 初始化key
    await AmapService.instance.init(androidKey: mapAndroidKey, iosKey: mapIOSKey);
    Navigator.push(context,
            MaterialPageRoute(builder: (context) => const MapFluttify()))
        .then((value) async {
      // printLong("value=>${value} == ${state.TDSelected}");
      // var reGeocode;
      setMapInView(value);
    });
  }

  setMapInView (Poi value) async {
    var reGeocode;
    if (value is Poi) {
      if (value.latLng != null) {

        latitude = value.latLng?.latitude ?? 0.0;
        longitude = value.latLng?.longitude ?? 0.0;
        reGeocode = await AmapSearch.instance.searchReGeocode(
            LatLng(
              latitude,
              longitude,
            )
        );

      }
      if (value.address != null) {
        pointEditCtr.text = (value.provinceName ?? '') + (value.cityName ?? '') + (value.adName ?? '') + (value.address ?? '');
        // addressEditCtr.text = (value.provinceName ?? '') + (value.cityName ?? '') + (value.adName ?? '') + (value.address ?? '');
        addressEditCtr.text = (value.address ?? '') + (value.title ?? '');


        state.provinceName = value.provinceName ?? '';
        state.cityName = value.cityName ?? '';
        state.districtName = value.adName ?? '';
        state.streetName = reGeocode.township ?? '';

        // 兼容直辖市的省 市 重名情况
        if (state.provinceName == state.cityName) {
          if (state.districtName.contains('区')) {
            state.cityName = '${value.cityName}辖区';
          } else {
            state.cityName = '${value.cityName}县';
          }
        }
        state.TDSelected = (value.provinceName ?? '') + '/' + (state.cityName ?? '') + '/' + (value.adName ?? '') + '/' + (reGeocode.township ?? '');

        state.provinceCode = AREA_RESOURCE[state.provinceName];
        state.cityCode = AREA_RESOURCE[state.cityName];
        // state.districtCode = AREA_RESOURCE[state.districtName];
        state.districtCode = AREA_STREET_RESOURCE['${state.districtName}_${state.cityCode}'] ?? '';;
        // state.streetCode = AREA_RESOURCE[state.streetName];
        state.streetCode =  AREA_STREET_RESOURCE['${state.streetName}_${state.districtCode}'] ?? '';




        if (!reGeocode.township!.isEmpty && state.streetCode != '') {
          // state.TDInitData = AREA_RESOURCE[reGeocode.township];
          // 街道名会重复
          state.TDInitData = state.streetCode;

        } else { // 如果取不到街道的数据  就拿上一级的
          // 重置文本
          state.streetName = '';
          state.TDSelected = (value.provinceName ?? '') + '/' + (value.cityName ?? '') + '/' + (value.adName ?? '');
          state.TDInitData = !value.adName!.isEmpty ? AREA_RESOURCE[value.adName]  : '';
        }
        areaEditCtr.text = state.TDSelected;



      }
    }
  }

  /// 更换图片
  openImagePicker() {
    _picMange.getPic(backImg: (imgStr) {
      imgUrl.value = imgStr;
    });
  }

  String removeLeadingOne(String input) {
    if (input.isNotEmpty && input[0] == '1') {
      // 去掉首位的 1
      return input.substring(1);
    }
    // 返回原字符串
    return input;
  }

  /// 提交拓店信息
  postSaveConsumer() {
    if (infoEditCtr.text.isEmpty) {
      MyCommonUtils.showToast("请输入门店名称");
      return;
    }

    if (nameEditCtr.text.isEmpty) {
      MyCommonUtils.showToast("请输入联系人");
      return;
    }

    // 如果选择同步创建账号且手机号不为空，需要验证密码
    // print('xxx1- ${state.isAddUserAccount} ${customerInfo} ${!isHasInfo}');
    if (state.isAddUserAccount && !isHasInfo && phoneEditCtr.text.isNotEmpty) {
      // 这里可以添加密码验证逻辑，目前默认使用手机号作为密码
      // 如果需要单独的密码输入，可以在这里添加验证  pwdEditCtr
      if (pwdEditCtr.text.isEmpty) {
        MyCommonUtils.showToast("请输入密码");
        return;
      }
    }


    if (areaEditCtr.text.isEmpty) {
      MyCommonUtils.showToast("请选择所在地区");
      return;
    }

    if (addressEditCtr.text.isEmpty) {
      MyCommonUtils.showToast("请输入门店详细地址");
      return;
    }

    if (channelTypeIndex < 0) {
      MyCommonUtils.showToast("请选择渠道");
      return;
    }
    if (groupIndex < 0) {
      MyCommonUtils.showToast("请选择全国分组");
      return;
    }
    if (dictIndex < 0) {
      MyCommonUtils.showToast("请选择价格组");
      return;
    }
    if (state.TDAreaInitData == null) {
      MyCommonUtils.showToast("请选择城市区域");
      return;
    }


    // if (phoneEditCtr.text.isEmpty) {
    //   MyCommonUtils.showToast("请输入手机号");
    //   return;
    // }

    // if (!MyCommonUtils.isValidPhoneNumber(phoneEditCtr.text)) {
    //   MyCommonUtils.showToast("请确认手机号是否有效");
    //   return;
    // }


    // if (state.level_price_required && priceLevelIndex < 0) {
    //   MyCommonUtils.showToast("请选择等级价格");
    //   return;
    // }




    dynamic userInfo = SpUtil.getObject('userInfo');
    String colonelId = '';

    // 获取用户信息中的业务员ID
    if (userInfo['colonelId'] != null) {
      colonelId = userInfo['colonelId'].toString();
    }

    // Map channel = channels[channelIndex];
    // Map route = routes[routeIndex];
    Map group = groupIndex > -1 ? groups[groupIndex] : {};
    Map dict = dictIndex > -1 ? dicts[dictIndex] : {};
    Map channelType = channelTypeIndex > -1 ? channelTypes[channelTypeIndex] : {};

    Map dept = deptIndex > -1 ? depts[deptIndex] : {};
    Map priceLevel = priceLevelIndex > -1 ? priceLevels[priceLevelIndex] : {};

    String _image = imgUrl.value;
    // print('imgUrl.value ${imgUrl.value}');
    if (imgUrl.value.isNotEmpty && !imgUrl.value.contains("http")) {
      _image = '';
      String prefix = '';
      if (!Apis.baseUrl.contains("seller.annto")) {  // 默认是 生产环境的biz04地址 如果不是生产环境  就用biz01的地址
        prefix = "https://annto-cos-biz01-**********.cos.ap-guangzhou.myqcloud.com";
      } else {
        prefix = Apis.imgCdnUrl;
      }
      _image = prefix + imgUrl.value;
    }
    // print('imgUrl.value ${_image}');
    // return;

    Map<String, dynamic> data = {
      // 必填字段
      "colonelId": colonelId, // 业务员Id
      "syncCreateAccount": phoneEditCtr.text.isNotEmpty && state.isAddUserAccount ? 1 : 0, // 是否同步创建账号 0.否 1.是
      "password": state.isAddUserAccount ? pwdEditCtr.text : "", // 当需要同步创建账号时必填，默认使用手机号作为密码
      "branchId": customerInfo['branchId'] ?? '', // 门店编码（编辑时从客户信息获取）
      "branchNo": customerInfo['branchId'] ?? '', // 门店编码（编辑时从客户信息获取）
      "branchName": infoEditCtr.text, // 门店名称
      "contactName": nameEditCtr.text, // 联系人名称
      "contactPhone": phoneEditCtr.text, // 联系人电话
      "status": "1", // 状态，默认为启用
      "areaId": state.TDAreaInitData, // 城市id，优先使用区县编码，否则使用城市编码
      "cityRegionName": areaCityEditCtr.text,
      "latitude": latitude.toString(), // 维度
      "longitude": longitude.toString(), // 经度

      // 保留原有字段以兼容现有逻辑
      // "channelCode": channel['value'] ?? 0,
      "deptId": dept['id'] ?? '',
      "customerLevelId": priceLevel['id'] ?? '',
      "consumerName": infoEditCtr.text, // 客户名称
      "contactMan": nameEditCtr.text, // 联系人
      "branchImages": _image,
      // "branchImages": imgUrl.value,
      "memo": desEditCtr.text, // 备注
      // "routeCode": route['routeNo'] ?? 0,
      "branchAddr": addressEditCtr.text, // 地址
      "telephoneNum": phoneEditCtr.text, //  手机号
      // "orgCode": _orgCode,
      "provinceName": state.provinceName,
      "cityName": state.cityName,
      "districtName": state.districtName,
      "streetName": state.streetName,
      "provinceCode": state.provinceCode,
      "cityCode": state.cityCode,
      "districtCode": state.districtCode,
      "threeAreaCityId": removeLeadingOne(state.districtCode),
      "streetCode": state.streetCode,
      "hdfkMaxAmt": maxDebtController.text,
      "isPayOnline": state.B2bOnlinePayment ? 1 : 0,
      "hdfkSupport": state.CashOnDelivery ? 1 : 0,
      // "isSyncB2b": state.isSyncB2b,
      "groupId": group['groupId'] ?? '', // 全国分组ID
      "channelId": channelType['channelId'] ?? '', // 渠道类型ID
      "salePriceCode": dict['dictValue'] ?? '', // 价格组ID
    };

    // print('datadatadata $data ${state.TDAreaInitData}');

    // return;

    if (customerInfo.isNotEmpty) {
      // 编辑/绑定
      SmartDialog.showLoading(msg: "正在提交...");
      // data['consumerId'] = customerInfo['id'];
      // data['id'] = customerInfo['id'];
      // if (type == "bind") {
      //   data["salesMan"] = salesMan;
      // }
      MyDio.put(
        Apis.b2bConsumerUpdateConsumer,
        queryParameters: data,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 200 || value['code'] == 0 || value['code'] == '200' || value['code'] == '0') {
            state.fillFlag = true;
            value['msg'] != '' ? MyCommonUtils.showToast(value['msg']) : MyCommonUtils.showToast("信息已修改");
            // routBindEventBus.fire(RouteBindEvent());
            // print('xxxxxxx $from');
            // if (from == "customerList" && !state.fillFlag) {
            //   Get.until((route) {
            //     return route.settings.name?.contains(PageName.B2bCustomerListPage) == true;
            //   });
            // } else {
            //   Get.back();
            //   Get.back();
            // }
            toggleEditMode();
          }
        },failCallBack: (value) {
          SmartDialog.dismiss();
        }
      );
    } else {
      // 新增
      SmartDialog.showLoading(msg: "正在提交...");
      MyDio.post(
        Apis.saveMemberBranch,
        queryParameters: data,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 200 || value['code'] == 0 || value['code'] == '200' || value['code'] == '0') {
            value['msg'] != '' ? MyCommonUtils.showToast(value['msg']) : MyCommonUtils.showToast("信息已提交");

            // 获取返回的 branchId
            String branchId = value['data']?.toString() ?? '';

            // 清空路由栈并跳转到客户信息页面，传递 branchId 参数
            Get.offAllNamed(PageName.B2bCustomerInfoPage, parameters: {
              'branchId': branchId,
            });
          }
        },failCallBack: (value) {
          print("value['code'] ${value.code == 1103006001}");

          SmartDialog.dismiss();
        }
      );
    }
  }

  /// 获取城市区域列表
  getAreaList() {
    try {
      SmartDialog.showLoading(msg: "加载中...");

      MyDio.get(
        Apis.getAreaList,
        queryParameters: {},
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 0) {
            // 处理区域列表数据
            print('区域列表数据222: $value');
            if (value['data'] != null && value['data'] is List && (value['data'] as List).isNotEmpty) {

              List<Map<String, dynamic>> originalData = (value['data'] as List)
                  .map((item) => Map<String, dynamic>.from(item as Map))
                  .toList();

              // 使用新的转换方法
              List<Map<String, dynamic>> hierarchicalData = convertToHierarchicalStructure(originalData);

              state.AreaLists = hierarchicalData;
            }
            // 这里可以根据需要处理数据并更新state
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '获取区域列表失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('获取区域列表失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      print('获取区域列表异常: $e');
    }
  }


  /// 将区域数据转换为层级结构
  /// level为1的对象作为父级，包含children数组
  /// level为2的对象作为子级，根据pid和areaId的匹配关系放入对应父级的children数组
  List<Map<String, dynamic>> convertToHierarchicalStructure(List<Map<String, dynamic>> areaData) {
    // 分离level为1和level为2的数据
    List<Map<String, dynamic>> level1Items = [];
    List<Map<String, dynamic>> level2Items = [];

    for (var item in areaData) {
      // 创建副本以避免修改原始数据
      Map<String, dynamic> itemCopy = Map<String, dynamic>.from(item);

      if (itemCopy['level'] == 1) {
        // 为level1项初始化children数组
        itemCopy['children'] = <Map<String, dynamic>>[];
        // 添加value和label字段
        itemCopy['value'] = itemCopy['areaId'];
        itemCopy['label'] = itemCopy['areaName'];
        level1Items.add(itemCopy);
      } else if (itemCopy['level'] == 2) {
        // 为level2项添加value和label字段
        itemCopy['value'] = itemCopy['areaId'];
        itemCopy['label'] = itemCopy['areaName'];
        level2Items.add(itemCopy);
      }
    }

    // 将level2的项目添加到对应的level1项目的children数组中
    for (var level2Item in level2Items) {
      String pid = level2Item['pid'].toString();

      // 查找匹配的level1项目
      for (var level1Item in level1Items) {
        String areaId = level1Item['areaId'].toString();

        if (pid == areaId) {
          // 将level2项目添加到level1项目的children数组中
          (level1Item['children'] as List<Map<String, dynamic>>).add(level2Item);
          break; // 找到匹配项后跳出循环
        }
      }
    }


    // 为每个level1项目的children数组添加"暂不选择"选项
    for (var level1Item in level1Items) {
      // 在children数组的开头插入"暂不选择"选项
      (level1Item['children'] as List<Map<String, dynamic>>).insert(0, {
        "value": "-1",
        "label": "暂不选择",
        "children": []
      });
    }

    return level1Items;
  }


  selectArea(BuildContext context) {
    TDCascader.showMultiCascader(context,
        title: '选择所属区域',
        subTitles: ['请选择', '请选择'],
        data: state.AreaLists as List<Map<dynamic, dynamic>>,
        initialData: state.TDAreaInitData,
        theme: 'tab',
        onChange: (List<MultiCascaderListModel> selectData) async {


          List result = [];
          int len = selectData.length;
          state.TDAreaInitData = selectData[len - 1].value!;
          print('selectData $selectData ${state.TDAreaInitData}');
          // selectData.forEach((element) {
          //   result.add(element.label);
          // });

          if (state.TDAreaInitData == '-1') {
            state.TDAreaInitData = selectData[len - 2].value!;
          }

          selectData.asMap().forEach((index, element) {
            if (element.label != '暂不选择') {
              result.add(element.label);
            }

          });

          state.TDAreaSelected = result.join('/');
          areaCityEditCtr.text = state.TDAreaSelected;
        },
        onClose: () {
          Navigator.of(context).pop();
        }
    );
  }




  /// 获取所在地区列表
  Future<void> getAreaCityList({String? pid}) async {
    try {
      SmartDialog.showLoading(msg: "加载中...");

      Map<String, dynamic> params = {};
      if (pid != null) {
        params['pid'] = pid;
      }

      MyDio.get(
        Apis.getAreaCityList,
        queryParameters: params,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == '0') {
            AreaCityBean areaCityBean = AreaCityBean.fromJson(value);
            print('所在地区列表数据: ${areaCityBean.data?.list?.length}');
            // 这里可以根据需要处理数据并更新state
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '获取地区列表失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('获取地区列表失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      print('获取地区列表异常: $e');
    }
  }

  /// 获取全国分组列表
  getGroupList() {
    try {
      SmartDialog.showLoading(msg: "加载中...");

      MyDio.get(
        Apis.getGroupList,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 0) {
            GroupListBean groupListBean = GroupListBean.fromJson(value);
            if (groupListBean.data?.list != null) {
              // 转换为Map格式，方便选择器使用
              groups = groupListBean.data!.list!.map((item) => {
                'groupId': item.groupId,
                'groupName': item.groupName,
                'sysCode': item.sysCode,
                'memo': item.memo,
              }).toList();

              print('全国分组列表数据: ${groups.length}');

              // 如果有客户信息，设置默认选中的分组
              if (customerInfo.isNotEmpty) {
                for (int index = 0; index < groups.length; index++) {
                  dynamic item = groups[index];
                  if (customerInfo['groupId'].toString() == item['groupId']) {
                    groupIndex = index;
                    groupEditCtr.text = item['groupName'] ?? '';
                    break;
                  }
                }
              }
            }
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '获取分组列表失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('获取分组列表失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      print('获取分组列表异常: $e');
    }
  }


  /// 获取价格组列表
  getDictList() {
    try {
      SmartDialog.showLoading(msg: "加载中...");
      Map<String, dynamic>? queryParameters = {};
      queryParameters["pageNum"] = 1;
      queryParameters["pageSize"] = 50;
      queryParameters["dictType"] = 'sys_price_code';

      MyDio.get(
        Apis.getDictList,
        queryParameters: queryParameters,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 200) {
            PriceGroupBean priceGroupBean = PriceGroupBean.fromJson(value);
            if (priceGroupBean.rows != null) {
              // 转换为Map格式，方便选择器使用
              dicts = priceGroupBean.rows!.map((item) => {
                'dictCode': item.dictCode,
                'dictLabel': item.dictLabel,
                'dictValue': item.dictValue,
                'dictType': item.dictType,
              }).toList();

              print('价格组列表数据: ${dicts.length}');

              // 如果有客户信息，设置默认选中的价格组
              if (customerInfo.isNotEmpty) {
                for (int index = 0; index < dicts.length; index++) {
                  dynamic item = dicts[index];
                  if (customerInfo['salePriceCode'].toString() == item['dictValue']) {
                    dictIndex = index;
                    dictEditCtr.text = item['dictLabel'] ?? '';
                    break;
                  }
                }
              }
            }
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '获取价格组列表失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('获取价格组列表失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      print('获取价格组列表异常: $e');
    }
  }

  /// 获取渠道类型列表
  getChannelList() {
    try {
      SmartDialog.showLoading(msg: "加载中...");

      MyDio.get(
        Apis.getChannelList,
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == 0) {
            ChannelListBean channelListBean = ChannelListBean.fromJson(value);
            if (channelListBean.data != null) {
              // 转换为Map格式，方便选择器使用
              channelTypes = channelListBean.data!.map((item) => {
                'channelId': item.channelId,
                'channelName': item.channelName,
                'sysCode': item.sysCode,
                'memo': item.memo,
                'hdfkSupport': item.hdfkSupport,
                'status': item.status,
              }).toList();

              print('渠道类型列表数据: ${channelTypes.length}');

              // 如果有客户信息，设置默认选中的渠道类型
              if (customerInfo.isNotEmpty) {
                for (int index = 0; index < channelTypes.length; index++) {
                  dynamic item = channelTypes[index];
                  if (customerInfo['channelId'].toString() == item['channelId']) {
                    channelTypeIndex = index;
                    channelTypeEditCtr.text = item['channelName'] ?? '';
                    break;
                  }
                }
              }
            }
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '获取渠道类型列表失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('获取渠道类型列表失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      MyCommonUtils.showToast("获取渠道类型列表异常");
      print('获取渠道类型列表异常: $e');
    }
  }

  /// 保存客户详情信息
  Future<void> editBranchInfo(EditBranchInfoRequest request) async {
    try {
      SmartDialog.showLoading(msg: "保存中...");

      MyDio.post(
        Apis.editBranchInfo,
        queryParameters: request.toJson(),
        successCallBack: (value) {
          SmartDialog.dismiss();
          if (value['code'] == '0') {
            MyCommonUtils.showToast("保存成功");
            Get.back(); // 返回上一页
          } else {
            MyCommonUtils.showToast(value['msg'] ?? '保存失败');
          }
        },
        failCallBack: (error) {
          SmartDialog.dismiss();
          MyCommonUtils.showToast("网络请求失败");
          print('保存客户详情失败: $error');
        },
      );
    } catch (e) {
      SmartDialog.dismiss();
      print('保存客户详情异常: $e');
    }
  }
}
