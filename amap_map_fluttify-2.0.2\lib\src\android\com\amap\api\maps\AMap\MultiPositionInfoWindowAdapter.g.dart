// ignore_for_file: non_constant_identifier_names, camel_case_types, missing_return, unused_import, unused_local_variable, dead_code, unnecessary_cast
//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

import 'dart:typed_data';

import 'package:amap_map_fluttify/src/android/android.export.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'package:foundation_fluttify/foundation_fluttify.dart';
import 'package:core_location_fluttify/core_location_fluttify.dart';
import 'package:amap_core_fluttify/amap_core_fluttify.dart';
import 'package:amap_search_fluttify/amap_search_fluttify.dart';
import 'package:amap_location_fluttify/amap_location_fluttify.dart';

class _com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter_SUB extends java_lang_Object with com_amap_api_maps_AMap_InfoWindowAdapter, com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter {}

mixin com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter on com_amap_api_maps_AMap_InfoWindowAdapter {
  

  static com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter subInstance() => _com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter_SUB();

  

  @override
  final String tag__ = 'amap_map_fluttify';

  

  

  
  Future<android_view_View?> getInfoWindowClick(com_amap_api_maps_model_Marker? var1) async {
    // print log
    if (fluttifyLogEnabled) {
      debugPrint('fluttify-dart: com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter@$refId::getInfoWindowClick([])');
    }
  
    // invoke native method
    final __result__ = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getInfoWindowClick', {"var1": var1, "__this__": this});
  
  
    // handle native call
  
  
    return AmapMapFluttifyAndroidAs<android_view_View>(__result__);
  }
  
  
  Future<android_view_View?> getOverturnInfoWindow(com_amap_api_maps_model_Marker? var1) async {
    // print log
    if (fluttifyLogEnabled) {
      debugPrint('fluttify-dart: com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter@$refId::getOverturnInfoWindow([])');
    }
  
    // invoke native method
    final __result__ = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindow', {"var1": var1, "__this__": this});
  
  
    // handle native call
  
  
    return AmapMapFluttifyAndroidAs<android_view_View>(__result__);
  }
  
  
  Future<android_view_View?> getOverturnInfoWindowClick(com_amap_api_maps_model_Marker? var1) async {
    // print log
    if (fluttifyLogEnabled) {
      debugPrint('fluttify-dart: com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter@$refId::getOverturnInfoWindowClick([])');
    }
  
    // invoke native method
    final __result__ = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindowClick', {"var1": var1, "__this__": this});
  
  
    // handle native call
  
  
    return AmapMapFluttifyAndroidAs<android_view_View>(__result__);
  }
  
}

extension com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter_Batch on List<com_amap_api_maps_AMap_MultiPositionInfoWindowAdapter> {
  //region methods
  
  Future<List<android_view_View?>> getInfoWindowClick_batch(List<com_amap_api_maps_model_Marker?> var1) async {
    assert(true);
  
    // invoke native method
    final resultBatch = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getInfoWindowClick_batch', [for (int __i__ = 0; __i__ < this.length; __i__++) {"var1": var1[__i__], "__this__": this[__i__]}]);
  
  
    return (resultBatch as List).map((__result__) => AmapMapFluttifyAndroidAs<android_view_View>(__result__)).cast<android_view_View?>().toList();
  }
  
  
  Future<List<android_view_View?>> getOverturnInfoWindow_batch(List<com_amap_api_maps_model_Marker?> var1) async {
    assert(true);
  
    // invoke native method
    final resultBatch = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindow_batch', [for (int __i__ = 0; __i__ < this.length; __i__++) {"var1": var1[__i__], "__this__": this[__i__]}]);
  
  
    return (resultBatch as List).map((__result__) => AmapMapFluttifyAndroidAs<android_view_View>(__result__)).cast<android_view_View?>().toList();
  }
  
  
  Future<List<android_view_View?>> getOverturnInfoWindowClick_batch(List<com_amap_api_maps_model_Marker?> var1) async {
    assert(true);
  
    // invoke native method
    final resultBatch = await kAmapMapFluttifyChannel.invokeMethod('com.amap.api.maps.AMap.MultiPositionInfoWindowAdapter::getOverturnInfoWindowClick_batch', [for (int __i__ = 0; __i__ < this.length; __i__++) {"var1": var1[__i__], "__this__": this[__i__]}]);
  
  
    return (resultBatch as List).map((__result__) => AmapMapFluttifyAndroidAs<android_view_View>(__result__)).cast<android_view_View?>().toList();
  }
  
  //endregion
}