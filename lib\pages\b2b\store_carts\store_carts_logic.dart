import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get.dart';
import '../../../api/config.dart';
import '../../../common/apis.dart';
import '../../../common/dio_utils.dart';
import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/b2b_app_cart_page.dart';
import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/chart_page_data.dart';
import '../../../response_data_models/b2b/b2bAppCart/b2b_app_cart_page/supplier_group_list.dart';
import '../../../utils/common_utils.dart';
import '../../../widget/MyDialog.dart';
import '../customer_info/logic.dart';
import 'store_carts_state.dart';

class StoreCartsLogic extends GetxController
    with GetSingleTickerProviderStateMixin {
  final StoreCartsState state = StoreCartsState();
  final infoPageState = Get.find<B2bCustomerInfoPageLogic>().state;

  late TabController tabController;

  @override
  void onInit() {
    super.onInit();

    // 初始化TabController
    tabController = TabController(length: 3, vsync: this);

    // 获取传入的门店ID
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null && arguments['storeId'] != null) {
      state.storeId = arguments['storeId'];
    }

    // 初始化模拟数据
    _initMockData();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  // 切换主Tab
  void switchTab(int index) {
    state.currentTabIndex.value = index;
  }

  // 切换最近搜索的子Tab
  void switchRecentSearchTab(int index) {
    state.recentSearchTabIndex.value = index;
    // 根据选中的时间范围过滤搜索数据
    _filterRecentSearches();
  }

  // 切换配送方式Tab
  void switchDeliveryTab(int index) {
    state.deliveryTabIndex.value = index;
    // 根据配送方式重新加载数据
    _loadCartItems(isRefresh: true);
  }

  // 下拉刷新
  Future<void> onRefresh() async {
    // 根据当前Tab刷新对应数据
    switch (state.currentTabIndex.value) {
      case 0:
        _loadCartItems();
        break;
      case 1:
        _loadFrequentItems();
        break;
      case 2:
        _loadRecentSearches();
        break;
    }
  }

  // 初始化模拟数据
  void _initMockData() {
    _loadCartItems();
    _loadFrequentItems();
    _loadRecentSearches();
  }

  // 加载购物车数据
  Future<void> _loadCartItems({bool isRefresh = true}) async {
    if (isRefresh) {
      state.currentPage.value = 1;
    } else {
      if (!state.hasMore.value) return;
      state.isLoadingMore.value = true;
      state.currentPage++;
    }
    SmartDialog.showLoading(msg: "加载中...");
    // 根据配送方式获取不同的模拟数据
    _getMockDataByDeliveryType((res) {
      state.isLoadingMore.value = false;
      SmartDialog.dismiss();
      if (isRefresh) {
        state.cartData.value = res.data ?? ChartPageData();
      } else {
        // 加载更多时合并数据
        if (state.cartData.value.supplierGroupList != null) {
          final existingData = state.cartData.value;
          List<SupplierGroupList> newSupplierGroups = [
            ...(existingData.supplierGroupList ?? []),
            ...(res.data!.supplierGroupList ?? [])
          ];
          state.cartData.value = ChartPageData(
            total: res.data?.total ?? 0,
            supplierGroupList: newSupplierGroups,
            payPlatform: res.data?.payPlatform,
          );
        } else {
          state.cartData.value = res.data ?? ChartPageData();
        }
      }
      state.total.value = res.data?.total ?? 0;
      state.hasMore.value =
          (state.cartData.value.supplierGroupList ?? []).length <
              state.total.value;
    }, () {
      // 失败回调
      state.isLoadingMore.value = false;
      SmartDialog.dismiss();
      MyCommonUtils.showToast("加载失败，请重试");
    });
  }

  // 根据配送方式获取模拟数据
  void _getMockDataByDeliveryType(
    void Function(B2bAppCartPage data) successCallback,
    void Function() failCallback,
  ) {
    MyDio.post(
      Apis.b2bAppCartPage,
      queryParameters: {
        "pageNo": state.currentPage.value,
        "pageSize": state.pageSize.value,
        "branchId": infoPageState.branchId, // 门店ID
        // "branchId": '467373522202984449', // 门店ID
        "productType": state.deliveryTabIndex.value == 0
            ? "local"
            : "global", // 商品类型 local-本地商品, global-全国商品
        "supplierId": '' // 入驻商ID 如有则传，否则不传
      },
      successCallBack: (value) {
        var data = B2bAppCartPage.fromJson(commonModalFromJson(value));
        successCallback(data);
      },
      failCallBack: (error) {
        failCallback();
      },
    );
  }

  // 处理购物车数据响应
  // void _processCartResponse(ChartPageData mockData, bool isRefresh) {
  //   if (isRefresh) {
  //     state.cartData.value = mockData;
  //   } else {
  //     // 加载更多时合并数据
  //     if (state.cartData.value != null) {
  //       final existingData = state.cartData.value!;
  //       List<SupplierGroupList> newSupplierGroups = [
  //         ...(existingData.supplierGroupList ?? []),
  //         ...(mockData.supplierGroupList ?? [])
  //       ];
  //       state.cartData.value = ChartPageData(
  //         total: mockData.total,
  //         supplierGroupList: newSupplierGroups,
  //         payPlatform: mockData.payPlatform,
  //       );
  //     } else {
  //       state.cartData.value = mockData;
  //     }
  //   }

  //   // 模拟分页逻辑
  //   state.currentPage.value++;
  //   if (state.currentPage.value > 3) {
  //     // 假设最多3页
  //     state.hasMore.value = false;
  //   }
  // }

  // 加载更多数据
  void loadMore() {
    if (state.hasMore.value && !state.isLoadingMore.value) {
      _loadCartItems(isRefresh: false);
    }
  }

  // 加载常购商品数据
  void _loadFrequentItems() {
    state.frequentItems.value = [
      // 这里可以添加常购商品的模拟数据
    ];
  }

  // 加载最近搜索数据
  void _loadRecentSearches() {
    final allSearches = [
      SearchItem(
        id: '1',
        keyword: '可口可乐',
        resultCount: 3,
        searchTime: '2024-05-30 16:20',
      ),
      SearchItem(
        id: '2',
        keyword: '雪花啤酒',
        resultCount: 5,
        searchTime: '2024-06-03 14:00',
      ),
      SearchItem(
        id: '3',
        keyword: '小黄鸭垃圾桶',
        resultCount: 8,
        searchTime: '2024-06-04 09:10',
      ),
      SearchItem(
        id: '4',
        keyword: '蓝月亮洗衣液',
        resultCount: 12,
        searchTime: '2024-06-05 10:23',
      ),
    ];

    state.recentSearches.value = allSearches;
    _filterRecentSearches();
  }

  // 根据时间范围过滤搜索记录
  void _filterRecentSearches() {
    // 这里可以根据选中的时间范围过滤数据
    // 目前显示所有数据
  }
}
