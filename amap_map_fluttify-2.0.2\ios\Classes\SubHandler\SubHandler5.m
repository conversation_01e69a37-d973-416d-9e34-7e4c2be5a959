//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler5.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler5)
- (NSDictionary<NSString*, Handler>*) getSubHandler5 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MACustomCalloutView::set_userData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomCalloutView::set_userData");
            }
        
            // args
            // id arg
            id userData;
            // jsonable
            if ([args[@"userData"] isKindOfClass:[NSNumber class]]
                    || [args[@"userData"] isKindOfClass:[NSString class]]
                    || [args[@"userData"] isKindOfClass:[NSArray class]]
                    || [args[@"userData"] isKindOfClass:[NSDictionary class]]) {
                userData = args[@"userData"];
            }
            // non jsonable
            else {
                userData = args[@"userData"];
            }
        
            // ref
            MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.userData = userData;
            methodResult(@"success");
        },
        
        @"MAOfflineItemCommonCity::set_province": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOfflineItemCommonCity::set_province");
            }
        
            // args
            // ref arg
            MAOfflineItem* province = (MAOfflineItem*) (args[@"province"] == [NSNull null] ? nil : args[@"province"]);
        
            // ref
            MAOfflineItemCommonCity* ref = (MAOfflineItemCommonCity*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.province = province;
            methodResult(@"success");
        },
        
        @"MACoordinateBounds::set_northEast": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateBounds::set_northEast");
            }
        
            // args
            // struct arg
            NSValue* northEastValue = (NSValue*) args[@"northEast"];
            CLLocationCoordinate2D northEast;
            if (northEastValue != nil && (NSNull*) northEastValue != [NSNull null]) {
              [northEastValue getValue:&northEast];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"northEast不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateBounds ref;
            [dataValue getValue:&ref];
        
            ref.northEast = northEast;
            methodResult(@"success");
        },
        
        @"MACoordinateBounds::set_southWest": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateBounds::set_southWest");
            }
        
            // args
            // struct arg
            NSValue* southWestValue = (NSValue*) args[@"southWest"];
            CLLocationCoordinate2D southWest;
            if (southWestValue != nil && (NSNull*) southWestValue != [NSNull null]) {
              [southWestValue getValue:&southWest];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"southWest不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateBounds ref;
            [dataValue getValue:&ref];
        
            ref.southWest = southWest;
            methodResult(@"success");
        },
        
        @"MACoordinateSpan::set_latitudeDelta": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateSpan::set_latitudeDelta");
            }
        
            // args
            // jsonable arg
            CLLocationDegrees latitudeDelta = [args[@"latitudeDelta"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateSpan ref;
            [dataValue getValue:&ref];
        
            ref.latitudeDelta = latitudeDelta;
            methodResult(@"success");
        },
        
        @"MACoordinateSpan::set_longitudeDelta": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateSpan::set_longitudeDelta");
            }
        
            // args
            // jsonable arg
            CLLocationDegrees longitudeDelta = [args[@"longitudeDelta"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateSpan ref;
            [dataValue getValue:&ref];
        
            ref.longitudeDelta = longitudeDelta;
            methodResult(@"success");
        },
        
        @"MACoordinateRegion::set_center": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateRegion::set_center");
            }
        
            // args
            // struct arg
            NSValue* centerValue = (NSValue*) args[@"center"];
            CLLocationCoordinate2D center;
            if (centerValue != nil && (NSNull*) centerValue != [NSNull null]) {
              [centerValue getValue:&center];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"center不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateRegion ref;
            [dataValue getValue:&ref];
        
            ref.center = center;
            methodResult(@"success");
        },
        
        @"MACoordinateRegion::set_span": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACoordinateRegion::set_span");
            }
        
            // args
            // struct arg
            NSValue* spanValue = (NSValue*) args[@"span"];
            MACoordinateSpan span;
            if (spanValue != nil && (NSNull*) spanValue != [NSNull null]) {
              [spanValue getValue:&span];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"span不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MACoordinateRegion ref;
            [dataValue getValue:&ref];
        
            ref.span = span;
            methodResult(@"success");
        },
        
        @"MAMapPoint::set_x": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapPoint::set_x");
            }
        
            // args
            // jsonable arg
            double x = [args[@"x"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapPoint ref;
            [dataValue getValue:&ref];
        
            ref.x = x;
            methodResult(@"success");
        },
        
        @"MAMapPoint::set_y": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapPoint::set_y");
            }
        
            // args
            // jsonable arg
            double y = [args[@"y"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapPoint ref;
            [dataValue getValue:&ref];
        
            ref.y = y;
            methodResult(@"success");
        },
        
        @"MAMapSize::set_width": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapSize::set_width");
            }
        
            // args
            // jsonable arg
            double width = [args[@"width"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapSize ref;
            [dataValue getValue:&ref];
        
            ref.width = width;
            methodResult(@"success");
        },
        
        @"MAMapSize::set_height": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapSize::set_height");
            }
        
            // args
            // jsonable arg
            double height = [args[@"height"] doubleValue];
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapSize ref;
            [dataValue getValue:&ref];
        
            ref.height = height;
            methodResult(@"success");
        },
        
        @"MAMapRect::set_origin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapRect::set_origin");
            }
        
            // args
            // struct arg
            NSValue* originValue = (NSValue*) args[@"origin"];
            MAMapPoint origin;
            if (originValue != nil && (NSNull*) originValue != [NSNull null]) {
              [originValue getValue:&origin];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"origin不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapRect ref;
            [dataValue getValue:&ref];
        
            ref.origin = origin;
            methodResult(@"success");
        },
        
        @"MAMapRect::set_size": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapRect::set_size");
            }
        
            // args
            // struct arg
            NSValue* sizeValue = (NSValue*) args[@"size"];
            MAMapSize size;
            if (sizeValue != nil && (NSNull*) sizeValue != [NSNull null]) {
              [sizeValue getValue:&size];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size不能为null"]);
              return;
            }
        
        
            // ref
            NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
            MAMapRect ref;
            [dataValue getValue:&ref];
        
            ref.size = size;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_visibile": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_visibile");
            }
        
            // args
            // jsonable arg
            BOOL visibile = [args[@"visibile"] boolValue];
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visibile = visibile;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_duration");
            }
        
            // args
            // jsonable arg
            NSTimeInterval duration = [args[@"duration"] doubleValue];
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.duration = duration;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_loop": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_loop");
            }
        
            // args
            // jsonable arg
            BOOL loop = [args[@"loop"] boolValue];
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.loop = loop;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_maxParticles": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_maxParticles");
            }
        
            // args
            // jsonable arg
            NSInteger maxParticles = [args[@"maxParticles"] longValue];
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.maxParticles = maxParticles;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_icon");
            }
        
            // args
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.icon = icon;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_startParticleSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_startParticleSize");
            }
        
            // args
            // struct arg
            NSValue* startParticleSizeValue = (NSValue*) args[@"startParticleSize"];
            CGSize startParticleSize;
            if (startParticleSizeValue != nil && (NSNull*) startParticleSizeValue != [NSNull null]) {
              [startParticleSizeValue getValue:&startParticleSize];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"startParticleSize不能为null"]);
              return;
            }
        
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.startParticleSize = startParticleSize;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleLifeTime": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleLifeTime");
            }
        
            // args
            // jsonable arg
            NSTimeInterval particleLifeTime = [args[@"particleLifeTime"] doubleValue];
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleLifeTime = particleLifeTime;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleStartColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleStartColor");
            }
        
            // args
            // ref arg
            id<MAParticleColorGenerate> particleStartColor = (id<MAParticleColorGenerate>) (args[@"particleStartColor"] == [NSNull null] ? nil : args[@"particleStartColor"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleStartColor = particleStartColor;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleStartSpeed": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleStartSpeed");
            }
        
            // args
            // ref arg
            id<MAParticleVelocityGenerate> particleStartSpeed = (id<MAParticleVelocityGenerate>) (args[@"particleStartSpeed"] == [NSNull null] ? nil : args[@"particleStartSpeed"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleStartSpeed = particleStartSpeed;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleEmissionModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleEmissionModule");
            }
        
            // args
            // ref arg
            MAParticleEmissionModuleOC* particleEmissionModule = (MAParticleEmissionModuleOC*) (args[@"particleEmissionModule"] == [NSNull null] ? nil : args[@"particleEmissionModule"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleEmissionModule = particleEmissionModule;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleShapeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleShapeModule");
            }
        
            // args
            // ref arg
            id<MAParticleShapeModule> particleShapeModule = (id<MAParticleShapeModule>) (args[@"particleShapeModule"] == [NSNull null] ? nil : args[@"particleShapeModule"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleShapeModule = particleShapeModule;
            methodResult(@"success");
        },
        
        @"MAParticleOverlayOptions::set_particleOverLifeModule": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAParticleOverlayOptions::set_particleOverLifeModule");
            }
        
            // args
            // ref arg
            MAParticleOverLifeModuleOC* particleOverLifeModule = (MAParticleOverLifeModuleOC*) (args[@"particleOverLifeModule"] == [NSNull null] ? nil : args[@"particleOverLifeModule"]);
        
            // ref
            MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.particleOverLifeModule = particleOverLifeModule;
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_url": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::set_url");
            }
        
            // args
            // jsonable arg
            NSString* url = (NSString*) args[@"url"];
        
            // ref
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.url = url;
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_key": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::set_key");
            }
        
            // args
            // jsonable arg
            NSString* key = (NSString*) args[@"key"];
        
            // ref
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.key = key;
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_Id": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::set_Id");
            }
        
            // args
            // jsonable arg
            NSString* Id = (NSString*) args[@"Id"];
        
            // ref
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.Id = Id;
            methodResult(@"success");
        },
        
        @"MAMVTTileOverlayOptions::set_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMVTTileOverlayOptions::set_visible");
            }
        
            // args
            // jsonable arg
            BOOL visible = [args[@"visible"] boolValue];
        
            // ref
            MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visible = visible;
            methodResult(@"success");
        },
        
        @"MAOverlayRenderer::set_strokeImage": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::set_strokeImage");
            }
        
            // args
            // ref arg
            UIImage* strokeImage = (UIImage*) (args[@"strokeImage"] == [NSNull null] ? nil : args[@"strokeImage"]);
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.strokeImage = strokeImage;
            methodResult(@"success");
        },
        
        @"MAOverlayRenderer::set_alpha": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayRenderer::set_alpha");
            }
        
            // args
            // jsonable arg
            CGFloat alpha = [args[@"alpha"] floatValue];
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.alpha = alpha;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorNode::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorNode::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorNode::set_weight": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorNode::set_weight");
            }
        
            // args
            // jsonable arg
            float weight = [args[@"weight"] floatValue];
        
            // ref
            MAHeatMapVectorNode* ref = (MAHeatMapVectorNode*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.weight = weight;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_type": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_type");
            }
        
            // args
            // enum arg
            MAHeatMapType type = (MAHeatMapType) [args[@"type"] integerValue];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.type = type;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_visible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_visible");
            }
        
            // args
            // jsonable arg
            BOOL visible = [args[@"visible"] boolValue];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visible = visible;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_inputNodes": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_inputNodes");
            }
        
            // args
            // list arg
            NSArray<MAHeatMapVectorNode*>* inputNodes = (NSArray<MAHeatMapVectorNode*>*) args[@"inputNodes"];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.inputNodes = inputNodes;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_size": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_size");
            }
        
            // args
            // jsonable arg
            CLLocationDistance size = [args[@"size"] doubleValue];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.size = size;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_gap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_gap");
            }
        
            // args
            // jsonable arg
            CGFloat gap = [args[@"gap"] floatValue];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.gap = gap;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_colors": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_colors");
            }
        
            // args
            // list arg
            NSArray<UIColor*>* colors = (NSArray<UIColor*>*) args[@"colors"];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.colors = colors;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_startPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_startPoints");
            }
        
            // args
            // jsonable arg
            NSArray<NSNumber*>* startPoints = (NSArray<NSNumber*>*) args[@"startPoints"];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.startPoints = startPoints;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlayOptions::set_opacity": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlayOptions::set_opacity");
            }
        
            // args
            // jsonable arg
            CGFloat opacity = [args[@"opacity"] floatValue];
        
            // ref
            MAHeatMapVectorOverlayOptions* ref = (MAHeatMapVectorOverlayOptions*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.opacity = opacity;
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorOverlay::set_option": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAHeatMapVectorOverlay::set_option");
            }
        
            // args
            // ref arg
            MAHeatMapVectorOverlayOptions* option = (MAHeatMapVectorOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
            MAHeatMapVectorOverlay* ref = (MAHeatMapVectorOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.option = option;
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_customID": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::set_customID");
            }
        
            // args
            // jsonable arg
            NSString* customID = (NSString*) args[@"customID"];
        
            // ref
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.customID = customID;
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_title": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::set_title");
            }
        
            // args
            // jsonable arg
            NSString* title = (NSString*) args[@"title"];
        
            // ref
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.title = title;
            methodResult(@"success");
        },
        
        @"MAMultiPointItem::set_subtitle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMultiPointItem::set_subtitle");
            }
        
            // args
            // jsonable arg
            NSString* subtitle = (NSString*) args[@"subtitle"];
        
            // ref
            MAMultiPointItem* ref = (MAMultiPointItem*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.subtitle = subtitle;
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_height": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::set_height");
            }
        
            // args
            // jsonable arg
            CGFloat height = [args[@"height"] floatValue];
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.height = height;
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_heightScale": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::set_heightScale");
            }
        
            // args
            // jsonable arg
            CGFloat heightScale = [args[@"heightScale"] floatValue];
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.heightScale = heightScale;
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_topColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::set_topColor");
            }
        
            // args
            // ref arg
            UIColor* topColor = (UIColor*) (args[@"topColor"] == [NSNull null] ? nil : args[@"topColor"]);
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.topColor = topColor;
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_sideColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::set_sideColor");
            }
        
            // args
            // ref arg
            UIColor* sideColor = (UIColor*) (args[@"sideColor"] == [NSNull null] ? nil : args[@"sideColor"]);
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.sideColor = sideColor;
            methodResult(@"success");
        },
        
        @"MACustomBuildingOverlayOption::set_visibile": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayOption::set_visibile");
            }
        
            // args
            // jsonable arg
            BOOL visibile = [args[@"visibile"] boolValue];
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visibile = visibile;
            methodResult(@"success");
        },
        
        @"MATracePoint::set_latitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATracePoint::set_latitude");
            }
        
            // args
            // jsonable arg
            CLLocationDegrees latitude = [args[@"latitude"] doubleValue];
        
            // ref
            MATracePoint* ref = (MATracePoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.latitude = latitude;
            methodResult(@"success");
        },
        
        @"MATracePoint::set_longitude": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATracePoint::set_longitude");
            }
        
            // args
            // jsonable arg
            CLLocationDegrees longitude = [args[@"longitude"] doubleValue];
        
            // ref
            MATracePoint* ref = (MATracePoint*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.longitude = longitude;
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_loc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::set_loc");
            }
        
            // args
            // struct arg
            NSValue* locValue = (NSValue*) args[@"loc"];
            CLLocationCoordinate2D loc;
            if (locValue != nil && (NSNull*) locValue != [NSNull null]) {
              [locValue getValue:&loc];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"loc不能为null"]);
              return;
            }
        
        
            // ref
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.loc = loc;
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_angle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::set_angle");
            }
        
            // args
            // jsonable arg
            double angle = [args[@"angle"] doubleValue];
        
            // ref
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.angle = angle;
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_speed": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::set_speed");
            }
        
            // args
            // jsonable arg
            double speed = [args[@"speed"] doubleValue];
        
            // ref
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.speed = speed;
            methodResult(@"success");
        },
        
        @"MATraceLocation::set_time": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MATraceLocation::set_time");
            }
        
            // args
            // jsonable arg
            double time = [args[@"time"] doubleValue];
        
            // ref
            MATraceLocation* ref = (MATraceLocation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.time = time;
            methodResult(@"success");
        },
        
        @"MAArc::set_startCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::set_startCoordinate");
            }
        
            // args
            // struct arg
            NSValue* startCoordinateValue = (NSValue*) args[@"startCoordinate"];
            CLLocationCoordinate2D startCoordinate;
            if (startCoordinateValue != nil && (NSNull*) startCoordinateValue != [NSNull null]) {
              [startCoordinateValue getValue:&startCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"startCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.startCoordinate = startCoordinate;
            methodResult(@"success");
        },
        
        @"MAArc::set_passedCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::set_passedCoordinate");
            }
        
            // args
            // struct arg
            NSValue* passedCoordinateValue = (NSValue*) args[@"passedCoordinate"];
            CLLocationCoordinate2D passedCoordinate;
            if (passedCoordinateValue != nil && (NSNull*) passedCoordinateValue != [NSNull null]) {
              [passedCoordinateValue getValue:&passedCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"passedCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.passedCoordinate = passedCoordinate;
            methodResult(@"success");
        },
        
        @"MAArc::set_endCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAArc::set_endCoordinate");
            }
        
            // args
            // struct arg
            NSValue* endCoordinateValue = (NSValue*) args[@"endCoordinate"];
            CLLocationCoordinate2D endCoordinate;
            if (endCoordinateValue != nil && (NSNull*) endCoordinateValue != [NSNull null]) {
              [endCoordinateValue getValue:&endCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"endCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAArc* ref = (MAArc*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.endCoordinate = endCoordinate;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_showsAccuracyRing": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_showsAccuracyRing");
            }
        
            // args
            // jsonable arg
            BOOL showsAccuracyRing = [args[@"showsAccuracyRing"] boolValue];
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsAccuracyRing = showsAccuracyRing;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_showsHeadingIndicator": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_showsHeadingIndicator");
            }
        
            // args
            // jsonable arg
            BOOL showsHeadingIndicator = [args[@"showsHeadingIndicator"] boolValue];
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsHeadingIndicator = showsHeadingIndicator;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_fillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_fillColor");
            }
        
            // args
            // ref arg
            UIColor* fillColor = (UIColor*) (args[@"fillColor"] == [NSNull null] ? nil : args[@"fillColor"]);
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.fillColor = fillColor;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_strokeColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_strokeColor");
            }
        
            // args
            // ref arg
            UIColor* strokeColor = (UIColor*) (args[@"strokeColor"] == [NSNull null] ? nil : args[@"strokeColor"]);
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.strokeColor = strokeColor;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_lineWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_lineWidth");
            }
        
            // args
            // jsonable arg
            CGFloat lineWidth = [args[@"lineWidth"] floatValue];
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lineWidth = lineWidth;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_locationDotBgColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_locationDotBgColor");
            }
        
            // args
            // ref arg
            UIColor* locationDotBgColor = (UIColor*) (args[@"locationDotBgColor"] == [NSNull null] ? nil : args[@"locationDotBgColor"]);
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.locationDotBgColor = locationDotBgColor;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_locationDotFillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_locationDotFillColor");
            }
        
            // args
            // ref arg
            UIColor* locationDotFillColor = (UIColor*) (args[@"locationDotFillColor"] == [NSNull null] ? nil : args[@"locationDotFillColor"]);
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.locationDotFillColor = locationDotFillColor;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_enablePulseAnnimation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_enablePulseAnnimation");
            }
        
            // args
            // jsonable arg
            BOOL enablePulseAnnimation = [args[@"enablePulseAnnimation"] boolValue];
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.enablePulseAnnimation = enablePulseAnnimation;
            methodResult(@"success");
        },
        
        @"MAUserLocationRepresentation::set_image": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAUserLocationRepresentation::set_image");
            }
        
            // args
            // ref arg
            UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
            // ref
            MAUserLocationRepresentation* ref = (MAUserLocationRepresentation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.image = image;
            methodResult(@"success");
        },
        
        @"MABaseOverlay::set_coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MABaseOverlay::set_coordinate");
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.coordinate = coordinate;
            methodResult(@"success");
        },
        
        @"MABaseOverlay::set_boundingMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MABaseOverlay::set_boundingMapRect");
            }
        
            // args
            // struct arg
            NSValue* boundingMapRectValue = (NSValue*) args[@"boundingMapRect"];
            MAMapRect boundingMapRect;
            if (boundingMapRectValue != nil && (NSNull*) boundingMapRectValue != [NSNull null]) {
              [boundingMapRectValue getValue:&boundingMapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"boundingMapRect不能为null"]);
              return;
            }
        
        
            // ref
            MABaseOverlay* ref = (MABaseOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.boundingMapRect = boundingMapRect;
            methodResult(@"success");
        },
        
        @"MAMapView::set_delegate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_delegate");
            }
        
            // args
            // ref arg
            id<MAMapViewDelegate> delegate = (id<MAMapViewDelegate>) (args[@"delegate"] == [NSNull null] ? nil : args[@"delegate"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.delegate = delegate;
            methodResult(@"success");
        },
        
        @"MAMapView::set_mapType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_mapType");
            }
        
            // args
            // enum arg
            MAMapType mapType = (MAMapType) [args[@"mapType"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.mapType = mapType;
            methodResult(@"success");
        },
        
        @"MAMapView::set_centerCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_centerCoordinate");
            }
        
            // args
            // struct arg
            NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
            CLLocationCoordinate2D centerCoordinate;
            if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
              [centerCoordinateValue getValue:&centerCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"centerCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.centerCoordinate = centerCoordinate;
            methodResult(@"success");
        },
        
        @"MAMapView::set_region": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_region");
            }
        
            // args
            // struct arg
            NSValue* regionValue = (NSValue*) args[@"region"];
            MACoordinateRegion region;
            if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
              [regionValue getValue:&region];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"region不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.region = region;
            methodResult(@"success");
        },
        
        @"MAMapView::set_visibleMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_visibleMapRect");
            }
        
            // args
            // struct arg
            NSValue* visibleMapRectValue = (NSValue*) args[@"visibleMapRect"];
            MAMapRect visibleMapRect;
            if (visibleMapRectValue != nil && (NSNull*) visibleMapRectValue != [NSNull null]) {
              [visibleMapRectValue getValue:&visibleMapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"visibleMapRect不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.visibleMapRect = visibleMapRect;
            methodResult(@"success");
        },
        
        @"MAMapView::set_limitRegion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_limitRegion");
            }
        
            // args
            // struct arg
            NSValue* limitRegionValue = (NSValue*) args[@"limitRegion"];
            MACoordinateRegion limitRegion;
            if (limitRegionValue != nil && (NSNull*) limitRegionValue != [NSNull null]) {
              [limitRegionValue getValue:&limitRegion];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"limitRegion不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.limitRegion = limitRegion;
            methodResult(@"success");
        },
        
        @"MAMapView::set_limitMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_limitMapRect");
            }
        
            // args
            // struct arg
            NSValue* limitMapRectValue = (NSValue*) args[@"limitMapRect"];
            MAMapRect limitMapRect;
            if (limitMapRectValue != nil && (NSNull*) limitMapRectValue != [NSNull null]) {
              [limitMapRectValue getValue:&limitMapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"limitMapRect不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.limitMapRect = limitMapRect;
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_zoomLevel");
            }
        
            // args
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.zoomLevel = zoomLevel;
            methodResult(@"success");
        },
        
        @"MAMapView::set_minZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_minZoomLevel");
            }
        
            // args
            // jsonable arg
            CGFloat minZoomLevel = [args[@"minZoomLevel"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.minZoomLevel = minZoomLevel;
            methodResult(@"success");
        },
        
        @"MAMapView::set_maxZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_maxZoomLevel");
            }
        
            // args
            // jsonable arg
            CGFloat maxZoomLevel = [args[@"maxZoomLevel"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.maxZoomLevel = maxZoomLevel;
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotationDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_rotationDegree");
            }
        
            // args
            // jsonable arg
            CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.rotationDegree = rotationDegree;
            methodResult(@"success");
        },
        
        @"MAMapView::set_cameraDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_cameraDegree");
            }
        
            // args
            // jsonable arg
            CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.cameraDegree = cameraDegree;
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomingInPivotsAroundAnchorPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_zoomingInPivotsAroundAnchorPoint");
            }
        
            // args
            // jsonable arg
            BOOL zoomingInPivotsAroundAnchorPoint = [args[@"zoomingInPivotsAroundAnchorPoint"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.zoomingInPivotsAroundAnchorPoint = zoomingInPivotsAroundAnchorPoint;
            methodResult(@"success");
        },
        
        @"MAMapView::set_zoomEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_zoomEnabled");
            }
        
            // args
            // jsonable arg
            BOOL zoomEnabled = [args[@"zoomEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.zoomEnabled = zoomEnabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_scrollEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_scrollEnabled");
            }
        
            // args
            // jsonable arg
            BOOL scrollEnabled = [args[@"scrollEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.scrollEnabled = scrollEnabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotateEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_rotateEnabled");
            }
        
            // args
            // jsonable arg
            BOOL rotateEnabled = [args[@"rotateEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.rotateEnabled = rotateEnabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_rotateCameraEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_rotateCameraEnabled");
            }
        
            // args
            // jsonable arg
            BOOL rotateCameraEnabled = [args[@"rotateCameraEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.rotateCameraEnabled = rotateCameraEnabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsBuildings": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsBuildings");
            }
        
            // args
            // jsonable arg
            BOOL showsBuildings = [args[@"showsBuildings"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsBuildings = showsBuildings;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsLabels": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsLabels");
            }
        
            // args
            // jsonable arg
            BOOL showsLabels = [args[@"showsLabels"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsLabels = showsLabels;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showTraffic": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showTraffic");
            }
        
            // args
            // jsonable arg
            BOOL showTraffic = [args[@"showTraffic"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showTraffic = showTraffic;
            methodResult(@"success");
        },
        
        @"MAMapView::set_touchPOIEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_touchPOIEnabled");
            }
        
            // args
            // jsonable arg
            BOOL touchPOIEnabled = [args[@"touchPOIEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.touchPOIEnabled = touchPOIEnabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsCompass": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsCompass");
            }
        
            // args
            // jsonable arg
            BOOL showsCompass = [args[@"showsCompass"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsCompass = showsCompass;
            methodResult(@"success");
        },
        
        @"MAMapView::set_compassOrigin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_compassOrigin");
            }
        
            // args
            // struct arg
            NSValue* compassOriginValue = (NSValue*) args[@"compassOrigin"];
            CGPoint compassOrigin;
            if (compassOriginValue != nil && (NSNull*) compassOriginValue != [NSNull null]) {
              [compassOriginValue getValue:&compassOrigin];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"compassOrigin不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.compassOrigin = compassOrigin;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsScale": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsScale");
            }
        
            // args
            // jsonable arg
            BOOL showsScale = [args[@"showsScale"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsScale = showsScale;
            methodResult(@"success");
        },
        
        @"MAMapView::set_scaleOrigin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_scaleOrigin");
            }
        
            // args
            // struct arg
            NSValue* scaleOriginValue = (NSValue*) args[@"scaleOrigin"];
            CGPoint scaleOrigin;
            if (scaleOriginValue != nil && (NSNull*) scaleOriginValue != [NSNull null]) {
              [scaleOriginValue getValue:&scaleOrigin];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"scaleOrigin不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.scaleOrigin = scaleOrigin;
            methodResult(@"success");
        },
        
        @"MAMapView::set_logoCenter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_logoCenter");
            }
        
            // args
            // struct arg
            NSValue* logoCenterValue = (NSValue*) args[@"logoCenter"];
            CGPoint logoCenter;
            if (logoCenterValue != nil && (NSNull*) logoCenterValue != [NSNull null]) {
              [logoCenterValue getValue:&logoCenter];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"logoCenter不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.logoCenter = logoCenter;
            methodResult(@"success");
        },
        
        @"MAMapView::set_maxRenderFrame": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_maxRenderFrame");
            }
        
            // args
            // jsonable arg
            NSUInteger maxRenderFrame = [args[@"maxRenderFrame"] unsignedIntegerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.maxRenderFrame = maxRenderFrame;
            methodResult(@"success");
        },
        
        @"MAMapView::set_isAllowDecreaseFrame": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_isAllowDecreaseFrame");
            }
        
            // args
            // jsonable arg
            BOOL isAllowDecreaseFrame = [args[@"isAllowDecreaseFrame"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.isAllowDecreaseFrame = isAllowDecreaseFrame;
            methodResult(@"success");
        },
        
        @"MAMapView::set_openGLESDisabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_openGLESDisabled");
            }
        
            // args
            // jsonable arg
            BOOL openGLESDisabled = [args[@"openGLESDisabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.openGLESDisabled = openGLESDisabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_renderringDisabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_renderringDisabled");
            }
        
            // args
            // jsonable arg
            BOOL renderringDisabled = [args[@"renderringDisabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.renderringDisabled = renderringDisabled;
            methodResult(@"success");
        },
        
        @"MAMapView::set_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_screenAnchor");
            }
        
            // args
            // struct arg
            NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
            CGPoint screenAnchor;
            if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
              [screenAnchorValue getValue:&screenAnchor];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"screenAnchor不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.screenAnchor = screenAnchor;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsWorldMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsWorldMap");
            }
        
            // args
            // jsonable arg
            NSNumber* showsWorldMap = (NSNumber*) args[@"showsWorldMap"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsWorldMap = showsWorldMap;
            methodResult(@"success");
        },
        
        @"MAMapView::set_mapLanguage": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_mapLanguage");
            }
        
            // args
            // jsonable arg
            NSNumber* mapLanguage = (NSNumber*) args[@"mapLanguage"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.mapLanguage = mapLanguage;
            methodResult(@"success");
        },
        
        @"MAMapView::set_selectedAnnotations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_selectedAnnotations");
            }
        
            // args
            // list arg
            NSArray<NSObject*>* selectedAnnotations = (NSArray<NSObject*>*) args[@"selectedAnnotations"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.selectedAnnotations = selectedAnnotations;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsUserLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsUserLocation");
            }
        
            // args
            // jsonable arg
            BOOL showsUserLocation = [args[@"showsUserLocation"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsUserLocation = showsUserLocation;
            methodResult(@"success");
        },
        
        @"MAMapView::set_customizeUserLocationAccuracyCircleRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_customizeUserLocationAccuracyCircleRepresentation");
            }
        
            // args
            // jsonable arg
            BOOL customizeUserLocationAccuracyCircleRepresentation = [args[@"customizeUserLocationAccuracyCircleRepresentation"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.customizeUserLocationAccuracyCircleRepresentation = customizeUserLocationAccuracyCircleRepresentation;
            methodResult(@"success");
        },
        
        @"MAMapView::set_userTrackingMode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_userTrackingMode");
            }
        
            // args
            // enum arg
            MAUserTrackingMode userTrackingMode = (MAUserTrackingMode) [args[@"userTrackingMode"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.userTrackingMode = userTrackingMode;
            methodResult(@"success");
        },
        
        @"MAMapView::set_distanceFilter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_distanceFilter");
            }
        
            // args
            // jsonable arg
            CLLocationDistance distanceFilter = [args[@"distanceFilter"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.distanceFilter = distanceFilter;
            methodResult(@"success");
        },
        
        @"MAMapView::set_desiredAccuracy": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_desiredAccuracy");
            }
        
            // args
            // jsonable arg
            CLLocationAccuracy desiredAccuracy = [args[@"desiredAccuracy"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.desiredAccuracy = desiredAccuracy;
            methodResult(@"success");
        },
        
        @"MAMapView::set_headingFilter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_headingFilter");
            }
        
            // args
            // jsonable arg
            CLLocationDegrees headingFilter = [args[@"headingFilter"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.headingFilter = headingFilter;
            methodResult(@"success");
        },
        
        @"MAMapView::set_pausesLocationUpdatesAutomatically": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_pausesLocationUpdatesAutomatically");
            }
        
            // args
            // jsonable arg
            BOOL pausesLocationUpdatesAutomatically = [args[@"pausesLocationUpdatesAutomatically"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.pausesLocationUpdatesAutomatically = pausesLocationUpdatesAutomatically;
            methodResult(@"success");
        },
        
        @"MAMapView::set_allowsBackgroundLocationUpdates": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_allowsBackgroundLocationUpdates");
            }
        
            // args
            // jsonable arg
            BOOL allowsBackgroundLocationUpdates = [args[@"allowsBackgroundLocationUpdates"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.allowsBackgroundLocationUpdates = allowsBackgroundLocationUpdates;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsIndoorMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsIndoorMap");
            }
        
            // args
            // jsonable arg
            BOOL showsIndoorMap = [args[@"showsIndoorMap"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsIndoorMap = showsIndoorMap;
            methodResult(@"success");
        },
        
        @"MAMapView::set_showsIndoorMapControl": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_showsIndoorMapControl");
            }
        
            // args
            // jsonable arg
            BOOL showsIndoorMapControl = [args[@"showsIndoorMapControl"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.showsIndoorMapControl = showsIndoorMapControl;
            methodResult(@"success");
        },
        
        @"MAMapView::set_customMapStyleEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::set_customMapStyleEnabled");
            }
        
            // args
            // jsonable arg
            BOOL customMapStyleEnabled = [args[@"customMapStyleEnabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.customMapStyleEnabled = customMapStyleEnabled;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_fillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_fillColor");
            }
        
            // args
            // ref arg
            UIColor* fillColor = (UIColor*) (args[@"fillColor"] == [NSNull null] ? nil : args[@"fillColor"]);
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.fillColor = fillColor;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_strokeColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_strokeColor");
            }
        
            // args
            // ref arg
            UIColor* strokeColor = (UIColor*) (args[@"strokeColor"] == [NSNull null] ? nil : args[@"strokeColor"]);
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.strokeColor = strokeColor;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_lineWidth");
            }
        
            // args
            // jsonable arg
            CGFloat lineWidth = [args[@"lineWidth"] floatValue];
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lineWidth = lineWidth;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineJoinType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_lineJoinType");
            }
        
            // args
            // enum arg
            MALineJoinType lineJoinType = (MALineJoinType) [args[@"lineJoinType"] integerValue];
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lineJoinType = lineJoinType;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineCapType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_lineCapType");
            }
        
            // args
            // enum arg
            MALineCapType lineCapType = (MALineCapType) [args[@"lineCapType"] integerValue];
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lineCapType = lineCapType;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_miterLimit": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_miterLimit");
            }
        
            // args
            // jsonable arg
            CGFloat miterLimit = [args[@"miterLimit"] floatValue];
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.miterLimit = miterLimit;
            methodResult(@"success");
        },
        
        @"MAOverlayPathRenderer::set_lineDashType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::set_lineDashType");
            }
        
            // args
            // enum arg
            MALineDashType lineDashType = (MALineDashType) [args[@"lineDashType"] integerValue];
        
            // ref
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            ref.lineDashType = lineDashType;
            methodResult(@"success");
        },
        
        @"MAGroundOverlay::set_alpha_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat alpha = [args[@"alpha"] floatValue];
        
                // ref
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.alpha = alpha;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPinAnnotationView::set_pinColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAPinAnnotationColor pinColor = (MAPinAnnotationColor) [args[@"pinColor"] integerValue];
        
                // ref
                MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.pinColor = pinColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPinAnnotationView::set_animatesDrop_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL animatesDrop = [args[@"animatesDrop"] boolValue];
        
                // ref
                MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.animatesDrop = animatesDrop;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapNode::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapNode::set_intensity_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float intensity = [args[@"intensity"] floatValue];
        
                // ref
                MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.intensity = intensity;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_data_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<MAHeatMapNode*>* data = (NSArray<MAHeatMapNode*>*) args[@"data"];
        
                // ref
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.data = data;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_radius_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger radius = [args[@"radius"] longValue];
        
                // ref
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.radius = radius;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_opacity_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat opacity = [args[@"opacity"] floatValue];
        
                // ref
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.opacity = opacity;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_gradient_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapGradient* gradient = (MAHeatMapGradient*) (args[@"gradient"] == [NSNull null] ? nil : args[@"gradient"]);
        
                // ref
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.gradient = gradient;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapTileOverlay::set_allowRetinaAdapting_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL allowRetinaAdapting = [args[@"allowRetinaAdapting"] boolValue];
        
                // ref
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.allowRetinaAdapting = allowRetinaAdapting;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_centerCoordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* centerCoordinateValue = (NSValue*) args[@"centerCoordinate"];
                CLLocationCoordinate2D centerCoordinate;
                if (centerCoordinateValue != nil && (NSNull*) centerCoordinateValue != [NSNull null]) {
                  [centerCoordinateValue getValue:&centerCoordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"centerCoordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.centerCoordinate = centerCoordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_zoomLevel_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.zoomLevel = zoomLevel;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_rotationDegree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.rotationDegree = rotationDegree;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_cameraDegree_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.cameraDegree = cameraDegree;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapStatus::set_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
                CGPoint screenAnchor;
                if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
                  [screenAnchorValue getValue:&screenAnchor];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"screenAnchor不能为null"]);
                  return;
                }
        
        
                // ref
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.screenAnchor = screenAnchor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_lockedToScreen_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL lockedToScreen = [args[@"lockedToScreen"] boolValue];
        
                // ref
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lockedToScreen = lockedToScreen;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPointAnnotation::set_lockedScreenPoint_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* lockedScreenPointValue = (NSValue*) args[@"lockedScreenPoint"];
                CGPoint lockedScreenPoint;
                if (lockedScreenPointValue != nil && (NSNull*) lockedScreenPointValue != [NSNull null]) {
                  [lockedScreenPointValue getValue:&lockedScreenPoint];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"lockedScreenPoint不能为null"]);
                  return;
                }
        
        
                // ref
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.lockedScreenPoint = lockedScreenPoint;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACircle::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MACircle* ref = (MACircle*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MACircle::set_radius_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDistance radius = [args[@"radius"] doubleValue];
        
                // ref
                MACircle* ref = (MACircle*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.radius = radius;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotation::set_title_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* title = (NSString*) args[@"title"];
        
                // ref
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.title = title;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotation::set_subtitle_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* subtitle = (NSString*) args[@"subtitle"];
        
                // ref
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.subtitle = subtitle;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleData_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                NSData* styleData = (NSData*) (args[@"styleData"] == [NSNull null] ? nil : args[@"styleData"]);
        
                // ref
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.styleData = styleData;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleDataOverseaPath_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* styleDataOverseaPath = (NSString*) args[@"styleDataOverseaPath"];
        
                // ref
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.styleDataOverseaPath = styleDataOverseaPath;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleId_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* styleId = (NSString*) args[@"styleId"];
        
                // ref
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.styleId = styleId;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleTextureData_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                NSData* styleTextureData = (NSData*) (args[@"styleTextureData"] == [NSNull null] ? nil : args[@"styleTextureData"]);
        
                // ref
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.styleTextureData = styleTextureData;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMapCustomStyleOptions::set_styleExtraData_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                NSData* styleExtraData = (NSData*) (args[@"styleExtraData"] == [NSNull null] ? nil : args[@"styleExtraData"]);
        
                // ref
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.styleExtraData = styleExtraData;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiColoredPolylineRenderer::set_strokeColors_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<UIColor*>* strokeColors = (NSArray<UIColor*>*) args[@"strokeColors"];
        
                // ref
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.strokeColors = strokeColors;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiColoredPolylineRenderer::set_gradient_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL gradient = [args[@"gradient"] boolValue];
        
                // ref
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.gradient = gradient;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnimatedAnnotation::set_movingDirection_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CLLocationDirection movingDirection = [args[@"movingDirection"] doubleValue];
        
                // ref
                MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.movingDirection = movingDirection;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiTexturePolylineRenderer::set_strokeTextureImages_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<UIImage*>* strokeTextureImages = (NSArray<UIImage*>*) args[@"strokeTextureImages"];
        
                // ref
                MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.strokeTextureImages = strokeTextureImages;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridNode::set_coordinate_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
        
                // ref
                MAHeatMapVectorGridNode* ref = (MAHeatMapVectorGridNode*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.coordinate = coordinate;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGrid::set_inputNodes_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<MAHeatMapVectorGridNode*>* inputNodes = (NSArray<MAHeatMapVectorGridNode*>*) args[@"inputNodes"];
        
                // ref
                MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.inputNodes = inputNodes;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGrid::set_color_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* color = (UIColor*) (args[@"color"] == [NSNull null] ? nil : args[@"color"]);
        
                // ref
                MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.color = color;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_type_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAHeatMapType type = (MAHeatMapType) [args[@"type"] integerValue];
        
                // ref
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.type = type;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_visible_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL visible = [args[@"visible"] boolValue];
        
                // ref
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.visible = visible;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_inputGrids_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // list arg
                NSArray<MAHeatMapVectorGrid*>* inputGrids = (NSArray<MAHeatMapVectorGrid*>*) args[@"inputGrids"];
        
                // ref
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.inputGrids = inputGrids;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_minZoom_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat minZoom = [args[@"minZoom"] floatValue];
        
                // ref
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.minZoom = minZoom;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlayOptions::set_maxZoom_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat maxZoom = [args[@"maxZoom"] floatValue];
        
                // ref
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.maxZoom = maxZoom;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAHeatMapVectorGridOverlay::set_option_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAHeatMapVectorGridOverlayOptions* option = (MAHeatMapVectorGridOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
                // ref
                MAHeatMapVectorGridOverlay* ref = (MAHeatMapVectorGridOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.option = option;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPathShowRange::set_begin_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float begin = [args[@"begin"] floatValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAPathShowRange ref;
                [dataValue getValue:&ref];
        
                ref.begin = begin;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPathShowRange::set_end_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                float end = [args[@"end"] floatValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAPathShowRange ref;
                [dataValue getValue:&ref];
        
                ref.end = end;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPolyline::set_drawStyleIndexes_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSArray<NSNumber*>* drawStyleIndexes = (NSArray<NSNumber*>*) args[@"drawStyleIndexes"];
        
                // ref
                MAMultiPolyline* ref = (MAMultiPolyline*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.drawStyleIndexes = drawStyleIndexes;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.icon = icon;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_pointSize_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* pointSizeValue = (NSValue*) args[@"pointSize"];
                CGSize pointSize;
                if (pointSizeValue != nil && (NSNull*) pointSizeValue != [NSNull null]) {
                  [pointSizeValue getValue:&pointSize];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"pointSize不能为null"]);
                  return;
                }
        
        
                // ref
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.pointSize = pointSize;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAMultiPointOverlayRenderer::set_anchor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* anchorValue = (NSValue*) args[@"anchor"];
                CGPoint anchor;
                if (anchorValue != nil && (NSNull*) anchorValue != [NSNull null]) {
                  [anchorValue getValue:&anchor];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"anchor不能为null"]);
                  return;
                }
        
        
                // ref
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.anchor = anchor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_is3DArrowLine_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL is3DArrowLine = [args[@"is3DArrowLine"] boolValue];
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.is3DArrowLine = is3DArrowLine;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_sideColor_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIColor* sideColor = (UIColor*) (args[@"sideColor"] == [NSNull null] ? nil : args[@"sideColor"]);
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.sideColor = sideColor;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_userInteractionEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL userInteractionEnabled = [args[@"userInteractionEnabled"] boolValue];
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.userInteractionEnabled = userInteractionEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_hitTestInset_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                CGFloat hitTestInset = [args[@"hitTestInset"] floatValue];
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.hitTestInset = hitTestInset;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_showRangeEnabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL showRangeEnabled = [args[@"showRangeEnabled"] boolValue];
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showRangeEnabled = showRangeEnabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAPolylineRenderer::set_showRange_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* showRangeValue = (NSValue*) args[@"showRange"];
                MAPathShowRange showRange;
                if (showRangeValue != nil && (NSNull*) showRangeValue != [NSNull null]) {
                  [showRangeValue getValue:&showRange];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"showRange不能为null"]);
                  return;
                }
        
        
                // ref
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.showRange = showRange;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAShape::set_title_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* title = (NSString*) args[@"title"];
        
                // ref
                MAShape* ref = (MAShape*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.title = title;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAShape::set_subtitle_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSString* subtitle = (NSString*) args[@"subtitle"];
        
                // ref
                MAShape* ref = (MAShape*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.subtitle = subtitle;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_zIndex_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger zIndex = [args[@"zIndex"] longValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.zIndex = zIndex;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_annotation_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.annotation = annotation;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_image_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.image = image;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_customCalloutView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MACustomCalloutView* customCalloutView = (MACustomCalloutView*) (args[@"customCalloutView"] == [NSNull null] ? nil : args[@"customCalloutView"]);
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.customCalloutView = customCalloutView;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_centerOffset_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* centerOffsetValue = (NSValue*) args[@"centerOffset"];
                CGPoint centerOffset;
                if (centerOffsetValue != nil && (NSNull*) centerOffsetValue != [NSNull null]) {
                  [centerOffsetValue getValue:&centerOffset];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"centerOffset不能为null"]);
                  return;
                }
        
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.centerOffset = centerOffset;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_calloutOffset_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* calloutOffsetValue = (NSValue*) args[@"calloutOffset"];
                CGPoint calloutOffset;
                if (calloutOffsetValue != nil && (NSNull*) calloutOffsetValue != [NSNull null]) {
                  [calloutOffsetValue getValue:&calloutOffset];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"calloutOffset不能为null"]);
                  return;
                }
        
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.calloutOffset = calloutOffset;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_enabled_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL enabled = [args[@"enabled"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.enabled = enabled;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_highlighted_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL highlighted = [args[@"highlighted"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.highlighted = highlighted;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_selected_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL selected = [args[@"selected"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.selected = selected;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_canShowCallout_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL canShowCallout = [args[@"canShowCallout"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.canShowCallout = canShowCallout;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_leftCalloutAccessoryView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIView* leftCalloutAccessoryView = (UIView*) (args[@"leftCalloutAccessoryView"] == [NSNull null] ? nil : args[@"leftCalloutAccessoryView"]);
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.leftCalloutAccessoryView = leftCalloutAccessoryView;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_rightCalloutAccessoryView_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                UIView* rightCalloutAccessoryView = (UIView*) (args[@"rightCalloutAccessoryView"] == [NSNull null] ? nil : args[@"rightCalloutAccessoryView"]);
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.rightCalloutAccessoryView = rightCalloutAccessoryView;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_draggable_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL draggable = [args[@"draggable"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.draggable = draggable;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_dragState_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // enum arg
                MAAnnotationViewDragState dragState = (MAAnnotationViewDragState) [args[@"dragState"] integerValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.dragState = dragState;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MAAnnotationView::set_canAdjustPositon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL canAdjustPositon = [args[@"canAdjustPositon"] boolValue];
        
                // ref
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.canAdjustPositon = canAdjustPositon;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_tileSize_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* tileSizeValue = (NSValue*) args[@"tileSize"];
                CGSize tileSize;
                if (tileSizeValue != nil && (NSNull*) tileSizeValue != [NSNull null]) {
                  [tileSizeValue getValue:&tileSize];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"tileSize不能为null"]);
                  return;
                }
        
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.tileSize = tileSize;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_minimumZ_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger minimumZ = [args[@"minimumZ"] longValue];
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.minimumZ = minimumZ;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_maximumZ_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger maximumZ = [args[@"maximumZ"] longValue];
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.maximumZ = maximumZ;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_canReplaceMapContent_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL canReplaceMapContent = [args[@"canReplaceMapContent"] boolValue];
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.canReplaceMapContent = canReplaceMapContent;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlay::set_disableOffScreenTileLoading_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                BOOL disableOffScreenTileLoading = [args[@"disableOffScreenTileLoading"] boolValue];
        
                // ref
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                    return;
                }
        
                ref.disableOffScreenTileLoading = disableOffScreenTileLoading;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_x_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger x = [args[@"x"] longValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                [dataValue getValue:&ref];
        
                ref.x = x;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
        @"MATileOverlayPath::set_y_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // jsonable arg
                NSInteger y = [args[@"y"] longValue];
        
                // ref
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                [dataValue getValue:&ref];
        
                ref.y = y;;
                methodResult(@"success");
            }
        
            methodResult(@"success");
        },
        
    };
}

@end
