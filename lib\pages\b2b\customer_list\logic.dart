import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:fuduoduo/domain/branch_list_bean.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:flutter_pickers/time_picker/model/pduration.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flustars/flustars.dart';
import '../../../base/base_logic.dart';
import '../../homePages/map_help/mapFluttify.dart';
import 'state.dart';
import 'package:intl/intl.dart';
import 'dart:io';

class B2bCustomerListPageLogic extends BaseLogic {
  final B2bCustomerListPageState state = B2bCustomerListPageState();

  AmapController? controller;
  Location? _location; // 用户定位信息

  @override
  void onReady() {
    super.onReady();
    getRouteOption();
  }

  @override
  void onInit() {
    super.onInit();
  }

  String getDateStr() {
    PDuration produceDate = state.produceDate;
    int? years = produceDate.year;
    int? months = produceDate.month;
    int? days = produceDate.day;
    int? hours = produceDate.hour;
    int? minutes = produceDate.minute;
    int? seconds = produceDate.second;
    DateTime dateTime =
        DateTime(years!, months!, days!, hours!, minutes!, seconds!);
    // 格式化日期
    String formattedDate = DateFormat('yyyy-MM-dd').format(dateTime);
    return formattedDate;
  }

  void changeProduceDate(PDuration produceDate) {
    state.produceDate = produceDate;
    update();
  }

  // 请求定位权限
  void requestPermission() async {
    // 申请权限
    if (Platform.isAndroid) {
      if (await Permission.location.request().isGranted) {
        if (controller != null) {
          setLocationManager();
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
          setLocationManager();
        }
      } else {
        MyCommonUtils.showToast("请打开定位");
      }
    } else {
      if (await Permission.location.request().isGranted ||
          await Permission.locationAlways.request().isGranted ||
          await Permission.locationWhenInUse.request().isGranted) {
        if (controller != null) {
          setLocationManager();
        } else {
          setLocationManager();
        }
      } else {
        MyCommonUtils.showToast("请打开定位");
      }
    }
  }

  // 对定位类的设置
  void setLocationManager() async {
    // 同意协议
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    // 初始化key
    await AmapService.instance.init(androidKey: mapAndroidKey, iosKey: mapIOSKey);
    // await Future.delayed(const Duration(milliseconds: 200));
    SmartDialog.showLoading(msg: "请求定位信息中...");
    _location = await AmapLocation.instance.fetchLocation(
        mode: LocationAccuracy.High, timeout: Duration(seconds: 2));
    print("location=>${_location}");
    if (_location != null) {
      SmartDialog.dismiss();
      // getUnbindRouteCustomerList();
    }
  }

  // 获取未绑定的线路客户列表
  void getUnbindRouteCustomerList() {
    String visitTime = getDateStr();
    Map<String, dynamic>? queryParameters = {};
    queryParameters['routeNo'] = "";
    queryParameters['visitTime'] = visitTime;
    SmartDialog.showLoading(msg: "加载中");
    MyDio.get(Apis.getUnbindRouteCustomerList, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        SmartDialog.dismiss();
        List list = response.data ?? [];
        printLong("unbindCustomerList=>${list}");
        if (list.length > 0) {
          state.unbindCustomerListExitFlag = true;
        } else {
          state.unbindCustomerListExitFlag = false;
        }
        getRouteOption();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
    });
  }

  // 获取线路列表
  void getRouteOption() {

    // b2b
    List list = [];
    Map unbindMap = {
      "id": "108",
      "params": {},
      "createBy": "124",
      "createTime": "2023-12-01 15:51:30",
      "updateBy": "124",
      "updateTime": "2024-02-27 20:55:01",
      "delFlag": "0",
      "routeNo": "b2b",
      "routeName": "客户筛选",
      "driverMan": "0001",
      "branchNo": "001",
      "routeState": "0"
    };
    list.insert(0, unbindMap);
    // 线路列表
    state.routeList = list;
    state.selectedRouteId = 'b2b';
    getB2bCustomerList();
    update();

    return;
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.routeOption, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        Map unbindMap = {
          "id": "108",
          "params": {},
          "createBy": "124",
          "createTime": "2023-12-01 15:51:30",
          "updateBy": "124",
          "updateTime": "2024-02-27 20:55:01",
          "delFlag": "0",
          "routeNo": "",
          "routeName": "未绑定线路",
          "driverMan": "0001",
          "branchNo": "001",
          "routeState": "0"
        };
        list.insert(0, unbindMap);
        // 线路列表
        state.routeList = list;
        if (list.length >= 2) {
          if (state.unbindCustomerListExitFlag) {
            String _id = list[0]['routeNo'];
            state.selectedRouteId = _id.toString();
          } else {
            String _id = list[1]['routeNo'];
            state.selectedRouteId = _id.toString();
          }
        } else {
          String _id = list[0]['routeNo'];
          state.selectedRouteId = _id.toString();
        }
        getCustomerList();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  // 获取拜访的线路客户列表
  void getCustomerList() {
    print('selectedRouteId   ${state.selectedRouteId}');
    final typeItem = state.typeList[state.selectedTypeIndex];
    final paths = state.selectedRouteId.isEmpty
        ? Apis.getUnbindRouteCustomerList
        : typeItem['api'];
    String visitTime = getDateStr();
    Map<String, dynamic>? queryParameters = {};
    queryParameters['routeNo'] = state.selectedRouteId;
    queryParameters['visitTime'] = visitTime;
    // 附近客户需要传坐标
    if (typeItem['type'] == '2' && _location != null) {
      queryParameters['currentLongitude'] =
          _location?.latLng?.longitude.toString();
      queryParameters['currentLatitude'] =
          _location?.latLng?.latitude.toString();
    }
    SmartDialog.showLoading(msg: "加载中");
    MyDio.get(paths, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        printLong("customerList=>${list}");
        SmartDialog.dismiss();
        state.customerList = list;
        print("customerListLength=>${state.customerList.length}");
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    }, failCallBack: (value) {
      SmartDialog.dismiss();
    });
  }
  // 获取拜访的线路客户列表

  /// 获取门店列表数据
  /// [isRefresh] 是否是刷新操作，true=刷新，false=加载更多
  void getBranchList({bool isRefresh = true}) {
    if (state.isLoading) return;

    if (isRefresh) {
      state.resetPagination();
      state.isRefreshing = true;
    } else {
      if (!state.hasMore) return;
      state.currentPage++;
    }

    state.isLoading = true;

    // 构建请求参数
    final typeItem = state.typeList[state.selectedTypeIndex];
    final request = BranchListRequest(
      pageNo: state.currentPage,
      pageSize: state.pageSize,
      isOpenSeas: 0, // 私有客户
      distance: typeItem['distance'],
    );

    // 如果有搜索关键词
    if (state.searchKeyword.isNotEmpty) {
      request.contactPhone = state.searchKeyword;
      request.branchName = state.searchKeyword;
    }

    // 如果需要位置信息（附近门店）
    if ((typeItem['type'] == '1' || typeItem['type'] == '2') && _location != null) {
      request.longitude = _location?.latLng?.longitude;
      request.latitude = _location?.latLng?.latitude;
    }

    // 获取用户信息中的业务员ID
    dynamic userInfo = SpUtil.getObject('userInfo');
    if (userInfo['colonelId'] != null) {
      request.colonelId = int.tryParse(userInfo['colonelId'].toString());
    }

    SmartDialog.showLoading(msg: "加载中...");

    // 调用API
    MyDio.get(
      Apis.b2bAppLists,
      queryParameters: request.toJson(),
      successCallBack: (value) {
        state.isLoading = false;
        state.isRefreshing = false;
        SmartDialog.dismiss();

        try {
          final response = BranchListBean.fromJson(value);
          if (response.code == 200 || response.code == 0) {
            final branchData = response.data;
            if (branchData != null && branchData.list != null) {
              if (isRefresh) {
                state.branchList = branchData.list!;
              } else {
                state.branchList.addAll(branchData.list!);
              }

              state.updatePagination(branchData.total ?? 0, branchData.list!.length);

              // 同时更新旧的customerList以保持兼容性
              state.customerList = state.branchList.map((branch) => {
                'branchId': branch.branchId,
                'branchNo': branch.branchNo,
                'branchName': branch.branchName,
                'branchAddr': branch.branchAddr,
                'longitude': branch.longitude,
                'latitude': branch.latitude,
                'contactPhone': branch.contactPhone,
                'lastVisitDay': branch.lastVisitDays,
                'lastActivityDay': branch.lastOrderDays,
                'lastVisitDays': branch.lastVisitDays,
                'lastOrderDays': branch.lastOrderDays,
                'branchImages': branch.branchImages,
                'imgUrls': branch.branchImages, // 保持向后兼容
              }).toList();

              printLong("门店列表获取成功: ${state.branchList.length}条数据");
            } else {
              if (isRefresh) {
                state.branchList.clear();
                state.customerList.clear();
              }
            }
          } else {
            MyCommonUtils.showToast(response.msg ?? "获取门店列表失败");
          }
        } catch (e) {
          print("解析门店列表数据失败: $e");
          MyCommonUtils.showToast("数据解析失败");
        }

        update();
      },
      failCallBack: (error) {
        state.isLoading = false;
        state.isRefreshing = false;
        SmartDialog.dismiss();
        MyCommonUtils.showToast("网络请求失败");
        update();
      },
    );
  }

  /// 兼容旧方法名
  void getB2bCustomerList() {
    getBranchList(isRefresh: true);
  }

  mapAddMarker(latitude, longitude) async {
    double lat = double.parse(latitude);
    double long = double.parse(longitude);
    await controller?.clear();
    var marker = MarkerOption(
        coordinate: LatLng(lat, long),
        visible: true,
        infoWindowEnabled: true,
        widget: const Icon(Icons.location_on, size: 24, color: Colors.red));
    controller?.addMarker(marker);
  }

  void setSelectedRouteId(BuildContext context, String _id) {
    state.selectedRouteId = _id;
    print("selectedRouteId=>${state.selectedRouteId}");
    state.selectedTypeIndex = 0;
    getB2bCustomerList();
    update();
  }

  void setSelectedTypeIndex(BuildContext context, int _index) {
    state.selectedTypeIndex = _index;
    getBranchList(isRefresh: true);
    update();
  }

  /// 搜索门店
  void searchBranches(String keyword) {
    state.searchKeyword = keyword;
    getBranchList(isRefresh: true);
  }

  /// 下拉刷新
  Future<void> onRefresh() async {
    getBranchList(isRefresh: true);
  }

  /// 上拉加载更多
  void loadMore() {
    if (state.hasMore && !state.isLoading) {
      getBranchList(isRefresh: false);
    }
  }

  /// 清空搜索
  void clearSearch() {
    state.searchKeyword = "";
    getBranchList(isRefresh: true);
  }

  // 获取客户信息
  void getConsumerByNo(context, showSignIn, consumerNo) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = consumerNo;
    MyDio.get(Apis.consumerByNo, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        dynamic item = response.data ?? {};
        state.consumerInfo = item;
        showSignIn(context);
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }
}
