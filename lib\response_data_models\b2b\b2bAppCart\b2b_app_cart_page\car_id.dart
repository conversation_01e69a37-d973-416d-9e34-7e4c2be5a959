import 'package:collection/collection.dart';

class CarId {
  String? type;
  String? branchId;
  String? supplierId;
  String? supplierItemId;
  String? areaItemId;
  String? skuId;
  String? spuId;
  int? unitSize;
  String? unit;
  String? id;

  CarId({
    this.type,
    this.branchId,
    this.supplierId,
    this.supplierItemId,
    this.areaItemId,
    this.skuId,
    this.spuId,
    this.unitSize,
    this.unit,
    this.id,
  });

  factory CarId.fromJson(Map<String, dynamic> json) => CarId(
        type: json['type'] as String?,
        branchId: json['branchId'] as String?,
        supplierId: json['supplierId'] as String?,
        supplierItemId: json['supplierItemId'] as String?,
        areaItemId: json['areaItemId'] as String?,
        skuId: json['skuId'] as String?,
        spuId: json['spuId'] as String?,
        unitSize: json['unitSize'] as int?,
        unit: json['unit'] as String?,
        id: json['id'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'type': type,
        'branchId': branchId,
        'supplierId': supplierId,
        'supplierItemId': supplierItemId,
        'areaItemId': areaItemId,
        'skuId': skuId,
        'spuId': spuId,
        'unitSize': unitSize,
        'unit': unit,
        'id': id,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! CarId) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      type.hashCode ^
      branchId.hashCode ^
      supplierId.hashCode ^
      supplierItemId.hashCode ^
      areaItemId.hashCode ^
      skuId.hashCode ^
      spuId.hashCode ^
      unitSize.hashCode ^
      unit.hashCode ^
      id.hashCode;
}
