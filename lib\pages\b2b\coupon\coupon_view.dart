import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'coupon_logic.dart';
import 'coupon_state.dart';
import '../../../resource/color_resource.dart';

/// 优惠券页面视图
class CouponPage extends StatelessWidget {
  const CouponPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.put(CouponLogic());
    final state = logic.state;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text(
          '优惠券',
          style: TextStyle(
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
                colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight),
          ),
        ),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
        bottom: TabBar(
          controller: logic.tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 2,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.normal,
          ),
          tabs: state.getTabTitles().map((title) => Tab(text: title)).toList(),
          onTap: (value) => logic.switchTab(value),
        ),
      ),
      body: TabBarView(
        controller: logic.tabController,
        children: [
          _buildCouponList(logic, 'available'),
          _buildCouponList(logic, 'unused'),
          _buildCouponList(logic, 'used'),
        ],
      ),
    );
  }

  /// 构建优惠券列表
  Widget _buildCouponList(CouponLogic logic, String status) {
    return Obx(() {
      List<CouponModel> coupons;
      switch (status) {
        case 'available':
          coupons = logic.state.availableCoupons;
          break;
        case 'unused':
          coupons = logic.state.unusedCoupons;
          break;
        case 'used':
          coupons = logic.state.usedCoupons;
          break;
        default:
          coupons = [];
      }

      if (coupons.isEmpty) {
        return _buildEmptyState(status);
      }

      return RefreshIndicator(
        onRefresh: () => logic.refreshData(status),
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          itemCount: coupons.length,
          itemBuilder: (context, index) {
            final coupon = coupons[index];
            return _buildCouponCard(logic, coupon);
          },
        ),
      );
    });
  }

  /// 构建空状态
  Widget _buildEmptyState(String status) {
    String message;
    switch (status) {
      case 'available':
        message = '暂无可领取的优惠券';
        break;
      case 'unused':
        message = '暂无未使用的优惠券';
        break;
      case 'used':
        message = '暂无已使用的优惠券';
        break;
      default:
        message = '暂无数据';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.card_giftcard,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建优惠券卡片
  Widget _buildCouponCard(CouponLogic logic, CouponModel coupon) {
    final isUsed = coupon.state == '2';
    final cardColor = logic.getCouponColor(coupon.state);
    final couponTemplateId = coupon.couponTemplateId;

    return Obx(() {
      final isExpanded = logic.isRuleExpanded(couponTemplateId);

      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 主要优惠券信息
            Container(
              height: 130,
              child: Row(
                children: [
                  // 左侧优惠券信息
                  Container(
                    width: 120,
                    height: 130,
                    decoration: BoxDecoration(
                      color: isUsed ? Colors.grey[400] : cardColor,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(8),
                        bottomLeft:
                            isExpanded ? Radius.zero : const Radius.circular(8),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          coupon.displayAmount,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          coupon.conditionText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  // 右侧详细信息
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            coupon.couponName,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isUsed ? Colors.grey[600] : Colors.black87,
                            ),
                          ),
                          // 顶部标题和按钮
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Text(
                                  coupon.scopeText,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: isUsed
                                        ? Colors.grey[500]
                                        : const Color(0xFF4A90E2),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              _buildActionButton(logic, coupon),
                            ],
                          ),
                          // 底部信息
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                logic.isExpiringThisWeek(
                                        coupon.expirationDateEnd)
                                    ? '${logic.getExpirationText(coupon.expirationDateEnd)}到期'
                                    : '有效期至: ${coupon.expirationDateEnd}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: logic.isExpiringThisWeek(
                                          coupon.expirationDateEnd)
                                      ? Color(0xffFF7A1A)
                                      : isUsed
                                          ? Colors.grey[500]
                                          : Colors.grey[600],
                                ),
                              ),
                              // 显示使用时间（仅已使用状态）
                              if (coupon.state == '2') ...[
                                const SizedBox(height: 2),
                                Text(
                                  '使用时间: ${DateTime.now().toString().substring(0, 10)}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 使用规则折叠区域
            _buildRulesSection(logic, coupon, isExpanded),
          ],
        ),
      );
    });
  }

  /// 构建使用规则区域
  Widget _buildRulesSection(
      CouponLogic logic, CouponModel coupon, bool isExpanded) {
    final isUsed = coupon.state == '2';
    final couponTemplateId = coupon.couponTemplateId;

    return Column(
      children: [
        // 分割线
        Container(
          height: 1,
          color: Colors.grey[200],
          margin: const EdgeInsets.symmetric(horizontal: 16),
        ),
        // 使用规则标题栏
        InkWell(
          onTap: () => logic.toggleRuleExpansion(couponTemplateId),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '使用规则',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isUsed ? Colors.grey[600] : Colors.black87,
                  ),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: isUsed ? Colors.grey[500] : Colors.grey[600],
                  size: 20,
                ),
              ],
            ),
          ),
        ),
        // 展开的规则内容
        if (isExpanded) ...[
          Container(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 优惠类型
                _buildRuleItem(
                    '优惠类型:', coupon.discountType == 0 ? '满减券' : '折扣券', isUsed),
                const SizedBox(height: 8),
                // 优惠券编码
                _buildRuleItem('优惠券编码:', coupon.couponTemplateId, isUsed),
                const SizedBox(height: 8),
                // 使用范围
                _buildRuleItem('使用范围:', coupon.scopeText, isUsed),
                const SizedBox(height: 8),
                // 有效期
                _buildRuleItem(
                    '有效期:',
                    '${coupon.expirationDateStart} 至 ${coupon.expirationDateEnd}',
                    isUsed),
                // const SizedBox(height: 8),
                // 券编号
                // _buildRuleItem('券编号:', coupon.couponId.toString(), isUsed),
                const SizedBox(height: 8),
                // 平台编码
                _buildRuleItem('平台编码:', coupon.sysCode.toString(), isUsed),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建规则项
  Widget _buildRuleItem(String label, String content, bool isUsed) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isUsed ? Colors.grey[500] : Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            content,
            style: TextStyle(
              fontSize: 12,
              color: isUsed ? Colors.grey[500] : Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(CouponLogic logic, CouponModel coupon) {
    final buttonText = logic.getButtonText(coupon.tabsType);
    final buttonColor = logic.getButtonColor(coupon.tabsType);
    final isEnabled = logic.isButtonEnabled(coupon.tabsType);
    return GestureDetector(
      onTap: isEnabled
          ? () {
              if (coupon.tabsType == 'available') {
                // 可领取
                logic.claimCoupon(coupon.couponTemplateId);
              }
            }
          : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
        decoration: BoxDecoration(
          color: isEnabled ? buttonColor : Colors.grey[400],
          borderRadius: BorderRadius.circular(40.w),
          border:
              isEnabled ? null : Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Text(
          buttonText,
          style: TextStyle(
            color: isEnabled ? Colors.white : Colors.grey[600],
            fontSize: 26.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
