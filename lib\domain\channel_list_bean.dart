class ChannelListBean {
  int? code;
  List<ChannelItem>? data;
  String? msg;

  ChannelListBean({this.code, this.data, this.msg});

  ChannelListBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    if (json['data'] != null) {
      data = <ChannelItem>[];
      json['data'].forEach((v) {
        data!.add(ChannelItem.fromJson(v));
      });
    }
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['msg'] = msg;
    return data;
  }
}

class ChannelItem {
  String? channelId;
  String? sysCode;
  String? channelName;
  String? memo;
  int? hdfkSupport;
  int? deleted;
  int? status;

  ChannelItem({
    this.channelId,
    this.sysCode,
    this.channelName,
    this.memo,
    this.hdfkSupport,
    this.deleted,
    this.status,
  });

  ChannelItem.fromJson(Map<String, dynamic> json) {
    channelId = json['channelId'];
    sysCode = json['sysCode'];
    channelName = json['channelName'];
    memo = json['memo'];
    hdfkSupport = json['hdfkSupport'];
    deleted = json['deleted'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['channelId'] = channelId;
    data['sysCode'] = sysCode;
    data['channelName'] = channelName;
    data['memo'] = memo;
    data['hdfkSupport'] = hdfkSupport;
    data['deleted'] = deleted;
    data['status'] = status;
    return data;
  }
}
