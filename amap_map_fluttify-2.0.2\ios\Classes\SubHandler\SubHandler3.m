//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler3.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler3)
- (NSDictionary<NSString*, Handler>*) getSubHandler3 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAMapView::get_renderringDisabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_renderringDisabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.renderringDisabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_screenAnchor");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = ref.screenAnchor;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowsWorldMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowsWorldMap");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSNumber* result = ref.showsWorldMap;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_mapLanguage": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_mapLanguage");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSNumber* result = ref.mapLanguage;
        
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_annotations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_annotations");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = ref.annotations;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_selectedAnnotations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_selectedAnnotations");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = ref.selectedAnnotations;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_annotationVisibleRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_annotationVisibleRect");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGRect result = ref.annotationVisibleRect;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGRect)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_showsUserLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_showsUserLocation");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsUserLocation;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_userLocation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_userLocation");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAUserLocation* result = ref.userLocation;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_customizeUserLocationAccuracyCircleRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_customizeUserLocationAccuracyCircleRepresentation");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.customizeUserLocationAccuracyCircleRepresentation;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_userLocationAccuracyCircle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_userLocationAccuracyCircle");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACircle* result = ref.userLocationAccuracyCircle;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_userTrackingMode": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_userTrackingMode");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAUserTrackingMode result = ref.userTrackingMode;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isUserLocationVisible": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isUserLocationVisible");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.userLocationVisible;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_distanceFilter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_distanceFilter");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDistance result = ref.distanceFilter;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_desiredAccuracy": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_desiredAccuracy");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationAccuracy result = ref.desiredAccuracy;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_headingFilter": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_headingFilter");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDegrees result = ref.headingFilter;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_pausesLocationUpdatesAutomatically": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_pausesLocationUpdatesAutomatically");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.pausesLocationUpdatesAutomatically;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_allowsBackgroundLocationUpdates": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_allowsBackgroundLocationUpdates");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.allowsBackgroundLocationUpdates;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_overlays": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_overlays");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = ref.overlays;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowsIndoorMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowsIndoorMap");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsIndoorMap;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_isShowsIndoorMapControl": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_isShowsIndoorMapControl");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.showsIndoorMapControl;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_indoorMapControlSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_indoorMapControlSize");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGSize result = ref.indoorMapControlSize;
        
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
            methodResult(__result__);
        },
        
        @"MAMapView::get_customMapStyleEnabled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAMapView::get_customMapStyleEnabled");
            }
        
            // ref object
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = ref.customMapStyleEnabled;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_fillColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_fillColor");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.fillColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_strokeColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_strokeColor");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIColor* result = ref.strokeColor;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_lineWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_lineWidth");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.lineWidth;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_lineJoinType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_lineJoinType");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MALineJoinType result = ref.lineJoinType;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_lineCapType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_lineCapType");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MALineCapType result = ref.lineCapType;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_miterLimit": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_miterLimit");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = ref.miterLimit;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAOverlayPathRenderer::get_lineDashType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAOverlayPathRenderer::get_lineDashType");
            }
        
            // ref object
            MAOverlayPathRenderer* ref = (MAOverlayPathRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MALineDashType result = ref.lineDashType;
        
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        
        @"MAGroundOverlayRenderer::get_groundOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MAGroundOverlayRenderer::get_groundOverlay");
            }
        
            // ref object
            MAGroundOverlayRenderer* ref = (MAGroundOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAGroundOverlay* result = ref.groundOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MACustomBuildingOverlayRenderer::get_customBuildingOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            // print log
            if (enableLog) {
                NSLog(@"MACustomBuildingOverlayRenderer::get_customBuildingOverlay");
            }
        
            // ref object
            MACustomBuildingOverlayRenderer* ref = (MACustomBuildingOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACustomBuildingOverlay* result = ref.customBuildingOverlay;
        
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        
        @"MAOfflineCity::get_cityCode_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineCity* ref = (MAOfflineCity*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.cityCode;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPoint::get_points_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMapPoint* result = ref.points;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint*)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPoint::get_pointCount_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSUInteger result = ref.pointCount;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPoint::get_cross180Longitude_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPoint* ref = (MAMultiPoint*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.cross180Longitude;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlay::get_icon_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.icon;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlay::get_alpha_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.alpha;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlay::get_zoomLevel_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.zoomLevel;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAGroundOverlay::get_bounds_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACoordinateBounds result = ref.bounds;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateBounds)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolygonRenderer::get_polygon_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolygonRenderer* ref = (MAPolygonRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAPolygon* result = ref.polygon;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPinAnnotationView::get_pinColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAPinAnnotationColor result = ref.pinColor;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPinAnnotationView::get_animatesDrop_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPinAnnotationView* ref = (MAPinAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.animatesDrop;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapNode::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapNode::get_intensity_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapNode* ref = (MAHeatMapNode*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                float result = ref.intensity;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapGradient::get_colors_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<UIColor*>* result = ref.colors;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapGradient::get_startPoints_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<NSNumber*>* result = ref.startPoints;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapTileOverlay::get_data_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAHeatMapNode*>* result = ref.data;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapTileOverlay::get_radius_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSInteger result = ref.radius;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapTileOverlay::get_opacity_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.opacity;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapTileOverlay::get_gradient_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapGradient* result = ref.gradient;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapTileOverlay::get_allowRetinaAdapting_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapTileOverlay* ref = (MAHeatMapTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.allowRetinaAdapting;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapStatus::get_centerCoordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.centerCoordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapStatus::get_zoomLevel_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.zoomLevel;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapStatus::get_rotationDegree_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.rotationDegree;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapStatus::get_cameraDegree_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.cameraDegree;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapStatus::get_screenAnchor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.screenAnchor;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPointAnnotation::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPointAnnotation::get_isLockedToScreen_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.lockedToScreen;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPointAnnotation::get_lockedScreenPoint_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPointAnnotation* ref = (MAPointAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.lockedScreenPoint;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACircle::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACircle* ref = (MACircle*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACircle::get_radius_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACircle* ref = (MACircle*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDistance result = ref.radius;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAArcRenderer::get_arc_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAArcRenderer* ref = (MAArcRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAArc* result = ref.arc;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotation::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotation::get_title_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.title;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotation::get_subtitle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.subtitle;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMapViewController::get_offlineMap_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMapViewController* ref = (MAOfflineMapViewController*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAOfflineMap* result = ref.offlineMap;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapCustomStyleOptions::get_styleData_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSData* result = ref.styleData;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapCustomStyleOptions::get_styleDataOverseaPath_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.styleDataOverseaPath;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapCustomStyleOptions::get_styleId_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.styleId;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapCustomStyleOptions::get_styleTextureData_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSData* result = ref.styleTextureData;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapCustomStyleOptions::get_styleExtraData_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMapCustomStyleOptions* ref = (MAMapCustomStyleOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSData* result = ref.styleExtraData;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlay::get_overlayOption_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlay* ref = (MAParticleOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAParticleOverlayOptions* result = ref.overlayOption;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiColoredPolylineRenderer::get_multiPolyline_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMultiPolyline* result = ref.multiPolyline;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiColoredPolylineRenderer::get_strokeColors_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<UIColor*>* result = ref.strokeColors;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiColoredPolylineRenderer::get_isGradient_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.gradient;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnimatedAnnotation::get_movingDirection_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationDirection result = ref.movingDirection;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiTexturePolylineRenderer::get_multiPolyline_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMultiPolyline* result = ref.multiPolyline;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiTexturePolylineRenderer::get_strokeTextureImages_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<UIImage*>* result = ref.strokeTextureImages;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridNode::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridNode* ref = (MAHeatMapVectorGridNode*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGrid::get_inputNodes_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAHeatMapVectorGridNode*>* result = ref.inputNodes;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGrid::get_color_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGrid* ref = (MAHeatMapVectorGrid*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.color;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_type_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapType result = ref.type;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_visible_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.visible;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_inputGrids_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAHeatMapVectorGrid*>* result = ref.inputGrids;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_minZoom_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.minZoom;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayOptions::get_maxZoom_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayOptions* ref = (MAHeatMapVectorGridOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.maxZoom;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlay::get_option_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlay* ref = (MAHeatMapVectorGridOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapVectorGridOverlayOptions* result = ref.option;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineProvince::get_cities_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineProvince* ref = (MAOfflineProvince*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray* result = ref.cities;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorOverlayRender::get_heatOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapVectorOverlay* result = ref.heatOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayRenderer::get_tileOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MATileOverlay* result = ref.tileOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_name_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.name;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_jianpin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.jianpin;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_pinyin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.pinyin;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_adcode_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.adcode;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_size_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                long long result = ref.size;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_itemStatus_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAOfflineItemStatus result = ref.itemStatus;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItem::get_downloadedSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItem* ref = (MAOfflineItem*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                long long result = ref.downloadedSize;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATouchPoi::get_name_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.name;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATouchPoi::get_coordinate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CLLocationCoordinate2D result = ref.coordinate;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATouchPoi::get_uid_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATouchPoi* ref = (MATouchPoi*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.uid;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPathShowRange::get_begin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAPathShowRange ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                float result = ref.begin;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPathShowRange::get_end_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAPathShowRange ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                float result = ref.end;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAHeatMapVectorGridOverlayRenderer::get_heatOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAHeatMapVectorGridOverlayRenderer* ref = (MAHeatMapVectorGridOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAHeatMapVectorGridOverlay* result = ref.heatOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPolyline::get_drawStyleIndexes_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPolyline* ref = (MAMultiPolyline*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<NSNumber*>* result = ref.drawStyleIndexes;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointOverlayRenderer::get_icon_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.icon;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointOverlayRenderer::get_pointSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.pointSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointOverlayRenderer::get_anchor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.anchor;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMultiPointOverlayRenderer::get_multiPointOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMultiPointOverlay* result = ref.multiPointOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorFloorInfo::get_floorName_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.floorName;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorFloorInfo::get_floorIndex_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                int result = ref.floorIndex;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorFloorInfo::get_floorNona_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.floorNona;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorFloorInfo::get_isPark_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorFloorInfo* ref = (MAIndoorFloorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.isPark;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_cnName_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.cnName;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_enName_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.enName;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_poiID_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.poiID;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_buildingType_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.buildingType;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_activeFloorIndex_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                int result = ref.activeFloorIndex;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_activeFloorInfoIndex_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                int result = ref.activeFloorInfoIndex;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_floorInfo_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray* result = ref.floorInfo;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_numberOfFloor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                int result = ref.numberOfFloor;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAIndoorInfo::get_numberOfParkFloor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAIndoorInfo* ref = (MAIndoorInfo*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                int result = ref.numberOfParkFloor;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_polyline_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAPolyline* result = ref.polyline;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_is3DArrowLine_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.is3DArrowLine;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_sideColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIColor* result = ref.sideColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_userInteractionEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.userInteractionEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_hitTestInset_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGFloat result = ref.hitTestInset;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_showRangeEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.showRangeEnabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAPolylineRenderer::get_showRange_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAPathShowRange result = ref.showRange;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAPathShowRange)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAShape::get_title_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAShape* ref = (MAShape*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.title;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAShape::get_subtitle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAShape* ref = (MAShape*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.subtitle;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_reuseIdentifier_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.reuseIdentifier;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_zIndex_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSInteger result = ref.zIndex;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_annotation_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                id<MAAnnotation> result = ref.annotation;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_image_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.image;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_imageView_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImageView* result = ref.imageView;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_customCalloutView_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACustomCalloutView* result = ref.customCalloutView;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_centerOffset_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.centerOffset;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_calloutOffset_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGPoint result = ref.calloutOffset;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_isEnabled_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.enabled;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_isHighlighted_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.highlighted;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_isSelected_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.selected;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_canShowCallout_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.canShowCallout;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_leftCalloutAccessoryView_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIView* result = ref.leftCalloutAccessoryView;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_rightCalloutAccessoryView_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIView* result = ref.rightCalloutAccessoryView;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_isDraggable_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.draggable;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_dragState_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAAnnotationViewDragState result = ref.dragState;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAAnnotationView::get_canAdjustPositon_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.canAdjustPositon;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_tileSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.tileSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_minimumZ_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSInteger result = ref.minimumZ;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_maximumZ_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSInteger result = ref.maximumZ;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_URLTemplate_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.URLTemplate;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_canReplaceMapContent_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.canReplaceMapContent;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlay::get_disableOffScreenTileLoading_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.disableOffScreenTileLoading;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayPath::get_x_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                NSInteger result = ref.x;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayPath::get_y_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                NSInteger result = ref.y;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayPath::get_contentScaleFactor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CGFloat result = ref.contentScaleFactor;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayPath::get_index_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                NSInteger result = ref.index;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MATileOverlayPath::get_requestId_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MATileOverlayPath ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                NSInteger result = ref.requestId;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomCalloutView::get_customView_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIView* result = ref.customView;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACustomCalloutView::get_userData_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSObject* result = ref.userData;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineItemCommonCity::get_province_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineItemCommonCity* ref = (MAOfflineItemCommonCity*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAOfflineItem* result = ref.province;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMap::get_provinces_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAOfflineProvince*>* result = ref.provinces;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMap::get_municipalities_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAOfflineItemMunicipality*>* result = ref.municipalities;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMap::get_nationWide_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAOfflineItemNationWide* result = ref.nationWide;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMap::get_cities_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSArray<MAOfflineCity*>* result = ref.cities;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOfflineMap::get_version_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.version;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACircleRenderer::get_circle_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MACircleRenderer* ref = (MACircleRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MACircle* result = ref.circle;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayRenderer::get_particleOverlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayRenderer* ref = (MAParticleOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAParticleOverlay* result = ref.particleOverlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateBounds::get_northEast_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateBounds ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CLLocationCoordinate2D result = ref.northEast;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateBounds::get_southWest_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateBounds ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CLLocationCoordinate2D result = ref.southWest;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateSpan::get_latitudeDelta_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateSpan ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CLLocationDegrees result = ref.latitudeDelta;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateSpan::get_longitudeDelta_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateSpan ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CLLocationDegrees result = ref.longitudeDelta;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateRegion::get_center_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateRegion ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                CLLocationCoordinate2D result = ref.center;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MACoordinateRegion::get_span_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MACoordinateRegion ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                MACoordinateSpan result = ref.span;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateSpan)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapPoint::get_x_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapPoint ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                double result = ref.x;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapPoint::get_y_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapPoint ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                double result = ref.y;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapSize::get_width_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapSize ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                double result = ref.width;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapSize::get_height_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapSize ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                double result = ref.height;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapRect::get_origin_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapRect ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                MAMapPoint result = ref.origin;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMapRect::get_size_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                NSValue* dataValue = (NSValue*) ((NSDictionary<NSString*, NSObject*>*) args)[@"__this__"];
                MAMapRect ref;
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) dataValue == [NSNull null] || dataValue == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                } else {
                    [dataValue getValue:&ref];
                }
        
                MAMapSize result = ref.size;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_visibile_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.visibile;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_duration_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSTimeInterval result = ref.duration;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_loop_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.loop;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_maxParticles_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSInteger result = ref.maxParticles;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_icon_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.icon;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_startParticleSize_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                CGSize result = ref.startParticleSize;
        
                // 返回值: 结构体
                NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGSize)];
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleLifeTime_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSTimeInterval result = ref.particleLifeTime;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleStartColor_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                id<MAParticleColorGenerate> result = ref.particleStartColor;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleStartSpeed_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                id<MAParticleVelocityGenerate> result = ref.particleStartSpeed;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleEmissionModule_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAParticleEmissionModuleOC* result = ref.particleEmissionModule;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleShapeModule_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                id<MAParticleShapeModule> result = ref.particleShapeModule;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAParticleOverlayOptions::get_particleOverLifeModule_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAParticleOverlayOptions* ref = (MAParticleOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAParticleOverLifeModuleOC* result = ref.particleOverLifeModule;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMVTTileOverlayOptions::get_url_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.url;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMVTTileOverlayOptions::get_key_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.key;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMVTTileOverlayOptions::get_Id_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                NSString* result = ref.Id;
        
                // 返回值: jsonable
                id __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMVTTileOverlayOptions::get_visible_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMVTTileOverlayOptions* ref = (MAMVTTileOverlayOptions*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                BOOL result = ref.visible;
        
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAMVTTileOverlay::get_option_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAMVTTileOverlay* ref = (MAMVTTileOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                MAMVTTileOverlayOptions* result = ref.option;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayRenderer::get_overlay_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                id<MAOverlay> result = ref.overlay;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
        @"MAOverlayRenderer::get_strokeImage_batch": ^(NSObject <FlutterPluginRegistrar>* registrar, id argsBatch, FlutterResult methodResult) {
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // ref object
                MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                UIImage* result = ref.strokeImage;
        
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        
    };
}

@end
