def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    // throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}
//plugins {
//    id 'com.android.application'
//    id 'kotlin-android'
//    id 'flutter' // 确保 Flutter 插件被正确引用
//}
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"


android {
    namespace "com.annto.ztb"
//    compileSdkVersion flutter.compileSdkVersion
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.annto.ztb"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
//        minSdkVersion flutter.minSdkVersion
        minSdkVersion 21
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
        }
    }

    signingConfigs {
        release {
            keyAlias 'qdgj'
            keyPassword 'zksrqdgj'
            storeFile file('../qdgj.jks')
            storePassword 'zksrqdgj'

            v1SigningEnabled true
            v2SigningEnabled true
        }
        debug {
            keyAlias 'qdgj'
            keyPassword 'zksrqdgj'
            storeFile file('../qdgj.jks')
            storePassword 'zksrqdgj'

            v1SigningEnabled true
            v2SigningEnabled true
        }

    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
            //关闭混淆, 高德地图在运行release包后可能出现运行崩溃
            minifyEnabled false //删除无用代码
            shrinkResources false //删除无用资源
        }

        debug {
            signingConfig signingConfigs.debug
        }

    }
}

flutter {
    source '../..'
}

dependencies {

    // Android 分包 （解决65535问题）
    implementation 'androidx.multidex:multidex:2.0.1'

    // 引入高德地图SDK
    // implementation('com.amap.api:3dmap:9.2.1')
    // implementation('com.amap.api:location:6.1.0')

}
