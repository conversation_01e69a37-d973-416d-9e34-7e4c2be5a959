import 'dart:io';
import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/pages/homePages/map_help/mapSearch.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sprintf/sprintf.dart';

import 'package:tdesign_flutter/tdesign_flutter.dart';

const mapAndroidKey = '3711738cc212e4070a19d5915aec0e4a';
const mapIOSKey = 'beaa23c0ada73789190676624c1c1d39';

class MapFluttify extends StatefulWidget {
  const MapFluttify({super.key});

  @override
  State<MapFluttify> createState() => _MapFluttifyState();
}

class _MapFluttifyState extends State<MapFluttify> {
  AmapController? _controller;
  late MapSearch _mapSearch;
  late Location _location;
  var _index = -1;
  List<Poi> _pois = [];
  Poi? _poi;
  final TextEditingController _searchTextCtr = TextEditingController();

  LatLng? _latLng;

  @override
  void initState() {
    super.initState();
    requestPermission();
    _mapSearch = MapSearch()..init();
    // _searchTextCtr.addListener(() {
    //   if (_searchTextCtr.text.isNotEmpty) {
    //     _getPoisWithText(_searchTextCtr.text).debounce(2000);
    //   } else {
    //     setState(() {
    //       _pois = [];
    //     });
    //   }
    // });
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
  }

  _getPoint() async {
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    await AmapLocation.instance.init(iosKey: mapIOSKey);
    _location = await AmapLocation.instance.fetchLocation(mode: LocationAccuracy.High);
    await Future.delayed(const Duration(milliseconds: 200));
    await _controller?.setCenterCoordinate(_location.latLng!);
    if (_location.latLng != null) {
      _latLng = _location.latLng!;
      _mapAddMarker(_latLng!);
      if (mounted) setState(() {});
    }
  }

  void requestPermission() async {
    // 申请权限
    if (Platform.isAndroid) {
      if (await Permission.location.request().isGranted) {
        if (_controller != null) {
          _getPoint();
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
          _getPoint();
        }
      } else {
        print("定位权限申请不通过");
      }
    } else {
      if (await Permission.location.request().isGranted &&
          await Permission.locationAlways.request().isGranted &&
          await Permission.locationWhenInUse.request().isGranted) {
        if (_controller != null) _getPoint();
      } else {
        print("定位权限申请不通过");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Scaffold(
            backgroundColor: const Color(0xffE3EEFC).withOpacity(0.8),
            body: _body()));
  }

  _body() {
    return Stack(children: [
      SizedBox(
          height: MediaQuery.of(context).size.height,
          child: AmapView(
              mapType: MapType.Standard,
              showZoomControl: false,
              zoomLevel: 15,
              showCompass: false,
              maskDelay: const Duration(milliseconds: 500),
              centerCoordinate: _latLng,
              onMapCreated: (controller) async {
                _controller = controller;
                _getPoint();
              },
              onMapClicked: (latLng) async {
                _latLng = latLng;
                _mapAddMarker(latLng);
              })),
      _views()
    ]);
  }

  _views() {
    return SafeArea(
        bottom: false,
        child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                // Container(
                //     margin: const EdgeInsets.only(left: 15),
                //     child: OutlinedButton(
                //         onPressed: () async {
                //           await AmapLocation.instance.stopLocation();
                //           Navigator.pop(context);
                //         },
                //         child: const Text('取消',
                //             style: TextStyle(color: Colors.blue)))),
                // Container(
                //     margin: const EdgeInsets.only(right: 15),
                //     child: ElevatedButton(
                //         style: ElevatedButton.styleFrom(
                //             backgroundColor: Colors.blue),
                //         onPressed: () async {

                //         },
                //         child: const Text('确定')))

                Container(
                  margin: const EdgeInsets.only(left: 15),
                  child: TDButton(
                    text: '  取消  ',
                    size: TDButtonSize.large,
                    type: TDButtonType.fill,
                    shape: TDButtonShape.round,
                    theme: TDButtonTheme.defaultTheme,
                    onTap: () async {
                      await AmapLocation.instance.stopLocation();
                      Navigator.pop(context);
                    }
                  ),
                ),

                Container(
                  margin: const EdgeInsets.only(right: 15),
                  child: TDButton(
                    text: '  确定  ',
                    size: TDButtonSize.large,
                    type: TDButtonType.fill,
                    shape: TDButtonShape.round,
                    theme: TDButtonTheme.primary,
                    onTap: () async {
                      await AmapLocation.instance.stopLocation();
                      if (_index >= 0 && _pois.isNotEmpty) {
                        Navigator.pop(context, _pois[_index]);
                      } else if (_poi != null) {
                        Navigator.pop(context, _poi);
                      } else {
                      SmartDialog.showToast('位置异常',
                      alignment: Alignment.center,
                      displayType: SmartToastType.last);
                      }
                    },
                  ),
                )
              ]),
              Container(
                  decoration: const BoxDecoration(color: Colors.white),
                  height: MediaQuery.of(context).size.height * 0.4,
                  child: Column(children: [
                    SizedBox(height: 10.w),
                    if (_latLng != null)
                      Text(sprintf('经度：%.6f 纬度：%.6f', [
                        _latLng?.longitude ?? 0.0,
                        _latLng?.latitude ?? 0.0
                      ])),
                    Row(children: [
                      Expanded(
                          child: Container(
                              constraints: const BoxConstraints(
                                  maxHeight: 40, minHeight: 40),
                              margin: const EdgeInsets.all(10),
                              child: TextField(
                                  textAlign: TextAlign.center,
                                  cursorColor: Colors.grey,
                                  controller: _searchTextCtr,
                                  onSubmitted: (value) async {
                                    FocusScope.of(context)
                                        .requestFocus(FocusNode());
                                    await _getPoisWithText(_searchTextCtr.text);
                                    _mapPositioning();
                                  },
                                  decoration: const InputDecoration(
                                      hintText: '搜索地点',
                                      contentPadding: EdgeInsets.all(0),
                                      filled: true,
                                      fillColor: Color(0xffededed),
                                      focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                              color: Color(0xffededed))),
                                      enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide(
                                        color: Colors.white,
                                      )),
                                      border: OutlineInputBorder(
                                          borderSide:
                                              BorderSide(color: Colors.white),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(10))),
                                      prefixIcon: Icon(Icons.search,
                                          color: Colors.grey))))),
                      TextButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white),
                          onPressed: () {
                            _searchTextCtr.text = "";
                          },
                          child: const Text('取消',
                              style: TextStyle(color: Colors.lightBlue)))
                    ]),
                    if (_pois.isNotEmpty) Expanded(child: _listView())
                  ]))
            ]));
  }

  _listView() {
    return ListView.separated(
        itemBuilder: (context, index) {
          return GestureDetector(
              onTap: () {
                /// 点击某一行
                //Navigator.of(context).pop(poi.address);
                if (_index == index) {
                  _index = -1;
                } else {
                  _index = index;
                }
                if (mounted) setState(() {});
              },
              child: _listCell(index));
        },
        separatorBuilder: (BuildContext context, int index) =>
            const Divider(height: 0.5, color: Color(0x33333333)),
        itemCount: _pois.length);
  }

  _listCell(int index) {
    var poi = _pois[index];
    return Container(
        color: Colors.transparent,
        padding: const EdgeInsets.all(10),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(poi.title ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 18)),
                Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  Text("${poi.distance}m内 | ",
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 14, color: Colors.grey)),
                  SizedBox(
                      width: 260.w,
                      child: Text("${poi.address}",
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              fontSize: 14, color: Colors.grey)))
                ])
              ]),
          Offstage(
              offstage: index != _index,
              child: const Icon(Icons.done, color: Color(0xffabb3be), size: 16))
        ]));
  }

  _mapAddMarker(LatLng latLng) async {
    await _controller?.clear();
    var marker = MarkerOption(
        coordinate: _latLng!,
        visible: true,
        infoWindowEnabled: true,
        widget: const Icon(Icons.location_on_outlined, size: 24));
    await _controller?.addMarker(marker);
    _getPoisWithLatLng();
    if (mounted) setState(() {});
  }

  _mapPositioning({bool isSearch = false}) async {
    await _controller?.setCenterCoordinate(_latLng!);
    if (isSearch) _getPoisWithLatLng();
  }

  _getPoisWithLatLng() async {
    _pois = await _mapSearch.searchWithLatLng(
        _latLng!.latitude, _latLng!.longitude);
    _index = -1;
    print(_pois.toString());
    if (mounted) setState(() {});
    _poi =
        await _mapSearch.getGeocodeList(_latLng!.latitude, _latLng!.longitude);
  }

  _getPoisWithText(String text) async {
    _pois = await _mapSearch.searchWithAddress(text, _location.city ?? "");
    if (_pois.isNotEmpty) {
      var poi = _pois.first;
      _latLng = poi.latLng;
      _mapAddMarker(_latLng!);
    }
  }
}
