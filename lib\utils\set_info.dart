import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/common/dio_manager.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'dart:math';
import 'package:fuduoduo/utils/storage_common.dart';

class SetInfo {
  SetInfo._();

  static SetInfo get instance => _getInstance();
  static SetInfo? _instance;

  static SetInfo _getInstance() {
    _instance ??= SetInfo._();
    return _instance!;
  }

  Map _unitMap = {};
  List _unitList = [];

  Map _sheetTypeMap = {};
  List _sheetTypeList = [];

  /// 编译环境是否是release
  bool isRelease = false;

  DateTime? _lastDateTime;
  DateTime? _lastDateTime2;

  String tmpUrl = '';

  List _checkBaseUrls = [
    {'title': '客户通生产环境', 'url': 'https://seller.annto.com/'},
    {'title': '客户通预发环境（安得）', 'url': 'https://bizhub-ver.annto.com/prod-api/'},
    {'title': '客户通测试环境（安得）', 'url': 'https://bizhub-uat.annto.com/prod-api/'},
    {'title': '客户通测试环境（中科）', 'url': 'http://testad-erp.zhongkeshangruan.cn/prod-api/'},
    {'title': '本地环境（简辉1）', 'url': 'http://192.168.0.123:8080/'},
    {'title': '本地环境（简辉2）', 'url': 'http://192.168.0.111:8080/'},
    {'title': '本地环境（李时轩）', 'url': 'http://192.168.0.198:8080/'},
    {'title': '本地环境（蒋剑超）', 'url': 'http://192.168.0.124:8080/'},
    {'title': '本地环境（刘聪）', 'url': 'http://192.168.0.100:8080/'},
    {'title': '本地环境（谢锦林）', 'url': 'http://192.168.0.10:8080/'},
  ];

  String checkAccountWebviewUrl = 'https://tf-shop.tongfuyouxuan.com/';

  List _checkAccountWebviewUrls = [
    {
      'title': '对账单H5入口地址——测试',
      'url': 'https://tf-shop-test.tongfuyouxuan.com/'
    },
    {
      'title': '对账单H5入口地址——预发',
      'url': 'https://pre-shop-fdd.tongfuyouxuan.com/'
    },
    {'title': '对账单H5入口地址——生产', 'url': 'https://tf-shop.tongfuyouxuan.com/'},
  ];

  String tmpWebviewUrl = 'https://shop.tongfuyouxuan.com/';

  List _checkWebviewUrls = [
    {'title': '代客下单H5入口地址——开发', 'url': 'https://dev-shop.tongfuyouxuan.com/'},
    {'title': '代客下单H5入口地址——测试', 'url': 'https://test-shop.tongfuyouxuan.com/'},
    {'title': '代客下单H5入口地址——预发', 'url': 'https://pre-shop.tongfuyouxuan.com/'},
    {'title': '代客下单H5入口地址——正式', 'url': 'https://shop.tongfuyouxuan.com/'},
  ];

  String shopBindingUrl = Apis.baseUrl == ('https://fdd.tongfuyouxuan.com/')
      ? 'https://tf-shop.tongfuyouxuan.com'
      : 'https://test-shop.tongfuyouxuan.com';

  List _shopBindingUrls = [
    {'title': '云商绑定地址——测试环境', 'url': 'https://test-shop.tongfuyouxuan.com'},
    {'title': '云商绑定地址——预发环境', 'url': 'https://pre-user-api.tongfuyouxuan.com'},
    {'title': '云商绑定地址——正式环境', 'url': 'https://tf-shop.tongfuyouxuan.com'},
  ];

  // String missionCenterUrl = (Apis.baseUrl == ('https://fdd.tongfuyouxuan.com/') || Apis.baseUrl == ('http://prod-fdd-a.tongfuyouxuan.com/') || Apis.baseUrl == ('http://prod-fdd-b.tongfuyouxuan.com/'))
  //       ? "https://tf-shop.tongfuyouxuan.com/"
  //   : Apis.baseUrl == ('https://fdd-pre.tongfuyouxuan.com/') ? "https://pre-shop.tongfuyouxuan.com/"
  //   : "https://tf-shop-test.tongfuyouxuan.com/";
  String missionCenterUrl = 'https://tf-shop.tongfuyouxuan.com/';

  List _missionCenterUrls = [
    {
      'title': '任务中心H5入口地址——测试',
      'url': 'https://tf-shop-test.tongfuyouxuan.com/'
    },
    {'title': '任务中心H5入口地址——预发', 'url': 'https://pre-shop.tongfuyouxuan.com/'},
    {'title': '任务中心H5入口地址——正式', 'url': 'https://tf-shop.tongfuyouxuan.com/'},
  ];

  /// 初始化加载
  initLoad() {
    _getCommonUnitListInfo();
    _getSheetTypeListInfo();
    isRelease = const bool.fromEnvironment('dart.vm.product');
  }

  /// 获取单位数据列表
  _getCommonUnitListInfo()async {
    var userType = await SecureStorageCommon.save('userType').get();
    if (userType == 'b2bSystem') return;

    Map<String, dynamic>? queryParameters = {};
    if (_lastDateTime == null ||
        DateTime.now().difference(_lastDateTime!) > Duration(seconds: 10)) {
      _lastDateTime = DateTime.now();
      MyDio.get(Apis.getUnitDictionaryList, queryParameters: queryParameters,
          successCallBack: (value) {
        if (value is Map) {
          _unitList = value['data'];
          _unitList.forEach((e) {
            _unitMap[e['value']] = e['label'];
          });
        }
      });
    }
  }

  /// 获取单据类型 (单据类型)
  _getSheetTypeListInfo() {
    if (_lastDateTime2 == null ||
        DateTime.now().difference(_lastDateTime2!) > Duration(seconds: 10)) {
      _lastDateTime2 = DateTime.now();
      MyDio.get(Apis.getSheetType, queryParameters: {},
          successCallBack: (value) {
        if (value is Map) {
          _sheetTypeList = value['data'];
          _sheetTypeList.forEach((e) {
            _sheetTypeMap[e['value']] = e['label'];
          });
        }
      });
    }
  }

  String? getItemUnitStr(String unit) {
    if (_unitMap.isEmpty) {
      _getCommonUnitListInfo();
      return null;
    }
    return _unitMap[unit];
  }

  String? getSheetName(String sheetType) {
    if (_sheetTypeMap.isEmpty) {
      _getSheetTypeListInfo();
      return null;
    }
    return _sheetTypeMap[sheetType];
  }

  changeHostUrl() {
    SmartDialog.show(builder: (context) {
      return Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(20.r))),
          margin: EdgeInsets.all(20.w),
          height: 800.h,
          child: ListView.separated(
              padding: EdgeInsets.all(0),
              itemBuilder: (context, index) {
                Map info = _checkBaseUrls[index];
                return InkWell(
                  onTap: () {
                    tmpUrl = info['url'];
                    if (tmpUrl.contains('http')){
                      dio.options.baseUrl = tmpUrl;
                      // 重置api类的静态属性
                      Apis().setBaseUrl(tmpUrl);
                    } 
                    SmartDialog.dismiss();
                  },
                  child: Container(
                      padding: EdgeInsets.all(20.w),
                      color: info['url'] == dio.options.baseUrl
                          ? Colors.blue
                          : Colors.transparent,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [Text(info['title']), Text(info['url'])],
                      )),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                    height: 1, color: ColorResource.GRAY_COMMON_COLOR);
              },
              itemCount: _checkBaseUrls.length)

          //   Column(
          //   mainAxisSize: MainAxisSize.min,
          //   children: [
          //     Container(
          //       child: TextField(
          //         controller: TextEditingController(text: tmpUrl),
          //         onChanged: (value) {
          //           if (value.isEmpty) {
          //             dio.options.baseUrl = Apis.baseUrl;
          //             tmpUrl = '';
          //           }
          //         },
          //         onSubmitted: (value) {
          //           tmpUrl = value;
          //           if (tmpUrl.contains('http')) {
          //             dio.options.baseUrl = tmpUrl;
          //           }
          //         },
          //         style: TextStyle(
          //             color: ColorResource.BLACK_COMMON_COLOR, fontSize: 32.sp),
          //         maxLines: 1,
          //         minLines: 1,
          //         keyboardType: TextInputType.text,
          //         decoration: InputDecoration(
          //             border: const OutlineInputBorder(
          //               borderSide: BorderSide(
          //                 color: Colors.transparent,
          //               ),
          //             ),
          //             enabledBorder: const OutlineInputBorder(
          //               borderSide: BorderSide(
          //                 color: Colors.transparent,
          //               ),
          //             ),
          //             disabledBorder: const OutlineInputBorder(
          //               borderSide: BorderSide(
          //                 color: Colors.transparent,
          //               ),
          //             ),
          //             focusedBorder: const OutlineInputBorder(
          //               borderSide: BorderSide(
          //                 color: Colors.transparent,
          //               ),
          //             ),
          //             contentPadding: const EdgeInsets.only(top: 0, bottom: 0),
          //             hintText: Apis.baseUrl,
          //             hintStyle: TextStyle(
          //                 color: ColorResource.GRAY_TITLE_COLOR, fontSize: 32.sp)),
          //       ),
          //     )
          //   ],
          // )
          );
    });
  }

  changeCheckAccountWebviewUrl() {
    SmartDialog.show(builder: (context) {
      return Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(20.r))),
          margin: EdgeInsets.all(20.w),
          height: 500.h,
          child: ListView.separated(
              padding: EdgeInsets.all(0),
              itemBuilder: (context, index) {
                Map info = _checkAccountWebviewUrls[index];
                return InkWell(
                  onTap: () {
                    checkAccountWebviewUrl = info['url'];
                    SmartDialog.dismiss();
                  },
                  child: Container(
                      padding: EdgeInsets.all(20.w),
                      color: info['url'] == tmpUrl
                          ? Colors.blue
                          : Colors.transparent,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [Text(info['title']), Text(info['url'])],
                      )),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                    height: 1, color: ColorResource.GRAY_COMMON_COLOR);
              },
              itemCount: _checkAccountWebviewUrls.length));
    });
  }

  changeWebviewUrl() {
    SmartDialog.show(builder: (context) {
      return Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(20.r))),
          margin: EdgeInsets.all(20.w),
          height: 800.h,
          child: ListView.separated(
              padding: EdgeInsets.all(0),
              itemBuilder: (context, index) {
                Map info = _checkWebviewUrls[index];
                return InkWell(
                  onTap: () {
                    tmpWebviewUrl = info['url'];
                    // if (tmpWebviewUrl.contains('http')) Apis.baseWebviewUrl = tmpUrl;
                    SmartDialog.dismiss();
                  },
                  child: Container(
                      padding: EdgeInsets.all(20.w),
                      color: info['url'] == tmpWebviewUrl
                          ? Colors.blue
                          : Colors.transparent,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [Text(info['title']), Text(info['url'])],
                      )),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                    height: 1, color: ColorResource.GRAY_COMMON_COLOR);
              },
              itemCount: _checkWebviewUrls.length));
    });
  }

  changeShopBindingUrl() {
    SmartDialog.show(builder: (context) {
      return Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(20.r))),
          margin: EdgeInsets.all(20.w),
          height: 800.h,
          child: ListView.separated(
              padding: EdgeInsets.all(0),
              itemBuilder: (context, index) {
                Map info = _shopBindingUrls[index];
                return InkWell(
                  onTap: () {
                    shopBindingUrl = info['url'];
                    SmartDialog.dismiss();
                  },
                  child: Container(
                      padding: EdgeInsets.all(20.w),
                      color: info['url'] == shopBindingUrl
                          ? Colors.blue
                          : Colors.transparent,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [Text(info['title']), Text(info['url'])],
                      )),
                );
              },
              separatorBuilder: (context, index) {
                return Container(
                    height: 1, color: ColorResource.GRAY_COMMON_COLOR);
              },
              itemCount: _shopBindingUrls.length));
    });
  }
}

/// 后端返回单位对应关系
String getItemUnitStr(String? unit) {
  if (unit == null) return '';
  String? unitStr = SetInfo.instance.getItemUnitStr(unit);
  // print('入参-----$unit----返参-----$unitStr');
  if (unitStr != null) return unitStr;

  /// 2023-10-12复制的返回参数
  Map infos = {
    '32': '枚',
    '22': '册',
    '23': '台',
    '24': '只',
    '25': '根',
    '26': '副',
    '27': '本',
    '28': '顶',
    '29': '节',
    '30': '棵',
    '31': '粒',
    '21': '把',
    '33': '米',
    '34': '批',
    '35': '提',
    '36': '桶',
    '37': '斤',
    '38': '片',
    '39': '双',
    '40': '份',
    '41': '排',
    '11': '捆',
    '1': '筒',
    '2': '条',
    '3': '打',
    '4': '件',
    '5': '罐',
    '7': '支',
    '8': '卷',
    '9': '箱',
    '10': '瓶',
    '0': '盒',
    '12': '双',
    '13': '套',
    '14': '板',
    '15': '包',
    '16': '杯',
    '17': '袋',
    '18': '块',
    '19': '碗',
    '20': '张'
  };
  return infos[unit] ?? '';
}

String getSheetNameWithSheetType(String? sheetType) {
  if (sheetType == null) return '';
  String? sheetName = SetInfo.instance.getSheetName(sheetType);
  if (sheetName != null) return sheetName;

  /// 2024-01-08复制的返回参数
  Map infos = {
    'YA': 'APP预收款',
    'DH': '订货会余额直接退款',
    'DK': '订货会转预收余额',
    'IF': '账户初始化单',
    'KF': '账户初始化单',
    'ZK': '资金账户转款',
    'CC': '采购退货出库单',
    'SS': '设备申请单',
    'XC': '销售出库单',
    'HD': '订货会',
    'SF': '收款单',
    'YS': '预收款单',
    'HK': '还款单',
    'DM': '订货会模板',
    'DZ': '订货会充值',
    'QK': '欠款单',
    'PC': '盘亏出库单',
    'PF': '采购费用单',
    'TZ': '库存调整单',
    'SC': '商品拆分单',
    'SZ': '商品组合单',
    'SR': '设备入库单',
    'ST': '设备出库单',
    'BY': '报溢入库单',
    'BC': '报损出库单',
    'QC': '其他出库单',
    'DC': '调拨出库单',
    'HC': '换货出库单',
    'PR': '盘盈入库单',
    'CT': '采购退货',
    'QT': '其他单',
    'DB': '调拨单',
    'HH': '换货单',
    'FK': '付款单',
    'AF': '资金账户流水',
    'GW': '购物车订单',
    'SK': '收款单',
    'CF': '费用单',
    'XT': '销售退货入库单',
    'XS': '销售订单',
    'CG': '采购计划单'
  };
  return infos[sheetType] ?? '';
}

/// 将字符串double类型转换成int
int intWithDoubleStr(str) {
  var a = double.tryParse('$str') ?? 0.0;
  return a.round();
}

/// 防止丢失精度, 默认两位小数点
double doubleNotloss(double number, {int decimal = 100}) {
  return (number * decimal).roundToDouble() / decimal;
}

/// 防止丢失精度, 默认两位小数点  不足的小数点位  自动补全
String doubleNotlossAuth(double number, {int decimal = 100}) {
  int exponent = (log(decimal) / log(10)).ceil();
  // print('xxxxx== ${(number * decimal).roundToDouble() / decimal}     ${((number * decimal).roundToDouble() / decimal).toStringAsFixed(exponent)} $exponent $decimal ${(log(1000) / log(10)).ceil()}');
  return ((number * decimal).roundToDouble() / decimal).toStringAsFixed(exponent);
}
