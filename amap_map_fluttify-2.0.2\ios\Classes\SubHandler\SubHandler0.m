//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler0.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler0)
- (NSDictionary<NSString*, Handler>*) getSubHandler0 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAGroundOverlay::groundOverlayWithBounds_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::groundOverlayWithBounds_icon(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* boundsValue = (NSValue*) args[@"bounds"];
            MACoordinateBounds bounds;
            if (boundsValue != nil && (NSNull*) boundsValue != [NSNull null]) {
              [boundsValue getValue:&bounds];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"bounds不能为null"]);
              return;
            }
        
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
        
        
            // invoke native method
            MAGroundOverlay* result = [MAGroundOverlay groundOverlayWithBounds: bounds icon: icon];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAGroundOverlay::groundOverlayWithCoordinate_zoomLevel_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::groundOverlayWithCoordinate_zoomLevel_icon(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
        
        
            // invoke native method
            MAGroundOverlay* result = [MAGroundOverlay groundOverlayWithCoordinate: coordinate zoomLevel: zoomLevel icon: icon];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAGroundOverlay::setGroundOverlayWithBounds_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::setGroundOverlayWithBounds_icon(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* boundsValue = (NSValue*) args[@"bounds"];
            MACoordinateBounds bounds;
            if (boundsValue != nil && (NSNull*) boundsValue != [NSNull null]) {
              [boundsValue getValue:&bounds];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"bounds不能为null"]);
              return;
            }
        
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setGroundOverlayWithBounds: bounds icon: icon];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAGroundOverlay::setGroundOverlayWithCoordinate_zoomLevel_icon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::setGroundOverlayWithCoordinate_zoomLevel_icon(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // ref arg
            UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
            // ref
            MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setGroundOverlayWithCoordinate: coordinate zoomLevel: zoomLevel icon: icon];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAPolygonRenderer::initWithPolygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygonRenderer::initWithPolygon(%@)", args);
            }
        
            // args
            // ref arg
            MAPolygon* polygon = (MAPolygon*) (args[@"polygon"] == [NSNull null] ? nil : args[@"polygon"]);
        
            // ref
            MAPolygonRenderer* ref = (MAPolygonRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPolygonRenderer* result = [ref initWithPolygon: polygon];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAHeatMapGradient::initWithColor_andWithStartPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapGradient::initWithColor_andWithStartPoints(%@)", args);
            }
        
            // args
            // list arg
            NSArray<UIColor*>* colors = (NSArray<UIColor*>*) args[@"colors"];
            // jsonable arg
            NSArray<NSNumber*>* startPoints = (NSArray<NSNumber*>*) args[@"startPoints"];
        
            // ref
            MAHeatMapGradient* ref = (MAHeatMapGradient*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapGradient* result = [ref initWithColor: colors andWithStartPoints: startPoints];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapStatus::statusWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapStatus::statusWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // jsonable arg
            CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
            // jsonable arg
            CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
            // struct arg
            NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
            CGPoint screenAnchor;
            if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
              [screenAnchorValue getValue:&screenAnchor];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"screenAnchor不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAMapStatus* result = [MAMapStatus statusWithCenterCoordinate: coordinate zoomLevel: zoomLevel rotationDegree: rotationDegree cameraDegree: cameraDegree screenAnchor: screenAnchor];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapStatus::initWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapStatus::initWithCenterCoordinate_zoomLevel_rotationDegree_cameraDegree_screenAnchor(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // jsonable arg
            CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
            // jsonable arg
            CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
            // struct arg
            NSValue* screenAnchorValue = (NSValue*) args[@"screenAnchor"];
            CGPoint screenAnchor;
            if (screenAnchorValue != nil && (NSNull*) screenAnchorValue != [NSNull null]) {
              [screenAnchorValue getValue:&screenAnchor];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"screenAnchor不能为null"]);
              return;
            }
        
        
            // ref
            MAMapStatus* ref = (MAMapStatus*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSObject* result = [ref initWithCenterCoordinate: coordinate zoomLevel: zoomLevel rotationDegree: rotationDegree cameraDegree: cameraDegree screenAnchor: screenAnchor];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACircle::circleWithCenterCoordinate_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::circleWithCenterCoordinate_radius(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordValue = (NSValue*) args[@"coord"];
            CLLocationCoordinate2D coord;
            if (coordValue != nil && (NSNull*) coordValue != [NSNull null]) {
              [coordValue getValue:&coord];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coord不能为null"]);
              return;
            }
        
            // jsonable arg
            CLLocationDistance radius = [args[@"radius"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MACircle* result = [MACircle circleWithCenterCoordinate: coord radius: radius];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACircle::circleWithMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::circleWithMapRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MACircle* result = [MACircle circleWithMapRect: mapRect];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACircle::setCircleWithCenterCoordinate_radius": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircle::setCircleWithCenterCoordinate_radius(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordValue = (NSValue*) args[@"coord"];
            CLLocationCoordinate2D coord;
            if (coordValue != nil && (NSNull*) coordValue != [NSNull null]) {
              [coordValue getValue:&coord];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coord不能为null"]);
              return;
            }
        
            // jsonable arg
            CLLocationDistance radius = [args[@"radius"] doubleValue];
        
            // ref
            MACircle* ref = (MACircle*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setCircleWithCenterCoordinate: coord radius: radius];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAArcRenderer::initWithArc": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAArcRenderer::initWithArc(%@)", args);
            }
        
            // args
            // ref arg
            MAArc* arc = (MAArc*) (args[@"arc"] == [NSNull null] ? nil : args[@"arc"]);
        
            // ref
            MAArcRenderer* ref = (MAArcRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAArcRenderer* result = [ref initWithArc: arc];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnnotation::setCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotation::setCoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* newCoordinateValue = (NSValue*) args[@"newCoordinate"];
            CLLocationCoordinate2D newCoordinate;
            if (newCoordinateValue != nil && (NSNull*) newCoordinateValue != [NSNull null]) {
              [newCoordinateValue getValue:&newCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"newCoordinate不能为null"]);
              return;
            }
        
        
            // ref
            id<MAAnnotation> ref = (id<MAAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCoordinate : newCoordinate];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAAnimatableAnnotation::step": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::step(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat timeDelta = [args[@"timeDelta"] floatValue];
        
            // ref
            id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref step : timeDelta];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAAnimatableAnnotation::isAnimationFinished": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::isAnimationFinished(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref isAnimationFinished];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnimatableAnnotation::shouldAnimationStart": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::shouldAnimationStart(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref shouldAnimationStart];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnimatableAnnotation::rotateDegree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatableAnnotation::rotateDegree(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAAnimatableAnnotation> ref = (id<MAAnimatableAnnotation>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationDirection result = [ref rotateDegree];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOfflineMapViewController::sharedInstance": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMapViewController::sharedInstance(%@)", args);
            }
        
            // args
        
        
            // ref
        
        
            // invoke native method
            MAOfflineMapViewController* result = [MAOfflineMapViewController sharedInstance];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolygon::polygonWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::polygonWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MAPolygon* result = [MAPolygon polygonWithCoordinates: coords count: count];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolygon::polygonWithPoints_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::polygonWithPoints_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
            MAMapPoint points[pointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                MAMapPoint pointsItem;
                [pointsValue getValue:&pointsItem];
                points[__i__] = pointsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MAPolygon* result = [MAPolygon polygonWithPoints: points count: count];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolygon::setPolygonWithPoints_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::setPolygonWithPoints_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
            MAMapPoint points[pointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                MAMapPoint pointsItem;
                [pointsValue getValue:&pointsItem];
                points[__i__] = pointsItem;
            }
            // jsonable arg
            NSInteger count = [args[@"count"] longValue];
        
            // ref
            MAPolygon* ref = (MAPolygon*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setPolygonWithPoints: points count: count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAPolygon::setPolygonWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygon::setPolygonWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSInteger count = [args[@"count"] longValue];
        
            // ref
            MAPolygon* ref = (MAPolygon*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setPolygonWithCoordinates: coords count: count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleOverlay::particleOverlayWithOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlay::particleOverlayWithOption(%@)", args);
            }
        
            // args
            // ref arg
            MAParticleOverlayOptions* option = (MAParticleOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
        
        
            // invoke native method
            MAParticleOverlay* result = [MAParticleOverlay particleOverlayWithOption: option];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleOverlay::updateOverlayOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlay::updateOverlayOption(%@)", args);
            }
        
            // args
            // ref arg
            MAParticleOverlayOptions* overlayOption = (MAParticleOverlayOptions*) (args[@"overlayOption"] == [NSNull null] ? nil : args[@"overlayOption"]);
        
            // ref
            MAParticleOverlay* ref = (MAParticleOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref updateOverlayOption : overlayOption];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAPolyline::polylineWithPoints_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::polylineWithPoints_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
            MAMapPoint points[pointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                MAMapPoint pointsItem;
                [pointsValue getValue:&pointsItem];
                points[__i__] = pointsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MAPolyline* result = [MAPolyline polylineWithPoints: points count: count];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolyline::polylineWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::polylineWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MAPolyline* result = [MAPolyline polylineWithCoordinates: coords count: count];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolyline::setPolylineWithPoints_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::setPolylineWithPoints_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* pointsValueList = (NSArray<NSValue*>*) args[@"points"];
            MAMapPoint points[pointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < pointsValueList.count; __i__++) {
                NSValue* pointsValue = (NSValue*) [pointsValueList objectAtIndex:__i__];
                MAMapPoint pointsItem;
                [pointsValue getValue:&pointsItem];
                points[__i__] = pointsItem;
            }
            // jsonable arg
            NSInteger count = [args[@"count"] longValue];
        
            // ref
            MAPolyline* ref = (MAPolyline*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setPolylineWithPoints: points count: count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAPolyline::setPolylineWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolyline::setPolylineWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSInteger count = [args[@"count"] longValue];
        
            // ref
            MAPolyline* ref = (MAPolyline*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setPolylineWithCoordinates: coords count: count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMultiColoredPolylineRenderer::initWithMultiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiColoredPolylineRenderer::initWithMultiPolyline(%@)", args);
            }
        
            // args
            // ref arg
            MAMultiPolyline* multiPolyline = (MAMultiPolyline*) (args[@"multiPolyline"] == [NSNull null] ? nil : args[@"multiPolyline"]);
        
            // ref
            MAMultiColoredPolylineRenderer* ref = (MAMultiColoredPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiColoredPolylineRenderer* result = [ref initWithMultiPolyline: multiPolyline];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnimatedAnnotation::addMoveAnimationWithKeyCoordinates_count_withDuration_withName_completeCallback": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::addMoveAnimationWithKeyCoordinates_count_withDuration_withName_completeCallback(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordinatesValueList = (NSArray<NSValue*>*) args[@"coordinates"];
            CLLocationCoordinate2D coordinates[coordinatesValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordinatesValueList.count; __i__++) {
                NSValue* coordinatesValue = (NSValue*) [coordinatesValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordinatesItem;
                [coordinatesValue getValue:&coordinatesItem];
                coordinates[__i__] = coordinatesItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
            // jsonable arg
            CGFloat duration = [args[@"duration"] floatValue];
            // jsonable arg
            NSString* name = (NSString*) args[@"name"];
        
        
            // ref
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAAnnotationMoveAnimation* result = [ref addMoveAnimationWithKeyCoordinates: coordinates count: count withDuration: duration withName: name completeCallback: ^(BOOL isFinished) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|BOOL#isFinished::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* argisFinished = @(isFinished);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|BOOL#isFinished" arguments:@{@"isFinished": argisFinished == nil ? [NSNull null] : argisFinished}];
                });
        
            }];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnimatedAnnotation::addMoveAnimationWithKeyCoordinates_count_withDuration_withName_completeCallback_stepCallback": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::addMoveAnimationWithKeyCoordinates_count_withDuration_withName_completeCallback_stepCallback(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordinatesValueList = (NSArray<NSValue*>*) args[@"coordinates"];
            CLLocationCoordinate2D coordinates[coordinatesValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordinatesValueList.count; __i__++) {
                NSValue* coordinatesValue = (NSValue*) [coordinatesValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordinatesItem;
                [coordinatesValue getValue:&coordinatesItem];
                coordinates[__i__] = coordinatesItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
            // jsonable arg
            CGFloat duration = [args[@"duration"] floatValue];
            // jsonable arg
            NSString* name = (NSString*) args[@"name"];
        
        
        
            // ref
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAAnnotationMoveAnimation* result = [ref addMoveAnimationWithKeyCoordinates: coordinates count: count withDuration: duration withName: name completeCallback: ^(BOOL isFinished) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|BOOL#isFinished::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* argisFinished = @(isFinished);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|BOOL#isFinished" arguments:@{@"isFinished": argisFinished == nil ? [NSNull null] : argisFinished}];
                });
        
            } stepCallback: ^(MAAnnotationMoveAnimation* currentAni) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|MAAnnotationMoveAnimation*#currentAni::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                MAAnnotationMoveAnimation* argcurrentAni = currentAni;
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|MAAnnotationMoveAnimation*#currentAni" arguments:@{@"currentAni": argcurrentAni == nil ? [NSNull null] : argcurrentAni}];
                });
        
            }];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnimatedAnnotation::allMoveAnimations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::allMoveAnimations(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray<MAAnnotationMoveAnimation*>* result = [ref allMoveAnimations];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnimatedAnnotation::setNeedsStart": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnimatedAnnotation::setNeedsStart(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnimatedAnnotation* ref = (MAAnimatedAnnotation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setNeedsStart ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOverlay::coordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlay::coordinate(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAOverlay> ref = (id<MAOverlay>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = [ref coordinate];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        @"MAOverlay::boundingMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlay::boundingMapRect(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAOverlay> ref = (id<MAOverlay>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = [ref boundingMapRect];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMultiTexturePolylineRenderer::initWithMultiPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiTexturePolylineRenderer::initWithMultiPolyline(%@)", args);
            }
        
            // args
            // ref arg
            MAMultiPolyline* multiPolyline = (MAMultiPolyline*) (args[@"multiPolyline"] == [NSNull null] ? nil : args[@"multiPolyline"]);
        
            // ref
            MAMultiTexturePolylineRenderer* ref = (MAMultiTexturePolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiTexturePolylineRenderer* result = [ref initWithMultiPolyline: multiPolyline];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAHeatMapVectorGridOverlay::heatMapOverlayWithOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorGridOverlay::heatMapOverlayWithOption(%@)", args);
            }
        
            // args
            // ref arg
            MAHeatMapVectorGridOverlayOptions* option = (MAHeatMapVectorGridOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
        
        
            // invoke native method
            MAHeatMapVectorGridOverlay* result = [MAHeatMapVectorGridOverlay heatMapOverlayWithOption: option];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAHeatMapVectorOverlayRender::initWithHeatOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlayRender::initWithHeatOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MAHeatMapVectorOverlay* heatOverlay = (MAHeatMapVectorOverlay*) (args[@"heatOverlay"] == [NSNull null] ? nil : args[@"heatOverlay"]);
        
            // ref
            MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorOverlayRender* result = [ref initWithHeatOverlay: heatOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAHeatMapVectorOverlayRender::getHeatMapItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlayRender::getHeatMapItem(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
            MAHeatMapVectorOverlayRender* ref = (MAHeatMapVectorOverlayRender*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorItem* result = [ref getHeatMapItem: coordinate];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATileOverlayRenderer::initWithTileOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlayRenderer::initWithTileOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MATileOverlay* tileOverlay = (MATileOverlay*) (args[@"tileOverlay"] == [NSNull null] ? nil : args[@"tileOverlay"]);
        
            // ref
            MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MATileOverlayRenderer* result = [ref initWithTileOverlay: tileOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATileOverlayRenderer::reloadData": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlayRenderer::reloadData(%@)", args);
            }
        
            // args
        
        
            // ref
            MATileOverlayRenderer* ref = (MATileOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref reloadData ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAHeatMapVectorGridOverlayRenderer::initWithHeatOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorGridOverlayRenderer::initWithHeatOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MAHeatMapVectorGridOverlay* heatOverlay = (MAHeatMapVectorGridOverlay*) (args[@"heatOverlay"] == [NSNull null] ? nil : args[@"heatOverlay"]);
        
            // ref
            MAHeatMapVectorGridOverlayRenderer* ref = (MAHeatMapVectorGridOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAHeatMapVectorGridOverlayRenderer* result = [ref initWithHeatOverlay: heatOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATraceManager::sharedInstance": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::sharedInstance(%@)", args);
            }
        
            // args
        
        
            // ref
        
        
            // invoke native method
            MATraceManager* result = [MATraceManager sharedInstance];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATraceManager::queryProcessedTraceWith_type_processingCallback_finishCallback_failedCallback": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::queryProcessedTraceWith_type_processingCallback_finishCallback_failedCallback(%@)", args);
            }
        
            // args
            // list arg
            NSArray<MATraceLocation*>* locations = (NSArray<MATraceLocation*>*) args[@"locations"];
            // enum arg
            AMapCoordinateType type = (AMapCoordinateType) [args[@"type"] integerValue];
        
        
        
        
            // ref
            MATraceManager* ref = (MATraceManager*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSOperation* result = [ref queryProcessedTraceWith: locations type: type processingCallback: ^(int index, NSArray<MATracePoint*>* points) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"MAProcessingCallback::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* argindex = @(index);
                // ref callback arg
                NSArray<MATracePoint*>* argpoints = points;
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"MAProcessingCallback_" arguments:@{@"index": argindex == nil ? [NSNull null] : argindex, @"points": argpoints == nil ? [NSNull null] : argpoints}];
                });
        
            } finishCallback: ^(NSArray<MATracePoint*>* points, double distance) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"MAFinishCallback::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                NSArray<MATracePoint*>* argpoints = points;
                // primitive callback arg
                NSNumber* argdistance = @(distance);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"MAFinishCallback_" arguments:@{@"points": argpoints == nil ? [NSNull null] : argpoints, @"distance": argdistance == nil ? [NSNull null] : argdistance}];
                });
        
            } failedCallback: ^(int errorCode, NSString* errorDesc) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"MAFailedCallback::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* argerrorCode = @(errorCode);
                // ref callback arg
                NSString* argerrorDesc = errorDesc;
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"MAFailedCallback_" arguments:@{@"errorCode": argerrorCode == nil ? [NSNull null] : argerrorCode, @"errorDesc": argerrorDesc == nil ? [NSNull null] : argerrorDesc}];
                });
        
            }];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATraceManager::start": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::start(%@)", args);
            }
        
            // args
        
        
            // ref
            MATraceManager* ref = (MATraceManager*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref start ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MATraceManager::stop": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATraceManager::stop(%@)", args);
            }
        
            // args
        
        
            // ref
            MATraceManager* ref = (MATraceManager*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref stop ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMultiPointOverlayRenderer::initWithMultiPointOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiPointOverlayRenderer::initWithMultiPointOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MAMultiPointOverlay* multiPointOverlay = (MAMultiPointOverlay*) (args[@"multiPointOverlay"] == [NSNull null] ? nil : args[@"multiPointOverlay"]);
        
            // ref
            MAMultiPointOverlayRenderer* ref = (MAMultiPointOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiPointOverlayRenderer* result = [ref initWithMultiPointOverlay: multiPointOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAPolylineRenderer::initWithPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolylineRenderer::initWithPolyline(%@)", args);
            }
        
            // args
            // ref arg
            MAPolyline* polyline = (MAPolyline*) (args[@"polyline"] == [NSNull null] ? nil : args[@"polyline"]);
        
            // ref
            MAPolylineRenderer* ref = (MAPolylineRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAPolylineRenderer* result = [ref initWithPolyline: polyline];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::name": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::name(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = [ref name];
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::coordinates": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::coordinates(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D* result = [ref coordinates];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D*)];
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::count(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSUInteger result = [ref count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::duration(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref duration];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::elapsedTime": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::elapsedTime(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref elapsedTime];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::cancel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::cancel(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref cancel ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::isCancelled": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::isCancelled(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref isCancelled];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnnotationMoveAnimation::passedPointCount": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationMoveAnimation::passedPointCount(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationMoveAnimation* ref = (MAAnnotationMoveAnimation*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSInteger result = [ref passedPointCount];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAAnnotationView::setSelected_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::setSelected_animated(%@)", args);
            }
        
            // args
            // jsonable arg
            BOOL selected = [args[@"selected"] boolValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setSelected : selected animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAAnnotationView::initWithAnnotation_reuseIdentifier": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::initWithAnnotation_reuseIdentifier(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
            // jsonable arg
            NSString* reuseIdentifier = (NSString*) args[@"reuseIdentifier"];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSObject* result = [ref initWithAnnotation: annotation reuseIdentifier: reuseIdentifier];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAAnnotationView::prepareForReuse": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::prepareForReuse(%@)", args);
            }
        
            // args
        
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref prepareForReuse ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAAnnotationView::setDragState_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAnnotationView::setDragState_animated(%@)", args);
            }
        
            // args
            // enum arg
            MAAnnotationViewDragState newDragState = (MAAnnotationViewDragState) [args[@"newDragState"] integerValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAAnnotationView* ref = (MAAnnotationView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setDragState : newDragState animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MATileOverlay::initWithURLTemplate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlay::initWithURLTemplate(%@)", args);
            }
        
            // args
            // jsonable arg
            NSString* URLTemplate = (NSString*) args[@"URLTemplate"];
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSObject* result = [ref initWithURLTemplate: URLTemplate];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MATileOverlay::loadTileAtPath_result": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlay::loadTileAtPath_result(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pathValue = (NSValue*) args[@"path"];
            MATileOverlayPath path;
            if (pathValue != nil && (NSNull*) pathValue != [NSNull null]) {
              [pathValue getValue:&path];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"path不能为null"]);
              return;
            }
        
        
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref loadTileAtPath : path result: ^(NSData* tileData, NSError* error) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|NSData*#tileData,NSError*#error::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                NSData* argtileData = tileData;
                // ref callback arg
                NSError* argerror = error;
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|NSData*#tileData,NSError*#error_" arguments:@{@"tileData": argtileData == nil ? [NSNull null] : argtileData, @"error": argerror == nil ? [NSNull null] : argerror}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MATileOverlay::cancelLoadOfTileAtPath": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MATileOverlay::cancelLoadOfTileAtPath(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pathValue = (NSValue*) args[@"path"];
            MATileOverlayPath path;
            if (pathValue != nil && (NSNull*) pathValue != [NSNull null]) {
              [pathValue getValue:&path];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"path不能为null"]);
              return;
            }
        
        
            // ref
            MATileOverlay* ref = (MATileOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref cancelLoadOfTileAtPath : path];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MACustomCalloutView::initWithCustomView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomCalloutView::initWithCustomView(%@)", args);
            }
        
            // args
            // ref arg
            UIView* customView = (UIView*) (args[@"customView"] == [NSNull null] ? nil : args[@"customView"]);
        
            // ref
            MACustomCalloutView* ref = (MACustomCalloutView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSObject* result = [ref initWithCustomView: customView];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAOfflineMap::sharedOfflineMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::sharedOfflineMap(%@)", args);
            }
        
            // args
        
        
            // ref
        
        
            // invoke native method
            MAOfflineMap* result = [MAOfflineMap sharedOfflineMap];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAOfflineMap::setupWithCompletionBlock": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::setupWithCompletionBlock(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setupWithCompletionBlock : ^(BOOL setupSuccess) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|BOOL#setupSuccess::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* argsetupSuccess = @(setupSuccess);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|BOOL#setupSuccess" arguments:@{@"setupSuccess": argsetupSuccess == nil ? [NSNull null] : argsetupSuccess}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOfflineMap::downloadItem_shouldContinueWhenAppEntersBackground_downloadBlock": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::downloadItem_shouldContinueWhenAppEntersBackground_downloadBlock(%@)", args);
            }
        
            // args
            // ref arg
            MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
            // jsonable arg
            BOOL shouldContinueWhenAppEntersBackground = [args[@"shouldContinueWhenAppEntersBackground"] boolValue];
        
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref downloadItem : item shouldContinueWhenAppEntersBackground: shouldContinueWhenAppEntersBackground downloadBlock: ^(MAOfflineItem* downloadItem, MAOfflineMapDownloadStatus downloadStatus, id info) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"MAOfflineMapDownloadBlock::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                MAOfflineItem* argdownloadItem = downloadItem;
                // enum callback arg
                NSNumber* argdownloadStatus = @((NSInteger) downloadStatus);
                // ref callback arg
                id arginfo = info;
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"MAOfflineMapDownloadBlock__" arguments:@{@"downloadItem": argdownloadItem == nil ? [NSNull null] : argdownloadItem, @"downloadStatus": argdownloadStatus == nil ? [NSNull null] : argdownloadStatus, @"info": arginfo == nil ? [NSNull null] : arginfo}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOfflineMap::isDownloadingForItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::isDownloadingForItem(%@)", args);
            }
        
            // args
            // ref arg
            MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref isDownloadingForItem: item];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOfflineMap::pauseItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::pauseItem(%@)", args);
            }
        
            // args
            // ref arg
            MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref pauseItem: item];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOfflineMap::deleteItem": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::deleteItem(%@)", args);
            }
        
            // args
            // ref arg
            MAOfflineItem* item = (MAOfflineItem*) (args[@"item"] == [NSNull null] ? nil : args[@"item"]);
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref deleteItem : item];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOfflineMap::cancelAll": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::cancelAll(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref cancelAll ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOfflineMap::clearDisk": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::clearDisk(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref clearDisk ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOfflineMap::checkNewestVersion": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOfflineMap::checkNewestVersion(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOfflineMap* ref = (MAOfflineMap*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref checkNewestVersion : ^(BOOL hasNewestVersion) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"MAOfflineMapNewestVersionBlock::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // primitive callback arg
                NSNumber* arghasNewestVersion = @(hasNewestVersion);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"MAOfflineMapNewestVersionBlock" arguments:@{@"hasNewestVersion": arghasNewestVersion == nil ? [NSNull null] : arghasNewestVersion}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MACircleRenderer::initWithCircle": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircleRenderer::initWithCircle(%@)", args);
            }
        
            // args
            // ref arg
            MACircle* circle = (MACircle*) (args[@"circle"] == [NSNull null] ? nil : args[@"circle"]);
        
            // ref
            MACircleRenderer* ref = (MACircleRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACircleRenderer* result = [ref initWithCircle: circle];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleOverlayRenderer::initWithParticleOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlayRenderer::initWithParticleOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MAParticleOverlay* particleOverlay = (MAParticleOverlay*) (args[@"particleOverlay"] == [NSNull null] ? nil : args[@"particleOverlay"]);
        
            // ref
            MAParticleOverlayRenderer* ref = (MAParticleOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleOverlayRenderer* result = [ref initWithParticleOverlay: particleOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"NSValue::valueWithMAMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::valueWithMAMapPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapPointValue = (NSValue*) args[@"mapPoint"];
            MAMapPoint mapPoint;
            if (mapPointValue != nil && (NSNull*) mapPointValue != [NSNull null]) {
              [mapPointValue getValue:&mapPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapPoint不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSValue* result = [NSValue valueWithMAMapPoint: mapPoint];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"NSValue::valueWithMAMapSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::valueWithMAMapSize(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapSizeValue = (NSValue*) args[@"mapSize"];
            MAMapSize mapSize;
            if (mapSizeValue != nil && (NSNull*) mapSizeValue != [NSNull null]) {
              [mapSizeValue getValue:&mapSize];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapSize不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSValue* result = [NSValue valueWithMAMapSize: mapSize];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"NSValue::valueWithMAMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::valueWithMAMapRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSValue* result = [NSValue valueWithMAMapRect: mapRect];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"NSValue::valueWithMACoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::valueWithMACoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSValue* result = [NSValue valueWithMACoordinate: coordinate];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"NSValue::MAMapPointValue": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::MAMapPointValue(%@)", args);
            }
        
            // args
        
        
            // ref
            NSValue* ref = (NSValue*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapPoint result = [ref MAMapPointValue];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"NSValue::MAMapSizeValue": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::MAMapSizeValue(%@)", args);
            }
        
            // args
        
        
            // ref
            NSValue* ref = (NSValue*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapSize result = [ref MAMapSizeValue];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapSize)];
        
            methodResult(__result__);
        },
        @"NSValue::MAMapRectValue": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::MAMapRectValue(%@)", args);
            }
        
            // args
        
        
            // ref
            NSValue* ref = (NSValue*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = [ref MAMapRectValue];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"NSValue::MACoordinateValue": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: NSValue::MACoordinateValue(%@)", args);
            }
        
            // args
        
        
            // ref
            NSValue* ref = (NSValue*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = [ref MACoordinateValue];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        @"MAParticleVelocityGenerate::getX": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getX(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref getX];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleVelocityGenerate::getY": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getY(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref getY];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleVelocityGenerate::getZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleVelocityGenerate::getZ(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleVelocityGenerate> ref = (id<MAParticleVelocityGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref getZ];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleRandomVelocityGenerate::initWithBoundaryValueX1_Y1_Z1_X2_Y2_Z2": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRandomVelocityGenerate::initWithBoundaryValueX1_Y1_Z1_X2_Y2_Z2(%@)", args);
            }
        
            // args
            // jsonable arg
            float x1 = [args[@"x1"] floatValue];
            // jsonable arg
            float y1 = [args[@"y1"] floatValue];
            // jsonable arg
            float z1 = [args[@"z1"] floatValue];
            // jsonable arg
            float x2 = [args[@"x2"] floatValue];
            // jsonable arg
            float y2 = [args[@"y2"] floatValue];
            // jsonable arg
            float z2 = [args[@"z2"] floatValue];
        
            // ref
            MAParticleRandomVelocityGenerate* ref = (MAParticleRandomVelocityGenerate*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleRandomVelocityGenerate* result = [ref initWithBoundaryValueX1: x1 Y1: y1 Z1: z1 X2: x2 Y2: y2 Z2: z2];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleColorGenerate::getColor": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleColorGenerate::getColor(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleColorGenerate> ref = (id<MAParticleColorGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float* result = [ref getColor];
        
            // result
            // return a (value)*
            NSValue* __result__ = [NSValue valueWithPointer:result];
        
            methodResult(__result__);
        },
        @"MAParticleRandomColorGenerate::initWithBoundaryColorR1_G1_B1_A1_R2_G2_B2_A2": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRandomColorGenerate::initWithBoundaryColorR1_G1_B1_A1_R2_G2_B2_A2(%@)", args);
            }
        
            // args
            // jsonable arg
            float r1 = [args[@"r1"] floatValue];
            // jsonable arg
            float g1 = [args[@"g1"] floatValue];
            // jsonable arg
            float b1 = [args[@"b1"] floatValue];
            // jsonable arg
            float a1 = [args[@"a1"] floatValue];
            // jsonable arg
            float r2 = [args[@"r2"] floatValue];
            // jsonable arg
            float g2 = [args[@"g2"] floatValue];
            // jsonable arg
            float b2 = [args[@"b2"] floatValue];
            // jsonable arg
            float a2 = [args[@"a2"] floatValue];
        
            // ref
            MAParticleRandomColorGenerate* ref = (MAParticleRandomColorGenerate*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleRandomColorGenerate* result = [ref initWithBoundaryColorR1: r1 G1: g1 B1: b1 A1: a1 R2: r2 G2: g2 B2: b2 A2: a2];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleRotationGenerate::getRotate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRotationGenerate::getRotate(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleRotationGenerate> ref = (id<MAParticleRotationGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = [ref getRotate];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleConstantRotationGenerate::initWithRotate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleConstantRotationGenerate::initWithRotate(%@)", args);
            }
        
            // args
            // jsonable arg
            float rotate = [args[@"rotate"] floatValue];
        
            // ref
            MAParticleConstantRotationGenerate* ref = (MAParticleConstantRotationGenerate*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleConstantRotationGenerate* result = [ref initWithRotate: rotate];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleSizeGenerate::getSizeX": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeX(%@)", args);
            }
        
            // args
            // jsonable arg
            float timeFrame = [args[@"timeFrame"] floatValue];
        
            // ref
            id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = [ref getSizeX: timeFrame];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleSizeGenerate::getSizeY": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeY(%@)", args);
            }
        
            // args
            // jsonable arg
            float timeFrame = [args[@"timeFrame"] floatValue];
        
            // ref
            id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = [ref getSizeY: timeFrame];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleSizeGenerate::getSizeZ": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSizeGenerate::getSizeZ(%@)", args);
            }
        
            // args
            // jsonable arg
            float timeFrame = [args[@"timeFrame"] floatValue];
        
            // ref
            id<MAParticleSizeGenerate> ref = (id<MAParticleSizeGenerate>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float result = [ref getSizeZ: timeFrame];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleCurveSizeGenerate::initWithCurveX_Y_Z": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleCurveSizeGenerate::initWithCurveX_Y_Z(%@)", args);
            }
        
            // args
            // jsonable arg
            float x = [args[@"x"] floatValue];
            // jsonable arg
            float y = [args[@"y"] floatValue];
            // jsonable arg
            float z = [args[@"z"] floatValue];
        
            // ref
            MAParticleCurveSizeGenerate* ref = (MAParticleCurveSizeGenerate*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleCurveSizeGenerate* result = [ref initWithCurveX: x Y: y Z: z];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleEmissionModuleOC::initWithEmissionRate_rateTime": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleEmissionModuleOC::initWithEmissionRate_rateTime(%@)", args);
            }
        
            // args
            // jsonable arg
            int rate = [args[@"rate"] intValue];
            // jsonable arg
            int rateTime = [args[@"rateTime"] intValue];
        
            // ref
            MAParticleEmissionModuleOC* ref = (MAParticleEmissionModuleOC*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleEmissionModuleOC* result = [ref initWithEmissionRate: rate rateTime: rateTime];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleShapeModule::getPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleShapeModule::getPoint(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleShapeModule> ref = (id<MAParticleShapeModule>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float* result = [ref getPoint];
        
            // result
            // return a (value)*
            NSValue* __result__ = [NSValue valueWithPointer:result];
        
            methodResult(__result__);
        },
        @"MAParticleShapeModule::isRatioEnable": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleShapeModule::isRatioEnable(%@)", args);
            }
        
            // args
        
        
            // ref
            id<MAParticleShapeModule> ref = (id<MAParticleShapeModule>) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref isRatioEnable];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAParticleSinglePointShapeModule::initWithShapeX_Y_Z_useRatio": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleSinglePointShapeModule::initWithShapeX_Y_Z_useRatio(%@)", args);
            }
        
            // args
            // jsonable arg
            float x = [args[@"x"] floatValue];
            // jsonable arg
            float y = [args[@"y"] floatValue];
            // jsonable arg
            float z = [args[@"z"] floatValue];
            // jsonable arg
            BOOL isUseRatio = [args[@"isUseRatio"] boolValue];
        
            // ref
            MAParticleSinglePointShapeModule* ref = (MAParticleSinglePointShapeModule*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleSinglePointShapeModule* result = [ref initWithShapeX: x Y: y Z: z useRatio: isUseRatio];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleRectShapeModule::initWithLeft_top_right_bottom_useRatio": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleRectShapeModule::initWithLeft_top_right_bottom_useRatio(%@)", args);
            }
        
            // args
            // jsonable arg
            float left = [args[@"left"] floatValue];
            // jsonable arg
            float top = [args[@"top"] floatValue];
            // jsonable arg
            float right = [args[@"right"] floatValue];
            // jsonable arg
            float bottom = [args[@"bottom"] floatValue];
            // jsonable arg
            BOOL isUseRatio = [args[@"isUseRatio"] boolValue];
        
            // ref
            MAParticleRectShapeModule* ref = (MAParticleRectShapeModule*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAParticleRectShapeModule* result = [ref initWithLeft: left top: top right: right bottom: bottom useRatio: isUseRatio];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAParticleOverLifeModuleOC::setVelocityOverLife": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setVelocityOverLife(%@)", args);
            }
        
            // args
            // ref arg
            id<MAParticleVelocityGenerate> velocity = (id<MAParticleVelocityGenerate>) (args[@"velocity"] == [NSNull null] ? nil : args[@"velocity"]);
        
            // ref
            MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setVelocityOverLife : velocity];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAParticleOverLifeModuleOC::setRotationOverLife": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setRotationOverLife(%@)", args);
            }
        
            // args
            // ref arg
            id<MAParticleRotationGenerate> rotation = (id<MAParticleRotationGenerate>) (args[@"rotation"] == [NSNull null] ? nil : args[@"rotation"]);
        
            // ref
            MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setRotationOverLife : rotation];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAParticleOverLifeModuleOC::setSizeOverLife": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setSizeOverLife(%@)", args);
            }
        
            // args
            // ref arg
            id<MAParticleSizeGenerate> size = (id<MAParticleSizeGenerate>) (args[@"size"] == [NSNull null] ? nil : args[@"size"]);
        
            // ref
            MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setSizeOverLife : size];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAParticleOverLifeModuleOC::setColorOverLife": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverLifeModuleOC::setColorOverLife(%@)", args);
            }
        
            // args
            // ref arg
            id<MAParticleColorGenerate> color = (id<MAParticleColorGenerate>) (args[@"color"] == [NSNull null] ? nil : args[@"color"]);
        
            // ref
            MAParticleOverLifeModuleOC* ref = (MAParticleOverLifeModuleOC*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setColorOverLife : color];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAParticleOverlayOptionsFactory::particleOverlayOptionsWithType": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAParticleOverlayOptionsFactory::particleOverlayOptionsWithType(%@)", args);
            }
        
            // args
            // enum arg
            MAParticleOverlayType particleType = (MAParticleOverlayType) [args[@"particleType"] integerValue];
        
            // ref
        
        
            // invoke native method
            NSArray<MAParticleOverlayOptions*>* result = [MAParticleOverlayOptionsFactory particleOverlayOptionsWithType: particleType];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMVTTileOverlay::mvtTileOverlayWithOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMVTTileOverlay::mvtTileOverlayWithOption(%@)", args);
            }
        
            // args
            // ref arg
            MAMVTTileOverlayOptions* option = (MAMVTTileOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
        
        
            // invoke native method
            MAMVTTileOverlay* result = [MAMVTTileOverlay mvtTileOverlayWithOption: option];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::initWithOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::initWithOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOverlayRenderer* result = [ref initWithOverlay: overlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::getViewMatrix": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getViewMatrix(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float* result = [ref getViewMatrix];
        
            // result
            // return a (value)*
            NSValue* __result__ = [NSValue valueWithPointer:result];
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::getProjectionMatrix": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getProjectionMatrix(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            float* result = [ref getProjectionMatrix];
        
            // result
            // return a (value)*
            NSValue* __result__ = [NSValue valueWithPointer:result];
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::getOffsetPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getOffsetPoint(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapPoint result = [ref getOffsetPoint];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::getMapZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::getMapZoomLevel(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref getMapZoomLevel];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::glPointForMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glPointForMapPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapPointValue = (NSValue*) args[@"mapPoint"];
            MAMapPoint mapPoint;
            if (mapPointValue != nil && (NSNull*) mapPointValue != [NSNull null]) {
              [mapPointValue getValue:&mapPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapPoint不能为null"]);
              return;
            }
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = [ref glPointForMapPoint: mapPoint];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::glPointsForMapPoints_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glPointsForMapPoints_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* mapPointsValueList = (NSArray<NSValue*>*) args[@"mapPoints"];
            MAMapPoint mapPoints[mapPointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < mapPointsValueList.count; __i__++) {
                NSValue* mapPointsValue = (NSValue*) [mapPointsValueList objectAtIndex:__i__];
                MAMapPoint mapPointsItem;
                [mapPointsValue getValue:&mapPointsItem];
                mapPoints[__i__] = mapPointsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint* result = [ref glPointsForMapPoints: mapPoints count: count];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint*)];
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::glWidthForWindowWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glWidthForWindowWidth(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat windowWidth = [args[@"windowWidth"] floatValue];
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGFloat result = [ref glWidthForWindowWidth: windowWidth];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::glRender": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::glRender(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref glRender ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::loadTexture": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::loadTexture(%@)", args);
            }
        
            // args
            // ref arg
            UIImage* textureImage = (UIImage*) (args[@"textureImage"] == [NSNull null] ? nil : args[@"textureImage"]);
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            GLuint result = [ref loadTexture: textureImage];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::deleteTexture": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::deleteTexture(%@)", args);
            }
        
            // args
            // jsonable arg
            GLuint textureId = [args[@"textureId"] unsignedIntegerValue];
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref deleteTexture : textureId];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAOverlayRenderer::setNeedsUpdate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAOverlayRenderer::setNeedsUpdate(%@)", args);
            }
        
            // args
        
        
            // ref
            MAOverlayRenderer* ref = (MAOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setNeedsUpdate ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAHeatMapVectorOverlay::heatMapOverlayWithOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAHeatMapVectorOverlay::heatMapOverlayWithOption(%@)", args);
            }
        
            // args
            // ref arg
            MAHeatMapVectorOverlayOptions* option = (MAHeatMapVectorOverlayOptions*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
        
        
            // invoke native method
            MAHeatMapVectorOverlay* result = [MAHeatMapVectorOverlay heatMapOverlayWithOption: option];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMultiPointOverlay::initWithMultiPointItems": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMultiPointOverlay::initWithMultiPointItems(%@)", args);
            }
        
            // args
            // list arg
            NSArray<MAMultiPointItem*>* items = (NSArray<MAMultiPointItem*>*) args[@"items"];
        
            // ref
            MAMultiPointOverlay* ref = (MAMultiPointOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMultiPointOverlay* result = [ref initWithMultiPointItems: items];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACustomBuildingOverlayOption::optionWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayOption::optionWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MACustomBuildingOverlayOption* result = [MACustomBuildingOverlayOption optionWithCoordinates: coords count: count];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACustomBuildingOverlayOption::setOptionWithCoordinates_count": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayOption::setOptionWithCoordinates_count(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordsValueList = (NSArray<NSValue*>*) args[@"coords"];
            CLLocationCoordinate2D coords[coordsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordsValueList.count; __i__++) {
                NSValue* coordsValue = (NSValue*) [coordsValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordsItem;
                [coordsValue getValue:&coordsItem];
                coords[__i__] = coordsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
            MACustomBuildingOverlayOption* ref = (MACustomBuildingOverlayOption*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            BOOL result = [ref setOptionWithCoordinates: coords count: count];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MACustomBuildingOverlay::addCustomOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlay::addCustomOption(%@)", args);
            }
        
            // args
            // ref arg
            MACustomBuildingOverlayOption* option = (MACustomBuildingOverlayOption*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
            MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addCustomOption : option];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MACustomBuildingOverlay::removeCustomOption": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlay::removeCustomOption(%@)", args);
            }
        
            // args
            // ref arg
            MACustomBuildingOverlayOption* option = (MACustomBuildingOverlayOption*) (args[@"option"] == [NSNull null] ? nil : args[@"option"]);
        
            // ref
            MACustomBuildingOverlay* ref = (MACustomBuildingOverlay*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref removeCustomOption : option];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAArc::arcWithStartCoordinate_passedCoordinate_endCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAArc::arcWithStartCoordinate_passedCoordinate_endCoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* startCoordinateValue = (NSValue*) args[@"startCoordinate"];
            CLLocationCoordinate2D startCoordinate;
            if (startCoordinateValue != nil && (NSNull*) startCoordinateValue != [NSNull null]) {
              [startCoordinateValue getValue:&startCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"startCoordinate不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* passedCoordinateValue = (NSValue*) args[@"passedCoordinate"];
            CLLocationCoordinate2D passedCoordinate;
            if (passedCoordinateValue != nil && (NSNull*) passedCoordinateValue != [NSNull null]) {
              [passedCoordinateValue getValue:&passedCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"passedCoordinate不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* endCoordinateValue = (NSValue*) args[@"endCoordinate"];
            CLLocationCoordinate2D endCoordinate;
            if (endCoordinateValue != nil && (NSNull*) endCoordinateValue != [NSNull null]) {
              [endCoordinateValue getValue:&endCoordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"endCoordinate不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAArc* result = [MAArc arcWithStartCoordinate: startCoordinate passedCoordinate: passedCoordinate endCoordinate: endCoordinate];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::setRegion_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setRegion_animated(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* regionValue = (NSValue*) args[@"region"];
            MACoordinateRegion region;
            if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
              [regionValue getValue:&region];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"region不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setRegion : region animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::regionThatFits": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::regionThatFits(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* regionValue = (NSValue*) args[@"region"];
            MACoordinateRegion region;
            if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
              [regionValue getValue:&region];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"region不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACoordinateRegion result = [ref regionThatFits: region];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        @"MAMapView::setVisibleMapRect_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_animated(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setVisibleMapRect : mapRect animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::mapRectThatFits": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapRectThatFits(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = [ref mapRectThatFits: mapRect];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMapView::mapRectThatFits_edgePadding": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapRectThatFits_edgePadding(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* insetsValue = (NSValue*) args[@"insets"];
            UIEdgeInsets insets;
            if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
              [insetsValue getValue:&insets];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"insets不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapRect result = [ref mapRectThatFits: mapRect edgePadding: insets];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMapView::setVisibleMapRect_edgePadding_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_edgePadding_animated(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* insetsValue = (NSValue*) args[@"insets"];
            UIEdgeInsets insets;
            if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
              [insetsValue getValue:&insets];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"insets不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setVisibleMapRect : mapRect edgePadding: insets animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setVisibleMapRect_edgePadding_animated_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setVisibleMapRect_edgePadding_animated_duration(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* mapRectValue = (NSValue*) args[@"mapRect"];
            MAMapRect mapRect;
            if (mapRectValue != nil && (NSNull*) mapRectValue != [NSNull null]) {
              [mapRectValue getValue:&mapRect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"mapRect不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* insetsValue = (NSValue*) args[@"insets"];
            UIEdgeInsets insets;
            if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
              [insetsValue getValue:&insets];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"insets不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
            // jsonable arg
            CFTimeInterval duration = [args[@"duration"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setVisibleMapRect : mapRect edgePadding: insets animated: animated duration: duration];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setCenterCoordinate_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCenterCoordinate_animated(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCenterCoordinate : coordinate animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setZoomLevel_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setZoomLevel_animated(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setZoomLevel : zoomLevel animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setZoomLevel_atPivot_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setZoomLevel_atPivot_animated(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
            // struct arg
            NSValue* pivotValue = (NSValue*) args[@"pivot"];
            CGPoint pivot;
            if (pivotValue != nil && (NSNull*) pivotValue != [NSNull null]) {
              [pivotValue getValue:&pivot];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"pivot不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setZoomLevel : zoomLevel atPivot: pivot animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setRotationDegree_animated_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setRotationDegree_animated_duration(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat rotationDegree = [args[@"rotationDegree"] floatValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
            // jsonable arg
            CFTimeInterval duration = [args[@"duration"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setRotationDegree : rotationDegree animated: animated duration: duration];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setCameraDegree_animated_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCameraDegree_animated_duration(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat cameraDegree = [args[@"cameraDegree"] floatValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
            // jsonable arg
            CFTimeInterval duration = [args[@"duration"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCameraDegree : cameraDegree animated: animated duration: duration];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::getMapStatus": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::getMapStatus(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAMapStatus* result = [ref getMapStatus];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::setMapStatus_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setMapStatus_animated(%@)", args);
            }
        
            // args
            // ref arg
            MAMapStatus* status = (MAMapStatus*) (args[@"status"] == [NSNull null] ? nil : args[@"status"]);
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setMapStatus : status animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setMapStatus_animated_duration": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setMapStatus_animated_duration(%@)", args);
            }
        
            // args
            // ref arg
            MAMapStatus* status = (MAMapStatus*) (args[@"status"] == [NSNull null] ? nil : args[@"status"]);
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
            // jsonable arg
            CFTimeInterval duration = [args[@"duration"] doubleValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setMapStatus : status animated: animated duration: duration];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setCompassImage": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCompassImage(%@)", args);
            }
        
            // args
            // ref arg
            UIImage* image = (UIImage*) (args[@"image"] == [NSNull null] ? nil : args[@"image"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCompassImage : image];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::takeSnapshotInRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::takeSnapshotInRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            CGRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            UIImage* result = [ref takeSnapshotInRect: rect];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::takeSnapshotInRect_withCompletionBlock": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::takeSnapshotInRect_withCompletionBlock(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            CGRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref takeSnapshotInRect : rect withCompletionBlock: ^(UIImage* resultImage, NSInteger state) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|UIImage*#resultImage,NSInteger#state::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                UIImage* argresultImage = resultImage;
                // primitive callback arg
                NSNumber* argstate = @(state);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|UIImage*#resultImage,NSInteger#state_" arguments:@{@"resultImage": argresultImage == nil ? [NSNull null] : argresultImage, @"state": argstate == nil ? [NSNull null] : argstate}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::takeSnapshotInRect_timeoutInterval_completionBlock": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::takeSnapshotInRect_timeoutInterval_completionBlock(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            CGRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
            // jsonable arg
            NSTimeInterval timeout = [args[@"timeout"] doubleValue];
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref takeSnapshotInRect : rect timeoutInterval: timeout completionBlock: ^(UIImage* resultImage, NSInteger state) {
                FlutterMethodChannel *channel = [FlutterMethodChannel
                      methodChannelWithName:[NSString stringWithFormat:@"void|UIImage*#resultImage,NSInteger#state::Callback@%@:%@", NSStringFromClass([ref class]), @(ref.hash)]
                            binaryMessenger:[[weakSelf registrar] messenger]
                                      codec:[FlutterStandardMethodCodec codecWithReaderWriter:[[FluttifyReaderWriter alloc] init]]];
        
                // print log
                if (enableLog) {
                    NSLog(@"");
                }
        
                // 构造可以直接传输的参数
                // ref callback arg
                UIImage* argresultImage = resultImage;
                // primitive callback arg
                NSNumber* argstate = @(state);
        
                dispatch_async(dispatch_get_main_queue(), ^{
                  [channel invokeMethod:@"void|UIImage*#resultImage,NSInteger#state_" arguments:@{@"resultImage": argresultImage == nil ? [NSNull null] : argresultImage, @"state": argstate == nil ? [NSNull null] : argstate}];
                });
        
            }];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::metersPerPointForZoomLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::metersPerPointForZoomLevel(%@)", args);
            }
        
            // args
            // jsonable arg
            CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            double result = [ref metersPerPointForZoomLevel: zoomLevel];
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapView::convertCoordinate_toPointToView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertCoordinate_toPointToView(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
            CLLocationCoordinate2D coordinate;
            if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
              [coordinateValue getValue:&coordinate];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"coordinate不能为null"]);
              return;
            }
        
            // ref arg
            UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGPoint result = [ref convertCoordinate: coordinate toPointToView: view];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGPoint)];
        
            methodResult(__result__);
        },
        @"MAMapView::convertPoint_toCoordinateFromView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertPoint_toCoordinateFromView(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            CGPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // ref arg
            UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CLLocationCoordinate2D result = [ref convertPoint: point toCoordinateFromView: view];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CLLocationCoordinate2D)];
        
            methodResult(__result__);
        },
        @"MAMapView::convertRegion_toRectToView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertRegion_toRectToView(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* regionValue = (NSValue*) args[@"region"];
            MACoordinateRegion region;
            if (regionValue != nil && (NSNull*) regionValue != [NSNull null]) {
              [regionValue getValue:&region];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"region不能为null"]);
              return;
            }
        
            // ref arg
            UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            CGRect result = [ref convertRegion: region toRectToView: view];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(CGRect)];
        
            methodResult(__result__);
        },
        @"MAMapView::convertRect_toRegionFromView": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::convertRect_toRegionFromView(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            CGRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
            // ref arg
            UIView* view = (UIView*) (args[@"view"] == [NSNull null] ? nil : args[@"view"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACoordinateRegion result = [ref convertRect: rect toRegionFromView: view];
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MACoordinateRegion)];
        
            methodResult(__result__);
        },
        @"MAMapView::reloadMap": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::reloadMap(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref reloadMap ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::clearDisk": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::clearDisk(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref clearDisk ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::reloadInternalTexture": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::reloadInternalTexture(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref reloadInternalTexture ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::mapContentApprovalNumber": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::mapContentApprovalNumber(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = [ref mapContentApprovalNumber];
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::satelliteImageApprovalNumber": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::satelliteImageApprovalNumber(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = [ref satelliteImageApprovalNumber];
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::terrainApprovalNumber": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::terrainApprovalNumber(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSString* result = [ref terrainApprovalNumber];
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::forceRefresh": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::forceRefresh(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref forceRefresh ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setConstructingRoadEnable": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setConstructingRoadEnable(%@)", args);
            }
        
            // args
            // jsonable arg
            BOOL enabled = [args[@"enabled"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setConstructingRoadEnable : enabled];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::updatePrivacyShow_privacyInfo": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::updatePrivacyShow_privacyInfo(%@)", args);
            }
        
            // args
            // enum arg
            AMapPrivacyShowStatus showStatus = (AMapPrivacyShowStatus) [args[@"showStatus"] integerValue];
            // enum arg
            AMapPrivacyInfoStatus containStatus = (AMapPrivacyInfoStatus) [args[@"containStatus"] integerValue];
        
            // ref
        
        
            // invoke native method
            [MAMapView updatePrivacyShow: showStatus privacyInfo: containStatus];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::updatePrivacyAgree": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::updatePrivacyAgree(%@)", args);
            }
        
            // args
            // enum arg
            AMapPrivacyAgreeStatus agreeStatus = (AMapPrivacyAgreeStatus) [args[@"agreeStatus"] integerValue];
        
            // ref
        
        
            // invoke native method
            [MAMapView updatePrivacyAgree: agreeStatus];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::addAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addAnnotation(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addAnnotation : annotation];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::addAnnotations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addAnnotations(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* annotations = (NSArray<NSObject*>*) args[@"annotations"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addAnnotations : annotations];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::removeAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::removeAnnotation(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref removeAnnotation : annotation];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::removeAnnotations": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::removeAnnotations(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* annotations = (NSArray<NSObject*>*) args[@"annotations"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref removeAnnotations : annotations];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::viewForAnnotation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::viewForAnnotation(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAAnnotationView* result = [ref viewForAnnotation: annotation];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::dequeueReusableAnnotationViewWithIdentifier": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::dequeueReusableAnnotationViewWithIdentifier(%@)", args);
            }
        
            // args
            // jsonable arg
            NSString* identifier = (NSString*) args[@"identifier"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAAnnotationView* result = [ref dequeueReusableAnnotationViewWithIdentifier: identifier];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::selectAnnotation_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::selectAnnotation_animated(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref selectAnnotation : annotation animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::deselectAnnotation_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::deselectAnnotation_animated(%@)", args);
            }
        
            // args
            // ref arg
            id<MAAnnotation> annotation = (id<MAAnnotation>) (args[@"annotation"] == [NSNull null] ? nil : args[@"annotation"]);
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref deselectAnnotation : annotation animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::showAnnotations_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::showAnnotations_animated(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* annotations = (NSArray<NSObject*>*) args[@"annotations"];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref showAnnotations : annotations animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::showAnnotations_edgePadding_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::showAnnotations_edgePadding_animated(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* annotations = (NSArray<NSObject*>*) args[@"annotations"];
            // struct arg
            NSValue* insetsValue = (NSValue*) args[@"insets"];
            UIEdgeInsets insets;
            if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
              [insetsValue getValue:&insets];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"insets不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref showAnnotations : annotations edgePadding: insets animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setUserTrackingMode_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setUserTrackingMode_animated(%@)", args);
            }
        
            // args
            // enum arg
            MAUserTrackingMode mode = (MAUserTrackingMode) [args[@"mode"] integerValue];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setUserTrackingMode : mode animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::updateUserLocationRepresentation": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::updateUserLocationRepresentation(%@)", args);
            }
        
            // args
            // ref arg
            MAUserLocationRepresentation* representation = (MAUserLocationRepresentation*) (args[@"representation"] == [NSNull null] ? nil : args[@"representation"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref updateUserLocationRepresentation : representation];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::overlaysInLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::overlaysInLevel(%@)", args);
            }
        
            // args
            // enum arg
            MAOverlayLevel level = (MAOverlayLevel) [args[@"level"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = [ref overlaysInLevel: level];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::addOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addOverlay : overlay];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::addOverlays": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addOverlays(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* overlays = (NSArray<NSObject*>*) args[@"overlays"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addOverlays : overlays];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::addOverlay_level": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addOverlay_level(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
            // enum arg
            MAOverlayLevel level = (MAOverlayLevel) [args[@"level"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addOverlay : overlay level: level];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::addOverlays_level": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::addOverlays_level(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* overlays = (NSArray<NSObject*>*) args[@"overlays"];
            // enum arg
            MAOverlayLevel level = (MAOverlayLevel) [args[@"level"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref addOverlays : overlays level: level];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::removeOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::removeOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref removeOverlay : overlay];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::removeOverlays": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::removeOverlays(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* overlays = (NSArray<NSObject*>*) args[@"overlays"];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref removeOverlays : overlays];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::insertOverlay_atIndex_level": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::insertOverlay_atIndex_level(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
            // jsonable arg
            NSUInteger index = [args[@"index"] unsignedIntegerValue];
            // enum arg
            MAOverlayLevel level = (MAOverlayLevel) [args[@"level"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref insertOverlay : overlay atIndex: index level: level];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::insertOverlay_aboveOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::insertOverlay_aboveOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
            // ref arg
            id<MAOverlay> sibling = (id<MAOverlay>) (args[@"sibling"] == [NSNull null] ? nil : args[@"sibling"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref insertOverlay : overlay aboveOverlay: sibling];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::insertOverlay_belowOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::insertOverlay_belowOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
            // ref arg
            id<MAOverlay> sibling = (id<MAOverlay>) (args[@"sibling"] == [NSNull null] ? nil : args[@"sibling"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref insertOverlay : overlay belowOverlay: sibling];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::insertOverlay_atIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::insertOverlay_atIndex(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
            // jsonable arg
            NSUInteger index = [args[@"index"] unsignedIntegerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref insertOverlay : overlay atIndex: index];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::exchangeOverlayAtIndex_withOverlayAtIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::exchangeOverlayAtIndex_withOverlayAtIndex(%@)", args);
            }
        
            // args
            // jsonable arg
            NSUInteger index1 = [args[@"index1"] unsignedIntegerValue];
            // jsonable arg
            NSUInteger index2 = [args[@"index2"] unsignedIntegerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref exchangeOverlayAtIndex : index1 withOverlayAtIndex: index2];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::exchangeOverlayAtIndex_withOverlayAtIndex_atLevel": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::exchangeOverlayAtIndex_withOverlayAtIndex_atLevel(%@)", args);
            }
        
            // args
            // jsonable arg
            NSUInteger index1 = [args[@"index1"] unsignedIntegerValue];
            // jsonable arg
            NSUInteger index2 = [args[@"index2"] unsignedIntegerValue];
            // enum arg
            MAOverlayLevel level = (MAOverlayLevel) [args[@"level"] integerValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref exchangeOverlayAtIndex : index1 withOverlayAtIndex: index2 atLevel: level];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::exchangeOverlay_withOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::exchangeOverlay_withOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay1 = (id<MAOverlay>) (args[@"overlay1"] == [NSNull null] ? nil : args[@"overlay1"]);
            // ref arg
            id<MAOverlay> overlay2 = (id<MAOverlay>) (args[@"overlay2"] == [NSNull null] ? nil : args[@"overlay2"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref exchangeOverlay : overlay1 withOverlay: overlay2];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::rendererForOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::rendererForOverlay(%@)", args);
            }
        
            // args
            // ref arg
            id<MAOverlay> overlay = (id<MAOverlay>) (args[@"overlay"] == [NSNull null] ? nil : args[@"overlay"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAOverlayRenderer* result = [ref rendererForOverlay: overlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::showOverlays_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::showOverlays_animated(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* overlays = (NSArray<NSObject*>*) args[@"overlays"];
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref showOverlays : overlays animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::showOverlays_edgePadding_animated": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::showOverlays_edgePadding_animated(%@)", args);
            }
        
            // args
            // list arg
            NSArray<NSObject*>* overlays = (NSArray<NSObject*>*) args[@"overlays"];
            // struct arg
            NSValue* insetsValue = (NSValue*) args[@"insets"];
            UIEdgeInsets insets;
            if (insetsValue != nil && (NSNull*) insetsValue != [NSNull null]) {
              [insetsValue getValue:&insets];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"insets不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL animated = [args[@"animated"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref showOverlays : overlays edgePadding: insets animated: animated];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::getHittedPolylinesWith_traverseAll": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::getHittedPolylinesWith_traverseAll(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* tappedCoordValue = (NSValue*) args[@"tappedCoord"];
            CLLocationCoordinate2D tappedCoord;
            if (tappedCoordValue != nil && (NSNull*) tappedCoordValue != [NSNull null]) {
              [tappedCoordValue getValue:&tappedCoord];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"tappedCoord不能为null"]);
              return;
            }
        
            // jsonable arg
            BOOL traverseAll = [args[@"traverseAll"] boolValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            NSArray* result = [ref getHittedPolylinesWith: tappedCoord traverseAll: traverseAll];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAMapView::setIndoorMapControlOrigin": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setIndoorMapControlOrigin(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* originValue = (NSValue*) args[@"origin"];
            CGPoint origin;
            if (originValue != nil && (NSNull*) originValue != [NSNull null]) {
              [originValue getValue:&origin];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"origin不能为null"]);
              return;
            }
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setIndoorMapControlOrigin : origin];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setCurrentIndoorMapFloorIndex": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCurrentIndoorMapFloorIndex(%@)", args);
            }
        
            // args
            // jsonable arg
            NSInteger floorIndex = [args[@"floorIndex"] longValue];
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCurrentIndoorMapFloorIndex : floorIndex];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::clearIndoorMapCache": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::clearIndoorMapCache(%@)", args);
            }
        
            // args
        
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref clearIndoorMapCache ];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAMapView::setCustomMapStyleOptions": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapView::setCustomMapStyleOptions(%@)", args);
            }
        
            // args
            // ref arg
            MAMapCustomStyleOptions* styleOptions = (MAMapCustomStyleOptions*) (args[@"styleOptions"] == [NSNull null] ? nil : args[@"styleOptions"]);
        
            // ref
            MAMapView* ref = (MAMapView*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            [ref setCustomMapStyleOptions : styleOptions];
        
            // result
            // 无返回值
            NSString* __result__ = @"success";
        
            methodResult(__result__);
        },
        @"MAGroundOverlayRenderer::initWithGroundOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlayRenderer::initWithGroundOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MAGroundOverlay* groundOverlay = (MAGroundOverlay*) (args[@"groundOverlay"] == [NSNull null] ? nil : args[@"groundOverlay"]);
        
            // ref
            MAGroundOverlayRenderer* ref = (MAGroundOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MAGroundOverlayRenderer* result = [ref initWithGroundOverlay: groundOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MACustomBuildingOverlayRenderer::initWithCustomBuildingOverlay": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACustomBuildingOverlayRenderer::initWithCustomBuildingOverlay(%@)", args);
            }
        
            // args
            // ref arg
            MACustomBuildingOverlay* customBuildingOverlay = (MACustomBuildingOverlay*) (args[@"customBuildingOverlay"] == [NSNull null] ? nil : args[@"customBuildingOverlay"]);
        
            // ref
            MACustomBuildingOverlayRenderer* ref = (MACustomBuildingOverlayRenderer*) args[@"__this__"];
            if ((NSNull *) ref == [NSNull null] || ref == nil) {
                methodResult([FlutterError errorWithCode:@"目标对象为nil" message:@"目标对象为nil" details:@"目标对象为nil"]);
                return;
            }
        
            // invoke native method
            MACustomBuildingOverlayRenderer* result = [ref initWithCustomBuildingOverlay: customBuildingOverlay];
        
            // result
            // return a ref
            NSObject* __result__ = result;
        
            methodResult(__result__);
        },
        @"MAGroundOverlay::groundOverlayWithBounds_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::groundOverlayWithBounds_icon(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* boundsValue = (NSValue*) args[@"bounds"];
                MACoordinateBounds bounds;
                if (boundsValue != nil && (NSNull*) boundsValue != [NSNull null]) {
                  [boundsValue getValue:&bounds];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"bounds不能为null"]);
                  return;
                }
        
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
        
        
                // invoke native method
                MAGroundOverlay* result = [MAGroundOverlay groundOverlayWithBounds: bounds icon: icon];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAGroundOverlay::groundOverlayWithCoordinate_zoomLevel_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::groundOverlayWithCoordinate_zoomLevel_icon(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
        
        
                // invoke native method
                MAGroundOverlay* result = [MAGroundOverlay groundOverlayWithCoordinate: coordinate zoomLevel: zoomLevel icon: icon];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAGroundOverlay::setGroundOverlayWithBounds_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::setGroundOverlayWithBounds_icon(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* boundsValue = (NSValue*) args[@"bounds"];
                MACoordinateBounds bounds;
                if (boundsValue != nil && (NSNull*) boundsValue != [NSNull null]) {
                  [boundsValue getValue:&bounds];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"bounds不能为null"]);
                  return;
                }
        
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setGroundOverlayWithBounds: bounds icon: icon];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAGroundOverlay::setGroundOverlayWithCoordinate_zoomLevel_icon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGroundOverlay::setGroundOverlayWithCoordinate_zoomLevel_icon(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // struct arg
                NSValue* coordinateValue = (NSValue*) args[@"coordinate"];
                CLLocationCoordinate2D coordinate;
                if (coordinateValue != nil && (NSNull*) coordinateValue != [NSNull null]) {
                  [coordinateValue getValue:&coordinate];
                } else {
                  methodResult([FlutterError errorWithCode:@"参数非法"
                                                   message:@"参数非法"
                                                   details:@"coordinate不能为null"]);
                  return;
                }
        
                // jsonable arg
                CGFloat zoomLevel = [args[@"zoomLevel"] floatValue];
                // ref arg
                UIImage* icon = (UIImage*) (args[@"icon"] == [NSNull null] ? nil : args[@"icon"]);
        
                // ref
                MAGroundOverlay* ref = (MAGroundOverlay*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                BOOL result = [ref setGroundOverlayWithCoordinate: coordinate zoomLevel: zoomLevel icon: icon];
        
                // result
                // 返回值: Value
                NSObject* __result__ = @(result);
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
        @"MAPolygonRenderer::initWithPolygon_batch": ^(NSObject <FlutterPluginRegistrar> * registrar, id argsBatch, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygonRenderer::initWithPolygon(%@)", argsBatch);
            }
        
            NSMutableArray* resultList = [NSMutableArray array];
        
            for (NSUInteger __i__ = 0; __i__ < ((NSArray<NSDictionary<NSString*, NSObject*>*>*) argsBatch).count; __i__++) {
                NSDictionary<NSString*, id>* args = [((NSArray<NSDictionary<NSString*, id>*>*) argsBatch) objectAtIndex:__i__];
        
                // args
                // ref arg
                MAPolygon* polygon = (MAPolygon*) (args[@"polygon"] == [NSNull null] ? nil : args[@"polygon"]);
        
                // ref
                MAPolygonRenderer* ref = (MAPolygonRenderer*) args[@"__this__"];
                // 批处理过程中出现nil引用则直接添加nil进结果列表, 然后进行下一次循环
                if ((NSNull *) ref == [NSNull null] || ref == nil) {
                    [resultList addObject: [NSNull null]];
                    continue;
                }
        
                // invoke native method
                MAPolygonRenderer* result = [ref initWithPolygon: polygon];
        
                // result
                // return a ref
                NSObject* __result__ = result;
        
                [resultList addObject:__result__ == nil ? [NSNull null] : __result__];
            }
        
            methodResult(resultList);
        },
    };
}

@end
