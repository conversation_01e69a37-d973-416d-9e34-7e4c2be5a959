/// 全国分组列表数据模型
class GroupListBean {
  int? code;
  GroupListData? data;
  String? msg;

  GroupListBean({this.code, this.data, this.msg});

  GroupListBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? GroupListData.fromJson(json['data']) : null;
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['msg'] = msg;
    return data;
  }
}

class GroupListData {
  List<GroupItem>? list;
  int? total;
  String? rest;

  GroupListData({this.list, this.total, this.rest});

  GroupListData.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <GroupItem>[];
      json['list'].forEach((v) {
        list!.add(GroupItem.fromJson(v));
      });
    }
    total = json['total'];
    rest = json['rest'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (list != null) {
      data['list'] = list!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    data['rest'] = rest;
    return data;
  }
}

class GroupItem {
  String? groupId;
  String? sysCode;
  String? groupName;
  String? memo;

  GroupItem({
    this.groupId,
    this.sysCode,
    this.groupName,
    this.memo,
  });

  GroupItem.fromJson(Map<String, dynamic> json) {
    groupId = json['groupId'];
    sysCode = json['sysCode'];
    groupName = json['groupName'];
    memo = json['memo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['groupId'] = groupId;
    data['sysCode'] = sysCode;
    data['groupName'] = groupName;
    data['memo'] = memo;
    return data;
  }
}
