//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import <Flutter/Flutter.h>

typedef void (^Handler)(NSObject <FlutterPluginRegistrar> *, id, FlutterResult);

@interface AmapMapFluttifyPlugin : NSObject<FlutterPlugin>

- (instancetype) initWithFlutterPluginRegistrar: (NSObject <FlutterPluginRegistrar> *) registrar;

@property(nonatomic) NSObject<FlutterPluginRegistrar>* registrar;

@end
