import 'dart:async';
import 'dart:io';

import 'package:amap_map_fluttify/amap_map_fluttify.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/resource/image_resource.dart';
import 'package:fuduoduo/route/index.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import 'package:fuduoduo/widget/ImageLoad.dart';
import 'package:fuduoduo/widget/single_image_upload_view.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vector_math/vector_math_64.dart' as v;

import '../../../provider/event/bind_sync_event.dart';
import '../../../utils/set_info.dart';
import '../../homePages/map_help/mapFluttify.dart';
import '../../../widget/photo_preview_view.dart';

import 'logic.dart';
import '../../../utils/open_third_url_utils.dart';
// const mapAndroidKey = '1e52b87bea82b1bde70e22639985b79c';
// const mapIOSKey = '3e04b9ed4440ee05b7cd3b9b9ea87e74';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class B2bCustomerInfoPage extends StatefulWidget {
  const B2bCustomerInfoPage({super.key});

  @override
  State<B2bCustomerInfoPage> createState() => B2bCustomerInfoPageState();
}

class B2bCustomerInfoPageState extends State<B2bCustomerInfoPage> {
  final logic = Get.put(B2bCustomerInfoPageLogic());
  final state = Get.find<B2bCustomerInfoPageLogic>().state;
  AmapController? _controller;
  LatLng? _latLng;
  Location? _location;
  String _userAddress = "";
  StateSetter? _setDiaLogState;
  double _distance = 0; // 门店与用户定位地址距离
  bool _isEffectiveDistance = false; // 是否是有效距离
  bool _isEditAddress = false; // 是否覆盖定位;
  String _signInImgUrls = ""; // 签到门头照片

  StreamSubscription? _bindSyncSubscription;

  @override
  void initState() {
    super.initState();
    // 获取获取定位权限
    requestPermission();
    // 获取门店详细信息
    logic.getBranchInfo(showSignIn, context);

    _bindSyncSubscription =
        bindSyncEventBus.on<BindSyncEvent>().listen((event) {
      logic.getkefuInfo();
    });
  }

  _getPoint() async {
    await AmapLocation.instance.updatePrivacyAgree(true);
    await AmapLocation.instance.updatePrivacyShow(true);
    await AmapLocation.instance.init(iosKey: mapIOSKey);
    _location =
        await AmapLocation.instance.fetchLocation(mode: LocationAccuracy.High);
    await Future.delayed(const Duration(milliseconds: 200));
    await _controller?.setCenterCoordinate(_location!.latLng!);
    if (null != _location!.latLng) {
      _latLng = _location!.latLng!;
      _mapAddMarker(_latLng!);

      dynamic consumerInfo = state.consumerInfo;
      if (consumerInfo['longitude'] != null &&
          consumerInfo['latitude'] != null) {
        print(consumerInfo['latitude']);
        String latitude = consumerInfo['latitude'].toString();
        String longitude = consumerInfo['longitude'].toString();
        LatLng consumerLatLng = LatLng(
            double.tryParse(latitude) ?? 0, double.tryParse(longitude) ?? 0);
        print('用户坐标');
        print(_latLng);
        print('门店坐标');
        print(consumerLatLng);
        double distance = logic.calculateDistance(_latLng!, consumerLatLng);
        print('距离${distance}');
        _setDiaLogState!(() {
          _distance = distance;
          _isEffectiveDistance = distance <= state.effectiveMaxDistance;
        });
      }
      logic.updateUserAddress(_location);
      _setDiaLogState!(() {
        _userAddress = _location!.address.toString();
      });
    }
  }

  void backCustomerListPage() {
    Get.back();
    Get.until((route) {
      return route.settings.name?.contains(PageName.CustomerListPage) == true;
    });
  }

  void requestPermission() async {
    // 申请权限
    if (Platform.isAndroid) {
      if (await Permission.location.request().isGranted) {
        if (_controller != null) {
          _getPoint();
        } else {
          await Future.delayed(const Duration(milliseconds: 1000));
          _getPoint();
        }
      } else {
        MyCommonUtils.showToast("请打开定位");
        backCustomerListPage();
      }
    } else {
      if (await Permission.location.request().isGranted ||
          await Permission.locationAlways.request().isGranted ||
          await Permission.locationWhenInUse.request().isGranted) {
        if (_controller != null) _getPoint();
      } else {
        MyCommonUtils.showToast("请打开定位");
        backCustomerListPage();
      }
    }
  }

  _mapAddMarker(LatLng latLng) async {
    await _controller?.clear();
    var marker = MarkerOption(
        coordinate: _latLng!,
        visible: true,
        infoWindowEnabled: true,
        widget: const Icon(Icons.location_on_outlined, size: 24));
    if (null != _controller) {
      await _controller?.addMarker(marker);
    }
    if (mounted) setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: WillPopScope(
      onWillPop: () async {
        /**返回 true 和不实现onWillPop一样，自动返回,
             *返回 false route不再响应物理返回事件，拦截返回事件自行处理
             */
        bool isBack = true;
        if (state.status == 'signOut') {
          isBack = false;
          await showDialog(
              context: context,
              builder: (BuildContext context) {
                return Dialog(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(15))),
                  child: SingleChildScrollView(
                    child: Container(
                      width: 500.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(15)),
                      ),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 40.w,
                          ),
                          Text('保留本次拜访?'),
                          Container(
                            width: 400.w,
                            alignment: Alignment.center,
                            padding: EdgeInsets.only(top: 40.w, bottom: 40.w),
                            child: Text(
                              '保留后需要完成本次客户拜访,才能拜访其他客户',
                              overflow: TextOverflow.fade,
                              style:
                                  TextStyle(fontSize: 24.w, color: Colors.grey),
                            ),
                          ),
                          Container(
                            height: 2.w,
                            color: Colors.grey,
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: InkWell(
                                onTap: () {
                                  logic.waive();
                                  isBack = true;
                                  Get.back();
                                },
                                child: Container(
                                  height: 80.w,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      border: Border(
                                          right: BorderSide(
                                              width: 2.w, color: Colors.grey))),
                                  child: Text('放弃拜访'),
                                ),
                              )),
                              Expanded(
                                  child: InkWell(
                                onTap: () {
                                  isBack = false;
                                  Navigator.pop(context);
                                  Get.back();
                                },
                                child: Container(
                                  height: 80.w,
                                  alignment: Alignment.center,
                                  child: Text(
                                    '保留',
                                    style: TextStyle(
                                        color: Color.fromRGBO(229, 40, 39, 1)),
                                  ),
                                ),
                              ))
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                );
              });
        }

        return isBack;
      },
      child: SingleChildScrollView(
        child: GetBuilder<B2bCustomerInfoPageLogic>(
            builder: (_) => Column(
                  children: [_HeaderBox(context), _ContentBox(context)],
                )),
      ),
    ));
  }

  // 内容
  _ContentBox(BuildContext context) {
    /// 切换环境
    var times = 0;
    var _lastPressedAt = DateTime.now();
    List<Color> _color = ColorResource.LINEAR_GRADIENT_COMMON_COLOR;
    return Container(
      transform: Matrix4.translation(v.Vector3(0.0, -20.0.w, 0.0)),
      decoration: BoxDecoration(
          color: Color.fromRGBO(250, 250, 250, 1),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.w), topRight: Radius.circular(20.w))),
      child: Column(
        children: [
          // Container(
          //   height: 80.w,
          //   padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
          //   decoration: BoxDecoration(
          //       color: Color.fromRGBO(255, 251, 232, 1),
          //       borderRadius: BorderRadius.only(
          //           topLeft: Radius.circular(20.w),
          //           topRight: Radius.circular(20.w))),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     crossAxisAlignment: CrossAxisAlignment.center,
          //     children: [
          //       Container(
          //         child: Row(
          //           crossAxisAlignment: CrossAxisAlignment.center,
          //           children: [
          //             Icon(
          //               Icons.error,
          //               size: 30.w,
          //               color: Color.fromRGBO(239, 143, 65, 1),
          //             ),
          //             SizedBox(
          //               width: 10.w,
          //             ),
          //             Text(
          //               '定位错误，未显示经纬度，请立即修改',
          //               style: TextStyle(fontSize: 20.w),
          //             )
          //           ],
          //         ),
          //       ),
          //       Icon(
          //         Icons.chevron_right,
          //         size: 30.w,
          //         color: Colors.black,
          //       )
          //     ],
          //   ),
          // ),
          Container(
            padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
            child: Column(
              children: [
                SizedBox(
                  height: 30.w,
                ),
                // Row(
                //   children: [
                //     Expanded(
                //         child: Column(
                //       children: [
                //         Text('欠款金额',
                //             style:
                //                 TextStyle(fontSize: 26.w, color: Colors.grey)),
                //         SizedBox(height: 10.w),
                //         Row(
                //             mainAxisAlignment: MainAxisAlignment.center,
                //             children: [
                //               Text('￥',
                //                   style: TextStyle(
                //                       fontSize: 13.w, color: Colors.red)),
                //               Text(
                //                   "${(state.consumerInfo['debtAmount'] ?? 0).toStringAsFixed(2)}",
                //                   style: TextStyle(
                //                       fontSize: 26.w, color: Colors.red))
                //             ])
                //       ],
                //     )),
                //     Expanded(
                //         child: Column(
                //       children: [
                //         Text('距上次拜访',
                //             style:
                //                 TextStyle(fontSize: 26.w, color: Colors.grey)),
                //         SizedBox(height: 10.w),
                //         Text("${state.consumerInfo['lastVisitDay'] ?? 0}天",
                //             style: TextStyle(fontSize: 26.w))
                //       ],
                //     )),
                //     Expanded(
                //         child: Column(
                //       children: [
                //         Text('距上次订货',
                //             style:
                //                 TextStyle(fontSize: 26.w, color: Colors.grey)),
                //         SizedBox(height: 10.w),
                //         Text("${state.consumerInfo['lastActivityDay'] ?? 0}天",
                //             style: TextStyle(fontSize: 26.w))
                //       ],
                //     ))
                //   ],
                // ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          margin: EdgeInsets.fromLTRB(0, 50.w, 10.w, 40.w),
                          width: 6.w,
                          height: 20.w,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(3.w),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            if (DateTime.now().difference(_lastPressedAt) >
                                const Duration(seconds: 1)) {
                              //两次点击间隔超过1秒则重新计时
                              _lastPressedAt = DateTime.now();
                              times = 0;
                            } else {
                              times++;
                              if (times >= 4) {
                                _lastPressedAt = DateTime.now();
                                SetInfo.instance.changeWebviewUrl();
                              }
                            }
                          },
                          child: Text(
                            '工作台',
                            style:
                                TextStyle(fontSize: 26.w, color: Colors.black),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Visibility(
                          visible: state.status == 'signIn',
                          child: InkWell(
                            onTap: () {
                              showSignIn(context, 'in');
                            },
                            child: Container(
                              height: 50.w,
                              width: 140.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  gradient: LinearGradient(colors: _color),
                                  borderRadius: BorderRadius.circular(25.w)),
                              child: Text('签到',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 26.w)),
                            ),
                          ),
                        ),
                        Visibility(
                          visible: state.status == 'signOut',
                          child: InkWell(
                            onTap: () {
                              showSignIn(context, 'out');
                            },
                            child: Container(
                              height: 50.w,
                              width: 140.w,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  gradient: LinearGradient(colors: _color),
                                  borderRadius: BorderRadius.circular(25.w)),
                              child: Text('签退',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 26.w)),
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),

                _BtnList(context),
              ],
            ),
          )
        ],
      ),
    );
  }

  _BtnList(BuildContext context) {
    var branchNo = SpUtil.getString('branchNo');
    var branchType = SpUtil.getString('branchType');

    List<Widget> _initListData() {
      var tempList = [
        // b2b
        {
          "name": "查看客户购物车",
          "path": PageName.StoreCartsPage,
          "icon": ImageResource.ICON_B2B_CART,
          "show": UserAppModuleUtils().moduleCanShow("show_carts"),
        },
        {
          "name": "发放优惠券",
          "path": PageName.B2bCouponPage,
          "icon": ImageResource.ICON_B2B_COUPON,
          "show": UserAppModuleUtils().moduleCanShow("show_coupon"),
        },
        {
          "name": "管理门店用户",
          "path": PageName.CustomerUserPage,
          "icon": ImageResource.ICON_B2B_USERS,
          "show": UserAppModuleUtils().moduleCanShow("store_users"),
        },


        // b2b

        {
          "name": "车销订货",
          "path": PageName.VehicleOrderPage,
          "icon": ImageResource.ICON_CAR_SALE,
          "show": branchNo != '001' &&
              UserAppModuleUtils().moduleCanShow("car_sale_order_goods"),
        },
        {
          "name": "访销订货",
          "path": PageName.VehicleOrderPage,
          "icon": ImageResource.ICON_VISIT_SALE,
          "show": (branchType == '1' || branchType == '4') &&
              UserAppModuleUtils().moduleCanShow("access_sale_order_goods")
        },
        {
          "name": "售后退货",
          "path": PageName.VehicleOrderPage,
          "icon": ImageResource.ICON_AFTERSALE_RETURN,
          "show": UserAppModuleUtils()
              .moduleCanShow("after_sales_returned_purchase"),
          "paramete": {"openType": "TH"}
        },
        {
          "name": "售后换货",
          "path": PageName.VehicleOrderPage,
          "icon": ImageResource.ICON_AFTERSALE_EXCHANGE,
          "show":
              UserAppModuleUtils().moduleCanShow("after_sales_exchange_goods"),
          "paramete": {"openType": "HH"}
        },
        {
          "name": "订单查询",
          "path": PageName.OrderQueryListPage,
          "icon": ImageResource.ICON_ORDER_QUERY,
          "show": UserAppModuleUtils().moduleCanShow("order_find"),
        },
        {
          "name": "订货会",
          "path": PageName.OrderMeetingListPage,
          "icon": ImageResource.ICON_ORDER_MEETING,
          "show": UserAppModuleUtils().moduleCanShow("order_goods_see"),
        },
        {
          "name": "费用管理",
          "path": PageName.CostAdministrationPage,
          "icon": ImageResource.ICON_COST_MANAGEMENT,
          "show": UserAppModuleUtils().moduleCanShow("cost_management"),
        },
        {
          "name": "陈列管理",
          "path": PageName.DisplayManagePage,
          "icon": ImageResource.ICON_DISPALY_MANAGEMENT,
          "show": UserAppModuleUtils().moduleCanShow("cost_management"),
        },
        {
          "name": "设备管理",
          "path": PageName.DeviceAdministrationListPage,
          "icon": ImageResource.ICON_DEVICE_MANAGEMENT,
          "show": UserAppModuleUtils().moduleCanShow("equipment_management"),
        },
        // {
        //   "name": "云商绑定",
        //   "path": "",
        //   "icon": ImageResource.ICON_CLOUD_BINDING,
        //   "show": UserAppModuleUtils().moduleCanShow("cloud_business_binding"),
        // },
        // {
        //   "name": "预收款",
        //   "path": PageName.AdvancePaymentListPage,
        //   "icon": ImageResource.ICON_ADVANCE_PAYMENT,
        //   "show": UserAppModuleUtils()
        //       .moduleCanShow("equipment_management"), //tallying_register
        // },
        {
          "name": "理货拍照",
          "path": PageName.TallyPhotoListPage,
          "icon": ImageResource.ICON_TALLY_PHOTO,
          "show": UserAppModuleUtils()
              .moduleCanShow("tallying_register"), //tallying_register
        },
        // {
        //   "name": "代客下单",
        //   "path": PageName.CommonWebViewPage,
        //   "icon": ImageResource.ICON_PLACE_ORDER,
        //   "show":
        //       UserAppModuleUtils().moduleCanShow('place_order'), //place_order
        // },
        // {
        //   "name": "处理品销售",
        //   "path": PageName.DisposalGoodsSalePage,
        //   "icon": ImageResource.ICON_DEAL_SALLE,
        //   "show": true, //place_order
        // },
        // {
        //   "name": "兑换",
        //   "path": PageName.ExchangeInfo,
        //   "icon": ImageResource.ICON_Exchange,
        //   "show": true,
        // },
      ].map((e) {
        var isShow = e['show'] == null ? true : e['show'];
        dynamic paramete = e['paramete'] ?? {};
        Map<String, String> parameters = {};
        parameters['branchNo'] = SpUtil.getString('branchNo')!;
        parameters['consumerNo'] = state.consumerNo;
        parameters['consumerName'] = state.consumerName;
        parameters['consumerAddress'] = state.consumerAddress;
        paramete.forEach((keys, value) {
          parameters[keys] = value;
        });
        // GestureDetector
        return Visibility(
            visible: isShow == true,
            child: InkWell(
              onTap: () {
                if ("${e['name']}" == "云商绑定") {
                  // logic.toCommonScanPage();
                  if (SpUtil.getBool("StartFlag") == false) {
                    MyCommonUtils.showToast("账户已停用，暂不支持绑定");
                    return;
                  }
                  if (SpUtil.getBool("CloudFlag") == false) {
                    MyCommonUtils.showToast("经销商未开通同福云商，暂不支持绑定");
                    return;
                  }
                  Get.toNamed(PageName.ShopBindingPage,
                          arguments: {"consumerInfo": state.consumerInfo})
                      ?.then((value) => {logic.getkefuInfo()});
                } else if ("${e['name']}" == "代客下单") {
                  if (state.consumerInfo["externalCode"] != null &&
                      state.consumerInfo["externalCode"]
                          .toString()
                          .isNotEmpty) {
                    logic.getPlaceOrderListUrl();
                  } else {
                    MyCommonUtils.showToast("暂不可用，请先完成云商绑定");
                  }
                } else {
                  Get.toNamed("${e['path']}", parameters: parameters);
                }
              },
              child: Stack(
                children: [
                  Container(
                    padding: EdgeInsets.only(top: 20.h),
                    width: (MediaQuery.of(context).size.width * 0.9 - 40.w) / 3, // 占盒子宽度的1/3
                    child: Column(
                        children: [
                          ExtendedImage.asset(
                            "${e["icon"]}",
                            width: 60.w,
                            height: 60.h,
                          ),
                          SizedBox(height: 16.w),
                          Text(
                            "${e["name"]}",
                            style: TextStyle(fontSize: 24.w),
                          )
                        ]
                    ),
                  ),
                  e["name"] == '管理门店用户' && state.userAcountNum == 0 ?
                  Positioned(
                    top: 20.h,  // distance from bottom
                    right: 10.w,
                    child: Transform.rotate(
                      angle: 45 * 3.14159 / 180,  // Rotate 45 degrees (converted to radians)
                      child: TDTag(
                        '未注册账号',
                        isLight: true,
                        theme: TDTagTheme.danger,
                        size: TDTagSize.small
                      ),
                    )// distance from right

                  )  :
                    Offstage(offstage: true, child: Text('')),
                ],
              )


            ));
      });
      return tempList.toList();
    }

    return Center(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9, // 设置宽度为屏幕宽度的90%
        constraints: BoxConstraints(minHeight: 360.h), // 设置最小高度为300.h
        margin: EdgeInsets.symmetric(vertical: 16.h),
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Wrap(
              runSpacing: 40.w,
              children: _initListData(),
            ),
          ],
        ),
      ),
    );
  }

  // 分类模块
  _HeaderBox(BuildContext context) {
    // List<Color> _color = [
    //   Color.fromRGBO(255, 112, 29, 1),
    //   Color.fromRGBO(255, 41, 29, 1)
    // ];
    List<Color> _color = ColorResource.LINEAR_GRADIENT_COMMON_COLOR;
    return Row(
      children: [
        Expanded(
            child: Container(
          padding: EdgeInsets.fromLTRB(0, 0, 0, 30.w),
          decoration: BoxDecoration(gradient: LinearGradient(colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppBar(
                  title: Text("${state.consumerInfo['consumerName'] ?? ''}"),
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  centerTitle: true,
                  foregroundColor: Colors.white,
                  leading: IconButton(
                      onPressed: () {
                        // Get.back();

                        // Get.toNamed(PageName.B2bCustomerListPage);
                        Get.offAllNamed(PageName.B2bCustomerListPage);
                      },
                      icon: Icon(Icons.arrow_back_ios_rounded,
                          color: ColorResource.WHITE_COMMON_COLOR)),
                  actions: [
                    // InkWell(
                    //   onTap: () {
                    //     Get.toNamed(PageName.B2bStoreInfoPage, arguments: {
                    //       "type": "edit",
                    //       "from": "customerInfo",
                    //       "customerInfo": state.consumerInfo
                    //     })?.then((result) {
                    //       if (result != null && result == true) {
                    //         logic.getBranchInfo(false, false);
                    //       }
                    //     });
                    //   },
                    //   child: Container(
                    //     height: 88.w,
                    //     alignment: Alignment.center,
                    //     padding: EdgeInsets.only(right: 32.w),
                    //     child: Icon(
                    //       Icons.settings,
                    //       size: 34.w,
                    //       color: Colors.white,
                    //     ),
                    //   ),
                    // )
                  ]),
              Container(
                padding: EdgeInsets.fromLTRB(20.w, 20.w, 20.w, 20.w),
                // decoration: BoxDecoration(
                //     // color: Color.fromRGBO(250, 250, 250, 1),
                //     borderRadius: BorderRadius.only(
                //         bottomLeft: Radius.circular(20.w), bottomRight: Radius.circular(20.w))),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // ImageLoad.loadWidget(
                        //     state.consumerInfo["imgUrls"] ?? "",
                        //     size: 120),

                        InkWell(
                          onTap: () {

                            if (state.consumerInfo["imgUrls"] == null || state.consumerInfo["imgUrls"].isEmpty) return;

                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (_) => PhotoPreviewWidget(
                                      imageFileList: [state.consumerInfo["imgUrls"]],
                                      from: 0,
                                      defaultImageIndex: 0,
                                    )
                                )
                            );
                          },
                          child: Container(
                            child: ImageLoad.loadWidget(state.consumerInfo["imgUrls"] ?? "",
                                size: 120),
                          ),
                        ),
                        SizedBox(width: 20.w),
                        Expanded(
                            child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.person_outline_outlined,
                                  size: 34.w,
                                  color: Colors.white,
                                ),

                                Container(
                                  width: 400.w,
                                    child: Text(
                                      "${state.consumerInfo['contactMan'] ?? '请设置联系人'}",
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 30.w),
                                    )),
                              ],
                            ),
                            SizedBox(
                              height: 20.w,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.call,
                                  size: 34.w,
                                  color: Colors.white,
                                ),
                                InkWell(
                                  onTap: () {
                                    // 获取客户详细信息
                                    OpenThirdUrlUtils.callPhone(state.consumerInfo['telephoneNum'] ?? '');
                                  },
                                  child:Container(
                                      child: Text(
                                        "${state.consumerInfo['telephoneNum'] ?? ''}",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 30.w,
                                            decoration: TextDecoration.underline,
                                            decorationColor: Colors.white),
                                      )),
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 20.w,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.location_on,
                                  size: 34.w,
                                  color: Colors.white,
                                ),
                                Expanded(
                                    child: Container(
                                        child: Text(
                                  "${state.consumerInfo['shippingAddress'] ?? '暂无地址'}",
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 24.w),
                                )))
                              ],
                            ),
                            SizedBox(
                              height: 20.w,
                            ),
                            // 更多信息按钮
                            Row(
                              children: [
                                InkWell(
                                  onTap: () {
                                    Get.toNamed(PageName.B2bStoreInfoPage, arguments: {
                                      "type": "edit",
                                      "from": "customerInfo",
                                      "customerInfo": state.consumerInfo
                                    })?.then((result) {
                                      if (result != null && result == true) {
                                        logic.getBranchInfo(false, false);
                                      }
                                    });
                                  },
                                  child: Container(
                                    height: 50.w,
                                    width: 160.w,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: ColorResource.LIGHT_GRAY_LAYER_BACKGROUND_COLOR,
                                        borderRadius: BorderRadius.circular(25.w)),
                                    child: Text('更多信息',
                                        style: TextStyle(
                                            color: ColorResource.ORANGE_RED_COMMON_COLOR, fontSize: 26.w)),
                                  ),
                                )


                              ],
                            ),
                            // 新增字段显示
                           ],
                        ))
                      ],
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: Column(
                          children: [
                            Text('本月订单',
                                style: TextStyle(
                                    fontSize: 28.w, color: Colors.white)),
                            Container(
                              child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                        "${state.consumerInfo['thisMonthOrderCount'] ?? 0}",
                                        style: TextStyle(
                                            fontSize: 28.w,
                                            color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                    Text('笔',
                                        style: TextStyle(
                                            fontSize: 18.w,
                                            color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                    Text('/',
                                        style: TextStyle(
                                            fontSize: 28.w,
                                            color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                    Text('￥',
                                        style: TextStyle(
                                            fontSize: 18.w,
                                            color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                    Text(
                                        "${(state.consumerInfo['thisMonthOrderAmount'] ?? 0).toStringAsFixed(2)}",
                                        style: TextStyle(
                                            fontSize: 28.w,
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold))
                                  ]),
                            )
                          ],
                        )),
                        Expanded(
                            child: Column(
                          children: [
                            Text('上月订单',
                                style: TextStyle(
                                    fontSize: 28.w, color: Colors.white)),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                    "${state.consumerInfo['lastMonthOrderCount'] ?? 0}",
                                    style: TextStyle(
                                        fontSize: 28.w, color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                Text('笔',
                                    style: TextStyle(
                                        fontSize: 18.w, color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                Text('/',
                                    style: TextStyle(
                                        fontSize: 28.w, color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                Text('￥',
                                    style: TextStyle(
                                        fontSize: 18.w, color: Colors.white,
                                        fontWeight: FontWeight.bold)),
                                Text(
                                    "${(state.consumerInfo['lastMonthOrderAmount'] ?? 0).toStringAsFixed(2)}",
                                    style: TextStyle(
                                        fontSize: 28.w,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold))
                              ],
                            )
                          ],
                        ))
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ))
      ],
    );
  }

  void showSignIn(BuildContext context, String signType) async {
    LatLng _latLngs = _latLng ?? LatLng(0.0, 0.0);
    dynamic consumerInfo = state.consumerInfo;
    bool isAuthorityEdit = state.isAuthorityEdit;
    bool _isConsumerLatLonNull = (consumerInfo['latitude'] == null ||
        consumerInfo['longitude'] == null ||
        consumerInfo['latitude'] == '' ||
        consumerInfo['longitude'] == '');
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async {
              return false;
            },
            child: Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(30.w))),
              child: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setDiaLogState) {
                _setDiaLogState = setDiaLogState;
                print('showSignIn=========1111====$_setDiaLogState');
                return SingleChildScrollView(
                  child: Container(
                    width: 500.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(30.w)),
                    ),
                    padding: EdgeInsets.fromLTRB(30.w, 30.w, 30.w, 0.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              '确保当前位置在门店定位范围${state.effectiveMaxDistance}米范围',
                              style: TextStyle(fontSize: 20.w),
                            )
                          ],
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 10.w),
                          height: 300.w,
                          alignment: Alignment.center,
                          color: Colors.grey,
                          child: AmapView(
                            mapType: MapType.Standard,
                            showZoomControl: false,
                            zoomLevel: 18,
                            showCompass: false,
                            maskDelay: const Duration(milliseconds: 500),
                            centerCoordinate: _latLngs,
                            onMapCreated: (controller) async {
                              _controller = controller;
                              _getPoint();
                            },
                            // onMapClicked: (latLng) async {
                            // _latLng = latLng;
                            // _mapAddMarker(latLng);
                            // }
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.fromLTRB(15.w, 5.w, 10.w, 5.w),
                          color: const Color.fromRGBO(32, 34, 36, 0.6),
                          child: Row(
                            children: [
                              Expanded(
                                child: Container(
                                  child: Text(
                                    "${_userAddress == '' ? '定位中' : _userAddress}",
                                    overflow: TextOverflow.fade,
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 20.w),
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  _getPoint();
                                },
                                child: Icon(
                                  Icons.refresh,
                                  size: 50.w,
                                  color: Colors.white,
                                ),
                              )
                            ],
                          ),
                        ),
                        Visibility(
                            visible: _isEffectiveDistance,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.task_alt,
                                  size: 30.w,
                                  color: Color.fromRGBO(49, 173, 114, 1),
                                ),
                                SizedBox(
                                  width: 10.w,
                                ),
                                Text(
                                  '当前位置在门店定位范围${_distance.toInt()}米范围内',
                                  style: TextStyle(fontSize: 20.w),
                                )
                              ],
                            )),
                        Visibility(
                          visible: !_isEffectiveDistance,
                          child: Row(
                            children: [
                              Icon(
                                Icons.error,
                                size: 30.w,
                                color: Color.fromRGBO(200, 118, 53, 1),
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              Text(
                                '位置异常（当前未在门店范围内）',
                                style: TextStyle(fontSize: 20.w),
                              )
                            ],
                          ),
                        ),
                        Visibility(
                            visible: (isAuthorityEdit ||
                                    _isEffectiveDistance ||
                                    _isConsumerLatLonNull) &&
                                signType == 'in' &&
                                state.isNeedPhoto,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 24.w,
                                ),
                                Container(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    '门头照：',
                                  ),
                                ),
                                SizedBox(
                                  height: 6.w,
                                ),
                                Container(
                                  width: 200.w,
                                  height: 200.w,
                                  decoration: BoxDecoration(),
                                  child: SingleImageUploadViewWidget(
                                    type: 0,
                                    maxLength: 1,
                                    centerText: '选择图片',
                                    showBottom: false,
                                    // onlyCamera: true,
                                    imageChange: (imageUrl) {
                                      if (imageUrl != "")
                                        MyCommonUtils.showToast("图片上传成功");
                                      setDiaLogState(() {
                                        _signInImgUrls = imageUrl;
                                      });
                                    },
                                  ),
                                )
                              ],
                            )),
                        // SizedBox(
                        //   height: 24.w,
                        // ),
                        // Visibility(
                        //     visible: signType == 'in',
                        //     child: Column(
                        //       children: [
                        //         Visibility(
                        //             visible: _isConsumerLatLonNull,
                        //             child: Container(
                        //               child: Text('门店暂无定位，是否使用当前位置签到?'),
                        //             )),
                        //         Visibility(
                        //             visible: (!_isConsumerLatLonNull &&
                        //                 isAuthorityEdit),
                        //             child: InkWell(
                        //               onTap: () {
                        //                 setDiaLogState(() {
                        //                   _isEditAddress = !_isEditAddress;
                        //                 });
                        //               },
                        //               child: Row(
                        //                 children: [
                        //                   Container(
                        //                     width: 20.w,
                        //                     height: 20.w,
                        //                     decoration: BoxDecoration(
                        //                         color: _isEditAddress
                        //                             ? Colors.red
                        //                             : Colors.white,
                        //                         border: Border.all(
                        //                             color: _isEditAddress
                        //                                 ? Colors.red
                        //                                 : Colors.grey,
                        //                             width: 2.w),
                        //                         borderRadius:
                        //                             BorderRadius.circular(
                        //                                 10.w)),
                        //                     child: Icon(
                        //                       Icons.check,
                        //                       size: 20.w,
                        //                       color: Colors.white,
                        //                     ),
                        //                   ),
                        //                   SizedBox(
                        //                     width: 6.w,
                        //                   ),
                        //                   Text('使用当前位置覆盖定位',
                        //                       style: TextStyle(
                        //                         fontSize: 20.w,
                        //                         color: ColorResource
                        //                             .GRAY_EDIT_COLOR,
                        //                       ))
                        //                 ],
                        //               ),
                        //             ))
                        //       ],
                        //     )),
                        SizedBox(
                          height: 60.w,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                                child: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                // if (signType == 'in') {
                                //   Get.back();
                                // }
                              },
                              child: Container(
                                height: 100.w,
                                alignment: Alignment.center,
                                child: Text(signType == 'in' ? '取消签到' : '取消签退'),
                              ),
                            )),
                            Expanded(
                                child: InkWell(
                              onTap: () {
                                dynamic obj = {
                                  "_isEffectiveDistance": _isEffectiveDistance,
                                  "_isEditAddress": _isEditAddress,
                                  "_isConsumerLatLonNull":
                                      _isConsumerLatLonNull,
                                  "_signInImgUrls": _signInImgUrls,
                                };
                                logic.sign(obj, context, signType);
                              },
                              child: Container(
                                height: 100.w,
                                alignment: Alignment.center,
                                child: signType == 'in'
                                    ? Text(
                                        (isAuthorityEdit &&
                                                !_isConsumerLatLonNull)
                                            ? (_isEffectiveDistance
                                                ? '提交并签到'
                                                : '提交审核')
                                            : '签到',
                                        style: TextStyle(
                                            color: (isAuthorityEdit ||
                                                    _isEffectiveDistance ||
                                                    _isConsumerLatLonNull)
                                                ? Colors.red
                                                : Colors.grey))
                                    : Text('签退',
                                        style: TextStyle(
                                            color: (isAuthorityEdit ||
                                                    _isEffectiveDistance ||
                                                    _isConsumerLatLonNull)
                                                ? Colors.red
                                                : Colors.grey)),
                              ),
                            )),
                          ],
                        )
                      ],
                    ),
                  ),
                );
              }),
            ),
          );
        });
  }

  @override
  void dispose() {
    if (_bindSyncSubscription != null) {
      _bindSyncSubscription!.cancel();
    }
    super.dispose();
  }
}
