import 'package:collection/collection.dart';

class B2bAppCustomerList {
	String? memberId;
	dynamic sysCode;
	String? memberPhone;
	String? memberName;
	dynamic wxUnionid;
	dynamic avatar;
	int? status;
	dynamic xcxOpenid;
	dynamic registerColonelId;
	dynamic loginToken;
	dynamic memo;
	String? branchName;
	dynamic branchIds;
	dynamic lastLoginTime;
	String? userName;
	dynamic isShopManager;
	dynamic pid;

	B2bAppCustomerList({
		this.memberId, 
		this.sysCode, 
		this.memberPhone, 
		this.memberName, 
		this.wxUnionid, 
		this.avatar, 
		this.status, 
		this.xcxOpenid, 
		this.registerColonelId, 
		this.loginToken, 
		this.memo, 
		this.branchName, 
		this.branchIds, 
		this.lastLoginTime, 
		this.userName, 
		this.isShopManager, 
		this.pid, 
	});

	factory B2bAppCustomerList.fromJson(Map<String, dynamic> json) {
		return B2bAppCustomerList(
			memberId: json['memberId'] as String?,
			sysCode: json['sysCode'] as dynamic,
			memberPhone: json['memberPhone'] as String?,
			memberName: json['memberName'] as String?,
			wxUnionid: json['wxUnionid'] as dynamic,
			avatar: json['avatar'] as dynamic,
			status: json['status'] as int?,
			xcxOpenid: json['xcxOpenid'] as dynamic,
			registerColonelId: json['registerColonelId'] as dynamic,
			loginToken: json['loginToken'] as dynamic,
			memo: json['memo'] as dynamic,
			branchName: json['branchName'] as String?,
			branchIds: json['branchIds'] as dynamic,
			lastLoginTime: json['lastLoginTime'] as dynamic,
			userName: json['userName'] as String?,
			isShopManager: json['isShopManager'] as dynamic,
			pid: json['pid'] as dynamic,
		);
	}



	Map<String, dynamic> toJson() => {
				'memberId': memberId,
				'sysCode': sysCode,
				'memberPhone': memberPhone,
				'memberName': memberName,
				'wxUnionid': wxUnionid,
				'avatar': avatar,
				'status': status,
				'xcxOpenid': xcxOpenid,
				'registerColonelId': registerColonelId,
				'loginToken': loginToken,
				'memo': memo,
				'branchName': branchName,
				'branchIds': branchIds,
				'lastLoginTime': lastLoginTime,
				'userName': userName,
				'isShopManager': isShopManager,
				'pid': pid,
			};

	@override
	bool operator ==(Object other) {
		if (identical(other, this)) return true;
		if (other is! B2bAppCustomerList) return false;
		final mapEquals = const DeepCollectionEquality().equals;
		return mapEquals(other.toJson(), toJson());
	}

	@override
	int get hashCode =>
			memberId.hashCode ^
			sysCode.hashCode ^
			memberPhone.hashCode ^
			memberName.hashCode ^
			wxUnionid.hashCode ^
			avatar.hashCode ^
			status.hashCode ^
			xcxOpenid.hashCode ^
			registerColonelId.hashCode ^
			loginToken.hashCode ^
			memo.hashCode ^
			branchName.hashCode ^
			branchIds.hashCode ^
			lastLoginTime.hashCode ^
			userName.hashCode ^
			isShopManager.hashCode ^
			pid.hashCode;
}
