import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:fuduoduo/utils/print_utils.dart';
import 'package:fuduoduo/utils/set_info.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'state.dart';
import 'package:fuduoduo/common/apis.dart';
import 'package:fuduoduo/domain/dio_default_result_bean.dart';
import 'package:flustars/flustars.dart';
import 'package:fuduoduo/pages/customer/info/logic.dart';
import 'package:fuduoduo/common/dio_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:fuduoduo/widget/order_meeting_charge_dialog.dart';
import 'package:fuduoduo/domain/order_meeting_list_bean.dart';
import 'package:fuduoduo/route/index.dart';
import '../../../resource/string_resource.dart';
import 'package:fuduoduo/pages/replenishment/replenishment/model.dart';

import 'package:fuduoduo/utils/scan_utils.dart';

class VehicleSaleLogic extends GetxController {
  final VehicleSaleState state = VehicleSaleState();
  final shopState = Get.find<CustomerInfoPageLogic>().state;

  RefreshController refreshController = RefreshController();
  ScrollController scrollController = ScrollController();
  int pageNum = 1;

  // 是否启用商品税
  String? item_tax_rate_show = SpUtil.getString('item_tax_rate_show');

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();

    // print('object===     ${state.isAllowNoStocks}');

    String? is_app_order_check_stock = SpUtil.getString('is_app_order_check_stock');
    state.isAllowNoStocks = is_app_order_check_stock == '1' ? true : false;

    if (is_app_order_check_stock == '1') {
      // 负库存显示全部
      state.select_tv = '全部';
      state.stockQtyFlag = '';

      state.selectList = [
        SortCondition(name: '全部', type:'',isSelected: true),
        SortCondition(name: '有库存',type:'1', isSelected: false),
        SortCondition(name: '无库存',type:'2', isSelected: false),
      ];
    }
    update();
  }

  // 获取购物车信息
  void getCartsInfo() {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = shopState.consumerNo;
    MyDio.get(Apis.getCartInfo, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        shopState.cartData = response.data?['transList'] ?? [];
        print("2=>cartData=>${shopState.cartData}");
        countTotal(shopState.cartData);
        countActivityTotal(shopState.cartData);
        getPageData();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  void getPageData() {
    getItemClsTreeNode();
    getActivityClsTreeNode(false);
    getPromoteList();
  }

  void getItemClsTreeNode() {
    Map<String, dynamic>? queryParameters = {};
    MyDio.get(Apis.getItemClsTreeNode, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        list.insert(0, {'clsName': '全部商品'});
        state.clsList = list;
        if (list.length != 0) {
          getItemByCls();
        }
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  filterUpdate() {
    // print('xxxx 2333');
    update([
      StringResource.ORDER_SALE_FILTTER_SEARCH,
      StringResource.ORDER_SALE_SEARCH
    ]);
  }

  // 获取促销赠品
  void getPromoteList() {
    DateTime now = DateTime.now();
    int timestamp = now.millisecondsSinceEpoch;
    int maxTime = 1000 * 60; // 1分钟
    int promotionLoadingTime = state.promotionLoadingTime;
    if (!((timestamp - promotionLoadingTime) > maxTime)) {
      return;
    }
    state.promotionLoadingTime = timestamp;
    Map<String, dynamic>? queryParameters = {};
    queryParameters['promotionType'] = 0;
    queryParameters['applicableChannel'] = shopState.consumerInfo['channelCode'] ?? '';
    // queryParameters['itemList'] = shopState.cartData;
    MyDio.post(
        Apis.promoteList,
        queryParameters: queryParameters, successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        var promotionInfo = {};
        (response.data ?? []).forEach((promotionPlan) {
          (promotionPlan['subList'] ?? []).forEach((promotionCondition) {
            String itemNo = promotionCondition['itemNo'];
            if (promotionInfo[itemNo] == null) {
              promotionInfo[itemNo] = {"info": []};
            }
            promotionInfo[itemNo]['info'].add({
              "goods": promotionCondition,
              "act": {
                "sheetNo": promotionPlan['sheetNo'],
                "id": promotionPlan['id'],
                "remark": promotionPlan['remark'],
                "applicableType": promotionPlan['applicableType'], // 买赠类型 1满赠，2每赠
                "promoteName": promotionPlan['promoteName'],
              }
            });
          });
        });
        state.promotionInfo = promotionInfo;
        printLong("promotionInfo=>${state.promotionInfo}");
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  void getActivityClsTreeNode(bool refresh) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = shopState.consumerNo;
    queryParameters['type'] = "0"; // 0 查询商品 1 查询赠品
    if (state.filter_branchNo.isNotEmpty) queryParameters['branchNo'] = state.filter_branchNo;
    MyDio.get(Apis.listActivityTemplate, queryParameters: queryParameters,
        successCallBack: (value) {
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        // 移除余额为0且过期的订货会
        list.removeWhere((e) =>
            (e['usableAmount'] ?? 0) <= 0 &&
            !DateTime.now().isBefore(DateTime.parse(e['endDate'])));
        state.activityClsList = list;
        if (list.length != 0 && !refresh) {
          getActivityItemList();
        }
        update();
      } else {
        MyCommonUtils.showToast(response.msg!);
      }
    });
  }

  // 获取订货会商品列表
  void getActivityItemList({bool isLoadMore = false, BuildContext? context, Function? cb}) {
    if (isLoadMore) {
      pageNum++;
    } else {
      pageNum = 1;
    }
    getPromoteList();
    List<dynamic> activityClsList = state.activityClsList;
    int selectedTwoClsIndex = state.selectedTwoClsIndex;
    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    if (!isActivity) return;
    Map<String, dynamic> item = activityClsList[selectedTwoClsIndex];
    if (item == null) return;
    String activityTemplateId = item['activityTemplateId'];
    String itemBrand = item['itemBrand'] ?? '';
    int activityType = item['activityType'];

    if (!isLoadMore) SmartDialog.showLoading(msg: "正在加载...");
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = shopState.consumerNo;
    queryParameters['productType'] = "0"; // 0 查询商品 1 查询赠品
    // queryParameters['branchNo'] = "";
    queryParameters['itemName'] = state.searchText;
    queryParameters['activityTemplateId'] = activityTemplateId;
    queryParameters['itemBrand'] = itemBrand;
    queryParameters['activityType'] = activityType;
    queryParameters['visitStockQtyFlag'] = state.stockQtyFlag;
    queryParameters['pageNum'] = pageNum;
    queryParameters['pageSize'] = 20;
    MyDio.get(Apis.getAppActivityItemList, queryParameters: queryParameters,
        successCallBack: (value) {
      SmartDialog.dismiss();
      refreshController.loadComplete();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];


        if (state.isScan) {
          state.isScan = false;
          state.ScanGoodsList = list;

          if (list.length == 0) {
            MyCommonUtils.showToast("无效条形码，商品失效或不存在");
          } else {
            ScanUtils.ScanDialog(context!, text: state.searchText, list: list, callback: (goods, cartGoods, cartTrans) => {
              // print('goods, cartGoods, cartTrans  ${goods} ${cartGoods} ${cartTrans}')
              cb!(goods, cartGoods, cartTrans)
            });
          }
          return;
        }

        if (isLoadMore) {
          state.activityGoodsList.addAll(list);
        } else {
          state.activityGoodsList = list;
        }
        if (list.length != 20) {
          refreshController.loadNoData();
        }
        update();
        if (pageNum == 1) {
          try {
            scrollController.jumpTo(0);
          } catch (e) {}
        }
      } else {
        refreshController.loadComplete();
        MyCommonUtils.showToast(response.msg!);
        if (state.isScan) {
          state.isScan = false;
        }
      }
    }, failCallBack: () {
      refreshController.loadComplete();
      SmartDialog.dismiss();
      if (state.isScan) {
        state.isScan = false;
      }
    });
  }

  // 通过分类id获取商品数据
  void getItemByCls({bool isLoadMore = false, BuildContext? context, String? type, Function? cb}) {
    if (isLoadMore) {
      pageNum++;
    } else {
      pageNum = 1;
    }
    getPromoteList();
    if (state.selectedOneClsIndex == state.actIndex) return;
    dynamic clsList = state.clsList;
    dynamic oneItem = clsList[state.selectedOneClsIndex] ?? {};
    dynamic twochildren = oneItem['children'] ?? [];
    dynamic twoItem =
        twochildren.length == 0 ? {} : twochildren[state.selectedTwoClsIndex];
    dynamic threechildren = twoItem['children'] ?? [];
    dynamic threeItem = threechildren.length == 0
        ? {}
        : threechildren[state.selectedThreeClsIndex];
    // 一级分类
    String oneClsNo = oneItem["clsNo"] ?? "";
    // 二级分类 没有取一级
    String twoClsNo = twoItem["clsNo"] ?? oneClsNo;
    // 三级分类   没有取二级
    String threeClsNo = threeItem["clsNo"] ?? twoClsNo;

    if (!isLoadMore) SmartDialog.showLoading(msg: "正在加载...");
    Map<String, dynamic>? queryParameters = {};
    queryParameters['visitStockQtyFlag'] = state.stockQtyFlag;
    queryParameters['consumerNo'] = shopState.consumerNo;
    queryParameters['clsNo'] = threeClsNo; // 0 查询商品 1 查询赠品
    queryParameters['isSift'] = "0"; // 销售or赠品（0销售1赠品）
    queryParameters['isCash'] = 0;
    queryParameters['itemName'] = state.searchText; // 商品名称
    queryParameters['pageNum'] = pageNum;
    queryParameters['pageSize'] = 20;
    queryParameters['promotionType'] = '0';
    if (state.is_app_org_order) queryParameters['branchNo'] = '';

    if (state.is_app_org_order && state.filter_branchNo.isNotEmpty) queryParameters['branchNo'] = state.filter_branchNo;
    if (state.is_app_org_order && state.filter_brandNo.isNotEmpty) queryParameters['itemBrand'] = state.filter_brandNo;

    String _url = Apis.getItemByCls;
    if (state.is_app_org_order) _url = Apis.getMultiOrgItemByCls;


    MyDio.get(_url, queryParameters: queryParameters,
      successCallBack: (value) {
      SmartDialog.dismiss();
      refreshController.loadComplete();
      var response = DioResultBean.fromJson(value);
      if (response.code == '200') {
        List list = response.data ?? [];
        if (state.isScan) {
          state.isScan = false;
          state.ScanGoodsList = list;

          if (list.length == 0) {
            MyCommonUtils.showToast("无效条形码，商品失效或不存在");
          } else {
            ScanUtils.ScanDialog(context!, text: state.searchText, list: list, callback: (goods, cartGoods, cartTrans) => {
              // print('goods, cartGoods, cartTrans  ${goods} ${cartGoods} ${cartTrans}')
              cb!(goods, cartGoods, cartTrans)
            });
          }
          return;
        } else {
          if (state.searchText.isEmpty && type == 'TDSlidePopup' && cb != null) {
            cb?.call();
          }
        }
        // if (state.searchText.isNotEmpty) state.searchText = '';
        if (isLoadMore) {
          state.goodsList.addAll(list);
        } else {
          state.goodsList = list;
        }
        if (list.length != 20) {
          refreshController.loadNoData();
        }
        update();
        if (pageNum == 1) {
          try {
            scrollController.jumpTo(0);
          } catch (e) {}
        }
      } else {
        refreshController.loadComplete();
        MyCommonUtils.showToast(response.msg!);
        if (state.isScan) {
          state.isScan = false;
        }
      }
    }, failCallBack: () {
      refreshController.loadComplete();
      SmartDialog.dismiss();
      if (state.isScan) {
        state.isScan = false;
      }
    });
  }

  // 选择一级分类
  void setSelectedOneClsIndex(int index) {
    if (state.selectedOneClsIndex == index) {
      state.isOpenExpand = !state.isOpenExpand;
      update();
      return;
    }
    state.isOpenExpand = true;
    state.selectedOneClsIndex = index;
    state.selectedTwoClsIndex = 0;
    if (index == state.actIndex) {
      // 选择的是订货会
      getActivityClsTreeNode(false);
    } else {
      state.selectedThreeClsIndex = 0;
      getItemByCls();
    }
    update();
  }

  //  选择二级分类
  void setSelectedTowClsIndex(int index) {
    state.selectedTwoClsIndex = index;
    state.selectedThreeClsIndex = 0;
    if (state.selectedOneClsIndex == state.actIndex) {
      // 选择的是订货会
      getActivityItemList();
    } else {
      getItemByCls();
    }
    update();
  }

  //  选三级级分类
  void setSelectedThreeClsIndex(int index) {
    state.selectedThreeClsIndex = index;
    update();
    getItemByCls();
  }

  void confirmSearch(searchText) {
    state.searchText = searchText;
    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    // 是订货会
    if (isActivity) {
      getActivityItemList();
    } else {
      getItemByCls();
    }
  }

  void confirmScanSearch(BuildContext context, String searchText, bool isScan, Function callBack) {
    state.searchText = searchText;
    state.isScan = isScan;
    bool isActivity = state.selectedOneClsIndex == state.actIndex;
    // 是订货会
    if (isActivity) {
      getActivityItemList(context: context, cb: callBack!);
    } else {
      getItemByCls(context: context, cb: callBack!);
    }
  }

  /// 获取购物车数量，用规格展示
  String getCartNumStr(cartGoods) {
    String size0 = '';
    String size1 = '';
    String size2 = '';
    (cartGoods?['packageList'] ?? []).forEach((package) {
      if ((package['detailQty'] ?? 0) > 0) {
        if (package['packageType'] == '0') {
          size0 =
              '${package['detailQty']}${getItemUnitStr(package['itemUnit'])}';
        } else if (package['packageType'] == '1') {
          size1 =
              '${package['detailQty']}${getItemUnitStr(package['itemUnit'])}';
        } else if (package['packageType'] == '2') {
          size2 =
              '${package['detailQty']}${getItemUnitStr(package['itemUnit'])}';
        }
      }
    });
    return size2 + size1 + size0;
  }

  /// 获取包装规格转换信息
  String getPackageSizeUnit(goods, package) {
    if ((package?['packageType'] ?? '') == '0') {
      return '最小单位';
    } else if ((package?['packageType'] ?? '') == '1' ||
        (package?['packageType'] ?? '') == '2') {
      return "1${getItemUnitStr(package['itemUnit'])}=${package['conversionQty']}${getItemUnitStr(goods?['itemUnit'] ?? '')}";
    } else {
      return '';
    }
  }

  // 操作更新购物车
  void updateCarts(goods, cartGoods, cartTrans, List cartData) {
    bool isActivity = state.selectedOneClsIndex == state.actIndex;

    print("goods['cartData'] $goods");

    String _branchNo = goods['branchNo'] ?? '';

    // 商品总价
    double TaxPrice = 0;


    (goods['packageList'] ?? []).forEach((package) {
      package['minDetailQty'] =
          (package['detailQty'] ?? 0) * (package['conversionQty'] ?? 0);
      package['detailPrice'] = doubleNotloss(
          (package['detailQty'] ?? 0) * (package['itemPrice'] ?? 0), decimal: 10000000);


      if (item_tax_rate_show == '1') {
        TaxPrice += doubleNotloss(
            (package['detailQty'] ?? 0) * (double.parse(package['unitPriceVat']) ?? 0), decimal: 10000000);

      }
    });

    // print("goods['TaxPrice'] ${TaxPrice} ${goods['itemTotalAmt']}");

    if ((goods['itemTotalQty'] ?? 0) > 0) {
      // 同步购物车数据
      if (cartTrans == null) {
        Map transGoods = {
          "itemNo": goods['itemNo'],
          "itemBarcode": goods['itemBarcode'],
          "itemName": goods['itemName'],
          "itemType": "0",
          "itemDeliveryType": goods['itemDeliveryType'] ?? 1,
          "itemSize": goods['itemSize'],
          "itemImg": goods['itemImg'],
          "itemRealStock": goods['itemRealStock'] ?? 0,
          "itemTotalAmt": goods['itemTotalAmt'] ?? 0.0,
          "itemTotalQty": goods['itemTotalQty'] ?? 0,
          "costPrice": goods['costPrice'] ?? 0.0,
          "salesChangePrice": goods['salesChangePrice'],
          'packageList': goods['packageList'],
          'branchNo': goods['branchNo'],
          'branchName': goods['branchName'],
        };
        Map trans = {};
        if (isActivity) {
          trans['transNo'] = 'HD';
          List activityClsList = state.activityClsList;
          Map item = activityClsList[state.selectedTwoClsIndex] ?? {};
          trans['activityOrderId'] = item['id'];
          trans['activityName'] = item['activityName'];
          trans['activityMoney'] = item['usableAmount'];
        } else {
          trans['transNo'] = 'XS';
        }
        trans['transKey'] = '${DateTime.now().microsecondsSinceEpoch}';
        trans['consumerNo'] = shopState.consumerNo;
        trans['sheetAmt'] = goods['itemTotalAmt'];
        trans['sheetQty'] = goods['itemTotalQty'];
        trans['deliveryType'] =
            (state.isTCTP && state.isFX) ? (goods['itemDeliveryType'] ?? 1) : 1;
        trans['detailList'] = [transGoods];
        cartData.add(trans);
      } else if (cartGoods == null) {
        Map transGoods = {
          "itemNo": goods['itemNo'],
          "itemName": goods['itemName'],
          "itemBarcode": goods['itemBarcode'],
          "itemType": "0",
          "itemDeliveryType": goods['itemDeliveryType'] ?? 1,
          "itemSize": goods['itemSize'],
          "itemImg": goods['itemImg'],
          "itemRealStock": goods['itemRealStock'] ?? 0,
          "itemTotalAmt": goods['itemTotalAmt'] ?? 0.0,
          "itemTotalQty": goods['itemTotalQty'] ?? 0,
          "costPrice": goods['costPrice'] ?? 0.0,
          "salesChangePrice": goods['salesChangePrice'],
          'packageList': goods['packageList'],
          'branchNo': goods['branchNo'],
          'branchName': goods['branchName'],
        };
        cartTrans['detailList'].add(transGoods);

        double totalAmt = 0;
        double totalNum = 0;
        (cartTrans['detailList'] as List).forEach((goods) {
          totalAmt += goods['itemTotalAmt'] ?? 0.0;
          totalNum += goods['itemTotalQty'] ?? 0;
        });
        cartTrans['sheetAmt'] = doubleNotloss(totalAmt, decimal: 10000000);
        ;
        cartTrans['sheetQty'] = totalNum.toInt();
      } else {
        cartGoods['salesChangePrice'] = goods['salesChangePrice'];
        cartGoods['itemTotalAmt'] = goods['itemTotalAmt'] ?? 0.0;
        cartGoods['itemTotalQty'] = goods['itemTotalQty'] ?? 0;
        cartGoods['itemRealStock'] = goods['itemRealStock'] ?? 0;
        cartGoods['packageList'] = goods['packageList'];
        // cartGoods['branchNo'] = goods['branchNo'];

        // 计算订单总金额、总数量
        double totalAmt = 0;
        double totalNum = 0;
        (cartTrans['detailList'] as List).forEach((goods) {
          totalAmt += goods['itemTotalAmt'] ?? 0.0;
          totalNum += goods['itemTotalQty'] ?? 0;
        });
        cartTrans['sheetAmt'] = doubleNotloss(totalAmt, decimal: 10000000);
        cartTrans['sheetQty'] = totalNum.toInt();
      }
    } else {
      // 数量为0删除购物车数据
      // print('cartTrans $cartTrans');
      // print('cartGoods $cartGoods');
      if (cartTrans != null && cartGoods != null) {

        (cartTrans['detailList'] as List).removeWhere(
            (e) => e['itemNo'] == cartGoods['itemNo'] && e['itemType'] == '0' && _branchNo == e['branchNo']);
        if (cartTrans['detailList'].isEmpty) {
          cartData.removeWhere((e) => e['transKey'] == cartTrans['transKey'] && _branchNo == e['branchNo'] );
          deleteCart(cartTrans);
        } else {
          // 计算订单总金额、总数量
          double totalAmt = 0;
          double totalNum = 0;
          (cartTrans['detailList'] as List).forEach((goods) {
            totalAmt += goods['itemTotalAmt'] ?? 0.0;
            totalNum += goods['itemTotalQty'] ?? 0;
          });
          cartTrans['sheetAmt'] = doubleNotloss(totalAmt, decimal: 10000000);
          cartTrans['sheetQty'] = totalNum.toInt();
        }
      }
    }

    // 重新计算金额
    if (isActivity) {
      countActivityTotal(cartData);
    } else {
      countTotal(cartData);
    }
  }

  updateLatestLists() {
    update([
      StringResource.SALE_LASTEST_PRICE_LIST
    ]);
  }

  /// 删除云端购物车
  void deleteCart(trans) {
    Map<String, dynamic>? queryParameters = {};
    queryParameters['consumerNo'] = shopState.consumerNo;
    queryParameters['transKey'] = trans['transKey'];
    MyDio.get(Apis.deleteCart,
        queryParameters: queryParameters, successCallBack: (value) {});
  }

  // 计算销售购物车总金额
  void countTotal(List cartData) {
    double totalAmt = 0.0;
    double totalNum = 0.0;
    cartData.forEach((trans) {
      if (trans['transNo'] == 'XS') {
        // totalAmt += e['sheetAmt'] ?? 0.0; // 销售购物车总金额
        trans['detailList'].forEach((goods) {
          if ((goods['select'] ?? true)) {
            totalAmt += goods['itemTotalAmt'];
          }
        });
        totalNum += trans['totalNum'] ?? 0.0; // 订货总数量（换算后）
      }
    });
    state.totalAmt = doubleNotloss(totalAmt, decimal: 10000000); // 销售购物车总金额
    state.totalNum = totalNum.toInt(); // 订货总数量（换算后）
    update();
  }

  // 计算订货会购物车总金额
  void countActivityTotal(List cartData) {
    double totalAmt = 0;
    cartData.forEach((trans) {
      if (trans['transNo'] == 'HD') {
        // totalAmt += trans['sheetAmt'] ?? 0.0;
        trans['detailList'].forEach((goods) {
          if ((goods['select'] ?? true)) {
            totalAmt += goods['itemTotalAmt'];
          }
        });
      }
    });
    state.activityTotalAmt = doubleNotloss(totalAmt, decimal: 10000000); // 销售购物车总金额
    update();
  }

  Map getGoodsTotalInfo(List packageList) {
    Map total = {};
    double totalAmt = 0;
    int totalNum = 0;
    packageList.forEach((item) {
      int buyNum = item['detailQty'];
      int num = item['conversionQty'];
      if (item['packageType'] == '0') {
        totalNum += buyNum;
      } else {
        totalNum += (buyNum * num);
      }
      if (item_tax_rate_show == '1') {
        totalAmt += (buyNum * (double.parse(item['unitPriceVat']) ?? 0.0));
      } else {
        totalAmt += (buyNum * (item['itemPrice'] ?? 0.0));
      }

    });
    total['totalNum'] = totalNum;
    total['totalAmt'] = doubleNotloss(totalAmt, decimal: 10000000);
    return total;
  }

  //弹窗-充值
  showOrderMeetingChargeDialog(BuildContext context, OrderMeetingBean item) {
    dynamic consumerInfo = shopState.consumerInfo;
    var branchNo = SpUtil.getString('branchNo');
    showDialog(
        context: context,
        // barrierDismissible: false,
        builder: (_context) {
          return OrderMeetingChargeDialog(
            orderMeetingName: item.activityName,
            orderMeetingType: item.activityType == 0 ? "品牌" : "商品",
            orderMeetingBalance: item.unBuyAmount.toString(),
            orderMeetingMinimum: item.minAmount.toString(),
            onConfirmClicked: (chargeMoney) {
              Get.toNamed(PageName.OrderMeetingSettlementPage, parameters: {
                "branchNo": branchNo ?? '',
                "consumerNo": consumerInfo['consumerNo'],
                "consumerName": consumerInfo['consumerName'] ?? '',
                "consumerAddress": consumerInfo['shippingAddress'] ?? '',
                "activityTemplateId": item.activityTemplateId!,
                "activityName": item.activityName!,
                "activityType": item.activityType.toString(),
                "balanceMoney": item.unBuyAmount.toString(),
                "chargeMoney": chargeMoney.toString(),
              })?.then((value) => {getActivityClsTreeNode(true)});
            },
          );
        });
  }

  /// 数组拷贝
  List copyWithList(List list) {
    List copyList = [];
    for (var item in list) {
      if (item is Map) {
        copyList.add(Map.from(item));
      } else if (item is List) {
        copyList.add(copyWithList(item));
      } else {
        copyList.add(item);
      }
    }
    return copyList;
  }

  /// 转换成包装规格数量  如2000个，转换成200箱20个
  String getPackageCount(int count, goods) {
    var result = "";
    if (count == 0) {
      result = '0';
    } else {
      int remainder = count;
      if (count < 0) remainder = -count;
      if (((goods['itemLargeBoxNum'] ?? 0) > 1 ||
          (goods['itemLargeBoxUnit'] ?? '').isNotEmpty) && goods['itemLargeBoxNum'] != null) {
        int large = remainder ~/ goods['itemLargeBoxNum'];
        if (large > 0) {
          result = '${large}${getItemUnitStr(goods['itemLargeBoxUnit'])}';
          remainder = (remainder % (goods['itemLargeBoxNum'] ?? 1)).toInt();
        }
      }
      if (((goods['itemMediumBoxNum'] ?? 0) > 1 ||
          (goods['itemMediumBoxUnit'] ?? '').isNotEmpty) && goods['itemLargeBoxNum'] != null) {
        int medium = remainder ~/ goods['itemMediumBoxNum'];
        if (medium > 0) {
          result =
              '$result$medium${getItemUnitStr(goods['itemMediumBoxUnit'])}';
          remainder = (remainder % (goods['itemMediumBoxNum'] ?? 1)).toInt();
        }
      }
      if (remainder > 0) {
        result = '$result$remainder${getItemUnitStr(goods['itemUnit'])}';
      }
      if (result.isEmpty) {
        result = '0';
      }
      if (count < 0) {
        result = '-${result}';
      }
    }
    return result;
  }
}
