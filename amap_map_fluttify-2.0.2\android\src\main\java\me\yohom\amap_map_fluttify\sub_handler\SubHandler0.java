//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler0 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // getter
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::get_MIN_OFFSET_DISTANCE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) __args__.get("__this__");
            
               Float __result__ = __this__.MIN_OFFSET_DISTANCE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.MapsInitializer::get_sdcardDir", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.MapsInitializer __this__ = (com.amap.api.maps.MapsInitializer) __args__.get("__this__");
            
               String __result__ = __this__.sdcardDir;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.MapsInitializer::get_TERRAIN_LOCAL_DEM_SOURCE_PATH", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.MapsInitializer __this__ = (com.amap.api.maps.MapsInitializer) __args__.get("__this__");
            
               String __result__ = __this__.TERRAIN_LOCAL_DEM_SOURCE_PATH;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.HeatMapLayerOptions::get_DEFAULT_GRADIENT", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) __args__.get("__this__");
            
               com.amap.api.maps.model.Gradient __result__ = __this__.DEFAULT_GRADIENT;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_nearLeft", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.nearLeft;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_nearRight", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.nearRight;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_farLeft", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.farLeft;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_farRight", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.farRight;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_latLngBounds", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLngBounds __result__ = __this__.latLngBounds;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_RED", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_RED;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_ORANGE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_ORANGE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_YELLOW", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_YELLOW;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_GREEN", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_GREEN;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_CYAN", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_CYAN;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_AZURE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_AZURE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_BLUE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_BLUE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_VIOLET", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_VIOLET;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_MAGENTA", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_MAGENTA;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_ROSE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
               Float __result__ = __this__.HUE_ROSE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_width", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
               Integer __result__ = __this__.width;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_height", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
               Integer __result__ = __this__.height;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_data", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
               byte[] __result__ = __this__.data;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.GroundOverlayOptions::get_NO_DIMENSION", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) __args__.get("__this__");
            
               Float __result__ = __this__.NO_DIMENSION;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_target", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.target;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_zoom", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
               Float __result__ = __this__.zoom;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_tilt", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
               Float __result__ = __this__.tilt;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_bearing", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
               Float __result__ = __this__.bearing;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_isAbroad", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
               Boolean __result__ = __this__.isAbroad;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.HeatmapTileProvider::get_DEFAULT_GRADIENT", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.HeatmapTileProvider __this__ = (com.amap.api.maps.model.HeatmapTileProvider) __args__.get("__this__");
            
               com.amap.api.maps.model.Gradient __result__ = __this__.DEFAULT_GRADIENT;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.LatLngBounds::get_southwest", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.southwest;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.LatLngBounds::get_northeast", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.northeast;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_IMAGE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
               Integer __result__ = __this__.TILESOURCE_TYPE_IMAGE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_VECTOR", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
               Integer __result__ = __this__.TILESOURCE_TYPE_VECTOR;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_IMAGE_DEM", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
               Integer __result__ = __this__.TILESOURCE_TYPE_IMAGE_DEM;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_FBO_TEXTURE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
               Integer __result__ = __this__.TILESOURCE_TYPE_FBO_TEXTURE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.LatLng::get_latitude", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.LatLng __this__ = (com.amap.api.maps.model.LatLng) __args__.get("__this__");
            
               Double __result__ = __this__.latitude;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.LatLng::get_longitude", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.LatLng __this__ = (com.amap.api.maps.model.LatLng) __args__.get("__this__");
            
               Double __result__ = __this__.longitude;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_offsetX", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.offsetX;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_offsetY", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.offsetY;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_minX", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.minX;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_maxX", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.maxX;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_minY", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.minY;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_maxY", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
               Integer __result__ = __this__.maxY;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.CrossOverlay.UpdateItem::get_dataUpdateFlag", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.CrossOverlay.UpdateItem __this__ = (com.amap.api.maps.model.CrossOverlay.UpdateItem) __args__.get("__this__");
            
               Integer __result__ = __this__.dataUpdateFlag;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.WeightedLatLng::get_intensity", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.WeightedLatLng __this__ = (com.amap.api.maps.model.WeightedLatLng) __args__.get("__this__");
            
               Double __result__ = __this__.intensity;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.WeightedLatLng::get_latLng", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.WeightedLatLng __this__ = (com.amap.api.maps.model.WeightedLatLng) __args__.get("__this__");
            
               com.amap.api.maps.model.LatLng __result__ = __this__.latLng;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_type", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
               Integer __result__ = __this__.type;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_color", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
               Integer __result__ = __this__.color;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_radius", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
               Float __result__ = __this__.radius;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.particle.SizeOverLife::get_DEFAULT_SIZE", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.particle.SizeOverLife __this__ = (com.amap.api.maps.model.particle.SizeOverLife) __args__.get("__this__");
            
               Integer __result__ = __this__.DEFAULT_SIZE;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_activeFloorName", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
               String __result__ = __this__.activeFloorName;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_activeFloorIndex", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
               Integer __result__ = __this__.activeFloorIndex;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_poiid", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
               String __result__ = __this__.poiid;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_floor_indexs", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
               int[] __result__ = __this__.floor_indexs;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_floor_names", (__rawArgs__, __methodResult__) -> {
               Map<String, Object> __args__ = (Map<String, Object>) __rawArgs__;
            
               // ref object
               com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
               String[] __result__ = __this__.floor_names;
            
                __methodResult__.success(__result__);
            });
            // getter
            put("com.amap.api.maps.utils.overlay.SmoothMoveMarker::get_MIN_OFFSET_DISTANCE_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.utils.overlay.SmoothMoveMarker __this__ = (com.amap.api.maps.utils.overlay.SmoothMoveMarker) __args__.get("__this__");
            
                    Float __result__ = __this__.MIN_OFFSET_DISTANCE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.MapsInitializer::get_sdcardDir_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.MapsInitializer __this__ = (com.amap.api.maps.MapsInitializer) __args__.get("__this__");
            
                    String __result__ = __this__.sdcardDir;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.MapsInitializer::get_TERRAIN_LOCAL_DEM_SOURCE_PATH_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.MapsInitializer __this__ = (com.amap.api.maps.MapsInitializer) __args__.get("__this__");
            
                    String __result__ = __this__.TERRAIN_LOCAL_DEM_SOURCE_PATH;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.HeatMapLayerOptions::get_DEFAULT_GRADIENT_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Gradient> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.HeatMapLayerOptions __this__ = (com.amap.api.maps.model.HeatMapLayerOptions) __args__.get("__this__");
            
                    com.amap.api.maps.model.Gradient __result__ = __this__.DEFAULT_GRADIENT;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_nearLeft_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.nearLeft;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_nearRight_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.nearRight;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_farLeft_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.farLeft;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_farRight_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.farRight;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.VisibleRegion::get_latLngBounds_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLngBounds> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.VisibleRegion __this__ = (com.amap.api.maps.model.VisibleRegion) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLngBounds __result__ = __this__.latLngBounds;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_RED_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_RED;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_ORANGE_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_ORANGE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_YELLOW_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_YELLOW;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_GREEN_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_GREEN;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_CYAN_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_CYAN;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_AZURE_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_AZURE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_BLUE_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_BLUE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_VIOLET_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_VIOLET;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_MAGENTA_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_MAGENTA;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.BitmapDescriptorFactory::get_HUE_ROSE_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.BitmapDescriptorFactory __this__ = (com.amap.api.maps.model.BitmapDescriptorFactory) __args__.get("__this__");
            
                    Float __result__ = __this__.HUE_ROSE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_width_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
                    Integer __result__ = __this__.width;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_height_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
                    Integer __result__ = __this__.height;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.Tile::get_data_batch", (__argsBatch__, __methodResult__) -> {
                List<byte[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.Tile __this__ = (com.amap.api.maps.model.Tile) __args__.get("__this__");
            
                    byte[] __result__ = __this__.data;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.GroundOverlayOptions::get_NO_DIMENSION_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.GroundOverlayOptions __this__ = (com.amap.api.maps.model.GroundOverlayOptions) __args__.get("__this__");
            
                    Float __result__ = __this__.NO_DIMENSION;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_target_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.target;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_zoom_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
                    Float __result__ = __this__.zoom;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_tilt_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
                    Float __result__ = __this__.tilt;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_bearing_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
                    Float __result__ = __this__.bearing;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CameraPosition::get_isAbroad_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CameraPosition __this__ = (com.amap.api.maps.model.CameraPosition) __args__.get("__this__");
            
                    Boolean __result__ = __this__.isAbroad;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.HeatmapTileProvider::get_DEFAULT_GRADIENT_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.Gradient> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.HeatmapTileProvider __this__ = (com.amap.api.maps.model.HeatmapTileProvider) __args__.get("__this__");
            
                    com.amap.api.maps.model.Gradient __result__ = __this__.DEFAULT_GRADIENT;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.LatLngBounds::get_southwest_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.southwest;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.LatLngBounds::get_northeast_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.LatLngBounds __this__ = (com.amap.api.maps.model.LatLngBounds) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.northeast;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_IMAGE_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
                    Integer __result__ = __this__.TILESOURCE_TYPE_IMAGE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_VECTOR_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
                    Integer __result__ = __this__.TILESOURCE_TYPE_VECTOR;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_IMAGE_DEM_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
                    Integer __result__ = __this__.TILESOURCE_TYPE_IMAGE_DEM;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileOverlaySource::get_TILESOURCE_TYPE_FBO_TEXTURE_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileOverlaySource __this__ = (com.amap.api.maps.model.TileOverlaySource) __args__.get("__this__");
            
                    Integer __result__ = __this__.TILESOURCE_TYPE_FBO_TEXTURE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.LatLng::get_latitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.LatLng __this__ = (com.amap.api.maps.model.LatLng) __args__.get("__this__");
            
                    Double __result__ = __this__.latitude;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.LatLng::get_longitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.LatLng __this__ = (com.amap.api.maps.model.LatLng) __args__.get("__this__");
            
                    Double __result__ = __this__.longitude;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_offsetX_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.offsetX;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_offsetY_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.offsetY;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_minX_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.minX;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_maxX_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.maxX;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_minY_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.minY;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.TileProjection::get_maxY_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.TileProjection __this__ = (com.amap.api.maps.model.TileProjection) __args__.get("__this__");
            
                    Integer __result__ = __this__.maxY;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.CrossOverlay.UpdateItem::get_dataUpdateFlag_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.CrossOverlay.UpdateItem __this__ = (com.amap.api.maps.model.CrossOverlay.UpdateItem) __args__.get("__this__");
            
                    Integer __result__ = __this__.dataUpdateFlag;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.WeightedLatLng::get_intensity_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.WeightedLatLng __this__ = (com.amap.api.maps.model.WeightedLatLng) __args__.get("__this__");
            
                    Double __result__ = __this__.intensity;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.WeightedLatLng::get_latLng_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.maps.model.LatLng> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.WeightedLatLng __this__ = (com.amap.api.maps.model.WeightedLatLng) __args__.get("__this__");
            
                    com.amap.api.maps.model.LatLng __result__ = __this__.latLng;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_type_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
                    Integer __result__ = __this__.type;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_color_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
                    Integer __result__ = __this__.color;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.ImageOptions::get_radius_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) __args__.get("__this__");
            
                    Float __result__ = __this__.radius;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.particle.SizeOverLife::get_DEFAULT_SIZE_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.particle.SizeOverLife __this__ = (com.amap.api.maps.model.particle.SizeOverLife) __args__.get("__this__");
            
                    Integer __result__ = __this__.DEFAULT_SIZE;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_activeFloorName_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
                    String __result__ = __this__.activeFloorName;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_activeFloorIndex_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
                    Integer __result__ = __this__.activeFloorIndex;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_poiid_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
                    String __result__ = __this__.poiid;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_floor_indexs_batch", (__argsBatch__, __methodResult__) -> {
                List<int[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
                    int[] __result__ = __this__.floor_indexs;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // getter
            put("com.amap.api.maps.model.IndoorBuildingInfo::get_floor_names_batch", (__argsBatch__, __methodResult__) -> {
                List<String[]> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref object
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) __args__.get("__this__");
            
                    String[] __result__ = __this__.floor_names;
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // setter
            put("com.amap.api.maps.model.CrossOverlay.UpdateItem::set_dataUpdateFlag", (__args__, __methodResult__) -> {
                // ref arg
                Number dataUpdateFlag = (Number) ((Map<String, Object>) __args__).get("dataUpdateFlag");
                com.amap.api.maps.model.CrossOverlay.UpdateItem __this__ = (com.amap.api.maps.model.CrossOverlay.UpdateItem) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.dataUpdateFlag = dataUpdateFlag.intValue();
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.ImageOptions::set_type", (__args__, __methodResult__) -> {
                // ref arg
                Number type = (Number) ((Map<String, Object>) __args__).get("type");
                com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.type = type.intValue();
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.ImageOptions::set_color", (__args__, __methodResult__) -> {
                // ref arg
                Number color = (Number) ((Map<String, Object>) __args__).get("color");
                com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.color = color.intValue();
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.ImageOptions::set_radius", (__args__, __methodResult__) -> {
                // ref arg
                Number radius = (Number) ((Map<String, Object>) __args__).get("radius");
                com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.radius = radius.floatValue();
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_activeFloorName", (__args__, __methodResult__) -> {
                // ref arg
                String activeFloorName = (String) ((Map<String, Object>) __args__).get("activeFloorName");
                com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.activeFloorName = activeFloorName;
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_activeFloorIndex", (__args__, __methodResult__) -> {
                // ref arg
                Number activeFloorIndex = (Number) ((Map<String, Object>) __args__).get("activeFloorIndex");
                com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.activeFloorIndex = activeFloorIndex.intValue();
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_poiid", (__args__, __methodResult__) -> {
                // ref arg
                String poiid = (String) ((Map<String, Object>) __args__).get("poiid");
                com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.poiid = poiid;
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_floor_indexs", (__args__, __methodResult__) -> {
                // ref arg
                int[] floor_indexs = (int[]) ((Map<String, Object>) __args__).get("floor_indexs");
                com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.floor_indexs = floor_indexs;
                __methodResult__.success("success");
            });
            // setter
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_floor_names", (__args__, __methodResult__) -> {
                // ref arg
                String[] floor_names = (String[]) ((Map<String, Object>) __args__).get("floor_names");
                com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                __this__.floor_names = floor_names;
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.CrossOverlay.UpdateItem::set_dataUpdateFlag_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    Number dataUpdateFlag = (Number) ((Map<String, Object>) __args__).get("dataUpdateFlag");
            
                    com.amap.api.maps.model.CrossOverlay.UpdateItem __this__ = (com.amap.api.maps.model.CrossOverlay.UpdateItem) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.dataUpdateFlag = dataUpdateFlag.intValue();
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.ImageOptions::set_type_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    Number type = (Number) ((Map<String, Object>) __args__).get("type");
            
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.type = type.intValue();
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.ImageOptions::set_color_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    Number color = (Number) ((Map<String, Object>) __args__).get("color");
            
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.color = color.intValue();
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.ImageOptions::set_radius_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    Number radius = (Number) ((Map<String, Object>) __args__).get("radius");
            
                    com.amap.api.maps.model.ImageOptions __this__ = (com.amap.api.maps.model.ImageOptions) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.radius = radius.floatValue();
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_activeFloorName_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    String activeFloorName = (String) ((Map<String, Object>) __args__).get("activeFloorName");
            
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.activeFloorName = activeFloorName;
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_activeFloorIndex_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    Number activeFloorIndex = (Number) ((Map<String, Object>) __args__).get("activeFloorIndex");
            
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.activeFloorIndex = activeFloorIndex.intValue();
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_poiid_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    String poiid = (String) ((Map<String, Object>) __args__).get("poiid");
            
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.poiid = poiid;
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_floor_indexs_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    int[] floor_indexs = (int[]) ((Map<String, Object>) __args__).get("floor_indexs");
            
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.floor_indexs = floor_indexs;
                }
            
                __methodResult__.success("success");
            });
            // setter batch
            put("com.amap.api.maps.model.IndoorBuildingInfo::set_floor_names_batch", (__argsBatch__, __methodResult__) -> {
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // ref arg
                    String[] floor_names = (String[]) ((Map<String, Object>) __args__).get("floor_names");
            
                    com.amap.api.maps.model.IndoorBuildingInfo __this__ = (com.amap.api.maps.model.IndoorBuildingInfo) ((Map<String, Object>) __args__).get("__this__");
            
                    __this__.floor_names = floor_names;
                }
            
                __methodResult__.success("success");
            });
            // method
            put("com.amap.api.offlineservice.AMapPermissionActivity::onRequestPermissionsResult", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                String[] var2 = (String[]) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                int[] var3 = (int[]) ((Map<String, Object>) __args__).get("var3");
            
                // ref
                com.amap.api.offlineservice.AMapPermissionActivity __this__ = (com.amap.api.offlineservice.AMapPermissionActivity) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.offlineservice.AMapPermissionActivity@" + __this__ + "::onRequestPermissionsResult(" + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onRequestPermissionsResult(var1.intValue(), var2, var3);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::newInstance", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment::newInstance(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.SupportMapFragment __result__ = null;
                try {
                    __result__ = com.amap.api.maps.SupportMapFragment.newInstance();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::newInstance__com_amap_api_maps_AMapOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.AMapOptions var0 = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment::newInstance(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.SupportMapFragment __result__ = null;
                try {
                    __result__ = com.amap.api.maps.SupportMapFragment.newInstance(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::getMap", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::getMap(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMap __result__ = null;
                try {
                    __result__ = __this__.getMap();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onAttach", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.app.Activity var1 = (android.app.Activity) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onAttach(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onAttach(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onCreate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onCreate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onCreate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onResume", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onResume(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onResume();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onPause", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onPause(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onPause();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onDestroyView", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onDestroyView(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroyView();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onDestroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onDestroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onDestroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onLowMemory", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onLowMemory(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onLowMemory();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::onSaveInstanceState", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::onSaveInstanceState(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.onSaveInstanceState(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::setArguments", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.os.Bundle var1 = (android.os.Bundle) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::setArguments(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setArguments(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.SupportMapFragment::setUserVisibleHint", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.SupportMapFragment __this__ = (com.amap.api.maps.SupportMapFragment) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.SupportMapFragment@" + __this__ + "::setUserVisibleHint(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setUserVisibleHint(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setScaleControlsEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setScaleControlsEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setScaleControlsEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomControlsEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setZoomControlsEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZoomControlsEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setCompassEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setCompassEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCompassEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setMyLocationButtonEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setMyLocationButtonEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMyLocationButtonEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setScrollGesturesEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setScrollGesturesEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setScrollGesturesEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomGesturesEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setZoomGesturesEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZoomGesturesEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setTiltGesturesEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setTiltGesturesEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTiltGesturesEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setRotateGesturesEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setRotateGesturesEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRotateGesturesEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setAllGesturesEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setAllGesturesEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAllGesturesEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setLogoPosition(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLogoPosition(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setZoomPosition(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZoomPosition(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getZoomPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::getZoomPosition(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getZoomPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isScaleControlsEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isScaleControlsEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isScaleControlsEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isZoomControlsEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isZoomControlsEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isZoomControlsEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isCompassEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isCompassEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isCompassEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isMyLocationButtonEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isMyLocationButtonEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isMyLocationButtonEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isScrollGesturesEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isScrollGesturesEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isScrollGesturesEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isZoomGesturesEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isZoomGesturesEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isZoomGesturesEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isTiltGesturesEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isTiltGesturesEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isTiltGesturesEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isRotateGesturesEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isRotateGesturesEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isRotateGesturesEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getLogoPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::getLogoPosition(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getLogoPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isIndoorSwitchEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isIndoorSwitchEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isIndoorSwitchEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setIndoorSwitchEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setIndoorSwitchEnabled(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIndoorSwitchEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoMarginRate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setLogoMarginRate(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLogoMarginRate(var1.intValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::getLogoMarginRate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::getLogoMarginRate(" + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getLogoMarginRate(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoLeftMargin", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setLogoLeftMargin(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLogoLeftMargin(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoBottomMargin", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setLogoBottomMargin(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLogoBottomMargin(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setZoomInByScreenCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setZoomInByScreenCenter(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZoomInByScreenCenter(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setGestureScaleByMapCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setGestureScaleByMapCenter(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setGestureScaleByMapCenter(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::isGestureScaleByMapCenter", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::isGestureScaleByMapCenter(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isGestureScaleByMapCenter();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.UiSettings::setLogoCenter", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.UiSettings __this__ = (com.amap.api.maps.UiSettings) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.UiSettings@" + __this__ + "::setLogoCenter(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLogoCenter(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindowUpdateTime", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::setInfoWindowUpdateTime(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowUpdateTime(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindowUpdateTime", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::getInfoWindowUpdateTime(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getInfoWindowUpdateTime();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindowType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::setInfoWindowType(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindowType", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::getInfoWindowType(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getInfoWindowType();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoWindow", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::getInfoWindow(" + "" + ")");
                }
            
                // invoke native method
                android.view.View __result__ = null;
                try {
                    __result__ = __this__.getInfoWindow();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoContent", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.view.View var1 = (android.view.View) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::setInfoContent(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoContent(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::setInfoWindow", (__args__, __methodResult__) -> {
                // args
                // ref arg
                android.view.View var1 = (android.view.View) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::setInfoWindow(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindow(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.InfoWindowParams::getInfoContents", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.InfoWindowParams __this__ = (com.amap.api.maps.InfoWindowParams) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.InfoWindowParams@" + __this__ + "::getInfoContents(" + "" + ")");
                }
            
                // invoke native method
                android.view.View __result__ = null;
                try {
                    __result__ = __this__.getInfoContents();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomIn", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::zoomIn(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.zoomIn();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomOut", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::zoomOut(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.zoomOut();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::scrollBy", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::scrollBy(" + var0 + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.scrollBy(var0.floatValue(), var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomTo", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::zoomTo(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.zoomTo(var0.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomBy__double", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::zoomBy(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.zoomBy(var0.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::zoomBy__double__android_graphics_Point", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                android.graphics.Point var1 = (android.graphics.Point) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::zoomBy(" + var0 + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.zoomBy(var0.floatValue(), var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newCameraPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CameraPosition var0 = (com.amap.api.maps.model.CameraPosition) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newCameraPosition(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newCameraPosition(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newLatLng(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLng(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngZoom", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newLatLngZoom(" + var0 + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngZoom(var0, var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBounds__com_amap_api_maps_model_LatLngBounds__int", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newLatLngBounds(" + var0 + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBounds(var0, var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeLatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var0 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::changeLatLng(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.changeLatLng(var0);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeBearing", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::changeBearing(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.changeBearing(var0.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::changeTilt", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::changeTilt(" + var0 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.changeTilt(var0.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBounds__com_amap_api_maps_model_LatLngBounds__int__int__int", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newLatLngBounds(" + var0 + var1 + var2 + var3 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBounds(var0, var1.intValue(), var2.intValue(), var3.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.CameraUpdateFactory::newLatLngBoundsRect", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLngBounds var0 = (com.amap.api.maps.model.LatLngBounds) ((Map<String, Object>) __args__).get("var0");
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
                // ref arg
                Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.CameraUpdateFactory::newLatLngBoundsRect(" + var0 + var1 + var2 + var3 + var4 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.CameraUpdate __result__ = null;
                try {
                    __result__ = com.amap.api.maps.CameraUpdateFactory.newLatLngBoundsRect(var0, var1.intValue(), var2.intValue(), var3.intValue(), var4.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapException::getErrorMessage", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.AMapException __this__ = (com.amap.api.maps.AMapException) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapException@" + __this__ + "::getErrorMessage(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getErrorMessage();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::logoPosition", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapOptions@" + __this__ + "::logoPosition(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMapOptions __result__ = null;
                try {
                    __result__ = __this__.logoPosition(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::zOrderOnTop", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapOptions@" + __this__ + "::zOrderOnTop(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMapOptions __result__ = null;
                try {
                    __result__ = __this__.zOrderOnTop(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.AMapOptions::mapType", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.AMapOptions __this__ = (com.amap.api.maps.AMapOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.AMapOptions@" + __this__ + "::mapType(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.AMapOptions __result__ = null;
                try {
                    __result__ = __this__.mapType(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
        }};
    }
}
