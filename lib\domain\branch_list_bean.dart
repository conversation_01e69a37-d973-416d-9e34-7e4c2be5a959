/*
 * 项目名：福多多APP
 * 作者：AI Assistant
 * 创建时间：2025年07月17日
 * 修改时间：2025年07月17日
 */

/// 门店列表响应数据结构
class BranchListBean {
  int? code;
  BranchListData? data;
  String? msg;

  BranchListBean({
    this.code,
    this.data,
    this.msg,
  });

  BranchListBean.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    data = json['data'] != null ? BranchListData.fromJson(json['data']) : null;
    msg = json['msg'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['msg'] = this.msg;
    return data;
  }
}

/// 门店列表数据
class BranchListData {
  List<BranchInfo>? list;
  int? total;

  BranchListData({
    this.list,
    this.total,
  });

  BranchListData.fromJson(Map<String, dynamic> json) {
    if (json['list'] != null) {
      list = <BranchInfo>[];
      json['list'].forEach((v) {
        list!.add(BranchInfo.fromJson(v));
      });
    }
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    data['total'] = this.total;
    return data;
  }
}

/// 门店信息
class BranchInfo {
  String? branchId;
  String? branchNo;
  String? branchName;
  String? branchAddr;
  double? longitude;
  double? latitude;
  String? contactPhone;
  int? lastVisitDays;
  int? lastOrderDays;
  String? branchImages;

  BranchInfo({
    this.branchId,
    this.branchNo,
    this.branchName,
    this.branchAddr,
    this.longitude,
    this.latitude,
    this.contactPhone,
    this.lastVisitDays,
    this.lastOrderDays,
    this.branchImages,
  });

  BranchInfo.fromJson(Map<String, dynamic> json) {
    branchId = json['branchId'];
    branchNo = json['branchNo'];
    branchName = json['branchName'];
    branchAddr = json['branchAddr'];
    longitude = json['longitude']?.toDouble();
    latitude = json['latitude']?.toDouble();
    contactPhone = json['contactPhone'];
    lastVisitDays = json['lastVisitDays'];
    lastOrderDays = json['lastOrderDays'];
    branchImages = json['branchImages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['branchId'] = this.branchId;
    data['branchNo'] = this.branchNo;
    data['branchName'] = this.branchName;
    data['branchAddr'] = this.branchAddr;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['contactPhone'] = this.contactPhone;
    data['lastVisitDays'] = this.lastVisitDays;
    data['lastOrderDays'] = this.lastOrderDays;
    data['branchImages'] = this.branchImages;
    return data;
  }
}

/// 门店查询请求参数
class BranchListRequest {
  int? colonelId;
  String? searchName;
  String? branchNo;
  String? branchName;
  String? contactPhone;
  int? pageNo;
  int? pageSize;
  int? isOpenSeas;
  String? distance;
  double? longitude;
  double? latitude;

  BranchListRequest({
    this.colonelId,
    this.branchNo,
    this.searchName,
    this.branchName,
    this.contactPhone,
    this.pageNo,
    this.pageSize,
    this.isOpenSeas,
    this.distance,
    this.longitude,
    this.latitude,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.colonelId != null) data['colonelId'] = this.colonelId;
    if (this.branchNo != null) data['branchNo'] = this.branchNo;
    if (this.searchName != null) data['searchName'] = this.searchName;
    if (this.branchName != null) data['branchName'] = this.branchName;
    if (this.contactPhone != null) data['contactPhone'] = this.contactPhone;
    if (this.pageNo != null) data['pageNo'] = this.pageNo;
    if (this.pageSize != null) data['pageSize'] = this.pageSize;
    if (this.isOpenSeas != null) data['isOpenSeas'] = this.isOpenSeas;
    if (this.distance != null) data['distance'] = this.distance;
    if (this.longitude != null) data['longitude'] = this.longitude;
    if (this.latitude != null) data['latitude'] = this.latitude;
    return data;
  }
}
