//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_map_fluttify.AmapMapFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler5 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.maps.model.MultiPointOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlay __this__ = (com.amap.api.maps.model.MultiPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MultiPointOverlay::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MultiPointOverlay __this__ = (com.amap.api.maps.model.MultiPointOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MultiPointOverlay@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BaseOptions::resetUpdateFlags", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BaseOptions __this__ = (com.amap.api.maps.model.BaseOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BaseOptions@" + __this__ + "::resetUpdateFlags(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.resetUpdateFlags();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolylineOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolylineOptionsCreator __this__ = (com.amap.api.maps.model.PolylineOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolylineOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolylineOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setPeriod", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setPeriod(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPeriod(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getPeriod", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getPeriod(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getPeriod();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setIcons", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> var1 = (java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setIcons(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIcons(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getIcons", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getIcons(" + "" + ")");
                }
            
                // invoke native method
                java.util.ArrayList<com.amap.api.maps.model.BitmapDescriptor> __result__ = null;
                try {
                    __result__ = __this__.getIcons();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setPerspective", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setPerspective(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPerspective(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isPerspective", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isPerspective(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isPerspective();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setIcon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setIcon(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setIcon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setAnchor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setAnchor(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAnchor(var1.floatValue(), var2.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setDraggable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setDraggable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDraggable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isDraggable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isDraggable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isDraggable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::hideInfoWindow", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::hideInfoWindow(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.hideInfoWindow();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isInfoWindowShown", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isInfoWindowShown(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isInfoWindowShown();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setToTop", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setToTop(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setToTop();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setFlat", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setFlat(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFlat(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isFlat", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isFlat(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isFlat();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setPositionByPixels", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setPositionByPixels(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPositionByPixels(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setAnimationListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.animation.Animation.AnimationListener var1 = (com.amap.api.maps.model.animation.Animation.AnimationListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setAnimationListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAnimationListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getAlpha", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getAlpha(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAlpha();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setAlpha", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setAlpha(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAlpha(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getDisplayLevel", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getDisplayLevel(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getDisplayLevel();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getOptions", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getOptions(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.MarkerOptions __result__ = null;
                try {
                    __result__ = __this__.getOptions();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isClickable", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isClickable(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isClickable();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isInfoWindowAutoOverturn", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isInfoWindowAutoOverturn(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isInfoWindowAutoOverturn();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setInfoWindowEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setInfoWindowEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setInfoWindowEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setMarkerOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.MarkerOptions var1 = (com.amap.api.maps.model.MarkerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setMarkerOptions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMarkerOptions(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setAutoOverturnInfoWindow", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setAutoOverturnInfoWindow(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAutoOverturnInfoWindow(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setClickable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setClickable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setClickable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setDisplayLevel", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setDisplayLevel(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDisplayLevel(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setFixingPointEnable", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setFixingPointEnable(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setFixingPointEnable(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isRemoved", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isRemoved(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isRemoved();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setPositionNotUpdate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setPositionNotUpdate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPositionNotUpdate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setRotateAngleNotUpdate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setRotateAngleNotUpdate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRotateAngleNotUpdate(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::setBelowMaskLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::setBelowMaskLayer(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setBelowMaskLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getAnchorU", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getAnchorU(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorU();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::getAnchorV", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::getAnchorV(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getAnchorV();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Marker::isViewMode", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Marker __this__ = (com.amap.api.maps.model.Marker) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Marker@" + __this__ + "::isViewMode(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isViewMode();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::tileProvider", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.TileProvider var1 = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::tileProvider(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.tileProvider(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::memCacheSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::memCacheSize(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.memCacheSize(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::diskCacheSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::diskCacheSize(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.diskCacheSize(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::diskCacheDir", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::diskCacheDir(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.diskCacheDir(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::memoryCacheEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::memoryCacheEnabled(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.memoryCacheEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::diskCacheEnabled", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::diskCacheEnabled(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.diskCacheEnabled(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getTileProvider", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getTileProvider(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileProvider __result__ = null;
                try {
                    __result__ = __this__.getTileProvider();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getMemCacheSize", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getMemCacheSize(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getMemCacheSize();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getDiskCacheSize", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getDiskCacheSize(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getDiskCacheSize();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getDiskCacheDir", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getDiskCacheDir(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getDiskCacheDir();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getMemoryCacheEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getMemoryCacheEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.getMemoryCacheEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlayOptions::getDiskCacheEnabled", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlayOptions __this__ = (com.amap.api.maps.model.TileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlayOptions@" + __this__ + "::getDiskCacheEnabled(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.getDiskCacheEnabled();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::getOptions", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::getOptions(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.HeatMapGridLayerOptions __result__ = null;
                try {
                    __result__ = __this__.getOptions();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.HeatMapGridLayer::setOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.HeatMapGridLayerOptions var1 = (com.amap.api.maps.model.HeatMapGridLayerOptions) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.HeatMapGridLayer __this__ = (com.amap.api.maps.model.HeatMapGridLayer) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.HeatMapGridLayer@" + __this__ + "::setOptions(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOptions(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getDrivingRouteStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getDrivingRouteStyle(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getDrivingRouteStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setDrivingRouteStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setDrivingRouteStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDrivingRouteStyle(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getTransitRouteStyle", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getTransitRouteStyle(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getTransitRouteStyle();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setTransitRouteStyle", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setTransitRouteStyle(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTransitRouteStyle(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getStartPoint", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getStartPoint(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getStartPoint();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setStartPoint", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setStartPoint(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStartPoint(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getEndPoint", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getEndPoint(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getEndPoint();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setEndPoint", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setEndPoint(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setEndPoint(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getEndName", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getEndName(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getEndName();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setEndName", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setEndName(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setEndName(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::getStartName", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::getStartName(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getStartName();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.RoutePara::setStartName", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.RoutePara __this__ = (com.amap.api.maps.model.RoutePara) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.RoutePara@" + __this__ + "::setStartName(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStartName(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.ArcOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.ArcOptionsCreator __this__ = (com.amap.api.maps.model.ArcOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.ArcOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.ArcOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CameraPositionCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CameraPositionCreator __this__ = (com.amap.api.maps.model.CameraPositionCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CameraPositionCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.CameraPosition[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::setData", (__args__, __methodResult__) -> {
                // args
                // ref arg
                byte[] var1 = (byte[]) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::setData(" + var1 + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.setData(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::setImageMode", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::setImageMode(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setImageMode(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::setGenerateCrossImageListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener var1 = (com.amap.api.maps.model.CrossOverlay.GenerateCrossImageListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::setGenerateCrossImageListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setGenerateCrossImageListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.CrossOverlay::setOnCrossVectorUpdateListener", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener var1 = (com.amap.api.maps.model.CrossOverlay.OnCrossVectorUpdateListener) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.CrossOverlay __this__ = (com.amap.api.maps.model.CrossOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.CrossOverlay@" + __this__ + "::setOnCrossVectorUpdateListener(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setOnCrossVectorUpdateListener(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverLifeModule::setVelocityOverLife", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.VelocityGenerate var1 = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverLifeModule __this__ = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverLifeModule@" + __this__ + "::setVelocityOverLife(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVelocityOverLife(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverLifeModule::setRotateOverLife", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.RotationOverLife var1 = (com.amap.api.maps.model.particle.RotationOverLife) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverLifeModule __this__ = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverLifeModule@" + __this__ + "::setRotateOverLife(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setRotateOverLife(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverLifeModule::setSizeOverLife", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.SizeOverLife var1 = (com.amap.api.maps.model.particle.SizeOverLife) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverLifeModule __this__ = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverLifeModule@" + __this__ + "::setSizeOverLife(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setSizeOverLife(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverLifeModule::setColorGenerate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ColorGenerate var1 = (com.amap.api.maps.model.particle.ColorGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverLifeModule __this__ = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverLifeModule@" + __this__ + "::setColorGenerate(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setColorGenerate(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.SizeOverLife::getSizeX", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.SizeOverLife __this__ = (com.amap.api.maps.model.particle.SizeOverLife) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.SizeOverLife@" + __this__ + "::getSizeX(" + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getSizeX(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.SizeOverLife::getSizeY", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.SizeOverLife __this__ = (com.amap.api.maps.model.particle.SizeOverLife) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.SizeOverLife@" + __this__ + "::getSizeY(" + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getSizeY(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.SizeOverLife::getSizeZ", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.SizeOverLife __this__ = (com.amap.api.maps.model.particle.SizeOverLife) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.SizeOverLife@" + __this__ + "::getSizeZ(" + var1 + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getSizeZ(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ColorGenerate::getColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ColorGenerate __this__ = (com.amap.api.maps.model.particle.ColorGenerate) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ColorGenerate@" + __this__ + "::getColor(" + "" + ")");
                }
            
                // invoke native method
                float[] __result__ = null;
                try {
                    __result__ = __this__.getColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.VelocityGenerate::getX", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.VelocityGenerate __this__ = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.VelocityGenerate@" + __this__ + "::getX(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getX();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.VelocityGenerate::getY", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.VelocityGenerate __this__ = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.VelocityGenerate@" + __this__ + "::getY(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getY();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.VelocityGenerate::getZ", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.VelocityGenerate __this__ = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.VelocityGenerate@" + __this__ + "::getZ(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZ();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.RotationOverLife::getRotate", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.RotationOverLife __this__ = (com.amap.api.maps.model.particle.RotationOverLife) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.RotationOverLife@" + __this__ + "::getRotate(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getRotate();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleShapeModule::isUseRatio", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleShapeModule __this__ = (com.amap.api.maps.model.particle.ParticleShapeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleShapeModule@" + __this__ + "::isUseRatio(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isUseRatio();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleShapeModule::getPoint", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleShapeModule __this__ = (com.amap.api.maps.model.particle.ParticleShapeModule) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleShapeModule@" + __this__ + "::getPoint(" + "" + ")");
                }
            
                // invoke native method
                float[] __result__ = null;
                try {
                    __result__ = __this__.getPoint();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory::defaultOptions", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var0 = (Number) ((Map<String, Object>) __args__).get("var0");
            
                // ref
            
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory::defaultOptions(" + var0 + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.particle.ParticleOverlayOptions> __result__ = null;
                try {
                    __result__ = com.amap.api.maps.model.particle.ParticleOverlayOptionsFactory.defaultOptions(var0.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::icon", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::icon(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.icon(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getIcon", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getIcon(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.BitmapDescriptor __result__ = null;
                try {
                    __result__ = __this__.getIcon();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getMaxParticles", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getMaxParticles(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getMaxParticles();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setMaxParticles", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setMaxParticles(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setMaxParticles(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::isLoop", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::isLoop(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isLoop();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setLoop", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setLoop(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setLoop(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getDuration", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getDuration(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getDuration();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setDuration", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setDuration(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setDuration(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleLifeTime", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleLifeTime(" + "" + ")");
                }
            
                // invoke native method
                Long __result__ = null;
                try {
                    __result__ = __this__.getParticleLifeTime();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleLifeTime", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleLifeTime(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleLifeTime(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleEmissionModule", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleEmissionModule(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleEmissionModule __result__ = null;
                try {
                    __result__ = __this__.getParticleEmissionModule();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleEmissionModule", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleEmissionModule var1 = (com.amap.api.maps.model.particle.ParticleEmissionModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleEmissionModule(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleEmissionModule(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleShapeModule", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleShapeModule(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleShapeModule __result__ = null;
                try {
                    __result__ = __this__.getParticleShapeModule();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleShapeModule", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleShapeModule var1 = (com.amap.api.maps.model.particle.ParticleShapeModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleShapeModule(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleShapeModule(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleStartSpeed", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleStartSpeed(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.VelocityGenerate __result__ = null;
                try {
                    __result__ = __this__.getParticleStartSpeed();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleStartSpeed", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.VelocityGenerate var1 = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleStartSpeed(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleStartSpeed(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleStartColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ColorGenerate var1 = (com.amap.api.maps.model.particle.ColorGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleStartColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleStartColor(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleStartColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleStartColor(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ColorGenerate __result__ = null;
                try {
                    __result__ = __this__.getParticleStartColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setParticleOverLifeModule", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleOverLifeModule var1 = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setParticleOverLifeModule(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setParticleOverLifeModule(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getParticleOverLifeModule", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getParticleOverLifeModule(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverLifeModule __result__ = null;
                try {
                    __result__ = __this__.getParticleOverLifeModule();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setStartParticleSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setStartParticleSize(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setStartParticleSize(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getStartParticleW", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getStartParticleW(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getStartParticleW();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getstartParticleH", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getstartParticleH(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getstartParticleH();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.particle.ParticleOverlayOptions __result__ = null;
                try {
                    __result__ = __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlayOptions::isVisibile", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlayOptions __this__ = (com.amap.api.maps.model.particle.ParticleOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlayOptions@" + __this__ + "::isVisibile(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisibile();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::destroy", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::destroy(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.destroy();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setStartParticleSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setStartParticleSize(" + var1 + var2 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStartParticleSize(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setMaxParticles", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setMaxParticles(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setMaxParticles(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setDuration", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setDuration(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDuration(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setParticleLifeTime", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setParticleLifeTime(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setParticleLifeTime(var1.longValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setParticleStartSpeed", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.VelocityGenerate var1 = (com.amap.api.maps.model.particle.VelocityGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setParticleStartSpeed(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setParticleStartSpeed(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setLoop", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setLoop(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setLoop(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setParticleShapeModule", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleShapeModule var1 = (com.amap.api.maps.model.particle.ParticleShapeModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setParticleShapeModule(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setParticleShapeModule(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setParticleEmission", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleEmissionModule var1 = (com.amap.api.maps.model.particle.ParticleEmissionModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setParticleEmission(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setParticleEmission(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::getCurrentParticleNum", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::getCurrentParticleNum(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getCurrentParticleNum();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setParticleOverLifeModule", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ParticleOverLifeModule var1 = (com.amap.api.maps.model.particle.ParticleOverLifeModule) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setParticleOverLifeModule(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setParticleOverLifeModule(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.particle.ParticleOverlay::setStartColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.particle.ColorGenerate var1 = (com.amap.api.maps.model.particle.ColorGenerate) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.particle.ParticleOverlay __this__ = (com.amap.api.maps.model.particle.ParticleOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.particle.ParticleOverlay@" + __this__ + "::setStartColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setStartColor(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileOverlayOptions::setTileProvider", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.TileProvider var1 = (com.amap.api.maps.model.TileProvider) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MVTTileOverlayOptions __this__ = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileOverlayOptions@" + __this__ + "::setTileProvider(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTileProvider(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileOverlayOptions::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MVTTileOverlayOptions __this__ = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileOverlayOptions@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileOverlayOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MVTTileOverlayOptions __this__ = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileOverlayOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileOverlayOptions::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.MVTTileOverlayOptions __this__ = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileOverlayOptions@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.MVTTileOverlayOptions::visible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.MVTTileOverlayOptions __this__ = (com.amap.api.maps.model.MVTTileOverlayOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.MVTTileOverlayOptions@" + __this__ + "::visible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.visible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::clone", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::clone(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.clone();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::position", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::position(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.position(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::text", (__args__, __methodResult__) -> {
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::text(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.text(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::visible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::visible(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.visible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::zIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::zIndex(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.zIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::rotate", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::rotate(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.rotate(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::align", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::align(" + var1 + var2 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.align(var1.intValue(), var2.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::backgroundColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::backgroundColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.backgroundColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::setObject", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.lang.Object var1 = (java.lang.Object) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::setObject(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.setObject(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::fontColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::fontColor(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.fontColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::fontSize", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::fontSize(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TextOptions __result__ = null;
                try {
                    __result__ = __this__.fontSize(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getPosition", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getPosition(" + "" + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getPosition();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getText", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getText(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getText();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getRotate", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getRotate(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getRotate();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getAlignX", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getAlignX(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getAlignX();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getAlignY", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getAlignY(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getAlignY();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getBackgroundColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getBackgroundColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getBackgroundColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getFontColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getFontColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFontColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getObject", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getObject(" + "" + ")");
                }
            
                // invoke native method
                java.lang.Object __result__ = null;
                try {
                    __result__ = __this__.getObject();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getFontSize", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getFontSize(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getFontSize();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TextOptions::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TextOptions __this__ = (com.amap.api.maps.model.TextOptions) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TextOptions@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.ImageOptions.ShapeType::value", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.ImageOptions.ShapeType __this__ = (com.amap.api.maps.model.ImageOptions.ShapeType) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.ImageOptions.ShapeType@" + __this__ + "::value(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.value();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::clearTileCache", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::clearTileCache(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.clearTileCache();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileOverlay::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.TileOverlay __this__ = (com.amap.api.maps.model.TileOverlay) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileOverlay@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.PolygonOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.PolygonOptionsCreator __this__ = (com.amap.api.maps.model.PolygonOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.PolygonOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.PolygonOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.TileProjectionCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.TileProjectionCreator __this__ = (com.amap.api.maps.model.TileProjectionCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.TileProjectionCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.TileProjection[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.GroundOverlayOptionsCreator::newArray", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.GroundOverlayOptionsCreator __this__ = (com.amap.api.maps.model.GroundOverlayOptionsCreator) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.GroundOverlayOptionsCreator@" + __this__ + "::newArray(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.GroundOverlayOptions[] __result__ = null;
                try {
                    __result__ = __this__.newArray(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.BaseOptions.BaseUpdateFlags::reset", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.BaseOptions.BaseUpdateFlags __this__ = (com.amap.api.maps.model.BaseOptions.BaseUpdateFlags) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.BaseOptions.BaseUpdateFlags@" + __this__ + "::reset(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.reset();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::remove", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::remove(" + "" + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.remove();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getId", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getId(" + "" + ")");
                }
            
                // invoke native method
                String __result__ = null;
                try {
                    __result__ = __this__.getId();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setPoints", (__args__, __methodResult__) -> {
                // args
                // ref arg
                java.util.List<com.amap.api.maps.model.LatLng> var1 = (java.util.List<com.amap.api.maps.model.LatLng>) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setPoints(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setPoints(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getPoints", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getPoints(" + "" + ")");
                }
            
                // invoke native method
                java.util.List<com.amap.api.maps.model.LatLng> __result__ = null;
                try {
                    __result__ = __this__.getPoints();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setGeodesic", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setGeodesic(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setGeodesic(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::isGeodesic", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::isGeodesic(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isGeodesic();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setDottedLine", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setDottedLine(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setDottedLine(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::isDottedLine", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::isDottedLine(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isDottedLine();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setWidth", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setWidth(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setWidth(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getWidth", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getWidth(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getWidth();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setColor", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setColor(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setColor(var1.intValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getColor", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getColor(" + "" + ")");
                }
            
                // invoke native method
                Integer __result__ = null;
                try {
                    __result__ = __this__.getColor();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setZIndex", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setZIndex(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setZIndex(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getZIndex", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getZIndex(" + "" + ")");
                }
            
                // invoke native method
                Float __result__ = null;
                try {
                    __result__ = __this__.getZIndex();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setVisible", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setVisible(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setVisible(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::isVisible", (__args__, __methodResult__) -> {
                // args
            
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::isVisible(" + "" + ")");
                }
            
                // invoke native method
                Boolean __result__ = null;
                try {
                    __result__ = __this__.isVisible();
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::getNearestLatLng", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.LatLng var1 = (com.amap.api.maps.model.LatLng) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::getNearestLatLng(" + var1 + ")");
                }
            
                // invoke native method
                com.amap.api.maps.model.LatLng __result__ = null;
                try {
                    __result__ = __this__.getNearestLatLng(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setTransparency", (__args__, __methodResult__) -> {
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setTransparency(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setTransparency(var1.floatValue());
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setAboveMaskLayer", (__args__, __methodResult__) -> {
                // args
                // ref arg
                boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setAboveMaskLayer(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setAboveMaskLayer(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
            // method
            put("com.amap.api.maps.model.Polyline::setCustomTexture", (__args__, __methodResult__) -> {
                // args
                // ref arg
                com.amap.api.maps.model.BitmapDescriptor var1 = (com.amap.api.maps.model.BitmapDescriptor) ((Map<String, Object>) __args__).get("var1");
            
                // ref
                com.amap.api.maps.model.Polyline __this__ = (com.amap.api.maps.model.Polyline) ((Map<String, Object>) __args__).get("__this__");
            
                // print log
                if (getEnableLog()) {
                    Log.d("fluttify-java", "fluttify-java: com.amap.api.maps.model.Polyline@" + __this__ + "::setCustomTexture(" + var1 + ")");
                }
            
                // invoke native method
                Void __result__ = null;
                try {
                    __this__.setCustomTexture(var1);
                } catch (Throwable throwable) {
                    throwable.printStackTrace();
                    if (getEnableLog()) {
                        Log.d("Current HEAP: ", getHEAP().toString());
                    }
                    __methodResult__.error(throwable.getMessage(), null, null);
                    return;
                }
            
                __methodResult__.success(__result__);
            });
        }};
    }
}
