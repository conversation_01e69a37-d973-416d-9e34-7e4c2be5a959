import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fuduoduo/pages/login/view.dart';
import 'package:fuduoduo/resource/color_resource.dart';
import 'package:fuduoduo/utils/color_utils.dart';
import 'package:fuduoduo/utils/common_utils.dart';
import 'package:get/get.dart';

import 'logic.dart';
import 'package:fuduoduo/pages/login/logic.dart';
import 'package:fuduoduo/pages/login/view.dart';

import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:fuduoduo/utils/storage.dart';
import 'package:fuduoduo/store/index.dart';
import 'package:fuduoduo/utils/user_appModule_utils.dart';
import '../../../route/index.dart';

import 'package:fuduoduo/utils/storage_common.dart';
import 'dart:convert';

class B2bChooseTypePage extends StatefulWidget {
  @override
  _B2bChooseTypeState createState() => _B2bChooseTypeState();
}

class _B2bChooseTypeState extends State<B2bChooseTypePage> {
  final logic = Get.put(B2bChooseTypeLogic());
  final state = Get
      .find<B2bChooseTypeLogic>()
      .state;
  final _loginState = Get.put(LoginLogic()).state;
  final publicState = Get.find<Public>().state;

  @override
  void initState() {
    super.initState();

    print('_loginState ${_loginState.AppLists}');
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorResource.GRAY_LOW_COMMON_COLOR,
      appBar: MyCommonUtils.customAppBar('请选择您的身份', callBack: () async {
        SpUtil.remove("access_token");
        SpUtil.remove("branchName");
        SpUtil.remove("branchNo");
        await SecureStorage.token().delete?.call();

        /// 退出登录，权限清空
        publicState.appModules = [];
        //清仓库
        publicState.branchInfo = {};
        UserAppModuleUtils().removeUserAppModules();
        Get.offNamed(PageName.LOGIN);
      }),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    var _selectId = 'erpSystem';
    const _selectColor = Color(0xffFF751A);

    List<TDRadio> _list = [];

    // 从登录状态中获取应用列表并转换为 TDRadio 组件
    if (_loginState.AppLists.isNotEmpty) {
      _list = _loginState.AppLists.asMap().entries.map<TDRadio>((entry) {
        Map<String, dynamic> item = entry.value;
        return TDRadio(
          selectColor: _selectColor,
          id: item['id'],
          title: item['type'] ?? '未知类型',
          cardMode: true,
        );
      }).toList();
    }


    // print('_list_list $_list');
    return SafeArea(
      child: Container(
          margin: EdgeInsets.only(left: 40.w, right: 40.w, top: 40.h, bottom: 40.h),
          child: Column(children: [
            TDRadioGroup(
              selectId: _selectId,
              cardMode: true,
              direction: Axis.vertical,
              onRadioGroupChange: (value) {
                // print('_qqselectId $value');
                _selectId = value!;
              },
              directionalTdRadios: _list
            ),

            Spacer(),

            InkWell(
              onTap: () async {
                print('InkWell $_selectId');

                if (_selectId == 'erpSystem') {
                  // 只需要保留选择状态就行了
                  await SecureStorageCommon.save<String>('userType').set('erpSystem');
                  if (_loginState.erpSystemItem != {}) {
                    // 保存租户信息
                    String jsonString = json.encode(_loginState.erpSystemItem);
                    await SecureStorageCommon.save<String>('userInfo').set(jsonString);

                    String accessToken = _loginState.erpSystemItem['token'];
                    SpUtil.putString("access_token", accessToken!);
                    /// 如果访问令牌不为空，则将其存储在安全存储中。
                    if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);

                    logic.getLoginInfo();
                  }
                  // var a = await SecureStorageCommon.save('userInfo').get();
                  // print('xxxx ${json.decode(a)['id']}');

                  // Get.offAndToNamed(PageName.TAB);


                } else if (_selectId == 'b2bSystem') {
                  await SecureStorageCommon.save<String>('userType').set('b2bSystem');
                  if (_loginState.b2bSystemItem != {}) {
                    // 保存租户信息
                    String jsonString = json.encode(_loginState.b2bSystemItem);
                    await SecureStorageCommon.save<String>('userInfo').set(jsonString);

                    String accessToken = _loginState.b2bSystemItem['token'];
                    print('accessTokenaccessTokenaccessTokenaccessToken $accessToken');
                    SpUtil.putString("access_token", accessToken!);
                    /// 如果访问令牌不为空，则将其存储在安全存储中。
                    if (!accessToken.isEmpty) SecureStorage.token<String>().set(accessToken!);

                  }

                  // ColorResource().setB2BColor();
                  Get.offAndToNamed(PageName.B2BTAB);
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: 80.w,
                padding: EdgeInsets.only(
                    left: 30.w, right: 30.w, bottom: 22.w, top: 22.w),
                decoration: BoxDecoration(
                    color: ColorResource.WHITE_COMMON_COLOR,
                    gradient: LinearGradient(
                        colors: ColorResource.LINEAR_GRADIENT_COMMON_COLOR,
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40.r),
                        topRight: Radius.circular(40.r),
                        bottomLeft: Radius.circular(40.r),
                        bottomRight: Radius.circular(40.r))),
                child: Text(
                  '继续',
                  style: TextStyle(color: Colors.white, fontSize: 28.sp),
                ),
              ),
            )


          ]))
    );
  }
}
