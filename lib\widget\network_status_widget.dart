import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../utils/network_manager.dart';
import '../utils/common_utils.dart';

/// 网络状态提示组件
class NetworkStatusWidget extends StatelessWidget {
  final Widget child;
  final bool respectSafeArea; // 是否考虑安全区域

  const NetworkStatusWidget({
    Key? key,
    required this.child,
    this.respectSafeArea = true, // 默认考虑安全区域
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NetworkManager>(
      builder: (networkManager) {
        return Stack(
          children: [
            child,
            // 无网络提示条 - 使用SafeArea确保在安全区域内
            Obx(() => !networkManager.isConnected.value
                ? Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: respectSafeArea
                        ? SafeArea(
                            child: _buildNoNetworkBanner(),
                          )
                        : _buildNoNetworkBanner(),
                  )
                : const SizedBox.shrink()),
          ],
        );
      },
    );
  }

  /// 构建无网络提示条
  Widget _buildNoNetworkBanner() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 26.w, vertical: 20.h),
      decoration: const BoxDecoration(
        color: Color(0xFFFFEBEE), // 浅红色背景
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFFFCDD2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 警告图标
          Container(
            width: 20.w,
            height: 20.w,
            decoration: const BoxDecoration(
              color: Color(0xFFFF5722), // 橙红色
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.warning,
              color: Colors.white,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 8.w),
          // 提示文字
          Expanded(
            child: Text(
              '当前网络不可用',
              style: TextStyle(
                color: const Color(0xFFD32F2F), // 深红色文字
                fontSize: 24.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // 刷新按钮
          // GestureDetector(
          //   onTap: () {
          //     NetworkManager.instance.checkConnection();
          //   },
          //   child: Container(
          //     padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          //     child: Icon(
          //       Icons.refresh,
          //       color: const Color(0xFFD32F2F),
          //       size: 26.sp,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

/// 简化版无网络提示组件（用于对话框等场景）
class SimpleNetworkStatusWidget extends StatelessWidget {
  const SimpleNetworkStatusWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NetworkManager>(
      builder: (networkManager) {
        return Obx(() => !networkManager.isConnected.value
            ? Container(
                margin: EdgeInsets.all(16.w),
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFEBEE),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: const Color(0xFFFFCDD2),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.wifi_off,
                      color: const Color(0xFFD32F2F),
                      size: 16.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '网络连接不可用',
                      style: TextStyle(
                        color: const Color(0xFFD32F2F),
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              )
            : const SizedBox.shrink());
      },
    );
  }
}

/// 网络状态检查混入类
mixin NetworkStatusMixin {
  /// 检查网络状态并显示提示
  Future<bool> checkNetworkWithToast() async {
    final isConnected = await NetworkManager.instance.checkConnection();
    if (!isConnected) {
      MyCommonUtils.showToast('当前网络不可用，请检查网络连接');
    }
    return isConnected;
  }
  
  /// 在网络请求前检查网络状态
  Future<bool> checkNetworkBeforeRequest() async {
    final isConnected = NetworkManager.instance.isConnected.value;
    if (!isConnected) {
      MyCommonUtils.showToast('网络连接不可用，请检查网络设置');
      return false;
    }
    return true;
  }
}
