import 'package:collection/collection.dart';

class List {
	String? couponId;
	String? sysCode;
	String? couponTemplateId;
	String? couponName;
	int? funcScope;
	int? state;
	String? expirationDateStart;
	String? expirationDateEnd;
	dynamic useTime;
	dynamic relateOrderNo;
	int? spuScope;
	int? discountType;
	int? triggerAmt;
	int? discountAmt;
	dynamic discountPercent;
	dynamic discountLimitAmt;
	int? receiveType;
	int? costFlag;
	int? excludable;
	dynamic spuScopeAs;
	int? supplierId;
	dynamic couponBatchId;
	String? createTime;
	int? sameTypeExcludable;
	dynamic supplierIdList;

	List({
		this.couponId, 
		this.sysCode, 
		this.couponTemplateId, 
		this.couponName, 
		this.funcScope, 
		this.state, 
		this.expirationDateStart, 
		this.expirationDateEnd, 
		this.useTime, 
		this.relateOrderNo, 
		this.spuScope, 
		this.discountType, 
		this.triggerAmt, 
		this.discountAmt, 
		this.discountPercent, 
		this.discountLimitAmt, 
		this.receiveType, 
		this.costFlag, 
		this.excludable, 
		this.spuScopeAs, 
		this.supplierId, 
		this.couponBatchId, 
		this.createTime, 
		this.sameTypeExcludable, 
		this.supplierIdList, 
	});

	factory List.fromJson(Map<String, dynamic> json) => List(
				couponId: json['couponId'] as String?,
				sysCode: json['sysCode'] as String?,
				couponTemplateId: json['couponTemplateId'] as String?,
				couponName: json['couponName'] as String?,
				funcScope: json['funcScope'] as int?,
				state: json['state'] as int?,
				expirationDateStart: json['expirationDateStart'] as String?,
				expirationDateEnd: json['expirationDateEnd'] as String?,
				useTime: json['useTime'] as dynamic,
				relateOrderNo: json['relateOrderNo'] as dynamic,
				spuScope: json['spuScope'] as int?,
				discountType: json['discountType'] as int?,
				triggerAmt: json['triggerAmt'] as int?,
				discountAmt: json['discountAmt'] as int?,
				discountPercent: json['discountPercent'] as dynamic,
				discountLimitAmt: json['discountLimitAmt'] as dynamic,
				receiveType: json['receiveType'] as int?,
				costFlag: json['costFlag'] as int?,
				excludable: json['excludable'] as int?,
				spuScopeAs: json['spuScopeAs'] as dynamic,
				supplierId: json['supplierId'] as int?,
				couponBatchId: json['couponBatchId'] as dynamic,
				createTime: json['createTime'] as String?,
				sameTypeExcludable: json['sameTypeExcludable'] as int?,
				supplierIdList: json['supplierIdList'] as dynamic,
			);

	Map<String, dynamic> toJson() => {
				'couponId': couponId,
				'sysCode': sysCode,
				'couponTemplateId': couponTemplateId,
				'couponName': couponName,
				'funcScope': funcScope,
				'state': state,
				'expirationDateStart': expirationDateStart,
				'expirationDateEnd': expirationDateEnd,
				'useTime': useTime,
				'relateOrderNo': relateOrderNo,
				'spuScope': spuScope,
				'discountType': discountType,
				'triggerAmt': triggerAmt,
				'discountAmt': discountAmt,
				'discountPercent': discountPercent,
				'discountLimitAmt': discountLimitAmt,
				'receiveType': receiveType,
				'costFlag': costFlag,
				'excludable': excludable,
				'spuScopeAs': spuScopeAs,
				'supplierId': supplierId,
				'couponBatchId': couponBatchId,
				'createTime': createTime,
				'sameTypeExcludable': sameTypeExcludable,
				'supplierIdList': supplierIdList,
			};

	@override
	bool operator ==(Object other) {
		if (identical(other, this)) return true;
		if (other is! List) return false;
		final mapEquals = const DeepCollectionEquality().equals;
		return mapEquals(other.toJson(), toJson());
	}

	@override
	int get hashCode =>
			couponId.hashCode ^
			sysCode.hashCode ^
			couponTemplateId.hashCode ^
			couponName.hashCode ^
			funcScope.hashCode ^
			state.hashCode ^
			expirationDateStart.hashCode ^
			expirationDateEnd.hashCode ^
			useTime.hashCode ^
			relateOrderNo.hashCode ^
			spuScope.hashCode ^
			discountType.hashCode ^
			triggerAmt.hashCode ^
			discountAmt.hashCode ^
			discountPercent.hashCode ^
			discountLimitAmt.hashCode ^
			receiveType.hashCode ^
			costFlag.hashCode ^
			excludable.hashCode ^
			spuScopeAs.hashCode ^
			supplierId.hashCode ^
			couponBatchId.hashCode ^
			createTime.hashCode ^
			sameTypeExcludable.hashCode ^
			supplierIdList.hashCode;
}
