class BranchInfoBean {
  int? code;
  String? msg;
  BranchInfoData? data;

  BranchInfoBean({
    this.code,
    this.msg,
    this.data,
  });

  factory BranchInfoBean.fromJson(Map<String, dynamic> json) {
    return BranchInfoBean(
      code: json['code'] as int?,
      msg: json['msg'] as String?,
      data: json['data'] != null
          ? BranchInfoData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': msg,
      'data': data?.toJson(),
    };
  }
}

class BranchInfoData {
  String? branchId;
  String? branchImages;
  String? branchNo;
  double? hdfkMaxAmt;
  String? branchName;
  String? branchAddr;
  String? contactName;
  String? contactPhone;
  String? colonelId;
  int? channelId;
  int? dictValue;
  String? channelName;
  String? areaId;
  String? areaName;
  String? groupId;
  String? groupName;
  int? status;
  int? auditState;
  int? salePriceCode;
  String? salePriceName;
  int? hdfkSupport;
  String? provinceName;
  String? cityName;
  String? districtName;
  String? provinceCode;
  String? cityCode;
  String? districtCode;
  String? streetCode;
  String? cityRegionName;
  int? lifecycleStage;
  String? lifecycleStageName;
  String? nextBranchLifecycleMessage;
  int? isPayOnline;
  String? memo;
  double? longitude;
  double? latitude;
  String? createTime;
  String? updateTime;
  int? lastVisitDays;
  int? lastOrderDays;
  int? thisMonthOrderCount;
  double? thisMonthOrderAmount;
  int? branchUserCount;
  String? imgUrls;

  BranchInfoData({
    this.branchId,
    this.branchNo,
    this.branchImages,
    this.branchName,
    this.hdfkMaxAmt,
    this.branchAddr,
    this.contactName,
    this.contactPhone,
    this.colonelId,
    this.dictValue,
    this.channelId,
    this.channelName,
    this.areaId,
    this.areaName,
    this.groupId,
    this.groupName,
    this.status,
    this.auditState,
    this.salePriceCode,
    this.salePriceName,
    this.hdfkSupport,
    this.provinceName,
    this.cityName,
    this.districtName,
    this.provinceCode,
    this.cityCode,
    this.districtCode,
    this.streetCode,
    this.cityRegionName,
    this.lifecycleStage,
    this.lifecycleStageName,
    this.nextBranchLifecycleMessage,
    this.isPayOnline,
    this.memo,
    this.longitude,
    this.latitude,
    this.createTime,
    this.updateTime,
    this.lastVisitDays,
    this.lastOrderDays,
    this.thisMonthOrderCount,
    this.thisMonthOrderAmount,
    this.branchUserCount,
    this.imgUrls,
  });

  factory BranchInfoData.fromJson(Map<String, dynamic> json) {
    return BranchInfoData(
      branchId: json['branchId']?.toString(),
      branchNo: json['branchNo']?.toString(),
      branchName: json['branchName']?.toString(),
      branchImages: json['branchImages']?.toString(),
      branchAddr: json['branchAddr']?.toString(),
      contactName: json['contactName']?.toString(),
      contactPhone: json['contactPhone']?.toString(),
      colonelId: json['colonelId']?.toString(),
      channelId: _parseToInt(json['channelId']),
      dictValue: _parseToInt(json['dictValue']),
      channelName: json['channelName']?.toString(),
      areaId: json['areaId']?.toString(),
      areaName: json['areaName']?.toString(),
      groupId: json['groupId']?.toString(),
      groupName: json['groupName']?.toString(),
      status: _parseToInt(json['status']),
      auditState: _parseToInt(json['auditState']),
      salePriceCode: _parseToInt(json['salePriceCode']),
      salePriceName: json['salePriceName']?.toString(),
      hdfkSupport: _parseToInt(json['hdfkSupport']),
      hdfkMaxAmt: _parseToDouble(json['hdfkMaxAmt']),
      provinceName: json['provinceName']?.toString(),
      cityName: json['cityName']?.toString(),
      districtName: json['districtName']?.toString(),
      provinceCode: json['provinceCode']?.toString(),
      cityCode: json['cityCode']?.toString(),
      districtCode: json['districtCode']?.toString(),
      streetCode: json['streetCode']?.toString(),
      cityRegionName: json['cityRegionName']?.toString(),
      lifecycleStage: _parseToInt(json['lifecycleStage']),
      lifecycleStageName: json['lifecycleStageName']?.toString(),
      nextBranchLifecycleMessage: json['nextBranchLifecycleMessage']?.toString(),
      isPayOnline: _parseToInt(json['isPayOnline']),
      memo: json['memo']?.toString(),
      longitude: _parseToDouble(json['longitude']),
      latitude: _parseToDouble(json['latitude']),
      createTime: json['createTime']?.toString(),
      updateTime: json['updateTime']?.toString(),
      lastVisitDays: _parseToInt(json['lastVisitDays']),
      lastOrderDays: _parseToInt(json['lastOrderDays']),
      thisMonthOrderCount: _parseToInt(json['thisMonthOrderCount']),
      thisMonthOrderAmount: _parseToDouble(json['thisMonthOrderAmount']),
      branchUserCount: _parseToInt(json['branchUserCount']),
      imgUrls: json['imgUrls']?.toString(),
    );
  }

  // 安全的整数解析方法
  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    if (value is double) return value.toInt();
    return null;
  }

  // 安全的双精度浮点数解析方法
  static double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'branchId': branchId,
      'branchNo': branchNo,
      'branchName': branchName,
      'branchAddr': branchAddr,
      'contactName': contactName,
      'contactPhone': contactPhone,
      'colonelId': colonelId,
      'channelId': channelId,
      'channelName': channelName,
      'areaId': areaId,
      'areaName': areaName,
      'groupId': groupId,
      'groupName': groupName,
      'status': status,
      'auditState': auditState,
      'salePriceCode': salePriceCode,
      'salePriceName': salePriceName,
      'hdfkSupport': hdfkSupport,
      'provinceName': provinceName,
      'cityName': cityName,
      'districtName': districtName,
      'provinceCode': provinceCode,
      'cityCode': cityCode,
      'districtCode': districtCode,
      'streetCode': streetCode,
      'cityRegionName': cityRegionName,
      'lifecycleStage': lifecycleStage,
      'lifecycleStageName': lifecycleStageName,
      'nextBranchLifecycleMessage': nextBranchLifecycleMessage,
      'isPayOnline': isPayOnline,
      'memo': memo,
      'longitude': longitude,
      'latitude': latitude,
      'createTime': createTime,
      'updateTime': updateTime,
      'lastVisitDays': lastVisitDays,
      'lastOrderDays': lastOrderDays,
      'thisMonthOrderCount': thisMonthOrderCount,
      'thisMonthOrderAmount': thisMonthOrderAmount,
      'branchUserCount': branchUserCount,
      'imgUrls': imgUrls,
    };
  }
}
