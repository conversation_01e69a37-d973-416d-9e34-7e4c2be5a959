//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_map_fluttify;

import android.content.Context;
import android.view.View;
import android.util.Log;
import android.app.Activity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMessageCodec;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformView;
import io.flutter.plugin.platform.PlatformViewFactory;

import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;

@SuppressWarnings("ALL")
class DownloadProgressViewFactory extends PlatformViewFactory {

    DownloadProgressViewFactory(BinaryMessenger messenger, Activity activity) {
        super(StandardMessageCodec.INSTANCE);

        this.messenger = messenger;
        this.activity = activity;
    }

    private BinaryMessenger messenger;
    private Activity activity;

    @Override
    public PlatformView create(Context context, int id, Object params) {
        Map<String, Object> __args__ = (Map<String, Object>) params;

        ////////////////////////////////初始化AndroidView////////////////////////////////////////

        ////////////////////////////////初始化AndroidView////////////////////////////////////////

        com.amap.api.maps.offlinemap.DownloadProgressView view = new com.amap.api.maps.offlinemap.DownloadProgressView(activity);

        // 同时存放viewId和refId的对象, 供后续viewId转refId使用
        getHEAP().put(String.valueOf(Integer.MAX_VALUE - id), view);
        getHEAP().put("com.amap.api.maps.offlinemap.DownloadProgressView:" + String.valueOf(System.identityHashCode(view)), view);
        return new PlatformView() {

            // add to HEAP
            @Override
            public View getView() {
                return view;
            }

            @Override
            public void dispose() {}
        };
    }
}
