//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

#import "SubHandler8.h"
#import "FluttifyMessageCodec.h"
#import <MAMapKit/MAMapKit.h>
#import "MATraceDelegate_Anonymous.h"
#import "MAMultiPointOverlayRendererDelegate_Anonymous.h"
#import "MAMapViewDelegate_Anonymous.h"

// Dart端一次方法调用所存在的栈, 只有当MethodChannel传递参数受限时, 再启用这个容器
extern NSMutableDictionary<NSString*, NSObject*>* STACK;
// Dart端随机存取对象的容器
extern NSMutableDictionary<NSString*, NSObject*>* HEAP;
// 日志打印开关
extern BOOL enableLog;

@implementation AmapMapFluttifyPlugin (SubHandler8)
- (NSDictionary<NSString*, Handler>*) getSubHandler8 {
    __weak __typeof(self)weakSelf = self;
    return @{
        @"MAMapSizeContainsSize::MAMapSizeContainsSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapSizeContainsSize::MAMapSizeContainsSize(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* size1Value = (NSValue*) args[@"size1"];
            MAMapSize size1;
            if (size1Value != nil && (NSNull*) size1Value != [NSNull null]) {
              [size1Value getValue:&size1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* size2Value = (NSValue*) args[@"size2"];
            MAMapSize size2;
            if (size2Value != nil && (NSNull*) size2Value != [NSNull null]) {
              [size2Value getValue:&size2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapSizeContainsSize(size1, size2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectContainsPoint::MAMapRectContainsPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectContainsPoint::MAMapRectContainsPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectContainsPoint(rect, point);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectIntersectsRect::MAMapRectIntersectsRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectIntersectsRect::MAMapRectIntersectsRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rect1Value = (NSValue*) args[@"rect1"];
            MAMapRect rect1;
            if (rect1Value != nil && (NSNull*) rect1Value != [NSNull null]) {
              [rect1Value getValue:&rect1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* rect2Value = (NSValue*) args[@"rect2"];
            MAMapRect rect2;
            if (rect2Value != nil && (NSNull*) rect2Value != [NSNull null]) {
              [rect2Value getValue:&rect2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectIntersectsRect(rect1, rect2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectContainsRect::MAMapRectContainsRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectContainsRect::MAMapRectContainsRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rect1Value = (NSValue*) args[@"rect1"];
            MAMapRect rect1;
            if (rect1Value != nil && (NSNull*) rect1Value != [NSNull null]) {
              [rect1Value getValue:&rect1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* rect2Value = (NSValue*) args[@"rect2"];
            MAMapRect rect2;
            if (rect2Value != nil && (NSNull*) rect2Value != [NSNull null]) {
              [rect2Value getValue:&rect2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectContainsRect(rect1, rect2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MACircleContainsPoint::MACircleContainsPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircleContainsPoint::MACircleContainsPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* centerValue = (NSValue*) args[@"center"];
            MAMapPoint center;
            if (centerValue != nil && (NSNull*) centerValue != [NSNull null]) {
              [centerValue getValue:&center];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"center不能为null"]);
              return;
            }
        
            // jsonable arg
            double radius = [args[@"radius"] doubleValue];
        
            // ref
        
        
            // invoke native method
            BOOL result = MACircleContainsPoint(point, center, radius);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MACircleContainsCoordinate::MACircleContainsCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MACircleContainsCoordinate::MACircleContainsCoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            CLLocationCoordinate2D point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* centerValue = (NSValue*) args[@"center"];
            CLLocationCoordinate2D center;
            if (centerValue != nil && (NSNull*) centerValue != [NSNull null]) {
              [centerValue getValue:&center];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"center不能为null"]);
              return;
            }
        
            // jsonable arg
            double radius = [args[@"radius"] doubleValue];
        
            // ref
        
        
            // invoke native method
            BOOL result = MACircleContainsCoordinate(point, center, radius);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAGetNearestMapPointFromPolyline::MAGetNearestMapPointFromPolyline": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGetNearestMapPointFromPolyline::MAGetNearestMapPointFromPolyline(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // list arg struct
            NSArray<NSValue*>* polylineValueList = (NSArray<NSValue*>*) args[@"polyline"];
            MAMapPoint polyline[polylineValueList.count];
            for (NSUInteger __i__ = 0; __i__ < polylineValueList.count; __i__++) {
                NSValue* polylineValue = (NSValue*) [polylineValueList objectAtIndex:__i__];
                MAMapPoint polylineItem;
                [polylineValue getValue:&polylineItem];
                polyline[__i__] = polylineItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            MAMapPoint result = MAGetNearestMapPointFromPolyline(point, polyline, count);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"MAPolygonContainsPoint::MAPolygonContainsPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygonContainsPoint::MAPolygonContainsPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // list arg struct
            NSArray<NSValue*>* polygonValueList = (NSArray<NSValue*>*) args[@"polygon"];
            MAMapPoint polygon[polygonValueList.count];
            for (NSUInteger __i__ = 0; __i__ < polygonValueList.count; __i__++) {
                NSValue* polygonValue = (NSValue*) [polygonValueList objectAtIndex:__i__];
                MAMapPoint polygonItem;
                [polygonValue getValue:&polygonItem];
                polygon[__i__] = polygonItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            BOOL result = MAPolygonContainsPoint(point, polygon, count);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAPolygonContainsCoordinate::MAPolygonContainsCoordinate": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolygonContainsCoordinate::MAPolygonContainsCoordinate(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            CLLocationCoordinate2D point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // list arg struct
            NSArray<NSValue*>* polygonValueList = (NSArray<NSValue*>*) args[@"polygon"];
            CLLocationCoordinate2D polygon[polygonValueList.count];
            for (NSUInteger __i__ = 0; __i__ < polygonValueList.count; __i__++) {
                NSValue* polygonValue = (NSValue*) [polygonValueList objectAtIndex:__i__];
                CLLocationCoordinate2D polygonItem;
                [polygonValue getValue:&polygonItem];
                polygon[__i__] = polygonItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
        
            // ref
        
        
            // invoke native method
            BOOL result = MAPolygonContainsCoordinate(point, polygon, count);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAGetNearestMapPointFromLine::MAGetNearestMapPointFromLine": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGetNearestMapPointFromLine::MAGetNearestMapPointFromLine(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* lineStartValue = (NSValue*) args[@"lineStart"];
            MAMapPoint lineStart;
            if (lineStartValue != nil && (NSNull*) lineStartValue != [NSNull null]) {
              [lineStartValue getValue:&lineStart];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"lineStart不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* lineEndValue = (NSValue*) args[@"lineEnd"];
            MAMapPoint lineEnd;
            if (lineEndValue != nil && (NSNull*) lineEndValue != [NSNull null]) {
              [lineEndValue getValue:&lineEnd];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"lineEnd不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            MAMapPoint result = MAGetNearestMapPointFromLine(lineStart, lineEnd, point);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"MAAreaForPolygon::MAAreaForPolygon": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAAreaForPolygon::MAAreaForPolygon(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* coordinatesValueList = (NSArray<NSValue*>*) args[@"coordinates"];
            CLLocationCoordinate2D coordinates[coordinatesValueList.count];
            for (NSUInteger __i__ = 0; __i__ < coordinatesValueList.count; __i__++) {
                NSValue* coordinatesValue = (NSValue*) [coordinatesValueList objectAtIndex:__i__];
                CLLocationCoordinate2D coordinatesItem;
                [coordinatesValue getValue:&coordinatesItem];
                coordinates[__i__] = coordinatesItem;
            }
            // jsonable arg
            int count = [args[@"count"] intValue];
        
            // ref
        
        
            // invoke native method
            double result = MAAreaForPolygon(coordinates, count);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapPointMake::MAMapPointMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapPointMake::MAMapPointMake(%@)", args);
            }
        
            // args
            // jsonable arg
            double x = [args[@"x"] doubleValue];
            // jsonable arg
            double y = [args[@"y"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MAMapPoint result = MAMapPointMake(x, y);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapPoint)];
        
            methodResult(__result__);
        },
        @"MAMapSizeMake::MAMapSizeMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapSizeMake::MAMapSizeMake(%@)", args);
            }
        
            // args
            // jsonable arg
            double width = [args[@"width"] doubleValue];
            // jsonable arg
            double height = [args[@"height"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MAMapSize result = MAMapSizeMake(width, height);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapSize)];
        
            methodResult(__result__);
        },
        @"MAMapRectMake::MAMapRectMake": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectMake::MAMapRectMake(%@)", args);
            }
        
            // args
            // jsonable arg
            double x = [args[@"x"] doubleValue];
            // jsonable arg
            double y = [args[@"y"] doubleValue];
            // jsonable arg
            double width = [args[@"width"] doubleValue];
            // jsonable arg
            double height = [args[@"height"] doubleValue];
        
            // ref
        
        
            // invoke native method
            MAMapRect result = MAMapRectMake(x, y, width, height);
        
            // result
            // 返回值: 结构体
            NSValue* __result__ = [NSValue value:&result withObjCType:@encode(MAMapRect)];
        
            methodResult(__result__);
        },
        @"MAMapRectGetMinX::MAMapRectGetMinX": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMinX::MAMapRectGetMinX(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMinX(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetMinY::MAMapRectGetMinY": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMinY::MAMapRectGetMinY(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMinY(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetMidX::MAMapRectGetMidX": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMidX::MAMapRectGetMidX(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMidX(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetMidY::MAMapRectGetMidY": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMidY::MAMapRectGetMidY(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMidY(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetMaxX::MAMapRectGetMaxX": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMaxX::MAMapRectGetMaxX(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMaxX(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetMaxY::MAMapRectGetMaxY": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetMaxY::MAMapRectGetMaxY(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetMaxY(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetWidth::MAMapRectGetWidth": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetWidth::MAMapRectGetWidth(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetWidth(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectGetHeight::MAMapRectGetHeight": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectGetHeight::MAMapRectGetHeight(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAMapRectGetHeight(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapPointEqualToPoint::MAMapPointEqualToPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapPointEqualToPoint::MAMapPointEqualToPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* point1Value = (NSValue*) args[@"point1"];
            MAMapPoint point1;
            if (point1Value != nil && (NSNull*) point1Value != [NSNull null]) {
              [point1Value getValue:&point1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* point2Value = (NSValue*) args[@"point2"];
            MAMapPoint point2;
            if (point2Value != nil && (NSNull*) point2Value != [NSNull null]) {
              [point2Value getValue:&point2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapPointEqualToPoint(point1, point2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapSizeEqualToSize::MAMapSizeEqualToSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapSizeEqualToSize::MAMapSizeEqualToSize(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* size1Value = (NSValue*) args[@"size1"];
            MAMapSize size1;
            if (size1Value != nil && (NSNull*) size1Value != [NSNull null]) {
              [size1Value getValue:&size1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* size2Value = (NSValue*) args[@"size2"];
            MAMapSize size2;
            if (size2Value != nil && (NSNull*) size2Value != [NSNull null]) {
              [size2Value getValue:&size2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapSizeEqualToSize(size1, size2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectEqualToRect::MAMapRectEqualToRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectEqualToRect::MAMapRectEqualToRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rect1Value = (NSValue*) args[@"rect1"];
            MAMapRect rect1;
            if (rect1Value != nil && (NSNull*) rect1Value != [NSNull null]) {
              [rect1Value getValue:&rect1];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect1不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* rect2Value = (NSValue*) args[@"rect2"];
            MAMapRect rect2;
            if (rect2Value != nil && (NSNull*) rect2Value != [NSNull null]) {
              [rect2Value getValue:&rect2];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect2不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectEqualToRect(rect1, rect2);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectIsNull::MAMapRectIsNull": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectIsNull::MAMapRectIsNull(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectIsNull(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAMapRectIsEmpty::MAMapRectIsEmpty": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAMapRectIsEmpty::MAMapRectIsEmpty(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            BOOL result = MAMapRectIsEmpty(rect);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAStringFromMapPoint::MAStringFromMapPoint": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAStringFromMapPoint::MAStringFromMapPoint(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSString* result = MAStringFromMapPoint(point);
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAStringFromMapSize::MAStringFromMapSize": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAStringFromMapSize::MAStringFromMapSize(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* sizeValue = (NSValue*) args[@"size"];
            MAMapSize size;
            if (sizeValue != nil && (NSNull*) sizeValue != [NSNull null]) {
              [sizeValue getValue:&size];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"size不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSString* result = MAStringFromMapSize(size);
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAStringFromMapRect::MAStringFromMapRect": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAStringFromMapRect::MAStringFromMapRect(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* rectValue = (NSValue*) args[@"rect"];
            MAMapRect rect;
            if (rectValue != nil && (NSNull*) rectValue != [NSNull null]) {
              [rectValue getValue:&rect];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"rect不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            NSString* result = MAStringFromMapRect(rect);
        
            // result
            // 返回值: jsonable
            id __result__ = result;
        
            methodResult(__result__);
        },
        @"MAGetDirectionFromCoords::MAGetDirectionFromCoords": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGetDirectionFromCoords::MAGetDirectionFromCoords(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* fromCoordValue = (NSValue*) args[@"fromCoord"];
            CLLocationCoordinate2D fromCoord;
            if (fromCoordValue != nil && (NSNull*) fromCoordValue != [NSNull null]) {
              [fromCoordValue getValue:&fromCoord];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"fromCoord不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* toCoordValue = (NSValue*) args[@"toCoord"];
            CLLocationCoordinate2D toCoord;
            if (toCoordValue != nil && (NSNull*) toCoordValue != [NSNull null]) {
              [toCoordValue getValue:&toCoord];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"toCoord不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            CLLocationDirection result = MAGetDirectionFromCoords(fromCoord, toCoord);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAGetDirectionFromPoints::MAGetDirectionFromPoints": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGetDirectionFromPoints::MAGetDirectionFromPoints(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* fromPointValue = (NSValue*) args[@"fromPoint"];
            MAMapPoint fromPoint;
            if (fromPointValue != nil && (NSNull*) fromPointValue != [NSNull null]) {
              [fromPointValue getValue:&fromPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"fromPoint不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* toPointValue = (NSValue*) args[@"toPoint"];
            MAMapPoint toPoint;
            if (toPointValue != nil && (NSNull*) toPointValue != [NSNull null]) {
              [toPointValue getValue:&toPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"toPoint不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            CLLocationDirection result = MAGetDirectionFromPoints(fromPoint, toPoint);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAGetDistanceFromPointToLine::MAGetDistanceFromPointToLine": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAGetDistanceFromPointToLine::MAGetDistanceFromPointToLine(%@)", args);
            }
        
            // args
            // struct arg
            NSValue* pointValue = (NSValue*) args[@"point"];
            MAMapPoint point;
            if (pointValue != nil && (NSNull*) pointValue != [NSNull null]) {
              [pointValue getValue:&point];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"point不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* lineBeginValue = (NSValue*) args[@"lineBegin"];
            MAMapPoint lineBegin;
            if (lineBeginValue != nil && (NSNull*) lineBeginValue != [NSNull null]) {
              [lineBeginValue getValue:&lineBegin];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"lineBegin不能为null"]);
              return;
            }
        
            // struct arg
            NSValue* lineEndValue = (NSValue*) args[@"lineEnd"];
            MAMapPoint lineEnd;
            if (lineEndValue != nil && (NSNull*) lineEndValue != [NSNull null]) {
              [lineEndValue getValue:&lineEnd];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"lineEnd不能为null"]);
              return;
            }
        
        
            // ref
        
        
            // invoke native method
            double result = MAGetDistanceFromPointToLine(point, lineBegin, lineEnd);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
        @"MAPolylineHitTest::MAPolylineHitTest": ^(NSObject <FlutterPluginRegistrar> * registrar, id args, FlutterResult methodResult) {
            if (enableLog) {
                NSLog(@"fluttify-objc: MAPolylineHitTest::MAPolylineHitTest(%@)", args);
            }
        
            // args
            // list arg struct
            NSArray<NSValue*>* linePointsValueList = (NSArray<NSValue*>*) args[@"linePoints"];
            MAMapPoint linePoints[linePointsValueList.count];
            for (NSUInteger __i__ = 0; __i__ < linePointsValueList.count; __i__++) {
                NSValue* linePointsValue = (NSValue*) [linePointsValueList objectAtIndex:__i__];
                MAMapPoint linePointsItem;
                [linePointsValue getValue:&linePointsItem];
                linePoints[__i__] = linePointsItem;
            }
            // jsonable arg
            NSUInteger count = [args[@"count"] unsignedIntegerValue];
            // struct arg
            NSValue* tappedPointValue = (NSValue*) args[@"tappedPoint"];
            MAMapPoint tappedPoint;
            if (tappedPointValue != nil && (NSNull*) tappedPointValue != [NSNull null]) {
              [tappedPointValue getValue:&tappedPoint];
            } else {
              methodResult([FlutterError errorWithCode:@"参数非法"
                                               message:@"参数非法"
                                               details:@"tappedPoint不能为null"]);
              return;
            }
        
            // jsonable arg
            CGFloat lineWidth = [args[@"lineWidth"] floatValue];
        
            // ref
        
        
            // invoke native method
            BOOL result = MAPolylineHitTest(linePoints, count, tappedPoint, lineWidth);
        
            // result
            // 返回值: Value
            NSObject* __result__ = @(result);
        
            methodResult(__result__);
        },
    };
}

@end
